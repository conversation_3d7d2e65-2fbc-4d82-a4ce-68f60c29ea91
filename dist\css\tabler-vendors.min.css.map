{"version": 3, "sources": ["tabler-vendors.css", "dist/css/tabler-vendors.css"], "names": [], "mappings": "AAaA,aACE,OAAA,EACA,WAAA,KACA,WAAA,IACA,cAAA,EACA,MAAA,oBAGF,iBACE,OAAA,QACA,QAAA,MAAA,EAGF,WACE,WAAA,yBACA,cAAA,KAGF,aACE,MAAA,KACA,OAAA,KACA,OAAA,IAAA,yBAAA,KACA,WAAA,EAAA,MAAA,OAAA,eACA,cAAA,KACA,WAAA,aACA,QAAA,EAEF,mBAAA,oBACE,QAAA,KAEF,8BACE,MAAA,KACA,OAAA,KACA,IAAA,OACA,MAAA,OACA,OAAA,IAAA,IAAA,EAAA,EAEF,yBAAA,mBACE,WAAA,EAAA,EAAA,EAAA,IAAA,OAAA,CAAA,EAAA,EAAA,EAAA,OAAA,kCAGF,cACE,WAAA,aAGF,YACE,iCAAA,sBACA,qCAAA,sBACA,qCAAA,sBACA,2CAAA,oBACA,2CAAA,oBACA,uBAAA,uBACA,6BAAA,oBACA,+BAAA,oBACA,6BAAA,oBACA,+BAAA,iBACA,4BAAA,oBACA,gCAAA,uBACA,uCAAA,uBACA,KAAA,QACA,oBAAA,KAAA,iBAAA,KAAA,YAAA,KAEF,kCACE,uBAAA,kBAEF,gBACE,KAAA,eAEF,6BACE,OAAA,yBAAA,yBAAA,yBACA,cAAA,0BACA,WAAA,KAEF,gDCGA,gDDDE,YAAA,yCAEF,kDCGA,sDDDE,OAAA,kBAEF,4DACE,QAAA,MAAA,YACA,UAAA,OAEF,uCACE,OAAA,kBACA,QAAA,MAAA,YACA,WAAA,MAAA,GAAA,CAAA,iBAAA,GAAA,CAAA,aAAA,IAEF,uCACE,uCACE,WAAA,MAGJ,+BACE,WAAA,6BAEF,kDACE,WAAA,KACA,iBAAA,qBAGF,MACE,qBAAA,KACA,cAAA,KAGF,UACE,MAAA,QAGF,YACE,MAAA,QAEF,2BACE,MAAA,KACA,OAAA,KAGF,0BCGA,yBAA0B,yBAA0B,wBDDlD,WAAA,6BAEF,mCAAA,iCACE,qBAAA,OAGF,aACE,WAAA,uBACA,MAAA,uBACA,WAAA,gCACA,QAAA,KAEF,qBACE,QAAA,MAAA,OAGF,YCGA,kBDDE,MAAA,uBAGF,oCACE,MAAA,QADF,+BACE,MAAA,QAGF,kCCOA,2CDLE,WAAA,iCACA,OAAA,IAAA,MAAA,yBACA,MAAA,uBAGF,iCACE,QAAA,EAEF,0CACE,MAAA,qBAGF,oBACE,MAAA,4BACA,WAAA,sCACA,UAAA,qBACA,QAAA,iBACA,WAAA,eAGF,0BACE,WAAA,cACA,OAAA,YACA,OAAA,YACA,YAAA,6BACA,QAAA,OAAA,gBAGF,4BACE,QAAA,IAAA,YAGF,iCACE,QAAA,EAAA,MAAA,YAGF,kCACE,UAAA,eAGF,iBACE,KAAA,iCAGF,qBACE,OAAA,mCAGF,wBACE,MAAA,kBAGF,uDACE,OAAA,mCCUF,mBDPA,gBAEE,cAAA,QAGF,aACE,WAAA,QACA,YAAA,QACA,UAAA,OACA,WAAA,4BAGF,oDACE,cAAA,EACA,YAAA,+BAGF,kCACE,iBAAA,2CACA,OAAA,yBAAA,yBAAA,yBACA,UAAA,KAEF,qEACE,QAAA,KACA,YAAA,OAEF,6FACE,MAAA,OACA,OAAA,OAGF,cACE,OAAA,yBAAA,yBAAA,yBACA,WAAA,2CACA,MAAA,uBACA,QAAA,EACA,QAAA,KACA,YAAA,OACA,gBAAA,OACA,YAAA,EACA,MAAA,OACA,OAAA,OACA,UAAA,KACA,WAAA,4BAEF,0BACE,IAAA,OAGF,UACE,OAAA,yBAAA,OAAA,mCACA,MAAA,gCACA,QAAA,eAEF,wBACE,OAAA,yBAAA,OAAA,oBACA,WAAA,kCACA,MAAA,oBAEF,oCACE,QAAA,EAEF,sBACE,OAAA,MAEF,gCACE,cAAA,0BAEF,uCACE,OAAA,KAGF,sBACE,YAAA,kBACA,WAAA,6BACA,wBAAA,oBAAA,gBAAA,oBAGF,mCACE,MAAA,kBAGF,kBACE,WAAA,uBAGF,KACE,kBAAA,oBAGF,aACE,OAAA,yBAAA,yBAAA,mCACA,cAAA,cACA,YAAA,sCAGF,oBACE,QAAA,EAAA,MAAA,EAGF,2BACE,WAAA,cAGF,iDACE,cAAA,yBAAA,yBAAA,mCACA,WAAA,eACA,QAAA,YAGF,UACE,OAAA,YAGF,eACE,WAAA,yBAAA,yBAAA,mCAGF,2BCQA,iDDNE,WAAA,cAGF,MACE,eAAA,KACA,gBAAA,mBACA,yBAAA,yBAGF,uBACE,MAAA,qCACA,OAAA,qCAEF,kDACE,MAAA,wCAEF,4CACE,MAAA,qBACA,KAAA,aACA,OAAA,aAGF,YACE,WAAA,gCACA,iBAAA,uBAGF,gBACE,cAAA,0BACA,MAAA,uBACA,aAAA,yBACA,WAAA,IAEF,sBACE,QAAA,EACA,WAAA,EAAA,EAAA,EAAA,OAAA,kCACA,aAAA,kCAGF,qBACE,cAAA,0BACA,QAAA,EAAA,IAAA,IAAA,IAEF,2BACE,QAAA,EACA,WAAA,EAAA,EAAA,EAAA,OAAA,kCAGF,aACE,cAAA,0BACA,SAAA,QAEF,oBAAA,mBAAA,oBACE,cAAA,0BAEF,0BACE,QAAA,EACA,WAAA,EAAA,EAAA,EAAA,OAAA,kCAGF,WACE,QAAA,MAEF,kBACE,MAAA,OACA,OAAA,OACA,KAAA,IACA,MAAA,KACA,cAAA,0BAEF,wBACE,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,qCAEF,wBACE,QAAA,EACA,WAAA,EAAA,EAAA,EAAA,OAAA,kCAEF,iBACE,aAAA,OAGF,cACE,YAAA,IACA,MAAA", "sourcesContent": ["/**\n * Converts a given value to a percentage string.\n *\n * @param {Number} $value - The value to be converted to a percentage.\n * @return {String} - The percentage representation of the value.\n */\n/**\n * Generates a transparent version of the given color.\n *\n * @param {Color} $color - The base color to be made transparent.\n * @param {Number} $alpha - The level of transparency, ranging from 0 (fully transparent) to 1 (fully opaque). Default is 1.\n * @return {Color} - The resulting color with the specified transparency.\n */\n.noUi-target {\n  border: 0;\n  box-shadow: none;\n  background: none;\n  border-radius: 0;\n  color: var(--tblr-primary);\n}\n\n.noUi-horizontal {\n  height: 1.25rem;\n  padding: 0.5rem 0;\n}\n\n.noUi-base {\n  background: var(--tblr-border-color);\n  border-radius: 1rem;\n}\n\n.noUi-handle {\n  width: 1rem;\n  height: 1rem;\n  border: 2px var(--tblr-border-style) #ffffff;\n  box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);\n  border-radius: 1rem;\n  background: currentColor;\n  outline: 0;\n}\n.noUi-handle:before, .noUi-handle:after {\n  content: none;\n}\n.noUi-horizontal .noUi-handle {\n  width: 1rem;\n  height: 1rem;\n  top: -0.5rem;\n  right: -0.5rem;\n  margin: 1px 1px 0 0;\n}\n.noUi-handle.noUi-active, .noUi-handle:focus {\n  box-shadow: 0 0 0 1px #f6f8fb, 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n}\n\n.noUi-connect {\n  background: currentColor;\n}\n\n.litepicker {\n  --litepicker-month-weekday-color: var(--tblr-secondary);\n  --litepicker-button-prev-month-color: var(--tblr-secondary);\n  --litepicker-button-next-month-color: var(--tblr-secondary);\n  --litepicker-button-prev-month-color-hover: var(--tblr-primary);\n  --litepicker-button-next-month-color-hover: var(--tblr-primary);\n  --litepicker-day-color: var(--tblr-body-color);\n  --litepicker-day-color-hover: var(--tblr-primary);\n  --litepicker-is-start-color-bg: var(--tblr-primary);\n  --litepicker-is-end-color-bg: var(--tblr-primary);\n  --litepicker-is-in-range-color: var(--tblr-info);\n  --litepicker-is-today-color: var(--tblr-primary);\n  --litepicker-month-header-color: var(--tblr-body-color);\n  --litepicker-container-months-color-bg: var(--tblr-bg-surface);\n  font: inherit;\n  user-select: none;\n}\n.litepicker .day-item.is-in-range {\n  --litepicker-day-color: var(--tblr-light);\n}\n.litepicker svg {\n  fill: none !important;\n}\n.litepicker .container__main {\n  border: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);\n  border-radius: var(--tblr-border-radius);\n  box-shadow: none;\n}\n.litepicker .container__months .month-item-name,\n.litepicker .container__months .month-item-year {\n  font-weight: var(--tblr-font-weight-medium) !important;\n}\n.litepicker .container__months .button-next-month,\n.litepicker .container__months .button-previous-month {\n  cursor: pointer !important;\n}\n.litepicker .container__months .month-item-weekdays-row > div {\n  padding: 0.5rem 0 !important;\n  font-size: 0.75rem;\n}\n.litepicker .container__days .day-item {\n  cursor: pointer !important;\n  padding: 0.5rem 0 !important;\n  transition: color 0.3s, background-color 0.3s, border-color 0.3s;\n}\n@media (prefers-reduced-motion: reduce) {\n  .litepicker .container__days .day-item {\n    transition: none;\n  }\n}\n.datepicker-inline .litepicker {\n  box-shadow: var(--tblr-box-shadow-input);\n}\n.datepicker-inline .litepicker .container__months {\n  box-shadow: none;\n  background-color: var(--tblr-bg-forms);\n}\n\n:root {\n  --ts-pr-clear-button: 0rem;\n  --ts-pr-caret: 0rem;\n}\n\n.ts-input {\n  color: inherit;\n}\n\n.ts-control {\n  color: inherit;\n}\n.ts-control .dropdown-menu {\n  width: 100%;\n  height: auto;\n}\n\n.ts-wrapper .form-control,\n.ts-wrapper .form-select, .ts-wrapper.form-control, .ts-wrapper.form-select {\n  box-shadow: var(--tblr-box-shadow-input);\n}\n.ts-wrapper.is-invalid .ts-control, .ts-wrapper.is-valid .ts-control {\n  --ts-pr-clear-button: 1.5rem;\n}\n\n.ts-dropdown {\n  background: var(--tblr-bg-surface);\n  color: var(--tblr-body-color);\n  box-shadow: var(--tblr-box-shadow-dropdown);\n  z-index: 1000;\n}\n.ts-dropdown .option {\n  padding: 0.5rem 0.75rem;\n}\n\n.ts-control,\n.ts-control input {\n  color: var(--tblr-body-color);\n}\n\n.ts-control input::placeholder {\n  color: #8a97ab;\n}\n\n.ts-wrapper.multi .ts-control > div,\n.ts-wrapper.multi.disabled .ts-control > div {\n  background: var(--tblr-bg-surface-secondary);\n  border: 1px solid var(--tblr-border-color);\n  color: var(--tblr-body-color);\n}\n\n.ts-wrapper.disabled .ts-control {\n  opacity: 1;\n}\n.ts-wrapper.disabled .ts-control > div.item {\n  color: var(--tblr-gray-500);\n}\n\n.apexcharts-tooltip {\n  color: var(--tblr-light) !important;\n  background: var(--tblr-bg-surface-dark) !important;\n  font-size: 0.765625rem !important;\n  padding: 0.25rem !important;\n  box-shadow: none !important;\n}\n\n.apexcharts-tooltip-title {\n  background: transparent !important;\n  border: 0 !important;\n  margin: 0 !important;\n  font-weight: var(--tblr-font-weight-bold);\n  padding: 0.25rem 0.5rem !important;\n}\n\n.apexcharts-tooltip-y-group {\n  padding: 2px 0 !important;\n}\n\n.apexcharts-tooltip-series-group {\n  padding: 0 0.5rem 0 !important;\n}\n\n.apexcharts-tooltip-marker:before {\n  font-size: 16px !important;\n}\n\n.apexcharts-text {\n  fill: var(--tblr-body-color) !important;\n}\n\n.apexcharts-gridline {\n  stroke: var(--tblr-border-color) !important;\n}\n\n.apexcharts-legend-text {\n  color: inherit !important;\n}\n\n.apexcharts-radialbar-track .apexcharts-radialbar-area {\n  stroke: var(--tblr-border-color) !important;\n}\n\n.apexcharts-svg,\n.apexcharts-canvas {\n  border-radius: inherit;\n}\n\n.jvm-tooltip {\n  background: #182433;\n  font-family: inherit;\n  font-size: 0.75rem;\n  box-shadow: var(--tblr-box-shadow-card);\n}\n\n.jvm-series-container .jvm-legend .jvm-legend-title {\n  border-bottom: 0;\n  font-weight: var(--tblr-font-weight-medium);\n}\n\n.jvm-series-container .jvm-legend {\n  background-color: var(--tblr-card-bg, var(--tblr-bg-surface));\n  border: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);\n  min-width: 8rem;\n}\n.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick {\n  display: flex;\n  align-items: center;\n}\n.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick .jvm-legend-tick-sample {\n  width: 0.75rem;\n  height: 0.75rem;\n}\n\n.jvm-zoom-btn {\n  border: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);\n  background: var(--tblr-card-bg, var(--tblr-bg-surface));\n  color: var(--tblr-body-color);\n  padding: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  line-height: 1;\n  width: 1.5rem;\n  height: 1.5rem;\n  font-size: 1rem;\n  box-shadow: var(--tblr-box-shadow-card);\n}\n.jvm-zoom-btn.jvm-zoomout {\n  top: 2.5rem;\n}\n\n.dropzone {\n  border: var(--tblr-border-width) dashed var(--tblr-border-color) !important;\n  color: var(--tblr-secondary) !important;\n  padding: 1rem !important;\n}\n.dropzone.dz-drag-hover {\n  border: var(--tblr-border-width) dashed var(--tblr-primary);\n  background: rgba(var(--tblr-primary-rgb), 0.01);\n  color: var(--tblr-primary);\n}\n.dropzone.dz-drag-hover .dz-message {\n  opacity: 1;\n}\n.dropzone .dz-preview {\n  margin: 0.5rem;\n}\n.dropzone .dz-preview .dz-image {\n  border-radius: var(--tblr-border-radius);\n}\n.dropzone .dz-preview .dz-success-mark {\n  height: 54px;\n}\n\n.fslightbox-container {\n  font-family: inherit !important;\n  background: rgba(24, 36, 51, 0.24) !important;\n  backdrop-filter: blur(4px) !important;\n}\n\n.fslightbox-slide-number-container {\n  color: inherit !important;\n}\n\n.fslightbox-slash {\n  background: currentColor !important;\n}\n\nbody {\n  --plyr-color-main: var(--tblr-primary);\n}\n\n.tox-tinymce {\n  border: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color) !important;\n  border-radius: 6px !important;\n  font-family: var(--tblr-font-sans-serif) !important;\n}\n\n.tox-toolbar__group {\n  padding: 0 0.5rem 0;\n}\n\n.tox .tox-toolbar__primary {\n  background: transparent !important;\n}\n\n.tox:not(.tox-tinymce-inline) .tox-editor-header {\n  border-bottom: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color) !important;\n  box-shadow: none !important;\n  padding: 0 !important;\n}\n\n.tox-tbtn {\n  margin: 0 !important;\n}\n\n.tox-statusbar {\n  border-top: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color) !important;\n}\n\n.tox .tox-toolbar-overlord,\n.tox:not(.tox-tinymce-inline) .tox-editor-header {\n  background: transparent !important;\n}\n\n:root {\n  --gl-star-size: auto;\n  --gl-star-color: var(--tblr-yellow);\n  --gl-star-color-inactive: var(--tblr-border-color);\n}\n\n[data-star-rating] svg {\n  width: var(--tblr-icon-size, --gl-star-size);\n  height: var(--tblr-icon-size, --gl-star-size);\n}\n[data-star-rating] :not(.gl-active) > .gl-star-full {\n  color: var(--gl-star-color-inactive) !important;\n}\n[data-star-rating] .gl-active > .gl-star-full {\n  color: var(--gl-star-color);\n  fill: currentColor;\n  stroke: currentColor;\n}\n\n.clr-picker {\n  box-shadow: var(--tblr-box-shadow-dropdown);\n  background-color: var(--tblr-bg-surface);\n}\n\ninput.clr-color {\n  border-radius: var(--tblr-border-radius);\n  color: var(--tblr-body-color);\n  border-color: var(--tblr-border-color);\n  background: transparent;\n}\ninput.clr-color:focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n  border-color: rgba(var(--tblr-primary-rgb), 0.25);\n}\n\n.clr-swatches button {\n  border-radius: var(--tblr-border-radius);\n  padding: 0 2px 4px 2px;\n}\n.clr-swatches button:focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n}\n\n.clr-preview {\n  border-radius: var(--tblr-border-radius);\n  overflow: visible;\n}\n.clr-preview button, .clr-preview:before, .clr-preview:after {\n  border-radius: var(--tblr-border-radius);\n}\n.clr-preview button:focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n}\n\n.clr-field {\n  display: block;\n}\n.clr-field button {\n  width: 1.5rem;\n  height: 1.5rem;\n  left: 6px;\n  right: auto;\n  border-radius: var(--tblr-border-radius);\n}\n.clr-field button:after {\n  box-shadow: inset 0 0 0 1px var(--tblr-border-color-translucent);\n}\n.clr-field button:focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n}\n.clr-field input {\n  padding-left: 2.5rem;\n}\n\n.typed-cursor {\n  font-weight: 500;\n  color: #6c7a91;\n}\n", "/**\n * Converts a given value to a percentage string.\n *\n * @param {Number} $value - The value to be converted to a percentage.\n * @return {String} - The percentage representation of the value.\n */\n/**\n * Generates a transparent version of the given color.\n *\n * @param {Color} $color - The base color to be made transparent.\n * @param {Number} $alpha - The level of transparency, ranging from 0 (fully transparent) to 1 (fully opaque). Default is 1.\n * @return {Color} - The resulting color with the specified transparency.\n */\n.noUi-target {\n  border: 0;\n  box-shadow: none;\n  background: none;\n  border-radius: 0;\n  color: var(--tblr-primary);\n}\n\n.noUi-horizontal {\n  height: 1.25rem;\n  padding: 0.5rem 0;\n}\n\n.noUi-base {\n  background: var(--tblr-border-color);\n  border-radius: 1rem;\n}\n\n.noUi-handle {\n  width: 1rem;\n  height: 1rem;\n  border: 2px var(--tblr-border-style) #ffffff;\n  box-shadow: 0 0.1rem 0.25rem rgba(0, 0, 0, 0.1);\n  border-radius: 1rem;\n  background: currentColor;\n  outline: 0;\n}\n.noUi-handle:before, .noUi-handle:after {\n  content: none;\n}\n.noUi-horizontal .noUi-handle {\n  width: 1rem;\n  height: 1rem;\n  top: -0.5rem;\n  right: -0.5rem;\n  margin: 1px 1px 0 0;\n}\n.noUi-handle.noUi-active, .noUi-handle:focus {\n  box-shadow: 0 0 0 1px #f6f8fb, 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n}\n\n.noUi-connect {\n  background: currentColor;\n}\n\n.litepicker {\n  --litepicker-month-weekday-color: var(--tblr-secondary);\n  --litepicker-button-prev-month-color: var(--tblr-secondary);\n  --litepicker-button-next-month-color: var(--tblr-secondary);\n  --litepicker-button-prev-month-color-hover: var(--tblr-primary);\n  --litepicker-button-next-month-color-hover: var(--tblr-primary);\n  --litepicker-day-color: var(--tblr-body-color);\n  --litepicker-day-color-hover: var(--tblr-primary);\n  --litepicker-is-start-color-bg: var(--tblr-primary);\n  --litepicker-is-end-color-bg: var(--tblr-primary);\n  --litepicker-is-in-range-color: var(--tblr-info);\n  --litepicker-is-today-color: var(--tblr-primary);\n  --litepicker-month-header-color: var(--tblr-body-color);\n  --litepicker-container-months-color-bg: var(--tblr-bg-surface);\n  font: inherit;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  user-select: none;\n}\n.litepicker .day-item.is-in-range {\n  --litepicker-day-color: var(--tblr-light);\n}\n.litepicker svg {\n  fill: none !important;\n}\n.litepicker .container__main {\n  border: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);\n  border-radius: var(--tblr-border-radius);\n  box-shadow: none;\n}\n.litepicker .container__months .month-item-name,\n.litepicker .container__months .month-item-year {\n  font-weight: var(--tblr-font-weight-medium) !important;\n}\n.litepicker .container__months .button-next-month,\n.litepicker .container__months .button-previous-month {\n  cursor: pointer !important;\n}\n.litepicker .container__months .month-item-weekdays-row > div {\n  padding: 0.5rem 0 !important;\n  font-size: 0.75rem;\n}\n.litepicker .container__days .day-item {\n  cursor: pointer !important;\n  padding: 0.5rem 0 !important;\n  transition: color 0.3s, background-color 0.3s, border-color 0.3s;\n}\n@media (prefers-reduced-motion: reduce) {\n  .litepicker .container__days .day-item {\n    transition: none;\n  }\n}\n.datepicker-inline .litepicker {\n  box-shadow: var(--tblr-box-shadow-input);\n}\n.datepicker-inline .litepicker .container__months {\n  box-shadow: none;\n  background-color: var(--tblr-bg-forms);\n}\n\n:root {\n  --ts-pr-clear-button: 0rem;\n  --ts-pr-caret: 0rem;\n}\n\n.ts-input {\n  color: inherit;\n}\n\n.ts-control {\n  color: inherit;\n}\n.ts-control .dropdown-menu {\n  width: 100%;\n  height: auto;\n}\n\n.ts-wrapper .form-control,\n.ts-wrapper .form-select, .ts-wrapper.form-control, .ts-wrapper.form-select {\n  box-shadow: var(--tblr-box-shadow-input);\n}\n.ts-wrapper.is-invalid .ts-control, .ts-wrapper.is-valid .ts-control {\n  --ts-pr-clear-button: 1.5rem;\n}\n\n.ts-dropdown {\n  background: var(--tblr-bg-surface);\n  color: var(--tblr-body-color);\n  box-shadow: var(--tblr-box-shadow-dropdown);\n  z-index: 1000;\n}\n.ts-dropdown .option {\n  padding: 0.5rem 0.75rem;\n}\n\n.ts-control,\n.ts-control input {\n  color: var(--tblr-body-color);\n}\n\n.ts-control input::-moz-placeholder {\n  color: #8a97ab;\n}\n\n.ts-control input::placeholder {\n  color: #8a97ab;\n}\n\n.ts-wrapper.multi .ts-control > div,\n.ts-wrapper.multi.disabled .ts-control > div {\n  background: var(--tblr-bg-surface-secondary);\n  border: 1px solid var(--tblr-border-color);\n  color: var(--tblr-body-color);\n}\n\n.ts-wrapper.disabled .ts-control {\n  opacity: 1;\n}\n.ts-wrapper.disabled .ts-control > div.item {\n  color: var(--tblr-gray-500);\n}\n\n.apexcharts-tooltip {\n  color: var(--tblr-light) !important;\n  background: var(--tblr-bg-surface-dark) !important;\n  font-size: 0.765625rem !important;\n  padding: 0.25rem !important;\n  box-shadow: none !important;\n}\n\n.apexcharts-tooltip-title {\n  background: transparent !important;\n  border: 0 !important;\n  margin: 0 !important;\n  font-weight: var(--tblr-font-weight-bold);\n  padding: 0.25rem 0.5rem !important;\n}\n\n.apexcharts-tooltip-y-group {\n  padding: 2px 0 !important;\n}\n\n.apexcharts-tooltip-series-group {\n  padding: 0 0.5rem 0 !important;\n}\n\n.apexcharts-tooltip-marker:before {\n  font-size: 16px !important;\n}\n\n.apexcharts-text {\n  fill: var(--tblr-body-color) !important;\n}\n\n.apexcharts-gridline {\n  stroke: var(--tblr-border-color) !important;\n}\n\n.apexcharts-legend-text {\n  color: inherit !important;\n}\n\n.apexcharts-radialbar-track .apexcharts-radialbar-area {\n  stroke: var(--tblr-border-color) !important;\n}\n\n.apexcharts-svg,\n.apexcharts-canvas {\n  border-radius: inherit;\n}\n\n.jvm-tooltip {\n  background: #182433;\n  font-family: inherit;\n  font-size: 0.75rem;\n  box-shadow: var(--tblr-box-shadow-card);\n}\n\n.jvm-series-container .jvm-legend .jvm-legend-title {\n  border-bottom: 0;\n  font-weight: var(--tblr-font-weight-medium);\n}\n\n.jvm-series-container .jvm-legend {\n  background-color: var(--tblr-card-bg, var(--tblr-bg-surface));\n  border: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);\n  min-width: 8rem;\n}\n.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick {\n  display: flex;\n  align-items: center;\n}\n.jvm-series-container .jvm-legend .jvm-legend-inner .jvm-legend-tick .jvm-legend-tick-sample {\n  width: 0.75rem;\n  height: 0.75rem;\n}\n\n.jvm-zoom-btn {\n  border: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color);\n  background: var(--tblr-card-bg, var(--tblr-bg-surface));\n  color: var(--tblr-body-color);\n  padding: 0;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  line-height: 1;\n  width: 1.5rem;\n  height: 1.5rem;\n  font-size: 1rem;\n  box-shadow: var(--tblr-box-shadow-card);\n}\n.jvm-zoom-btn.jvm-zoomout {\n  top: 2.5rem;\n}\n\n.dropzone {\n  border: var(--tblr-border-width) dashed var(--tblr-border-color) !important;\n  color: var(--tblr-secondary) !important;\n  padding: 1rem !important;\n}\n.dropzone.dz-drag-hover {\n  border: var(--tblr-border-width) dashed var(--tblr-primary);\n  background: rgba(var(--tblr-primary-rgb), 0.01);\n  color: var(--tblr-primary);\n}\n.dropzone.dz-drag-hover .dz-message {\n  opacity: 1;\n}\n.dropzone .dz-preview {\n  margin: 0.5rem;\n}\n.dropzone .dz-preview .dz-image {\n  border-radius: var(--tblr-border-radius);\n}\n.dropzone .dz-preview .dz-success-mark {\n  height: 54px;\n}\n\n.fslightbox-container {\n  font-family: inherit !important;\n  background: rgba(24, 36, 51, 0.24) !important;\n  -webkit-backdrop-filter: blur(4px) !important;\n  backdrop-filter: blur(4px) !important;\n}\n\n.fslightbox-slide-number-container {\n  color: inherit !important;\n}\n\n.fslightbox-slash {\n  background: currentColor !important;\n}\n\nbody {\n  --plyr-color-main: var(--tblr-primary);\n}\n\n.tox-tinymce {\n  border: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color) !important;\n  border-radius: 6px !important;\n  font-family: var(--tblr-font-sans-serif) !important;\n}\n\n.tox-toolbar__group {\n  padding: 0 0.5rem 0;\n}\n\n.tox .tox-toolbar__primary {\n  background: transparent !important;\n}\n\n.tox:not(.tox-tinymce-inline) .tox-editor-header {\n  border-bottom: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color) !important;\n  box-shadow: none !important;\n  padding: 0 !important;\n}\n\n.tox-tbtn {\n  margin: 0 !important;\n}\n\n.tox-statusbar {\n  border-top: var(--tblr-border-width) var(--tblr-border-style) var(--tblr-border-color) !important;\n}\n\n.tox .tox-toolbar-overlord,\n.tox:not(.tox-tinymce-inline) .tox-editor-header {\n  background: transparent !important;\n}\n\n:root {\n  --gl-star-size: auto;\n  --gl-star-color: var(--tblr-yellow);\n  --gl-star-color-inactive: var(--tblr-border-color);\n}\n\n[data-star-rating] svg {\n  width: var(--tblr-icon-size, --gl-star-size);\n  height: var(--tblr-icon-size, --gl-star-size);\n}\n[data-star-rating] :not(.gl-active) > .gl-star-full {\n  color: var(--gl-star-color-inactive) !important;\n}\n[data-star-rating] .gl-active > .gl-star-full {\n  color: var(--gl-star-color);\n  fill: currentColor;\n  stroke: currentColor;\n}\n\n.clr-picker {\n  box-shadow: var(--tblr-box-shadow-dropdown);\n  background-color: var(--tblr-bg-surface);\n}\n\ninput.clr-color {\n  border-radius: var(--tblr-border-radius);\n  color: var(--tblr-body-color);\n  border-color: var(--tblr-border-color);\n  background: transparent;\n}\ninput.clr-color:focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n  border-color: rgba(var(--tblr-primary-rgb), 0.25);\n}\n\n.clr-swatches button {\n  border-radius: var(--tblr-border-radius);\n  padding: 0 2px 4px 2px;\n}\n.clr-swatches button:focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n}\n\n.clr-preview {\n  border-radius: var(--tblr-border-radius);\n  overflow: visible;\n}\n.clr-preview button, .clr-preview:before, .clr-preview:after {\n  border-radius: var(--tblr-border-radius);\n}\n.clr-preview button:focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n}\n\n.clr-field {\n  display: block;\n}\n.clr-field button {\n  width: 1.5rem;\n  height: 1.5rem;\n  left: 6px;\n  right: auto;\n  border-radius: var(--tblr-border-radius);\n}\n.clr-field button:after {\n  box-shadow: inset 0 0 0 1px var(--tblr-border-color-translucent);\n}\n.clr-field button:focus {\n  outline: 0;\n  box-shadow: 0 0 0 0.25rem rgba(var(--tblr-primary-rgb), 0.25);\n}\n.clr-field input {\n  padding-left: 2.5rem;\n}\n\n.typed-cursor {\n  font-weight: 500;\n  color: #6c7a91;\n}\n\n/*# sourceMappingURL=tabler-vendors.css.map */"]}