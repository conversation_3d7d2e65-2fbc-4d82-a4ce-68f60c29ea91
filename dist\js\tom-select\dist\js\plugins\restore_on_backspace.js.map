{"version": 3, "file": "restore_on_backspace.js", "sources": ["../../../src/plugins/restore_on_backspace/plugin.ts"], "sourcesContent": ["/**\n * Plugin: \"restore_on_backspace\" (Tom <PERSON>)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\nimport type TomSelect from '../../tom-select.ts';\nimport { TomOption } from '../../types/index.ts';\n\ntype TPluginOptions = {\n\ttext?:(option:TomOption)=>string,\n};\n\nexport default function(this:TomSelect, userOptions:TPluginOptions) {\n\tconst self = this;\n\n\tconst options = Object.assign({\n\t\ttext: (option:TomOption) => {\n\t\t\treturn option[self.settings.labelField];\n\t\t}\n\t},userOptions);\n\n\tself.on('item_remove',function(value:string){\n\t\tif( !self.isFocused ){\n\t\t\treturn;\n\t\t}\n\n\t\tif( self.control_input.value.trim() === '' ){\n\t\t\tvar option = self.options[value];\n\t\t\tif( option ){\n\t\t\t\tself.setTextboxValue(options.text.call(self, option));\n\t\t\t}\n\t\t}\n\t});\n\n};\n"], "names": ["userOptions", "self", "options", "Object", "assign", "text", "option", "settings", "labelField", "on", "value", "isFocused", "control_input", "trim", "setTextboxValue", "call"], "mappings": ";;;;;;;;;;;CAAA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAQe,eAAA,EAAyBA,WAA0B,EAAE;GACnE,MAAMC,IAAI,GAAG,IAAI;CAEjB,EAAA,MAAMC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC;KAC7BC,IAAI,EAAGC,MAAgB,IAAK;CAC3B,MAAA,OAAOA,MAAM,CAACL,IAAI,CAACM,QAAQ,CAACC,UAAU,CAAC;CACxC;IACA,EAACR,WAAW,CAAC;CAEdC,EAAAA,IAAI,CAACQ,EAAE,CAAC,aAAa,EAAC,UAASC,KAAY,EAAC;CAC3C,IAAA,IAAI,CAACT,IAAI,CAACU,SAAS,EAAE;CACpB,MAAA;CACD;KAEA,IAAIV,IAAI,CAACW,aAAa,CAACF,KAAK,CAACG,IAAI,EAAE,KAAK,EAAE,EAAE;CAC3C,MAAA,IAAIP,MAAM,GAAGL,IAAI,CAACC,OAAO,CAACQ,KAAK,CAAC;CAChC,MAAA,IAAIJ,MAAM,EAAE;CACXL,QAAAA,IAAI,CAACa,eAAe,CAACZ,OAAO,CAACG,IAAI,CAACU,IAAI,CAACd,IAAI,EAAEK,MAAM,CAAC,CAAC;CACtD;CACD;CACD,GAAC,CAAC;CAEH;;;;;;;;"}