{"version": 3, "file": "caret_position.js", "sources": ["../../../src/utils.ts", "../../../src/vanilla.ts", "../../../src/plugins/caret_position/plugin.ts"], "sourcesContent": ["\nimport type TomSelect from './tom-select.ts';\nimport { TomLoadCallback } from './types/index.ts';\n\n\n/**\n * Converts a scalar to its best string representation\n * for hash keys and HTML attribute values.\n *\n * Transformations:\n *   'str'     -> 'str'\n *   null      -> ''\n *   undefined -> ''\n *   true      -> '1'\n *   false     -> '0'\n *   0         -> '0'\n *   1         -> '1'\n *\n */\nexport const hash_key = (value:undefined|null|boolean|string|number):string|null => {\n\tif (typeof value === 'undefined' || value === null) return null;\n\treturn get_hash(value);\n};\n\nexport const get_hash = (value:boolean|string|number):string => {\n\tif (typeof value === 'boolean') return value ? '1' : '0';\n\treturn value + '';\n};\n\n/**\n * Escapes a string for use within HTML.\n *\n */\nexport const escape_html = (str:string):string => {\n\treturn (str + '')\n\t\t.replace(/&/g, '&amp;')\n\t\t.replace(/</g, '&lt;')\n\t\t.replace(/>/g, '&gt;')\n\t\t.replace(/\"/g, '&quot;');\n};\n\n\n/**\n * use setTimeout if timeout > 0 \n */\nexport const timeout = (fn:()=>void,timeout:number): number | null => {\n\tif( timeout > 0 ){\n\t\treturn window.setTimeout(fn,timeout);\n\t}\n\n\tfn.call(null);\n\treturn null;\n}\n\n/**\n * Debounce the user provided load function\n *\n */\nexport const loadDebounce = (fn:(value:string,callback:TomLoadCallback) => void,delay:number) => {\n\tvar timeout: null|ReturnType<typeof setTimeout>;\n\treturn function(this:TomSelect, value:string,callback:TomLoadCallback) {\n\t\tvar self = this;\n\n\t\tif( timeout ){\n\t\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\t\tclearTimeout(timeout);\n\t\t}\n\t\ttimeout = setTimeout(function() {\n\t\t\ttimeout = null;\n\t\t\tself.loadedSearches[value] = true;\n\t\t\tfn.call(self, value, callback);\n\n\t\t}, delay);\n\t};\n};\n\n\n/**\n * Debounce all fired events types listed in `types`\n * while executing the provided `fn`.\n *\n */\nexport const debounce_events = ( self:TomSelect, types:string[], fn:() => void ) => {\n\tvar type:string;\n\tvar trigger = self.trigger;\n\tvar event_args:{ [key: string]: any } = {};\n\n\t// override trigger method\n\tself.trigger = function(){\n\t\tvar type = arguments[0];\n\t\tif (types.indexOf(type) !== -1) {\n\t\t\tevent_args[type] = arguments;\n\t\t} else {\n\t\t\treturn trigger.apply(self, arguments);\n\t\t}\n\t};\n\n\t// invoke provided function\n\tfn.apply(self, []);\n\tself.trigger = trigger;\n\n\t// trigger queued events\n\tfor( type of types ){\n\t\tif( type in event_args ){\n\t\t\ttrigger.apply(self, event_args[type]);\n\t\t}\n\t}\n};\n\n\n/**\n * Determines the current selection within a text input control.\n * Returns an object containing:\n *   - start\n *   - length\n *\n * Note: \"selectionStart, selectionEnd ... apply only to inputs of types text, search, URL, tel and password\"\n * \t- https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange\n */\nexport const getSelection = (input:HTMLInputElement):{ start: number; length: number } => {\n\treturn {\n\t\tstart\t: input.selectionStart || 0,\n\t\tlength\t: (input.selectionEnd||0) - (input.selectionStart||0),\n\t};\n};\n\n\n/**\n * Prevent default\n *\n */\nexport const preventDefault = (evt?:Event, stop:boolean=false):void => {\n\tif( evt ){\n\t\tevt.preventDefault();\n\t\tif( stop ){\n\t\t\tevt.stopPropagation();\n\t\t}\n\t}\n}\n\n\n/**\n * Add event helper\n *\n */\nexport const addEvent = (target:EventTarget, type:string, callback:EventListenerOrEventListenerObject, options?:object):void => {\n\ttarget.addEventListener(type,callback,options);\n};\n\n\n/**\n * Return true if the requested key is down\n * Will return false if more than one control character is pressed ( when [ctrl+shift+a] != [ctrl+a] )\n * The current evt may not always set ( eg calling advanceSelection() )\n *\n */\nexport const isKeyDown = ( key_name:keyof (KeyboardEvent|MouseEvent), evt?:KeyboardEvent|MouseEvent ) => {\n\n\tif( !evt ){\n\t\treturn false;\n\t}\n\n\tif( !evt[key_name] ){\n\t\treturn false;\n\t}\n\n\tvar count = (evt.altKey?1:0) + (evt.ctrlKey?1:0) + (evt.shiftKey?1:0) + (evt.metaKey?1:0);\n\n\tif( count === 1 ){\n\t\treturn true;\n\t}\n\n\treturn false;\n};\n\n\n/**\n * Get the id of an element\n * If the id attribute is not set, set the attribute with the given id\n *\n */\nexport const getId = (el:Element,id:string) => {\n\tconst existing_id = el.getAttribute('id');\n\tif( existing_id ){\n\t\treturn existing_id;\n\t}\n\n\tel.setAttribute('id',id);\n\treturn id;\n};\n\n\n/**\n * Returns a string with backslashes added before characters that need to be escaped.\n */\nexport const addSlashes = (str:string):string => {\n\treturn str.replace(/[\\\\\"']/g, '\\\\$&');\n};\n\n/**\n *\n */\nexport const append = ( parent:Element|DocumentFragment, node: string|Node|null|undefined ):void =>{\n\tif( node ) parent.append(node);\n};\n\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\nexport const iterate = (object:[]|{[key:string]:any}, callback:(value:any,key:any)=>any) => {\n\n\tif ( Array.isArray(object)) {\n\t\tobject.forEach(callback);\n\n\t}else{\n\n\t\tfor (var key in object) {\n\t\t\tif (object.hasOwnProperty(key)) {\n\t\t\t\tcallback(object[key], key);\n\t\t\t}\n\t\t}\n\t}\n};\n", "\nimport { iterate } from './utils.ts';\n\n/**\n * Return a dom element from either a dom query string, jQuery object, a dom element or html string\n * https://stackoverflow.com/questions/494143/creating-a-new-dom-element-from-an-html-string-using-built-in-dom-methods-or-pro/35385518#35385518\n *\n * param query should be {}\n */\nexport const getDom = ( query:any ):HTMLElement => {\n\n\tif( query.jquery ){\n\t\treturn query[0];\n\t}\n\n\tif( query instanceof HTMLElement ){\n\t\treturn query;\n\t}\n\n\tif( isHtmlString(query) ){\n\t\tvar tpl = document.createElement('template');\n\t\ttpl.innerHTML = query.trim(); // Never return a text node of whitespace as the result\n\t\treturn tpl.content.firstChild as HTMLElement;\n\t}\n\n\treturn document.querySelector(query);\n};\n\nexport const isHtmlString = (arg:any): boolean => {\n\tif( typeof arg === 'string' && arg.indexOf('<') > -1 ){\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nexport const escapeQuery = (query:string):string => {\n\treturn query.replace(/['\"\\\\]/g, '\\\\$&');\n}\n\n/**\n * Dispatch an event\n *\n */\nexport const triggerEvent = ( dom_el:HTMLElement, event_name:string ):void => {\n\tvar event = document.createEvent('HTMLEvents');\n\tevent.initEvent(event_name, true, false);\n\tdom_el.dispatchEvent(event)\n};\n\n/**\n * Apply CSS rules to a dom element\n *\n */\nexport const applyCSS = ( dom_el:HTMLElement, css:{ [key: string]: string|number }):void => {\n\tObject.assign(dom_el.style, css);\n}\n\n\n/**\n * Add css classes\n *\n */\nexport const addClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n\tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map( cls => {\n\t\t\tel.classList.add( cls );\n\t\t});\n\t});\n}\n\n/**\n * Remove css classes\n *\n */\n export const removeClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n \tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map(cls => {\n\t \t\tel.classList.remove( cls );\n\t\t});\n \t});\n }\n\n\n/**\n * Return arguments\n *\n */\nexport const classesArray = (args:string[]|string[][]):string[] => {\n\tvar classes:string[] = [];\n\titerate( args, (_classes) =>{\n\t\tif( typeof _classes === 'string' ){\n\t\t\t_classes = _classes.trim().split(/[\\t\\n\\f\\r\\s]/);\n\t\t}\n\t\tif( Array.isArray(_classes) ){\n\t\t\tclasses = classes.concat(_classes);\n\t\t}\n\t});\n\n\treturn classes.filter(Boolean);\n}\n\n\n/**\n * Create an array from arg if it's not already an array\n *\n */\nexport const castAsArray = (arg:any):Array<any> => {\n\tif( !Array.isArray(arg) ){\n \t\targ = [arg];\n \t}\n\treturn arg;\n}\n\n\n/**\n * Get the closest node to the evt.target matching the selector\n * Stops at wrapper\n *\n */\nexport const parentMatch = ( target:null|HTMLElement, selector:string, wrapper?:HTMLElement ):HTMLElement|void => {\n\n\tif( wrapper && !wrapper.contains(target) ){\n\t\treturn;\n\t}\n\n\twhile( target && target.matches ){\n\n\t\tif( target.matches(selector) ){\n\t\t\treturn target;\n\t\t}\n\n\t\ttarget = target.parentNode as HTMLElement;\n\t}\n}\n\n\n/**\n * Get the first or last item from an array\n *\n * > 0 - right (last)\n * <= 0 - left (first)\n *\n */\nexport const getTail = ( list:Array<any>|NodeList, direction:number=0 ):any => {\n\n\tif( direction > 0 ){\n\t\treturn list[list.length-1];\n\t}\n\n\treturn list[0];\n}\n\n/**\n * Return true if an object is empty\n *\n */\nexport const isEmptyObject = (obj:object):boolean => {\n\treturn (Object.keys(obj).length === 0);\n}\n\n\n/**\n * Get the index of an element amongst sibling nodes of the same type\n *\n */\nexport const nodeIndex = ( el:null|Element, amongst?:string ):number => {\n\tif (!el) return -1;\n\n\tamongst = amongst || el.nodeName;\n\n\tvar i = 0;\n\twhile( el = el.previousElementSibling ){\n\n\t\tif( el.matches(amongst) ){\n\t\t\ti++;\n\t\t}\n\t}\n\treturn i;\n}\n\n\n/**\n * Set attributes of an element\n *\n */\nexport const setAttr = (el:Element,attrs:{ [key: string]: null|string|number }) => {\n\titerate( attrs,(val,attr) => {\n\t\tif( val == null ){\n\t\t\tel.removeAttribute(attr as string);\n\t\t}else{\n\t\t\tel.setAttribute(attr as string, ''+val);\n\t\t}\n\t});\n}\n\n\n/**\n * Replace a node\n */\nexport const replaceNode = ( existing:Node, replacement:Node ) => {\n\tif( existing.parentNode ) existing.parentNode.replaceChild(replacement, existing);\n}\n", "/**\n * Plugin: \"dropdown_input\" (Tom <PERSON>)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport type TomSelect from '../../tom-select.ts';\nimport { nodeIndex, removeClasses } from '../../vanilla.ts';\n\n\nexport default function(this:TomSelect) {\n\tvar self = this;\n\n\t/**\n\t * Moves the caret to the specified index.\n\t *\n\t * The input must be moved by leaving it in place and moving the\n\t * siblings, due to the fact that focus cannot be restored once lost\n\t * on mobile webkit devices\n\t *\n\t */\n\tself.hook('instead','setCaret',(new_pos:number) => {\n\n\t\tif( self.settings.mode === 'single' || !self.control.contains(self.control_input) ) {\n\t\t\tnew_pos = self.items.length;\n\t\t} else {\n\t\t\tnew_pos = Math.max(0, Math.min(self.items.length, new_pos));\n\n\t\t\tif( new_pos != self.caretPos && !self.isPending ){\n\n\t\t\t\tself.controlChildren().forEach((child,j) => {\n\t\t\t\t\tif( j < new_pos ){\n\t\t\t\t\t\tself.control_input.insertAdjacentElement('beforebegin', child );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.control.appendChild( child );\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tself.caretPos = new_pos;\n\t});\n\n\tself.hook('instead','moveCaret',(direction:number) => {\n\n\t\tif( !self.isFocused ) return;\n\n\t\t// move caret before or after selected items\n\t\tconst last_active\t\t= self.getLastActive(direction);\n\t\tif( last_active ){\n\t\t\tconst idx = nodeIndex(last_active);\n\t\t\tself.setCaret(direction > 0 ? idx + 1: idx);\n\t\t\tself.setActiveItem();\n\t\t\tremoveClasses(last_active as HTMLElement,'last-active');\n\n\t\t// move caret left or right of current position\n\t\t}else{\n\t\t\tself.setCaret(self.caretPos + direction);\n\n\t\t}\n\n\t});\n\n};\n"], "names": ["iterate", "object", "callback", "Array", "isArray", "for<PERSON>ach", "key", "hasOwnProperty", "removeClasses", "elmts", "classes", "norm_classes", "classesArray", "cast<PERSON><PERSON><PERSON><PERSON>", "map", "el", "cls", "classList", "remove", "args", "_classes", "trim", "split", "concat", "filter", "Boolean", "arg", "nodeIndex", "amongst", "nodeName", "i", "previousElementSibling", "matches", "self", "hook", "new_pos", "settings", "mode", "control", "contains", "control_input", "items", "length", "Math", "max", "min", "caretPos", "isPending", "controlChildren", "child", "j", "insertAdjacentElement", "append<PERSON><PERSON><PERSON>", "direction", "isFocused", "last_active", "getLastActive", "idx", "setCaret", "setActiveItem"], "mappings": ";;;;;;;;;;;CAKA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CA4LA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,MAAMA,OAAO,GAAGA,CAACC,MAA4B,EAAEC,QAAiC,KAAK;CAE3F,EAAA,IAAKC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;CAC3BA,IAAAA,MAAM,CAACI,OAAO,CAACH,QAAQ,CAAC;CAEzB,GAAC,MAAI;CAEJ,IAAA,KAAK,IAAII,GAAG,IAAIL,MAAM,EAAE;CACvB,MAAA,IAAIA,MAAM,CAACM,cAAc,CAACD,GAAG,CAAC,EAAE;CAC/BJ,QAAAA,QAAQ,CAACD,MAAM,CAACK,GAAG,CAAC,EAAEA,GAAG,CAAC;CAC3B;CACD;CACD;CACD,CAAC;;CC3JD;CACA;CACA;CACA;CACQ,MAAME,aAAa,GAAGA,CAAEC,KAA+B,EAAE,GAAGC,OAA2B,KAAM;CAEnG,EAAA,IAAIC,YAAY,GAAIC,YAAY,CAACF,OAAO,CAAC;CAC1CD,EAAAA,KAAK,GAAMI,WAAW,CAACJ,KAAK,CAAC;CAE7BA,EAAAA,KAAK,CAACK,GAAG,CAAEC,EAAE,IAAI;CAChBJ,IAAAA,YAAY,CAACG,GAAG,CAACE,GAAG,IAAI;CACtBD,MAAAA,EAAE,CAACE,SAAS,CAACC,MAAM,CAAEF,GAAI,CAAC;CAC5B,KAAC,CAAC;CACF,GAAC,CAAC;CACH,CAAC;;CAGF;CACA;CACA;CACA;CACO,MAAMJ,YAAY,GAAIO,IAAwB,IAAc;GAClE,IAAIT,OAAgB,GAAG,EAAE;CACzBV,EAAAA,OAAO,CAAEmB,IAAI,EAAGC,QAAQ,IAAI;CAC3B,IAAA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;OACjCA,QAAQ,GAAGA,QAAQ,CAACC,IAAI,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;CACjD;CACA,IAAA,IAAInB,KAAK,CAACC,OAAO,CAACgB,QAAQ,CAAC,EAAE;CAC5BV,MAAAA,OAAO,GAAGA,OAAO,CAACa,MAAM,CAACH,QAAQ,CAAC;CACnC;CACD,GAAC,CAAC;CAEF,EAAA,OAAOV,OAAO,CAACc,MAAM,CAACC,OAAO,CAAC;CAC/B,CAAC;;CAGD;CACA;CACA;CACA;CACO,MAAMZ,WAAW,GAAIa,GAAO,IAAgB;CAClD,EAAA,IAAI,CAACvB,KAAK,CAACC,OAAO,CAACsB,GAAG,CAAC,EAAE;KACvBA,GAAG,GAAG,CAACA,GAAG,CAAC;CACZ;CACD,EAAA,OAAOA,GAAG;CACX,CAAC;;CAkDD;CACA;CACA;CACA;CACO,MAAMC,SAAS,GAAGA,CAAEZ,EAAe,EAAEa,OAAe,KAAa;CACvE,EAAA,IAAI,CAACb,EAAE,EAAE,OAAO,CAAC,CAAC;CAElBa,EAAAA,OAAO,GAAGA,OAAO,IAAIb,EAAE,CAACc,QAAQ;GAEhC,IAAIC,CAAC,GAAG,CAAC;CACT,EAAA,OAAOf,EAAE,GAAGA,EAAE,CAACgB,sBAAsB,EAAE;CAEtC,IAAA,IAAIhB,EAAE,CAACiB,OAAO,CAACJ,OAAO,CAAC,EAAE;CACxBE,MAAAA,CAAC,EAAE;CACJ;CACD;CACA,EAAA,OAAOA,CAAC;CACT,CAAC;;CC1LD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAMe,eAAyB,IAAA;GACvC,IAAIG,IAAI,GAAG,IAAI;;CAEf;CACD;CACA;CACA;CACA;CACA;CACA;CACA;GACCA,IAAI,CAACC,IAAI,CAAC,SAAS,EAAC,UAAU,EAAEC,OAAc,IAAK;CAElD,IAAA,IAAIF,IAAI,CAACG,QAAQ,CAACC,IAAI,KAAK,QAAQ,IAAI,CAACJ,IAAI,CAACK,OAAO,CAACC,QAAQ,CAACN,IAAI,CAACO,aAAa,CAAC,EAAG;CACnFL,MAAAA,OAAO,GAAGF,IAAI,CAACQ,KAAK,CAACC,MAAM;CAC5B,KAAC,MAAM;CACNP,MAAAA,OAAO,GAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACZ,IAAI,CAACQ,KAAK,CAACC,MAAM,EAAEP,OAAO,CAAC,CAAC;OAE3D,IAAIA,OAAO,IAAIF,IAAI,CAACa,QAAQ,IAAI,CAACb,IAAI,CAACc,SAAS,EAAE;SAEhDd,IAAI,CAACe,eAAe,EAAE,CAAC3C,OAAO,CAAC,CAAC4C,KAAK,EAACC,CAAC,KAAK;WAC3C,IAAIA,CAAC,GAAGf,OAAO,EAAE;aAChBF,IAAI,CAACO,aAAa,CAACW,qBAAqB,CAAC,aAAa,EAAEF,KAAM,CAAC;CAChE,WAAC,MAAM;CACNhB,YAAAA,IAAI,CAACK,OAAO,CAACc,WAAW,CAAEH,KAAM,CAAC;CAClC;CACD,SAAC,CAAC;CACH;CACD;KAEAhB,IAAI,CAACa,QAAQ,GAAGX,OAAO;CACxB,GAAC,CAAC;GAEFF,IAAI,CAACC,IAAI,CAAC,SAAS,EAAC,WAAW,EAAEmB,SAAgB,IAAK;CAErD,IAAA,IAAI,CAACpB,IAAI,CAACqB,SAAS,EAAG;;CAEtB;CACA,IAAA,MAAMC,WAAW,GAAItB,IAAI,CAACuB,aAAa,CAACH,SAAS,CAAC;CAClD,IAAA,IAAIE,WAAW,EAAE;CAChB,MAAA,MAAME,GAAG,GAAG9B,SAAS,CAAC4B,WAAW,CAAC;CAClCtB,MAAAA,IAAI,CAACyB,QAAQ,CAACL,SAAS,GAAG,CAAC,GAAGI,GAAG,GAAG,CAAC,GAAEA,GAAG,CAAC;OAC3CxB,IAAI,CAAC0B,aAAa,EAAE;CACpBnD,MAAAA,aAAa,CAAC+C,WAAW,EAAgB,aAAa,CAAC;;CAExD;CACA,KAAC,MAAI;OACJtB,IAAI,CAACyB,QAAQ,CAACzB,IAAI,CAACa,QAAQ,GAAGO,SAAS,CAAC;CAEzC;CAED,GAAC,CAAC;CAEH;;;;;;;;"}