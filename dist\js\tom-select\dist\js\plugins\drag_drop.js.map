{"version": 3, "file": "drag_drop.js", "sources": ["../../../src/utils.ts", "../../../src/vanilla.ts", "../../../src/plugins/drag_drop/plugin.ts"], "sourcesContent": ["\nimport type TomSelect from './tom-select.ts';\nimport { TomLoadCallback } from './types/index.ts';\n\n\n/**\n * Converts a scalar to its best string representation\n * for hash keys and HTML attribute values.\n *\n * Transformations:\n *   'str'     -> 'str'\n *   null      -> ''\n *   undefined -> ''\n *   true      -> '1'\n *   false     -> '0'\n *   0         -> '0'\n *   1         -> '1'\n *\n */\nexport const hash_key = (value:undefined|null|boolean|string|number):string|null => {\n\tif (typeof value === 'undefined' || value === null) return null;\n\treturn get_hash(value);\n};\n\nexport const get_hash = (value:boolean|string|number):string => {\n\tif (typeof value === 'boolean') return value ? '1' : '0';\n\treturn value + '';\n};\n\n/**\n * Escapes a string for use within HTML.\n *\n */\nexport const escape_html = (str:string):string => {\n\treturn (str + '')\n\t\t.replace(/&/g, '&amp;')\n\t\t.replace(/</g, '&lt;')\n\t\t.replace(/>/g, '&gt;')\n\t\t.replace(/\"/g, '&quot;');\n};\n\n\n/**\n * use setTimeout if timeout > 0 \n */\nexport const timeout = (fn:()=>void,timeout:number): number | null => {\n\tif( timeout > 0 ){\n\t\treturn window.setTimeout(fn,timeout);\n\t}\n\n\tfn.call(null);\n\treturn null;\n}\n\n/**\n * Debounce the user provided load function\n *\n */\nexport const loadDebounce = (fn:(value:string,callback:TomLoadCallback) => void,delay:number) => {\n\tvar timeout: null|ReturnType<typeof setTimeout>;\n\treturn function(this:TomSelect, value:string,callback:TomLoadCallback) {\n\t\tvar self = this;\n\n\t\tif( timeout ){\n\t\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\t\tclearTimeout(timeout);\n\t\t}\n\t\ttimeout = setTimeout(function() {\n\t\t\ttimeout = null;\n\t\t\tself.loadedSearches[value] = true;\n\t\t\tfn.call(self, value, callback);\n\n\t\t}, delay);\n\t};\n};\n\n\n/**\n * Debounce all fired events types listed in `types`\n * while executing the provided `fn`.\n *\n */\nexport const debounce_events = ( self:TomSelect, types:string[], fn:() => void ) => {\n\tvar type:string;\n\tvar trigger = self.trigger;\n\tvar event_args:{ [key: string]: any } = {};\n\n\t// override trigger method\n\tself.trigger = function(){\n\t\tvar type = arguments[0];\n\t\tif (types.indexOf(type) !== -1) {\n\t\t\tevent_args[type] = arguments;\n\t\t} else {\n\t\t\treturn trigger.apply(self, arguments);\n\t\t}\n\t};\n\n\t// invoke provided function\n\tfn.apply(self, []);\n\tself.trigger = trigger;\n\n\t// trigger queued events\n\tfor( type of types ){\n\t\tif( type in event_args ){\n\t\t\ttrigger.apply(self, event_args[type]);\n\t\t}\n\t}\n};\n\n\n/**\n * Determines the current selection within a text input control.\n * Returns an object containing:\n *   - start\n *   - length\n *\n * Note: \"selectionStart, selectionEnd ... apply only to inputs of types text, search, URL, tel and password\"\n * \t- https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange\n */\nexport const getSelection = (input:HTMLInputElement):{ start: number; length: number } => {\n\treturn {\n\t\tstart\t: input.selectionStart || 0,\n\t\tlength\t: (input.selectionEnd||0) - (input.selectionStart||0),\n\t};\n};\n\n\n/**\n * Prevent default\n *\n */\nexport const preventDefault = (evt?:Event, stop:boolean=false):void => {\n\tif( evt ){\n\t\tevt.preventDefault();\n\t\tif( stop ){\n\t\t\tevt.stopPropagation();\n\t\t}\n\t}\n}\n\n\n/**\n * Add event helper\n *\n */\nexport const addEvent = (target:EventTarget, type:string, callback:EventListenerOrEventListenerObject, options?:object):void => {\n\ttarget.addEventListener(type,callback,options);\n};\n\n\n/**\n * Return true if the requested key is down\n * Will return false if more than one control character is pressed ( when [ctrl+shift+a] != [ctrl+a] )\n * The current evt may not always set ( eg calling advanceSelection() )\n *\n */\nexport const isKeyDown = ( key_name:keyof (KeyboardEvent|MouseEvent), evt?:KeyboardEvent|MouseEvent ) => {\n\n\tif( !evt ){\n\t\treturn false;\n\t}\n\n\tif( !evt[key_name] ){\n\t\treturn false;\n\t}\n\n\tvar count = (evt.altKey?1:0) + (evt.ctrlKey?1:0) + (evt.shiftKey?1:0) + (evt.metaKey?1:0);\n\n\tif( count === 1 ){\n\t\treturn true;\n\t}\n\n\treturn false;\n};\n\n\n/**\n * Get the id of an element\n * If the id attribute is not set, set the attribute with the given id\n *\n */\nexport const getId = (el:Element,id:string) => {\n\tconst existing_id = el.getAttribute('id');\n\tif( existing_id ){\n\t\treturn existing_id;\n\t}\n\n\tel.setAttribute('id',id);\n\treturn id;\n};\n\n\n/**\n * Returns a string with backslashes added before characters that need to be escaped.\n */\nexport const addSlashes = (str:string):string => {\n\treturn str.replace(/[\\\\\"']/g, '\\\\$&');\n};\n\n/**\n *\n */\nexport const append = ( parent:Element|DocumentFragment, node: string|Node|null|undefined ):void =>{\n\tif( node ) parent.append(node);\n};\n\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\nexport const iterate = (object:[]|{[key:string]:any}, callback:(value:any,key:any)=>any) => {\n\n\tif ( Array.isArray(object)) {\n\t\tobject.forEach(callback);\n\n\t}else{\n\n\t\tfor (var key in object) {\n\t\t\tif (object.hasOwnProperty(key)) {\n\t\t\t\tcallback(object[key], key);\n\t\t\t}\n\t\t}\n\t}\n};\n", "\nimport { iterate } from './utils.ts';\n\n/**\n * Return a dom element from either a dom query string, jQuery object, a dom element or html string\n * https://stackoverflow.com/questions/494143/creating-a-new-dom-element-from-an-html-string-using-built-in-dom-methods-or-pro/35385518#35385518\n *\n * param query should be {}\n */\nexport const getDom = ( query:any ):HTMLElement => {\n\n\tif( query.jquery ){\n\t\treturn query[0];\n\t}\n\n\tif( query instanceof HTMLElement ){\n\t\treturn query;\n\t}\n\n\tif( isHtmlString(query) ){\n\t\tvar tpl = document.createElement('template');\n\t\ttpl.innerHTML = query.trim(); // Never return a text node of whitespace as the result\n\t\treturn tpl.content.firstChild as HTMLElement;\n\t}\n\n\treturn document.querySelector(query);\n};\n\nexport const isHtmlString = (arg:any): boolean => {\n\tif( typeof arg === 'string' && arg.indexOf('<') > -1 ){\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nexport const escapeQuery = (query:string):string => {\n\treturn query.replace(/['\"\\\\]/g, '\\\\$&');\n}\n\n/**\n * Dispatch an event\n *\n */\nexport const triggerEvent = ( dom_el:HTMLElement, event_name:string ):void => {\n\tvar event = document.createEvent('HTMLEvents');\n\tevent.initEvent(event_name, true, false);\n\tdom_el.dispatchEvent(event)\n};\n\n/**\n * Apply CSS rules to a dom element\n *\n */\nexport const applyCSS = ( dom_el:HTMLElement, css:{ [key: string]: string|number }):void => {\n\tObject.assign(dom_el.style, css);\n}\n\n\n/**\n * Add css classes\n *\n */\nexport const addClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n\tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map( cls => {\n\t\t\tel.classList.add( cls );\n\t\t});\n\t});\n}\n\n/**\n * Remove css classes\n *\n */\n export const removeClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n \tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map(cls => {\n\t \t\tel.classList.remove( cls );\n\t\t});\n \t});\n }\n\n\n/**\n * Return arguments\n *\n */\nexport const classesArray = (args:string[]|string[][]):string[] => {\n\tvar classes:string[] = [];\n\titerate( args, (_classes) =>{\n\t\tif( typeof _classes === 'string' ){\n\t\t\t_classes = _classes.trim().split(/[\\t\\n\\f\\r\\s]/);\n\t\t}\n\t\tif( Array.isArray(_classes) ){\n\t\t\tclasses = classes.concat(_classes);\n\t\t}\n\t});\n\n\treturn classes.filter(Boolean);\n}\n\n\n/**\n * Create an array from arg if it's not already an array\n *\n */\nexport const castAsArray = (arg:any):Array<any> => {\n\tif( !Array.isArray(arg) ){\n \t\targ = [arg];\n \t}\n\treturn arg;\n}\n\n\n/**\n * Get the closest node to the evt.target matching the selector\n * Stops at wrapper\n *\n */\nexport const parentMatch = ( target:null|HTMLElement, selector:string, wrapper?:HTMLElement ):HTMLElement|void => {\n\n\tif( wrapper && !wrapper.contains(target) ){\n\t\treturn;\n\t}\n\n\twhile( target && target.matches ){\n\n\t\tif( target.matches(selector) ){\n\t\t\treturn target;\n\t\t}\n\n\t\ttarget = target.parentNode as HTMLElement;\n\t}\n}\n\n\n/**\n * Get the first or last item from an array\n *\n * > 0 - right (last)\n * <= 0 - left (first)\n *\n */\nexport const getTail = ( list:Array<any>|NodeList, direction:number=0 ):any => {\n\n\tif( direction > 0 ){\n\t\treturn list[list.length-1];\n\t}\n\n\treturn list[0];\n}\n\n/**\n * Return true if an object is empty\n *\n */\nexport const isEmptyObject = (obj:object):boolean => {\n\treturn (Object.keys(obj).length === 0);\n}\n\n\n/**\n * Get the index of an element amongst sibling nodes of the same type\n *\n */\nexport const nodeIndex = ( el:null|Element, amongst?:string ):number => {\n\tif (!el) return -1;\n\n\tamongst = amongst || el.nodeName;\n\n\tvar i = 0;\n\twhile( el = el.previousElementSibling ){\n\n\t\tif( el.matches(amongst) ){\n\t\t\ti++;\n\t\t}\n\t}\n\treturn i;\n}\n\n\n/**\n * Set attributes of an element\n *\n */\nexport const setAttr = (el:Element,attrs:{ [key: string]: null|string|number }) => {\n\titerate( attrs,(val,attr) => {\n\t\tif( val == null ){\n\t\t\tel.removeAttribute(attr as string);\n\t\t}else{\n\t\t\tel.setAttribute(attr as string, ''+val);\n\t\t}\n\t});\n}\n\n\n/**\n * Replace a node\n */\nexport const replaceNode = ( existing:Node, replacement:Node ) => {\n\tif( existing.parentNode ) existing.parentNode.replaceChild(replacement, existing);\n}\n", "/**\n * Plugin: \"drag_drop\" (Tom Select)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport type TomSelect from '../../tom-select.ts';\nimport { TomOption, TomItem } from '../../types/index.ts';\nimport { escape_html, preventDefault, addEvent } from '../../utils.ts';\nimport { getDom, setAttr } from '../../vanilla.ts';\n\n\nconst insertAfter = (referenceNode:Element, newNode:Element) => {\n\treferenceNode.parentNode?.insertBefore(newNode, referenceNode.nextSibling);\n}\n\nconst insertBefore = (referenceNode:Element, newNode:Element) => {\n\treferenceNode.parentNode?.insertBefore(newNode, referenceNode);\n}\n\nconst isBefore = (referenceNode:Element|undefined|null, newNode:Element|undefined|null) =>{\n\t\n\tdo{\n\t\tnewNode = newNode?.previousElementSibling;\n\n\t\tif( referenceNode == newNode ){\n\t\t\treturn true;\n\t\t}\n\n\t}while( newNode && newNode.previousElementSibling );\n\n\treturn false;\n}\n\nexport default function(this:TomSelect) {\n\tvar self = this;\n\tif (self.settings.mode !== 'multi') return;\n\n\tvar orig_lock\t\t= self.lock;\n\tvar orig_unlock\t\t= self.unlock;\n\tlet sortable = true;\n\t\tlet drag_item:TomItem|undefined;\n\n\n\t/**\n\t * Add draggable attribute to item\n\t */\n\tself.hook('after','setupTemplates',() => {\n\n\t\tvar orig_render_item = self.settings.render.item;\n\n\t\tself.settings.render.item = (data:TomOption, escape:typeof escape_html) => {\n\t\t\tconst item = getDom(orig_render_item.call(self, data, escape)) as TomItem;\n\t\t\tsetAttr(item,{'draggable':'true'});\n\n\n\t\t\t// prevent doc_mousedown (see tom-select.ts)\n\t\t\tconst mousedown = (evt:Event) => {\n\t\t\t\tif( !sortable ) preventDefault(evt);\n\t\t\t\tevt.stopPropagation();\n\t\t\t}\n\n\t\t\tconst dragStart = (evt:Event) => {\n\t\t\t\tdrag_item = item;\n\t\t\t\t\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\titem.classList.add('ts-dragging');\n\t\t\t\t}, 0);\n\t\t\t\t\n\t\t\t}\n\n\t\t\tconst dragOver = (evt:Event) =>{\n\t\t\t\tevt.preventDefault();\n\t\t\t\titem.classList.add('ts-drag-over');\n\t\t\t\tmoveitem(item,drag_item);\n\t\t\t}\n\n\t\t\tconst dragLeave = () => {\n\t\t\t\titem.classList.remove('ts-drag-over');\n\t\t\t}\n\n\t\t\tconst moveitem = (targetitem:TomItem, dragitem:TomItem|undefined) => {\n\t\t\t\tif( dragitem === undefined ) return;\n\t\t\t\t\n\t\t\t\tif( isBefore(dragitem,item) ){\n\t\t\t\t\tinsertAfter(targetitem,dragitem);\n\t\t\t\t}else{\n\t\t\t\t\tinsertBefore(targetitem,dragitem);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst dragend = () => {\n\t\t\t\tdocument.querySelectorAll('.ts-drag-over').forEach(el=> el.classList.remove('ts-drag-over'));\n\t\t\t\tdrag_item?.classList.remove('ts-dragging');\n\t\t\t\tdrag_item = undefined;\n\n\t\t\t\tvar values:string[] = [];\n\t\t\t\tself.control.querySelectorAll(`[data-value]`).forEach((el:Element)=> {\n\t\t\t\t\tif( (<HTMLOptionElement>el).dataset.value ){\n\t\t\t\t\t\tlet value = (<HTMLOptionElement>el).dataset.value;\n\t\t\t\t\t\tif( value ){\n\t\t\t\t\t\t\tvalues.push(value);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tself.setValue(values);\n\t\t\t}\t\n\n\n\t\t\taddEvent(item,'mousedown', mousedown);\n\t\t\taddEvent(item,'dragstart', dragStart);\n\t\t\taddEvent(item,'dragenter', dragOver)\n\t\t\taddEvent(item,'dragover', dragOver);\n\t\t\taddEvent(item,'dragleave', dragLeave);\n\t\t\taddEvent(item,'dragend', dragend);\n\t\t\t\t\n\t\t\treturn item;\n\t\t}\t\n\t});\n\n\n\n\tself.hook('instead','lock',()=>{\n\t\tsortable = false;\n\t\treturn orig_lock.call(self);\n\t});\n\n\tself.hook('instead','unlock',()=>{\n\t\tsortable = true;\n\t\treturn orig_unlock.call(self);\n\t});\n\n};\n"], "names": ["preventDefault", "evt", "stop", "stopPropagation", "addEvent", "target", "type", "callback", "options", "addEventListener", "iterate", "object", "Array", "isArray", "for<PERSON>ach", "key", "hasOwnProperty", "getDom", "query", "j<PERSON>y", "HTMLElement", "isHtmlString", "tpl", "document", "createElement", "innerHTML", "trim", "content", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "arg", "indexOf", "setAttr", "el", "attrs", "val", "attr", "removeAttribute", "setAttribute", "insertAfter", "referenceNode", "newNode", "_referenceNode$parent", "parentNode", "insertBefore", "nextS<PERSON>ling", "_referenceNode$parent2", "isBefore", "_newNode", "previousElementSibling", "self", "settings", "mode", "orig_lock", "lock", "orig_unlock", "unlock", "sortable", "drag_item", "hook", "orig_render_item", "render", "item", "data", "escape", "call", "mousedown", "dragStart", "setTimeout", "classList", "add", "dragOver", "moveitem", "dragLeave", "remove", "targetitem", "dragitem", "undefined", "dragend", "_drag_item", "querySelectorAll", "values", "control", "dataset", "value", "push", "setValue"], "mappings": ";;;;;;;;;;;CAKA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CA6GA;CACA;CACA;CACA;CACO,MAAMA,cAAc,GAAGA,CAACC,GAAU,EAAEC,IAAY,GAAC,KAAK,KAAU;CACtE,EAAA,IAAID,GAAG,EAAE;KACRA,GAAG,CAACD,cAAc,EAAE;CACpB,IAAA,IAAIE,IAAI,EAAE;OACTD,GAAG,CAACE,eAAe,EAAE;CACtB;CACD;CACD,CAAC;;CAGD;CACA;CACA;CACA;CACO,MAAMC,QAAQ,GAAGA,CAACC,MAAkB,EAAEC,IAAW,EAAEC,QAA2C,EAAEC,OAAe,KAAU;GAC/HH,MAAM,CAACI,gBAAgB,CAACH,IAAI,EAACC,QAAQ,EAACC,OAAO,CAAC;CAC/C,CAAC;;CA2DD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,MAAME,OAAO,GAAGA,CAACC,MAA4B,EAAEJ,QAAiC,KAAK;CAE3F,EAAA,IAAKK,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;CAC3BA,IAAAA,MAAM,CAACG,OAAO,CAACP,QAAQ,CAAC;CAEzB,GAAC,MAAI;CAEJ,IAAA,KAAK,IAAIQ,GAAG,IAAIJ,MAAM,EAAE;CACvB,MAAA,IAAIA,MAAM,CAACK,cAAc,CAACD,GAAG,CAAC,EAAE;CAC/BR,QAAAA,QAAQ,CAACI,MAAM,CAACI,GAAG,CAAC,EAAEA,GAAG,CAAC;CAC3B;CACD;CACD;CACD,CAAC;;CClOD;CACA;CACA;CACA;CACA;CACA;CACO,MAAME,MAAM,GAAKC,KAAS,IAAkB;GAElD,IAAIA,KAAK,CAACC,MAAM,EAAE;KACjB,OAAOD,KAAK,CAAC,CAAC,CAAC;CAChB;GAEA,IAAIA,KAAK,YAAYE,WAAW,EAAE;CACjC,IAAA,OAAOF,KAAK;CACb;CAEA,EAAA,IAAIG,YAAY,CAACH,KAAK,CAAC,EAAE;CACxB,IAAA,IAAII,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;KAC5CF,GAAG,CAACG,SAAS,GAAGP,KAAK,CAACQ,IAAI,EAAE,CAAC;CAC7B,IAAA,OAAOJ,GAAG,CAACK,OAAO,CAACC,UAAU;CAC9B;CAEA,EAAA,OAAOL,QAAQ,CAACM,aAAa,CAACX,KAAK,CAAC;CACrC,CAAC;CAEM,MAAMG,YAAY,GAAIS,GAAO,IAAc;CACjD,EAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;CACrD,IAAA,OAAO,IAAI;CACZ;CACA,EAAA,OAAO,KAAK;CACb,CAAC;;CA4JD;CACA;CACA;CACA;CACO,MAAMC,OAAO,GAAGA,CAACC,EAAU,EAACC,KAA2C,KAAK;CAClFxB,EAAAA,OAAO,CAAEwB,KAAK,EAAC,CAACC,GAAG,EAACC,IAAI,KAAK;KAC5B,IAAID,GAAG,IAAI,IAAI,EAAE;CAChBF,MAAAA,EAAE,CAACI,eAAe,CAACD,IAAc,CAAC;CACnC,KAAC,MAAI;OACJH,EAAE,CAACK,YAAY,CAACF,IAAI,EAAY,EAAE,GAACD,GAAG,CAAC;CACxC;CACD,GAAC,CAAC;CACH,CAAC;;CCzMD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAQA,MAAMI,WAAW,GAAGA,CAACC,aAAqB,EAAEC,OAAe,KAAK;CAAA,EAAA,IAAAC,qBAAA;CAC/D,EAAA,CAAAA,qBAAA,GAAAF,aAAa,CAACG,UAAU,KAAxBD,IAAAA,IAAAA,qBAAA,CAA0BE,YAAY,CAACH,OAAO,EAAED,aAAa,CAACK,WAAW,CAAC;CAC3E,CAAC;CAED,MAAMD,YAAY,GAAGA,CAACJ,aAAqB,EAAEC,OAAe,KAAK;CAAA,EAAA,IAAAK,sBAAA;CAChE,EAAA,CAAAA,sBAAA,GAAAN,aAAa,CAACG,UAAU,KAAA,IAAA,IAAxBG,sBAAA,CAA0BF,YAAY,CAACH,OAAO,EAAED,aAAa,CAAC;CAC/D,CAAC;CAED,MAAMO,QAAQ,GAAGA,CAACP,aAAoC,EAAEC,OAA8B,KAAI;GAEzF,GAAE;CAAA,IAAA,IAAAO,QAAA;CACDP,IAAAA,OAAO,IAAAO,QAAA,GAAGP,OAAO,KAAPO,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,QAAA,CAASC,sBAAsB;KAEzC,IAAIT,aAAa,IAAIC,OAAO,EAAE;CAC7B,MAAA,OAAO,IAAI;CACZ;CAED,GAAC,QAAOA,OAAO,IAAIA,OAAO,CAACQ,sBAAsB;CAEjD,EAAA,OAAO,KAAK;CACb,CAAC;CAEc,eAAyB,IAAA;GACvC,IAAIC,IAAI,GAAG,IAAI;CACf,EAAA,IAAIA,IAAI,CAACC,QAAQ,CAACC,IAAI,KAAK,OAAO,EAAE;CAEpC,EAAA,IAAIC,SAAS,GAAIH,IAAI,CAACI,IAAI;CAC1B,EAAA,IAAIC,WAAW,GAAIL,IAAI,CAACM,MAAM;GAC9B,IAAIC,QAAQ,GAAG,IAAI;CAClB,EAAA,IAAIC,SAA2B;;CAGhC;CACD;CACA;CACCR,EAAAA,IAAI,CAACS,IAAI,CAAC,OAAO,EAAC,gBAAgB,EAAC,MAAM;KAExC,IAAIC,gBAAgB,GAAGV,IAAI,CAACC,QAAQ,CAACU,MAAM,CAACC,IAAI;KAEhDZ,IAAI,CAACC,QAAQ,CAACU,MAAM,CAACC,IAAI,GAAG,CAACC,IAAc,EAAEC,MAAyB,KAAK;CAC1E,MAAA,MAAMF,IAAI,GAAG7C,MAAM,CAAC2C,gBAAgB,CAACK,IAAI,CAACf,IAAI,EAAEa,IAAI,EAAEC,MAAM,CAAC,CAAY;OACzEhC,OAAO,CAAC8B,IAAI,EAAC;CAAC,QAAA,WAAW,EAAC;CAAM,OAAC,CAAC;;CAGlC;OACA,MAAMI,SAAS,GAAIjE,GAAS,IAAK;CAChC,QAAA,IAAI,CAACwD,QAAQ,EAAGzD,cAAc,CAACC,GAAG,CAAC;SACnCA,GAAG,CAACE,eAAe,EAAE;QACrB;OAED,MAAMgE,SAAS,GAAIlE,GAAS,IAAK;CAChCyD,QAAAA,SAAS,GAAGI,IAAI;CAEhBM,QAAAA,UAAU,CAAC,MAAM;CAChBN,UAAAA,IAAI,CAACO,SAAS,CAACC,GAAG,CAAC,aAAa,CAAC;UACjC,EAAE,CAAC,CAAC;QAEL;OAED,MAAMC,QAAQ,GAAItE,GAAS,IAAI;SAC9BA,GAAG,CAACD,cAAc,EAAE;CACpB8D,QAAAA,IAAI,CAACO,SAAS,CAACC,GAAG,CAAC,cAAc,CAAC;CAClCE,QAAAA,QAAQ,CAACV,IAAI,EAACJ,SAAS,CAAC;QACxB;OAED,MAAMe,SAAS,GAAGA,MAAM;CACvBX,QAAAA,IAAI,CAACO,SAAS,CAACK,MAAM,CAAC,cAAc,CAAC;QACrC;CAED,MAAA,MAAMF,QAAQ,GAAGA,CAACG,UAAkB,EAAEC,QAA0B,KAAK;SACpE,IAAIA,QAAQ,KAAKC,SAAS,EAAG;CAE7B,QAAA,IAAI9B,QAAQ,CAAC6B,QAAQ,EAACd,IAAI,CAAC,EAAE;CAC5BvB,UAAAA,WAAW,CAACoC,UAAU,EAACC,QAAQ,CAAC;CACjC,SAAC,MAAI;CACJhC,UAAAA,YAAY,CAAC+B,UAAU,EAACC,QAAQ,CAAC;CAClC;QACA;OAED,MAAME,OAAO,GAAGA,MAAM;CAAA,QAAA,IAAAC,UAAA;CACrBxD,QAAAA,QAAQ,CAACyD,gBAAgB,CAAC,eAAe,CAAC,CAAClE,OAAO,CAACmB,EAAE,IAAGA,EAAE,CAACoC,SAAS,CAACK,MAAM,CAAC,cAAc,CAAC,CAAC;SAC5F,CAAAK,UAAA,GAAArB,SAAS,KAATqB,IAAAA,IAAAA,UAAA,CAAWV,SAAS,CAACK,MAAM,CAAC,aAAa,CAAC;CAC1ChB,QAAAA,SAAS,GAAGmB,SAAS;SAErB,IAAII,MAAe,GAAG,EAAE;SACxB/B,IAAI,CAACgC,OAAO,CAACF,gBAAgB,CAAC,CAAc,YAAA,CAAA,CAAC,CAAClE,OAAO,CAAEmB,EAAU,IAAI;CACpE,UAAA,IAAwBA,EAAE,CAAEkD,OAAO,CAACC,KAAK,EAAE;CAC1C,YAAA,IAAIA,KAAK,GAAuBnD,EAAE,CAAEkD,OAAO,CAACC,KAAK;CACjD,YAAA,IAAIA,KAAK,EAAE;CACVH,cAAAA,MAAM,CAACI,IAAI,CAACD,KAAK,CAAC;CACnB;CACD;CACD,SAAC,CAAC;CAEFlC,QAAAA,IAAI,CAACoC,QAAQ,CAACL,MAAM,CAAC;QACrB;CAGD7E,MAAAA,QAAQ,CAAC0D,IAAI,EAAC,WAAW,EAAEI,SAAS,CAAC;CACrC9D,MAAAA,QAAQ,CAAC0D,IAAI,EAAC,WAAW,EAAEK,SAAS,CAAC;CACrC/D,MAAAA,QAAQ,CAAC0D,IAAI,EAAC,WAAW,EAAES,QAAQ,CAAC;CACpCnE,MAAAA,QAAQ,CAAC0D,IAAI,EAAC,UAAU,EAAES,QAAQ,CAAC;CACnCnE,MAAAA,QAAQ,CAAC0D,IAAI,EAAC,WAAW,EAAEW,SAAS,CAAC;CACrCrE,MAAAA,QAAQ,CAAC0D,IAAI,EAAC,SAAS,EAAEgB,OAAO,CAAC;CAEjC,MAAA,OAAOhB,IAAI;MACX;CACF,GAAC,CAAC;CAIFZ,EAAAA,IAAI,CAACS,IAAI,CAAC,SAAS,EAAC,MAAM,EAAC,MAAI;CAC9BF,IAAAA,QAAQ,GAAG,KAAK;CAChB,IAAA,OAAOJ,SAAS,CAACY,IAAI,CAACf,IAAI,CAAC;CAC5B,GAAC,CAAC;CAEFA,EAAAA,IAAI,CAACS,IAAI,CAAC,SAAS,EAAC,QAAQ,EAAC,MAAI;CAChCF,IAAAA,QAAQ,GAAG,IAAI;CACf,IAAA,OAAOF,WAAW,CAACU,IAAI,CAACf,IAAI,CAAC;CAC9B,GAAC,CAAC;CAEH;;;;;;;;"}