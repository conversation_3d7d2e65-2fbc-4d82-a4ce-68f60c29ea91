{"version": 3, "names": ["elements", "document", "querySelectorAll", "length", "for<PERSON>ach", "element", "autosize", "options", "dataOptions", "getAttribute", "JSON", "parse", "Object", "assign", "enableScrollSpy", "error", "value", "parseInt", "innerHTML", "countUp", "window", "CountUp", "start", "maskElementList", "slice", "call", "map", "maskEl", "IMask", "mask", "dataset", "lazy", "top", "bottom", "right", "left", "auto", "basePlacements", "end", "clippingParents", "viewport", "popper", "reference", "variationPlacements", "reduce", "acc", "placement", "concat", "placements", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases", "getNodeName", "nodeName", "toLowerCase", "getWindow", "node", "toString", "ownerDocument", "defaultView", "isElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "applyStyles", "_ref", "state", "keys", "name", "style", "styles", "attributes", "removeAttribute", "setAttribute", "effect", "_ref2", "initialStyles", "position", "strategy", "margin", "arrow", "hasOwnProperty", "property", "attribute", "applyStyles$1", "enabled", "phase", "fn", "requires", "getBasePlacement", "split", "max", "Math", "min", "round", "getUAString", "uaData", "navigator", "userAgentData", "brands", "Array", "isArray", "item", "brand", "version", "join", "userAgent", "isLayoutViewport", "test", "getBoundingClientRect", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "offsetHeight", "height", "visualViewport", "addVisualOffsets", "x", "offsetLeft", "y", "offsetTop", "getLayoutRect", "abs", "contains", "parent", "child", "rootNode", "getRootNode", "next", "isSameNode", "parentNode", "host", "getComputedStyle", "isTableElement", "indexOf", "getDocumentElement", "documentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "getContainingBlock", "isFirefox", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "filter", "getOffsetParent", "getMainAxisFromPlacement", "within", "mathMax", "mathMin", "withinMaxClamp", "v", "getFreshSideObject", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "key", "toPaddingObject", "padding", "rects", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "len", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "offset", "axisProp", "centerOffset", "_options$element", "querySelector", "arrow$1", "requiresIfExists", "getVariation", "unsetSides", "roundOffsetsByDPR", "win", "dpr", "devicePixelRatio", "mapToStyles", "_Object$assign2", "popperRect", "variation", "offsets", "gpuAcceleration", "adaptive", "roundOffsets", "isFixed", "_offsets$x", "_offsets$y", "_ref3", "hasX", "hasY", "sideX", "sideY", "heightProp", "widthProp", "_Object$assign", "commonStyles", "_ref4", "computeStyles", "_ref5", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "computeStyles$1", "data", "passive", "instance", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "addEventListener", "update", "removeEventListener", "eventListeners", "hash", "getOppositePlacement", "replace", "matched", "getOppositeVariationPlacement", "getWindowScroll", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "getWindowScrollBarX", "getViewportRect", "html", "layoutViewport", "getDocumentRect", "_element$ownerDocumen", "winScroll", "body", "scrollWidth", "scrollHeight", "direction", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "getScrollParent", "listScrollParents", "list", "isBody", "target", "updatedList", "rectToClientRect", "rect", "getInnerBoundingClientRect", "clientTop", "clientLeft", "getClientRectFromMixedType", "clippingParent", "getClippingParents", "clipperElement", "getClippingRect", "boundary", "rootBoundary", "mainClippingParents", "firstClippingParent", "clippingRect", "accRect", "computeOffsets", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$strategy", "_options$boundary", "_options$rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "clippingClientRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "getExpandedFallbackPlacements", "oppositePlacement", "flip", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "referenceRect", "checksMap", "Map", "makeFallbackChecks", "firstFittingPlacement", "i", "_basePlacement", "isStartVariation", "isVertical", "mainVariationSide", "altVariationSide", "checks", "push", "every", "check", "set", "_loop", "_i", "fittingPlacement", "find", "get", "reset", "flip$1", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "hide$1", "distanceAndSkiddingToXY", "invertDistance", "skidding", "distance", "_options$offset", "_data$state$placement", "offset$1", "popperOffsets$1", "getAltAxis", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "isBasePlacement", "tetherOffsetValue", "normalizedTetherOffsetValue", "offsetModifierState", "_offsetModifierState$", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMax", "preventedOffset", "_offsetModifierState$2", "_mainSide", "_altSide", "_offset", "_len", "_min", "_max", "isOriginSide", "_offsetModifierValue", "_tetherMin", "_tetherMax", "_preventedOffset", "preventOverflow$1", "getHTMLElementScroll", "getNodeScroll", "isElementScaled", "getCompositeRect", "elementOrVirtualElement", "isOffsetParentAnElement", "offsetParentIsScaled", "order", "modifiers", "visited", "Set", "result", "modifier", "add", "dep", "has", "depModifier", "orderModifiers", "orderedModifiers", "debounce", "pending", "Promise", "resolve", "then", "undefined", "mergeByName", "merged", "current", "existing", "DEFAULT_OPTIONS", "areValidElements", "arguments", "args", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "effectCleanupFns", "isDestroyed", "setOptions", "setOptionsAction", "cleanupModifierEffects", "m", "_ref$options", "cleanupFn", "forceUpdate", "_state$elements", "index", "_state$orderedModifie", "_state$orderedModifie2", "destroy", "onFirstUpdate", "createPopper", "elementMap", "Data", "instanceMap", "size", "console", "from", "remove", "delete", "dropdownTriggerList", "dropdownTriggerEl", "Dropdown", "tooltipTriggerList", "tooltipTriggerEl", "delay", "show", "<PERSON><PERSON><PERSON>", "popoverTriggerList", "popoverTriggerEl", "Popover", "switchesTriggerList", "switchTriggerEl", "e", "stopPropagation", "classList", "toggle", "toastsTriggerList", "toastTriggerEl", "hasAttribute", "toastEl", "Toast", "prefix", "hexToRgba", "hex", "opacity", "exec", "getColor", "color", "c", "getPropertyValue", "trim"], "sources": ["../../js/src/autosize.js", "../../js/src/countup.js", "../../js/src/input-mask.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/enums.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/math.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/userAgent.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/within.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getVariation.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/flip.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/hide.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/offset.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/debounce.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/createPopper.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/popper-lite.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/popper.js", "../../../node_modules/.pnpm/bootstrap@5.3.3_@popperjs+core@2.11.8/node_modules/bootstrap/dist/js/bootstrap.esm.js", "../../js/src/dropdown.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/switch-icon.js", "../../js/src/toast.js", "../../js/src/tabler.js"], "sourcesContent": ["// Autosize plugin\n\nimport autosize from 'autosize';\n\nconst elements = document.querySelectorAll('[data-bs-toggle=\"autosize\"]');\nif (elements.length) {\n\telements.forEach(function (element) {\n\t\tautosize && autosize(element);\n\t});\n}", "const elements = document.querySelectorAll('[data-countup]');\n\nif (elements.length) {\n\telements.forEach(function (element) {\n\t\tlet options = {};\n\t\ttry {\n\t\t\tconst dataOptions = element.getAttribute('data-countup') ? JSON.parse(element.getAttribute('data-countup')) : {};\n\t\t\toptions = Object.assign({'enableScrollSpy': true}, dataOptions);\n\t\t\t\n\t\t} catch (error) {}\n\n\t\tconst value = parseInt(element.innerHTML, 10);\n\n\t\tconst countUp = new window.countUp.CountUp(element, value, options);\n\t\tif (!countUp.error) {\n\t\t\tcountUp.start();\n\t\t}\n\t});\n}\n", "// Input mask plugin\n\nimport IMask from 'imask';\n\nvar maskElementList = [].slice.call(document.querySelectorAll('[data-mask]'));\nmaskElementList.map(function (maskEl) {\n\tIMask && new IMask(maskEl, {\n\t\tmask: maskEl.dataset.mask,\n\t\tlazy: maskEl.dataset['mask-visible'] === 'true'\n\t})\n});", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}", "import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}", "import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref, win) {\n  var x = _ref.x,\n      y = _ref.y;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }, getWindow(popper)) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/*!\n  * Bootstrap v5.3.3 (https://getbootstrap.com/)\n  * Copyright 2011-2024 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)\n  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n  */\nimport * as <PERSON><PERSON> from '@popperjs/core';\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map();\nconst Data = {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map());\n    }\n    const instanceMap = elementMap.get(element);\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`);\n      return;\n    }\n    instanceMap.set(key, instance);\n  },\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null;\n    }\n    return null;\n  },\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return;\n    }\n    const instanceMap = elementMap.get(element);\n    instanceMap.delete(key);\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element);\n    }\n  }\n};\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000;\nconst MILLISECONDS_MULTIPLIER = 1000;\nconst TRANSITION_END = 'transitionend';\n\n/**\n * Properly escape IDs selectors to handle weird IDs\n * @param {string} selector\n * @returns {string}\n */\nconst parseSelector = selector => {\n  if (selector && window.CSS && window.CSS.escape) {\n    // document.querySelector needs escaping to handle IDs (html5+) containing for instance /\n    selector = selector.replace(/#([^\\s\"#']+)/g, (match, id) => `#${CSS.escape(id)}`);\n  }\n  return selector;\n};\n\n// Shout-out Angus Croll (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`;\n  }\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase();\n};\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID);\n  } while (document.getElementById(prefix));\n  return prefix;\n};\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0;\n  }\n\n  // Get transition-duration of the element\n  let {\n    transitionDuration,\n    transitionDelay\n  } = window.getComputedStyle(element);\n  const floatTransitionDuration = Number.parseFloat(transitionDuration);\n  const floatTransitionDelay = Number.parseFloat(transitionDelay);\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0;\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0];\n  transitionDelay = transitionDelay.split(',')[0];\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER;\n};\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END));\n};\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false;\n  }\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0];\n  }\n  return typeof object.nodeType !== 'undefined';\n};\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object;\n  }\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(parseSelector(object));\n  }\n  return null;\n};\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false;\n  }\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible';\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])');\n  if (!closedDetails) {\n    return elementIsVisible;\n  }\n  if (closedDetails !== element) {\n    const summary = element.closest('summary');\n    if (summary && summary.parentNode !== closedDetails) {\n      return false;\n    }\n    if (summary === null) {\n      return false;\n    }\n  }\n  return elementIsVisible;\n};\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true;\n  }\n  if (element.classList.contains('disabled')) {\n    return true;\n  }\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled;\n  }\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false';\n};\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null;\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode();\n    return root instanceof ShadowRoot ? root : null;\n  }\n  if (element instanceof ShadowRoot) {\n    return element;\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null;\n  }\n  return findShadowRoot(element.parentNode);\n};\nconst noop = () => {};\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight; // eslint-disable-line no-unused-expressions\n};\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery;\n  }\n  return null;\n};\nconst DOMContentLoadedCallbacks = [];\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback();\n        }\n      });\n    }\n    DOMContentLoadedCallbacks.push(callback);\n  } else {\n    callback();\n  }\n};\nconst isRTL = () => document.documentElement.dir === 'rtl';\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery();\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME;\n      const JQUERY_NO_CONFLICT = $.fn[name];\n      $.fn[name] = plugin.jQueryInterface;\n      $.fn[name].Constructor = plugin;\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT;\n        return plugin.jQueryInterface;\n      };\n    }\n  });\n};\nconst execute = (possibleCallback, args = [], defaultValue = possibleCallback) => {\n  return typeof possibleCallback === 'function' ? possibleCallback(...args) : defaultValue;\n};\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback);\n    return;\n  }\n  const durationPadding = 5;\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding;\n  let called = false;\n  const handler = ({\n    target\n  }) => {\n    if (target !== transitionElement) {\n      return;\n    }\n    called = true;\n    transitionElement.removeEventListener(TRANSITION_END, handler);\n    execute(callback);\n  };\n  transitionElement.addEventListener(TRANSITION_END, handler);\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement);\n    }\n  }, emulatedDuration);\n};\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length;\n  let index = list.indexOf(activeElement);\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0];\n  }\n  index += shouldGetNext ? 1 : -1;\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength;\n  }\n  return list[Math.max(0, Math.min(index, listLength - 1))];\n};\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/;\nconst stripNameRegex = /\\..*/;\nconst stripUidRegex = /::\\d+$/;\nconst eventRegistry = {}; // Events storage\nlet uidEvent = 1;\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n};\nconst nativeEvents = new Set(['click', 'dblclick', 'mouseup', 'mousedown', 'contextmenu', 'mousewheel', 'DOMMouseScroll', 'mouseover', 'mouseout', 'mousemove', 'selectstart', 'selectend', 'keydown', 'keypress', 'keyup', 'orientationchange', 'touchstart', 'touchmove', 'touchend', 'touchcancel', 'pointerdown', 'pointermove', 'pointerup', 'pointerleave', 'pointercancel', 'gesturestart', 'gesturechange', 'gestureend', 'focus', 'blur', 'change', 'reset', 'select', 'submit', 'focusin', 'focusout', 'load', 'unload', 'beforeunload', 'resize', 'move', 'DOMContentLoaded', 'readystatechange', 'error', 'abort', 'scroll']);\n\n/**\n * Private methods\n */\n\nfunction makeEventUid(element, uid) {\n  return uid && `${uid}::${uidEvent++}` || element.uidEvent || uidEvent++;\n}\nfunction getElementEvents(element) {\n  const uid = makeEventUid(element);\n  element.uidEvent = uid;\n  eventRegistry[uid] = eventRegistry[uid] || {};\n  return eventRegistry[uid];\n}\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    hydrateObj(event, {\n      delegateTarget: element\n    });\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn);\n    }\n    return fn.apply(element, [event]);\n  };\n}\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector);\n    for (let {\n      target\n    } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue;\n        }\n        hydrateObj(event, {\n          delegateTarget: target\n        });\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn);\n        }\n        return fn.apply(target, [event]);\n      }\n    }\n  };\n}\nfunction findHandler(events, callable, delegationSelector = null) {\n  return Object.values(events).find(event => event.callable === callable && event.delegationSelector === delegationSelector);\n}\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const isDelegated = typeof handler === 'string';\n  // TODO: tooltip passes `false` instead of selector, so we need to check\n  const callable = isDelegated ? delegationFunction : handler || delegationFunction;\n  let typeEvent = getTypeEvent(originalTypeEvent);\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent;\n  }\n  return [isDelegated, callable, typeEvent];\n}\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return;\n  }\n  let [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction);\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget)) {\n          return fn.call(this, event);\n        }\n      };\n    };\n    callable = wrapFunction(callable);\n  }\n  const events = getElementEvents(element);\n  const handlers = events[typeEvent] || (events[typeEvent] = {});\n  const previousFunction = findHandler(handlers, callable, isDelegated ? handler : null);\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff;\n    return;\n  }\n  const uid = makeEventUid(callable, originalTypeEvent.replace(namespaceRegex, ''));\n  const fn = isDelegated ? bootstrapDelegationHandler(element, handler, callable) : bootstrapHandler(element, callable);\n  fn.delegationSelector = isDelegated ? handler : null;\n  fn.callable = callable;\n  fn.oneOff = oneOff;\n  fn.uidEvent = uid;\n  handlers[uid] = fn;\n  element.addEventListener(typeEvent, fn, isDelegated);\n}\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector);\n  if (!fn) {\n    return;\n  }\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector));\n  delete events[typeEvent][fn.uidEvent];\n}\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {};\n  for (const [handlerKey, event] of Object.entries(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      removeHandler(element, events, typeEvent, event.callable, event.delegationSelector);\n    }\n  }\n}\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '');\n  return customEvents[event] || event;\n}\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false);\n  },\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true);\n  },\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return;\n    }\n    const [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction);\n    const inNamespace = typeEvent !== originalTypeEvent;\n    const events = getElementEvents(element);\n    const storeElementEvent = events[typeEvent] || {};\n    const isNamespace = originalTypeEvent.startsWith('.');\n    if (typeof callable !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!Object.keys(storeElementEvent).length) {\n        return;\n      }\n      removeHandler(element, events, typeEvent, callable, isDelegated ? handler : null);\n      return;\n    }\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1));\n      }\n    }\n    for (const [keyHandlers, event] of Object.entries(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '');\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        removeHandler(element, events, typeEvent, event.callable, event.delegationSelector);\n      }\n    }\n  },\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null;\n    }\n    const $ = getjQuery();\n    const typeEvent = getTypeEvent(event);\n    const inNamespace = event !== typeEvent;\n    let jQueryEvent = null;\n    let bubbles = true;\n    let nativeDispatch = true;\n    let defaultPrevented = false;\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args);\n      $(element).trigger(jQueryEvent);\n      bubbles = !jQueryEvent.isPropagationStopped();\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped();\n      defaultPrevented = jQueryEvent.isDefaultPrevented();\n    }\n    const evt = hydrateObj(new Event(event, {\n      bubbles,\n      cancelable: true\n    }), args);\n    if (defaultPrevented) {\n      evt.preventDefault();\n    }\n    if (nativeDispatch) {\n      element.dispatchEvent(evt);\n    }\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault();\n    }\n    return evt;\n  }\n};\nfunction hydrateObj(obj, meta = {}) {\n  for (const [key, value] of Object.entries(meta)) {\n    try {\n      obj[key] = value;\n    } catch (_unused) {\n      Object.defineProperty(obj, key, {\n        configurable: true,\n        get() {\n          return value;\n        }\n      });\n    }\n  }\n  return obj;\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true;\n  }\n  if (value === 'false') {\n    return false;\n  }\n  if (value === Number(value).toString()) {\n    return Number(value);\n  }\n  if (value === '' || value === 'null') {\n    return null;\n  }\n  if (typeof value !== 'string') {\n    return value;\n  }\n  try {\n    return JSON.parse(decodeURIComponent(value));\n  } catch (_unused) {\n    return value;\n  }\n}\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`);\n}\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value);\n  },\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`);\n  },\n  getDataAttributes(element) {\n    if (!element) {\n      return {};\n    }\n    const attributes = {};\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'));\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '');\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length);\n      attributes[pureKey] = normalizeData(element.dataset[key]);\n    }\n    return attributes;\n  },\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`));\n  }\n};\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {};\n  }\n  static get DefaultType() {\n    return {};\n  }\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!');\n  }\n  _getConfig(config) {\n    config = this._mergeConfigObj(config);\n    config = this._configAfterMerge(config);\n    this._typeCheckConfig(config);\n    return config;\n  }\n  _configAfterMerge(config) {\n    return config;\n  }\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {}; // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    };\n  }\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const [property, expectedTypes] of Object.entries(configTypes)) {\n      const value = config[property];\n      const valueType = isElement(value) ? 'element' : toType(value);\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`);\n      }\n    }\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst VERSION = '5.3.3';\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super();\n    element = getElement(element);\n    if (!element) {\n      return;\n    }\n    this._element = element;\n    this._config = this._getConfig(config);\n    Data.set(this._element, this.constructor.DATA_KEY, this);\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY);\n    EventHandler.off(this._element, this.constructor.EVENT_KEY);\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null;\n    }\n  }\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated);\n  }\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element);\n    config = this._configAfterMerge(config);\n    this._typeCheckConfig(config);\n    return config;\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY);\n  }\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null);\n  }\n  static get VERSION() {\n    return VERSION;\n  }\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`;\n  }\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`;\n  }\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`;\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target');\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href');\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || !hrefAttribute.includes('#') && !hrefAttribute.startsWith('.')) {\n      return null;\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`;\n    }\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null;\n  }\n  return selector ? selector.split(',').map(sel => parseSelector(sel)).join(',') : null;\n};\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector));\n  },\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector);\n  },\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector));\n  },\n  parents(element, selector) {\n    const parents = [];\n    let ancestor = element.parentNode.closest(selector);\n    while (ancestor) {\n      parents.push(ancestor);\n      ancestor = ancestor.parentNode.closest(selector);\n    }\n    return parents;\n  },\n  prev(element, selector) {\n    let previous = element.previousElementSibling;\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous];\n      }\n      previous = previous.previousElementSibling;\n    }\n    return [];\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling;\n    while (next) {\n      if (next.matches(selector)) {\n        return [next];\n      }\n      next = next.nextElementSibling;\n    }\n    return [];\n  },\n  focusableChildren(element) {\n    const focusables = ['a', 'button', 'input', 'textarea', 'select', 'details', '[tabindex]', '[contenteditable=\"true\"]'].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',');\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el));\n  },\n  getSelectorFromElement(element) {\n    const selector = getSelector(element);\n    if (selector) {\n      return SelectorEngine.findOne(selector) ? selector : null;\n    }\n    return null;\n  },\n  getElementFromSelector(element) {\n    const selector = getSelector(element);\n    return selector ? SelectorEngine.findOne(selector) : null;\n  },\n  getMultipleElementsFromSelector(element) {\n    const selector = getSelector(element);\n    return selector ? SelectorEngine.find(selector) : [];\n  }\n};\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`;\n  const name = component.NAME;\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault();\n    }\n    if (isDisabled(this)) {\n      return;\n    }\n    const target = SelectorEngine.getElementFromSelector(this) || this.closest(`.${name}`);\n    const instance = component.getOrCreateInstance(target);\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]();\n  });\n};\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$f = 'alert';\nconst DATA_KEY$a = 'bs.alert';\nconst EVENT_KEY$b = `.${DATA_KEY$a}`;\nconst EVENT_CLOSE = `close${EVENT_KEY$b}`;\nconst EVENT_CLOSED = `closed${EVENT_KEY$b}`;\nconst CLASS_NAME_FADE$5 = 'fade';\nconst CLASS_NAME_SHOW$8 = 'show';\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME$f;\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE);\n    if (closeEvent.defaultPrevented) {\n      return;\n    }\n    this._element.classList.remove(CLASS_NAME_SHOW$8);\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE$5);\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated);\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove();\n    EventHandler.trigger(this._element, EVENT_CLOSED);\n    this.dispose();\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config](this);\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close');\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$e = 'button';\nconst DATA_KEY$9 = 'bs.button';\nconst EVENT_KEY$a = `.${DATA_KEY$9}`;\nconst DATA_API_KEY$6 = '.data-api';\nconst CLASS_NAME_ACTIVE$3 = 'active';\nconst SELECTOR_DATA_TOGGLE$5 = '[data-bs-toggle=\"button\"]';\nconst EVENT_CLICK_DATA_API$6 = `click${EVENT_KEY$a}${DATA_API_KEY$6}`;\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME$e;\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE$3));\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this);\n      if (config === 'toggle') {\n        data[config]();\n      }\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API$6, SELECTOR_DATA_TOGGLE$5, event => {\n  event.preventDefault();\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE$5);\n  const data = Button.getOrCreateInstance(button);\n  data.toggle();\n});\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$d = 'swipe';\nconst EVENT_KEY$9 = '.bs.swipe';\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY$9}`;\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY$9}`;\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY$9}`;\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY$9}`;\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY$9}`;\nconst POINTER_TYPE_TOUCH = 'touch';\nconst POINTER_TYPE_PEN = 'pen';\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event';\nconst SWIPE_THRESHOLD = 40;\nconst Default$c = {\n  endCallback: null,\n  leftCallback: null,\n  rightCallback: null\n};\nconst DefaultType$c = {\n  endCallback: '(function|null)',\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)'\n};\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super();\n    this._element = element;\n    if (!element || !Swipe.isSupported()) {\n      return;\n    }\n    this._config = this._getConfig(config);\n    this._deltaX = 0;\n    this._supportPointerEvents = Boolean(window.PointerEvent);\n    this._initEvents();\n  }\n\n  // Getters\n  static get Default() {\n    return Default$c;\n  }\n  static get DefaultType() {\n    return DefaultType$c;\n  }\n  static get NAME() {\n    return NAME$d;\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY$9);\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX;\n      return;\n    }\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX;\n    }\n  }\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX;\n    }\n    this._handleSwipe();\n    execute(this._config.endCallback);\n  }\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ? 0 : event.touches[0].clientX - this._deltaX;\n  }\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX);\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return;\n    }\n    const direction = absDeltaX / this._deltaX;\n    this._deltaX = 0;\n    if (!direction) {\n      return;\n    }\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback);\n  }\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event));\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event));\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT);\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event));\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event));\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event));\n    }\n  }\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH);\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0;\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$c = 'carousel';\nconst DATA_KEY$8 = 'bs.carousel';\nconst EVENT_KEY$8 = `.${DATA_KEY$8}`;\nconst DATA_API_KEY$5 = '.data-api';\nconst ARROW_LEFT_KEY$1 = 'ArrowLeft';\nconst ARROW_RIGHT_KEY$1 = 'ArrowRight';\nconst TOUCHEVENT_COMPAT_WAIT = 500; // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next';\nconst ORDER_PREV = 'prev';\nconst DIRECTION_LEFT = 'left';\nconst DIRECTION_RIGHT = 'right';\nconst EVENT_SLIDE = `slide${EVENT_KEY$8}`;\nconst EVENT_SLID = `slid${EVENT_KEY$8}`;\nconst EVENT_KEYDOWN$1 = `keydown${EVENT_KEY$8}`;\nconst EVENT_MOUSEENTER$1 = `mouseenter${EVENT_KEY$8}`;\nconst EVENT_MOUSELEAVE$1 = `mouseleave${EVENT_KEY$8}`;\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY$8}`;\nconst EVENT_LOAD_DATA_API$3 = `load${EVENT_KEY$8}${DATA_API_KEY$5}`;\nconst EVENT_CLICK_DATA_API$5 = `click${EVENT_KEY$8}${DATA_API_KEY$5}`;\nconst CLASS_NAME_CAROUSEL = 'carousel';\nconst CLASS_NAME_ACTIVE$2 = 'active';\nconst CLASS_NAME_SLIDE = 'slide';\nconst CLASS_NAME_END = 'carousel-item-end';\nconst CLASS_NAME_START = 'carousel-item-start';\nconst CLASS_NAME_NEXT = 'carousel-item-next';\nconst CLASS_NAME_PREV = 'carousel-item-prev';\nconst SELECTOR_ACTIVE = '.active';\nconst SELECTOR_ITEM = '.carousel-item';\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM;\nconst SELECTOR_ITEM_IMG = '.carousel-item img';\nconst SELECTOR_INDICATORS = '.carousel-indicators';\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]';\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]';\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY$1]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY$1]: DIRECTION_LEFT\n};\nconst Default$b = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n};\nconst DefaultType$b = {\n  interval: '(number|boolean)',\n  // TODO:v6 remove boolean support\n  keyboard: 'boolean',\n  pause: '(string|boolean)',\n  ride: '(boolean|string)',\n  touch: 'boolean',\n  wrap: 'boolean'\n};\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n    this._interval = null;\n    this._activeElement = null;\n    this._isSliding = false;\n    this.touchTimeout = null;\n    this._swipeHelper = null;\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element);\n    this._addEventListeners();\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle();\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default$b;\n  }\n  static get DefaultType() {\n    return DefaultType$b;\n  }\n  static get NAME() {\n    return NAME$c;\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT);\n  }\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next();\n    }\n  }\n  prev() {\n    this._slide(ORDER_PREV);\n  }\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element);\n    }\n    this._clearInterval();\n  }\n  cycle() {\n    this._clearInterval();\n    this._updateInterval();\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval);\n  }\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return;\n    }\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle());\n      return;\n    }\n    this.cycle();\n  }\n  to(index) {\n    const items = this._getItems();\n    if (index > items.length - 1 || index < 0) {\n      return;\n    }\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index));\n      return;\n    }\n    const activeIndex = this._getItemIndex(this._getActive());\n    if (activeIndex === index) {\n      return;\n    }\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV;\n    this._slide(order, items[index]);\n  }\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose();\n    }\n    super.dispose();\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval;\n    return config;\n  }\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN$1, event => this._keydown(event));\n    }\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER$1, () => this.pause());\n      EventHandler.on(this._element, EVENT_MOUSELEAVE$1, () => this._maybeEnableCycle());\n    }\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners();\n    }\n  }\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault());\n    }\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return;\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause();\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout);\n      }\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval);\n    };\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    };\n    this._swipeHelper = new Swipe(this._element, swipeConfig);\n  }\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return;\n    }\n    const direction = KEY_TO_DIRECTION[event.key];\n    if (direction) {\n      event.preventDefault();\n      this._slide(this._directionToOrder(direction));\n    }\n  }\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element);\n  }\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return;\n    }\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement);\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE$2);\n    activeIndicator.removeAttribute('aria-current');\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement);\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE$2);\n      newActiveIndicator.setAttribute('aria-current', 'true');\n    }\n  }\n  _updateInterval() {\n    const element = this._activeElement || this._getActive();\n    if (!element) {\n      return;\n    }\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10);\n    this._config.interval = elementInterval || this._config.defaultInterval;\n  }\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return;\n    }\n    const activeElement = this._getActive();\n    const isNext = order === ORDER_NEXT;\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap);\n    if (nextElement === activeElement) {\n      return;\n    }\n    const nextElementIndex = this._getItemIndex(nextElement);\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      });\n    };\n    const slideEvent = triggerEvent(EVENT_SLIDE);\n    if (slideEvent.defaultPrevented) {\n      return;\n    }\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // TODO: change tests that use empty divs to avoid this check\n      return;\n    }\n    const isCycling = Boolean(this._interval);\n    this.pause();\n    this._isSliding = true;\n    this._setActiveIndicatorElement(nextElementIndex);\n    this._activeElement = nextElement;\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END;\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV;\n    nextElement.classList.add(orderClassName);\n    reflow(nextElement);\n    activeElement.classList.add(directionalClassName);\n    nextElement.classList.add(directionalClassName);\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName);\n      nextElement.classList.add(CLASS_NAME_ACTIVE$2);\n      activeElement.classList.remove(CLASS_NAME_ACTIVE$2, orderClassName, directionalClassName);\n      this._isSliding = false;\n      triggerEvent(EVENT_SLID);\n    };\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated());\n    if (isCycling) {\n      this.cycle();\n    }\n  }\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE);\n  }\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element);\n  }\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element);\n  }\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval);\n      this._interval = null;\n    }\n  }\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT;\n    }\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV;\n  }\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT;\n    }\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT;\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config);\n      if (typeof config === 'number') {\n        data.to(config);\n        return;\n      }\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`);\n        }\n        data[config]();\n      }\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API$5, SELECTOR_DATA_SLIDE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this);\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return;\n  }\n  event.preventDefault();\n  const carousel = Carousel.getOrCreateInstance(target);\n  const slideIndex = this.getAttribute('data-bs-slide-to');\n  if (slideIndex) {\n    carousel.to(slideIndex);\n    carousel._maybeEnableCycle();\n    return;\n  }\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next();\n    carousel._maybeEnableCycle();\n    return;\n  }\n  carousel.prev();\n  carousel._maybeEnableCycle();\n});\nEventHandler.on(window, EVENT_LOAD_DATA_API$3, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE);\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel);\n  }\n});\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$b = 'collapse';\nconst DATA_KEY$7 = 'bs.collapse';\nconst EVENT_KEY$7 = `.${DATA_KEY$7}`;\nconst DATA_API_KEY$4 = '.data-api';\nconst EVENT_SHOW$6 = `show${EVENT_KEY$7}`;\nconst EVENT_SHOWN$6 = `shown${EVENT_KEY$7}`;\nconst EVENT_HIDE$6 = `hide${EVENT_KEY$7}`;\nconst EVENT_HIDDEN$6 = `hidden${EVENT_KEY$7}`;\nconst EVENT_CLICK_DATA_API$4 = `click${EVENT_KEY$7}${DATA_API_KEY$4}`;\nconst CLASS_NAME_SHOW$7 = 'show';\nconst CLASS_NAME_COLLAPSE = 'collapse';\nconst CLASS_NAME_COLLAPSING = 'collapsing';\nconst CLASS_NAME_COLLAPSED = 'collapsed';\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`;\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal';\nconst WIDTH = 'width';\nconst HEIGHT = 'height';\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing';\nconst SELECTOR_DATA_TOGGLE$4 = '[data-bs-toggle=\"collapse\"]';\nconst Default$a = {\n  parent: null,\n  toggle: true\n};\nconst DefaultType$a = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n};\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n    this._isTransitioning = false;\n    this._triggerArray = [];\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE$4);\n    for (const elem of toggleList) {\n      const selector = SelectorEngine.getSelectorFromElement(elem);\n      const filterElement = SelectorEngine.find(selector).filter(foundElement => foundElement === this._element);\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem);\n      }\n    }\n    this._initializeChildren();\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown());\n    }\n    if (this._config.toggle) {\n      this.toggle();\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default$a;\n  }\n  static get DefaultType() {\n    return DefaultType$a;\n  }\n  static get NAME() {\n    return NAME$b;\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide();\n    } else {\n      this.show();\n    }\n  }\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return;\n    }\n    let activeChildren = [];\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES).filter(element => element !== this._element).map(element => Collapse.getOrCreateInstance(element, {\n        toggle: false\n      }));\n    }\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return;\n    }\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW$6);\n    if (startEvent.defaultPrevented) {\n      return;\n    }\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide();\n    }\n    const dimension = this._getDimension();\n    this._element.classList.remove(CLASS_NAME_COLLAPSE);\n    this._element.classList.add(CLASS_NAME_COLLAPSING);\n    this._element.style[dimension] = 0;\n    this._addAriaAndCollapsedClass(this._triggerArray, true);\n    this._isTransitioning = true;\n    const complete = () => {\n      this._isTransitioning = false;\n      this._element.classList.remove(CLASS_NAME_COLLAPSING);\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW$7);\n      this._element.style[dimension] = '';\n      EventHandler.trigger(this._element, EVENT_SHOWN$6);\n    };\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1);\n    const scrollSize = `scroll${capitalizedDimension}`;\n    this._queueCallback(complete, this._element, true);\n    this._element.style[dimension] = `${this._element[scrollSize]}px`;\n  }\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return;\n    }\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE$6);\n    if (startEvent.defaultPrevented) {\n      return;\n    }\n    const dimension = this._getDimension();\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`;\n    reflow(this._element);\n    this._element.classList.add(CLASS_NAME_COLLAPSING);\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW$7);\n    for (const trigger of this._triggerArray) {\n      const element = SelectorEngine.getElementFromSelector(trigger);\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false);\n      }\n    }\n    this._isTransitioning = true;\n    const complete = () => {\n      this._isTransitioning = false;\n      this._element.classList.remove(CLASS_NAME_COLLAPSING);\n      this._element.classList.add(CLASS_NAME_COLLAPSE);\n      EventHandler.trigger(this._element, EVENT_HIDDEN$6);\n    };\n    this._element.style[dimension] = '';\n    this._queueCallback(complete, this._element, true);\n  }\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW$7);\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle); // Coerce string values\n    config.parent = getElement(config.parent);\n    return config;\n  }\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT;\n  }\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return;\n    }\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE$4);\n    for (const element of children) {\n      const selected = SelectorEngine.getElementFromSelector(element);\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected));\n      }\n    }\n  }\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent);\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element));\n  }\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return;\n    }\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen);\n      element.setAttribute('aria-expanded', isOpen);\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {};\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false;\n    }\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config);\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`);\n        }\n        data[config]();\n      }\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API$4, SELECTOR_DATA_TOGGLE$4, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || event.delegateTarget && event.delegateTarget.tagName === 'A') {\n    event.preventDefault();\n  }\n  for (const element of SelectorEngine.getMultipleElementsFromSelector(this)) {\n    Collapse.getOrCreateInstance(element, {\n      toggle: false\n    }).toggle();\n  }\n});\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$a = 'dropdown';\nconst DATA_KEY$6 = 'bs.dropdown';\nconst EVENT_KEY$6 = `.${DATA_KEY$6}`;\nconst DATA_API_KEY$3 = '.data-api';\nconst ESCAPE_KEY$2 = 'Escape';\nconst TAB_KEY$1 = 'Tab';\nconst ARROW_UP_KEY$1 = 'ArrowUp';\nconst ARROW_DOWN_KEY$1 = 'ArrowDown';\nconst RIGHT_MOUSE_BUTTON = 2; // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE$5 = `hide${EVENT_KEY$6}`;\nconst EVENT_HIDDEN$5 = `hidden${EVENT_KEY$6}`;\nconst EVENT_SHOW$5 = `show${EVENT_KEY$6}`;\nconst EVENT_SHOWN$5 = `shown${EVENT_KEY$6}`;\nconst EVENT_CLICK_DATA_API$3 = `click${EVENT_KEY$6}${DATA_API_KEY$3}`;\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY$6}${DATA_API_KEY$3}`;\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY$6}${DATA_API_KEY$3}`;\nconst CLASS_NAME_SHOW$6 = 'show';\nconst CLASS_NAME_DROPUP = 'dropup';\nconst CLASS_NAME_DROPEND = 'dropend';\nconst CLASS_NAME_DROPSTART = 'dropstart';\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center';\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center';\nconst SELECTOR_DATA_TOGGLE$3 = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)';\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE$3}.${CLASS_NAME_SHOW$6}`;\nconst SELECTOR_MENU = '.dropdown-menu';\nconst SELECTOR_NAVBAR = '.navbar';\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav';\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)';\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start';\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end';\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start';\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end';\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start';\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start';\nconst PLACEMENT_TOPCENTER = 'top';\nconst PLACEMENT_BOTTOMCENTER = 'bottom';\nconst Default$9 = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 2],\n  popperConfig: null,\n  reference: 'toggle'\n};\nconst DefaultType$9 = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n};\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n    this._popper = null;\n    this._parent = this._element.parentNode; // dropdown wrapper\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] || SelectorEngine.prev(this._element, SELECTOR_MENU)[0] || SelectorEngine.findOne(SELECTOR_MENU, this._parent);\n    this._inNavbar = this._detectNavbar();\n  }\n\n  // Getters\n  static get Default() {\n    return Default$9;\n  }\n  static get DefaultType() {\n    return DefaultType$9;\n  }\n  static get NAME() {\n    return NAME$a;\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show();\n  }\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return;\n    }\n    const relatedTarget = {\n      relatedTarget: this._element\n    };\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW$5, relatedTarget);\n    if (showEvent.defaultPrevented) {\n      return;\n    }\n    this._createPopper();\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop);\n      }\n    }\n    this._element.focus();\n    this._element.setAttribute('aria-expanded', true);\n    this._menu.classList.add(CLASS_NAME_SHOW$6);\n    this._element.classList.add(CLASS_NAME_SHOW$6);\n    EventHandler.trigger(this._element, EVENT_SHOWN$5, relatedTarget);\n  }\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return;\n    }\n    const relatedTarget = {\n      relatedTarget: this._element\n    };\n    this._completeHide(relatedTarget);\n  }\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy();\n    }\n    super.dispose();\n  }\n  update() {\n    this._inNavbar = this._detectNavbar();\n    if (this._popper) {\n      this._popper.update();\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE$5, relatedTarget);\n    if (hideEvent.defaultPrevented) {\n      return;\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop);\n      }\n    }\n    if (this._popper) {\n      this._popper.destroy();\n    }\n    this._menu.classList.remove(CLASS_NAME_SHOW$6);\n    this._element.classList.remove(CLASS_NAME_SHOW$6);\n    this._element.setAttribute('aria-expanded', 'false');\n    Manipulator.removeDataAttribute(this._menu, 'popper');\n    EventHandler.trigger(this._element, EVENT_HIDDEN$5, relatedTarget);\n  }\n  _getConfig(config) {\n    config = super._getConfig(config);\n    if (typeof config.reference === 'object' && !isElement(config.reference) && typeof config.reference.getBoundingClientRect !== 'function') {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME$a.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`);\n    }\n    return config;\n  }\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)');\n    }\n    let referenceElement = this._element;\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent;\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference);\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference;\n    }\n    const popperConfig = this._getPopperConfig();\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig);\n  }\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW$6);\n  }\n  _getPlacement() {\n    const parentDropdown = this._parent;\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT;\n    }\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT;\n    }\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER;\n    }\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER;\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end';\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP;\n    }\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM;\n  }\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null;\n  }\n  _getOffset() {\n    const {\n      offset\n    } = this._config;\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10));\n    }\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element);\n    }\n    return offset;\n  }\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      }, {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    };\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static'); // TODO: v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }];\n    }\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    };\n  }\n  _selectMenuItem({\n    key,\n    target\n  }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element));\n    if (!items.length) {\n      return;\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY$1, !items.includes(target)).focus();\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config]();\n    });\n  }\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || event.type === 'keyup' && event.key !== TAB_KEY$1) {\n      return;\n    }\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN);\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle);\n      if (!context || context._config.autoClose === false) {\n        continue;\n      }\n      const composedPath = event.composedPath();\n      const isMenuTarget = composedPath.includes(context._menu);\n      if (composedPath.includes(context._element) || context._config.autoClose === 'inside' && !isMenuTarget || context._config.autoClose === 'outside' && isMenuTarget) {\n        continue;\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && (event.type === 'keyup' && event.key === TAB_KEY$1 || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue;\n      }\n      const relatedTarget = {\n        relatedTarget: context._element\n      };\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event;\n      }\n      context._completeHide(relatedTarget);\n    }\n  }\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName);\n    const isEscapeEvent = event.key === ESCAPE_KEY$2;\n    const isUpOrDownEvent = [ARROW_UP_KEY$1, ARROW_DOWN_KEY$1].includes(event.key);\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return;\n    }\n    if (isInput && !isEscapeEvent) {\n      return;\n    }\n    event.preventDefault();\n\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE$3) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE$3)[0] || SelectorEngine.next(this, SELECTOR_DATA_TOGGLE$3)[0] || SelectorEngine.findOne(SELECTOR_DATA_TOGGLE$3, event.delegateTarget.parentNode);\n    const instance = Dropdown.getOrCreateInstance(getToggleButton);\n    if (isUpOrDownEvent) {\n      event.stopPropagation();\n      instance.show();\n      instance._selectMenuItem(event);\n      return;\n    }\n    if (instance._isShown()) {\n      // else is escape and we check if it is shown\n      event.stopPropagation();\n      instance.hide();\n      getToggleButton.focus();\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE$3, Dropdown.dataApiKeydownHandler);\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler);\nEventHandler.on(document, EVENT_CLICK_DATA_API$3, Dropdown.clearMenus);\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus);\nEventHandler.on(document, EVENT_CLICK_DATA_API$3, SELECTOR_DATA_TOGGLE$3, function (event) {\n  event.preventDefault();\n  Dropdown.getOrCreateInstance(this).toggle();\n});\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$9 = 'backdrop';\nconst CLASS_NAME_FADE$4 = 'fade';\nconst CLASS_NAME_SHOW$5 = 'show';\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME$9}`;\nconst Default$8 = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true,\n  // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n};\nconst DefaultType$8 = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n};\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super();\n    this._config = this._getConfig(config);\n    this._isAppended = false;\n    this._element = null;\n  }\n\n  // Getters\n  static get Default() {\n    return Default$8;\n  }\n  static get DefaultType() {\n    return DefaultType$8;\n  }\n  static get NAME() {\n    return NAME$9;\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback);\n      return;\n    }\n    this._append();\n    const element = this._getElement();\n    if (this._config.isAnimated) {\n      reflow(element);\n    }\n    element.classList.add(CLASS_NAME_SHOW$5);\n    this._emulateAnimation(() => {\n      execute(callback);\n    });\n  }\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback);\n      return;\n    }\n    this._getElement().classList.remove(CLASS_NAME_SHOW$5);\n    this._emulateAnimation(() => {\n      this.dispose();\n      execute(callback);\n    });\n  }\n  dispose() {\n    if (!this._isAppended) {\n      return;\n    }\n    EventHandler.off(this._element, EVENT_MOUSEDOWN);\n    this._element.remove();\n    this._isAppended = false;\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div');\n      backdrop.className = this._config.className;\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE$4);\n      }\n      this._element = backdrop;\n    }\n    return this._element;\n  }\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement);\n    return config;\n  }\n  _append() {\n    if (this._isAppended) {\n      return;\n    }\n    const element = this._getElement();\n    this._config.rootElement.append(element);\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback);\n    });\n    this._isAppended = true;\n  }\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated);\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$8 = 'focustrap';\nconst DATA_KEY$5 = 'bs.focustrap';\nconst EVENT_KEY$5 = `.${DATA_KEY$5}`;\nconst EVENT_FOCUSIN$2 = `focusin${EVENT_KEY$5}`;\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY$5}`;\nconst TAB_KEY = 'Tab';\nconst TAB_NAV_FORWARD = 'forward';\nconst TAB_NAV_BACKWARD = 'backward';\nconst Default$7 = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n};\nconst DefaultType$7 = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n};\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super();\n    this._config = this._getConfig(config);\n    this._isActive = false;\n    this._lastTabNavDirection = null;\n  }\n\n  // Getters\n  static get Default() {\n    return Default$7;\n  }\n  static get DefaultType() {\n    return DefaultType$7;\n  }\n  static get NAME() {\n    return NAME$8;\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return;\n    }\n    if (this._config.autofocus) {\n      this._config.trapElement.focus();\n    }\n    EventHandler.off(document, EVENT_KEY$5); // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN$2, event => this._handleFocusin(event));\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event));\n    this._isActive = true;\n  }\n  deactivate() {\n    if (!this._isActive) {\n      return;\n    }\n    this._isActive = false;\n    EventHandler.off(document, EVENT_KEY$5);\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const {\n      trapElement\n    } = this._config;\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return;\n    }\n    const elements = SelectorEngine.focusableChildren(trapElement);\n    if (elements.length === 0) {\n      trapElement.focus();\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus();\n    } else {\n      elements[0].focus();\n    }\n  }\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return;\n    }\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD;\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top';\nconst SELECTOR_STICKY_CONTENT = '.sticky-top';\nconst PROPERTY_PADDING = 'padding-right';\nconst PROPERTY_MARGIN = 'margin-right';\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body;\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth;\n    return Math.abs(window.innerWidth - documentWidth);\n  }\n  hide() {\n    const width = this.getWidth();\n    this._disableOverFlow();\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width);\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width);\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width);\n  }\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow');\n    this._resetElementAttributes(this._element, PROPERTY_PADDING);\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING);\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN);\n  }\n  isOverflowing() {\n    return this.getWidth() > 0;\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow');\n    this._element.style.overflow = 'hidden';\n  }\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth();\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return;\n      }\n      this._saveInitialAttribute(element, styleProperty);\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty);\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`);\n    };\n    this._applyManipulationCallback(selector, manipulationCallBack);\n  }\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty);\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue);\n    }\n  }\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty);\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty);\n        return;\n      }\n      Manipulator.removeDataAttribute(element, styleProperty);\n      element.style.setProperty(styleProperty, value);\n    };\n    this._applyManipulationCallback(selector, manipulationCallBack);\n  }\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector);\n      return;\n    }\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel);\n    }\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$7 = 'modal';\nconst DATA_KEY$4 = 'bs.modal';\nconst EVENT_KEY$4 = `.${DATA_KEY$4}`;\nconst DATA_API_KEY$2 = '.data-api';\nconst ESCAPE_KEY$1 = 'Escape';\nconst EVENT_HIDE$4 = `hide${EVENT_KEY$4}`;\nconst EVENT_HIDE_PREVENTED$1 = `hidePrevented${EVENT_KEY$4}`;\nconst EVENT_HIDDEN$4 = `hidden${EVENT_KEY$4}`;\nconst EVENT_SHOW$4 = `show${EVENT_KEY$4}`;\nconst EVENT_SHOWN$4 = `shown${EVENT_KEY$4}`;\nconst EVENT_RESIZE$1 = `resize${EVENT_KEY$4}`;\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY$4}`;\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY$4}`;\nconst EVENT_KEYDOWN_DISMISS$1 = `keydown.dismiss${EVENT_KEY$4}`;\nconst EVENT_CLICK_DATA_API$2 = `click${EVENT_KEY$4}${DATA_API_KEY$2}`;\nconst CLASS_NAME_OPEN = 'modal-open';\nconst CLASS_NAME_FADE$3 = 'fade';\nconst CLASS_NAME_SHOW$4 = 'show';\nconst CLASS_NAME_STATIC = 'modal-static';\nconst OPEN_SELECTOR$1 = '.modal.show';\nconst SELECTOR_DIALOG = '.modal-dialog';\nconst SELECTOR_MODAL_BODY = '.modal-body';\nconst SELECTOR_DATA_TOGGLE$2 = '[data-bs-toggle=\"modal\"]';\nconst Default$6 = {\n  backdrop: true,\n  focus: true,\n  keyboard: true\n};\nconst DefaultType$6 = {\n  backdrop: '(boolean|string)',\n  focus: 'boolean',\n  keyboard: 'boolean'\n};\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element);\n    this._backdrop = this._initializeBackDrop();\n    this._focustrap = this._initializeFocusTrap();\n    this._isShown = false;\n    this._isTransitioning = false;\n    this._scrollBar = new ScrollBarHelper();\n    this._addEventListeners();\n  }\n\n  // Getters\n  static get Default() {\n    return Default$6;\n  }\n  static get DefaultType() {\n    return DefaultType$6;\n  }\n  static get NAME() {\n    return NAME$7;\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget);\n  }\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return;\n    }\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW$4, {\n      relatedTarget\n    });\n    if (showEvent.defaultPrevented) {\n      return;\n    }\n    this._isShown = true;\n    this._isTransitioning = true;\n    this._scrollBar.hide();\n    document.body.classList.add(CLASS_NAME_OPEN);\n    this._adjustDialog();\n    this._backdrop.show(() => this._showElement(relatedTarget));\n  }\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return;\n    }\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE$4);\n    if (hideEvent.defaultPrevented) {\n      return;\n    }\n    this._isShown = false;\n    this._isTransitioning = true;\n    this._focustrap.deactivate();\n    this._element.classList.remove(CLASS_NAME_SHOW$4);\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated());\n  }\n  dispose() {\n    EventHandler.off(window, EVENT_KEY$4);\n    EventHandler.off(this._dialog, EVENT_KEY$4);\n    this._backdrop.dispose();\n    this._focustrap.deactivate();\n    super.dispose();\n  }\n  handleUpdate() {\n    this._adjustDialog();\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop),\n      // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    });\n  }\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    });\n  }\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element);\n    }\n    this._element.style.display = 'block';\n    this._element.removeAttribute('aria-hidden');\n    this._element.setAttribute('aria-modal', true);\n    this._element.setAttribute('role', 'dialog');\n    this._element.scrollTop = 0;\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog);\n    if (modalBody) {\n      modalBody.scrollTop = 0;\n    }\n    reflow(this._element);\n    this._element.classList.add(CLASS_NAME_SHOW$4);\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate();\n      }\n      this._isTransitioning = false;\n      EventHandler.trigger(this._element, EVENT_SHOWN$4, {\n        relatedTarget\n      });\n    };\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated());\n  }\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS$1, event => {\n      if (event.key !== ESCAPE_KEY$1) {\n        return;\n      }\n      if (this._config.keyboard) {\n        this.hide();\n        return;\n      }\n      this._triggerBackdropTransition();\n    });\n    EventHandler.on(window, EVENT_RESIZE$1, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog();\n      }\n    });\n    EventHandler.on(this._element, EVENT_MOUSEDOWN_DISMISS, event => {\n      // a bad trick to segregate clicks that may start inside dialog but end outside, and avoid listen to scrollbar clicks\n      EventHandler.one(this._element, EVENT_CLICK_DISMISS, event2 => {\n        if (this._element !== event.target || this._element !== event2.target) {\n          return;\n        }\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition();\n          return;\n        }\n        if (this._config.backdrop) {\n          this.hide();\n        }\n      });\n    });\n  }\n  _hideModal() {\n    this._element.style.display = 'none';\n    this._element.setAttribute('aria-hidden', true);\n    this._element.removeAttribute('aria-modal');\n    this._element.removeAttribute('role');\n    this._isTransitioning = false;\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN);\n      this._resetAdjustments();\n      this._scrollBar.reset();\n      EventHandler.trigger(this._element, EVENT_HIDDEN$4);\n    });\n  }\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE$3);\n  }\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED$1);\n    if (hideEvent.defaultPrevented) {\n      return;\n    }\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight;\n    const initialOverflowY = this._element.style.overflowY;\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return;\n    }\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden';\n    }\n    this._element.classList.add(CLASS_NAME_STATIC);\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC);\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY;\n      }, this._dialog);\n    }, this._dialog);\n    this._element.focus();\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight;\n    const scrollbarWidth = this._scrollBar.getWidth();\n    const isBodyOverflowing = scrollbarWidth > 0;\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight';\n      this._element.style[property] = `${scrollbarWidth}px`;\n    }\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft';\n      this._element.style[property] = `${scrollbarWidth}px`;\n    }\n  }\n  _resetAdjustments() {\n    this._element.style.paddingLeft = '';\n    this._element.style.paddingRight = '';\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config](relatedTarget);\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API$2, SELECTOR_DATA_TOGGLE$2, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this);\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault();\n  }\n  EventHandler.one(target, EVENT_SHOW$4, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return;\n    }\n    EventHandler.one(target, EVENT_HIDDEN$4, () => {\n      if (isVisible(this)) {\n        this.focus();\n      }\n    });\n  });\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR$1);\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide();\n  }\n  const data = Modal.getOrCreateInstance(target);\n  data.toggle(this);\n});\nenableDismissTrigger(Modal);\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$6 = 'offcanvas';\nconst DATA_KEY$3 = 'bs.offcanvas';\nconst EVENT_KEY$3 = `.${DATA_KEY$3}`;\nconst DATA_API_KEY$1 = '.data-api';\nconst EVENT_LOAD_DATA_API$2 = `load${EVENT_KEY$3}${DATA_API_KEY$1}`;\nconst ESCAPE_KEY = 'Escape';\nconst CLASS_NAME_SHOW$3 = 'show';\nconst CLASS_NAME_SHOWING$1 = 'showing';\nconst CLASS_NAME_HIDING = 'hiding';\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop';\nconst OPEN_SELECTOR = '.offcanvas.show';\nconst EVENT_SHOW$3 = `show${EVENT_KEY$3}`;\nconst EVENT_SHOWN$3 = `shown${EVENT_KEY$3}`;\nconst EVENT_HIDE$3 = `hide${EVENT_KEY$3}`;\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY$3}`;\nconst EVENT_HIDDEN$3 = `hidden${EVENT_KEY$3}`;\nconst EVENT_RESIZE = `resize${EVENT_KEY$3}`;\nconst EVENT_CLICK_DATA_API$1 = `click${EVENT_KEY$3}${DATA_API_KEY$1}`;\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY$3}`;\nconst SELECTOR_DATA_TOGGLE$1 = '[data-bs-toggle=\"offcanvas\"]';\nconst Default$5 = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n};\nconst DefaultType$5 = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n};\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n    this._isShown = false;\n    this._backdrop = this._initializeBackDrop();\n    this._focustrap = this._initializeFocusTrap();\n    this._addEventListeners();\n  }\n\n  // Getters\n  static get Default() {\n    return Default$5;\n  }\n  static get DefaultType() {\n    return DefaultType$5;\n  }\n  static get NAME() {\n    return NAME$6;\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget);\n  }\n  show(relatedTarget) {\n    if (this._isShown) {\n      return;\n    }\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW$3, {\n      relatedTarget\n    });\n    if (showEvent.defaultPrevented) {\n      return;\n    }\n    this._isShown = true;\n    this._backdrop.show();\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide();\n    }\n    this._element.setAttribute('aria-modal', true);\n    this._element.setAttribute('role', 'dialog');\n    this._element.classList.add(CLASS_NAME_SHOWING$1);\n    const completeCallBack = () => {\n      if (!this._config.scroll || this._config.backdrop) {\n        this._focustrap.activate();\n      }\n      this._element.classList.add(CLASS_NAME_SHOW$3);\n      this._element.classList.remove(CLASS_NAME_SHOWING$1);\n      EventHandler.trigger(this._element, EVENT_SHOWN$3, {\n        relatedTarget\n      });\n    };\n    this._queueCallback(completeCallBack, this._element, true);\n  }\n  hide() {\n    if (!this._isShown) {\n      return;\n    }\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE$3);\n    if (hideEvent.defaultPrevented) {\n      return;\n    }\n    this._focustrap.deactivate();\n    this._element.blur();\n    this._isShown = false;\n    this._element.classList.add(CLASS_NAME_HIDING);\n    this._backdrop.hide();\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW$3, CLASS_NAME_HIDING);\n      this._element.removeAttribute('aria-modal');\n      this._element.removeAttribute('role');\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset();\n      }\n      EventHandler.trigger(this._element, EVENT_HIDDEN$3);\n    };\n    this._queueCallback(completeCallback, this._element, true);\n  }\n  dispose() {\n    this._backdrop.dispose();\n    this._focustrap.deactivate();\n    super.dispose();\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED);\n        return;\n      }\n      this.hide();\n    };\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop);\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    });\n  }\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    });\n  }\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return;\n      }\n      if (this._config.keyboard) {\n        this.hide();\n        return;\n      }\n      EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED);\n    });\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config](this);\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API$1, SELECTOR_DATA_TOGGLE$1, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this);\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault();\n  }\n  if (isDisabled(this)) {\n    return;\n  }\n  EventHandler.one(target, EVENT_HIDDEN$3, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus();\n    }\n  });\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR);\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide();\n  }\n  const data = Offcanvas.getOrCreateInstance(target);\n  data.toggle(this);\n});\nEventHandler.on(window, EVENT_LOAD_DATA_API$2, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show();\n  }\n});\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide();\n    }\n  }\n});\nenableDismissTrigger(Offcanvas);\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n// js-docs-start allow-list\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i;\nconst DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  dd: [],\n  div: [],\n  dl: [],\n  dt: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n};\n// js-docs-end allow-list\n\nconst uriAttributes = new Set(['background', 'cite', 'href', 'itemtype', 'longdesc', 'poster', 'src', 'xlink:href']);\n\n/**\n * A pattern that recognizes URLs that are safe wrt. XSS in URL navigation\n * contexts.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/15.2.8/packages/core/src/sanitization/url_sanitizer.ts#L38\n */\n// eslint-disable-next-line unicorn/better-regex\nconst SAFE_URL_PATTERN = /^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i;\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase();\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue));\n    }\n    return true;\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp).some(regex => regex.test(attributeName));\n};\nfunction sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml;\n  }\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml);\n  }\n  const domParser = new window.DOMParser();\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html');\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'));\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase();\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove();\n      continue;\n    }\n    const attributeList = [].concat(...element.attributes);\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || []);\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName);\n      }\n    }\n  }\n  return createdDocument.body.innerHTML;\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$5 = 'TemplateFactory';\nconst Default$4 = {\n  allowList: DefaultAllowlist,\n  content: {},\n  // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n};\nconst DefaultType$4 = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n};\nconst DefaultContentType = {\n  entry: '(string|element|function|null)',\n  selector: '(string|element)'\n};\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super();\n    this._config = this._getConfig(config);\n  }\n\n  // Getters\n  static get Default() {\n    return Default$4;\n  }\n  static get DefaultType() {\n    return DefaultType$4;\n  }\n  static get NAME() {\n    return NAME$5;\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content).map(config => this._resolvePossibleFunction(config)).filter(Boolean);\n  }\n  hasContent() {\n    return this.getContent().length > 0;\n  }\n  changeContent(content) {\n    this._checkContent(content);\n    this._config.content = {\n      ...this._config.content,\n      ...content\n    };\n    return this;\n  }\n  toHtml() {\n    const templateWrapper = document.createElement('div');\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template);\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector);\n    }\n    const template = templateWrapper.children[0];\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass);\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '));\n    }\n    return template;\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config);\n    this._checkContent(config.content);\n  }\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({\n        selector,\n        entry: content\n      }, DefaultContentType);\n    }\n  }\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template);\n    if (!templateElement) {\n      return;\n    }\n    content = this._resolvePossibleFunction(content);\n    if (!content) {\n      templateElement.remove();\n      return;\n    }\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement);\n      return;\n    }\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content);\n      return;\n    }\n    templateElement.textContent = content;\n  }\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg;\n  }\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this]);\n  }\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = '';\n      templateElement.append(element);\n      return;\n    }\n    templateElement.textContent = element.textContent;\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$4 = 'tooltip';\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn']);\nconst CLASS_NAME_FADE$2 = 'fade';\nconst CLASS_NAME_MODAL = 'modal';\nconst CLASS_NAME_SHOW$2 = 'show';\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner';\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`;\nconst EVENT_MODAL_HIDE = 'hide.bs.modal';\nconst TRIGGER_HOVER = 'hover';\nconst TRIGGER_FOCUS = 'focus';\nconst TRIGGER_CLICK = 'click';\nconst TRIGGER_MANUAL = 'manual';\nconst EVENT_HIDE$2 = 'hide';\nconst EVENT_HIDDEN$2 = 'hidden';\nconst EVENT_SHOW$2 = 'show';\nconst EVENT_SHOWN$2 = 'shown';\nconst EVENT_INSERTED = 'inserted';\nconst EVENT_CLICK$1 = 'click';\nconst EVENT_FOCUSIN$1 = 'focusin';\nconst EVENT_FOCUSOUT$1 = 'focusout';\nconst EVENT_MOUSEENTER = 'mouseenter';\nconst EVENT_MOUSELEAVE = 'mouseleave';\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n};\nconst Default$3 = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 6],\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' + '<div class=\"tooltip-arrow\"></div>' + '<div class=\"tooltip-inner\"></div>' + '</div>',\n  title: '',\n  trigger: 'hover focus'\n};\nconst DefaultType$3 = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n};\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)');\n    }\n    super(element, config);\n\n    // Private\n    this._isEnabled = true;\n    this._timeout = 0;\n    this._isHovered = null;\n    this._activeTrigger = {};\n    this._popper = null;\n    this._templateFactory = null;\n    this._newContent = null;\n\n    // Protected\n    this.tip = null;\n    this._setListeners();\n    if (!this._config.selector) {\n      this._fixTitle();\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default$3;\n  }\n  static get DefaultType() {\n    return DefaultType$3;\n  }\n  static get NAME() {\n    return NAME$4;\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true;\n  }\n  disable() {\n    this._isEnabled = false;\n  }\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled;\n  }\n  toggle() {\n    if (!this._isEnabled) {\n      return;\n    }\n    this._activeTrigger.click = !this._activeTrigger.click;\n    if (this._isShown()) {\n      this._leave();\n      return;\n    }\n    this._enter();\n  }\n  dispose() {\n    clearTimeout(this._timeout);\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler);\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'));\n    }\n    this._disposePopper();\n    super.dispose();\n  }\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements');\n    }\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return;\n    }\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW$2));\n    const shadowRoot = findShadowRoot(this._element);\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element);\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return;\n    }\n\n    // TODO: v6 remove this or make it optional\n    this._disposePopper();\n    const tip = this._getTipElement();\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'));\n    const {\n      container\n    } = this._config;\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip);\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED));\n    }\n    this._popper = this._createPopper(tip);\n    tip.classList.add(CLASS_NAME_SHOW$2);\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop);\n      }\n    }\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN$2));\n      if (this._isHovered === false) {\n        this._leave();\n      }\n      this._isHovered = false;\n    };\n    this._queueCallback(complete, this.tip, this._isAnimated());\n  }\n  hide() {\n    if (!this._isShown()) {\n      return;\n    }\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE$2));\n    if (hideEvent.defaultPrevented) {\n      return;\n    }\n    const tip = this._getTipElement();\n    tip.classList.remove(CLASS_NAME_SHOW$2);\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop);\n      }\n    }\n    this._activeTrigger[TRIGGER_CLICK] = false;\n    this._activeTrigger[TRIGGER_FOCUS] = false;\n    this._activeTrigger[TRIGGER_HOVER] = false;\n    this._isHovered = null; // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return;\n      }\n      if (!this._isHovered) {\n        this._disposePopper();\n      }\n      this._element.removeAttribute('aria-describedby');\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN$2));\n    };\n    this._queueCallback(complete, this.tip, this._isAnimated());\n  }\n  update() {\n    if (this._popper) {\n      this._popper.update();\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle());\n  }\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate());\n    }\n    return this.tip;\n  }\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml();\n\n    // TODO: remove this check in v6\n    if (!tip) {\n      return null;\n    }\n    tip.classList.remove(CLASS_NAME_FADE$2, CLASS_NAME_SHOW$2);\n    // TODO: v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`);\n    const tipId = getUID(this.constructor.NAME).toString();\n    tip.setAttribute('id', tipId);\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE$2);\n    }\n    return tip;\n  }\n  setContent(content) {\n    this._newContent = content;\n    if (this._isShown()) {\n      this._disposePopper();\n      this.show();\n    }\n  }\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content);\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      });\n    }\n    return this._templateFactory;\n  }\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    };\n  }\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title');\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig());\n  }\n  _isAnimated() {\n    return this._config.animation || this.tip && this.tip.classList.contains(CLASS_NAME_FADE$2);\n  }\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW$2);\n  }\n  _createPopper(tip) {\n    const placement = execute(this._config.placement, [this, tip, this._element]);\n    const attachment = AttachmentMap[placement.toUpperCase()];\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment));\n  }\n  _getOffset() {\n    const {\n      offset\n    } = this._config;\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10));\n    }\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element);\n    }\n    return offset;\n  }\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this._element]);\n  }\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [{\n        name: 'flip',\n        options: {\n          fallbackPlacements: this._config.fallbackPlacements\n        }\n      }, {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }, {\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      }, {\n        name: 'arrow',\n        options: {\n          element: `.${this.constructor.NAME}-arrow`\n        }\n      }, {\n        name: 'preSetPlacement',\n        enabled: true,\n        phase: 'beforeMain',\n        fn: data => {\n          // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n          // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n          this._getTipElement().setAttribute('data-popper-placement', data.state.placement);\n        }\n      }]\n    };\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    };\n  }\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ');\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK$1), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event);\n          context.toggle();\n        });\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ? this.constructor.eventName(EVENT_MOUSEENTER) : this.constructor.eventName(EVENT_FOCUSIN$1);\n        const eventOut = trigger === TRIGGER_HOVER ? this.constructor.eventName(EVENT_MOUSELEAVE) : this.constructor.eventName(EVENT_FOCUSOUT$1);\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event);\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true;\n          context._enter();\n        });\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event);\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] = context._element.contains(event.relatedTarget);\n          context._leave();\n        });\n      }\n    }\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide();\n      }\n    };\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler);\n  }\n  _fixTitle() {\n    const title = this._element.getAttribute('title');\n    if (!title) {\n      return;\n    }\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title);\n    }\n    this._element.setAttribute('data-bs-original-title', title); // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title');\n  }\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true;\n      return;\n    }\n    this._isHovered = true;\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show();\n      }\n    }, this._config.delay.show);\n  }\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return;\n    }\n    this._isHovered = false;\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide();\n      }\n    }, this._config.delay.hide);\n  }\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout);\n    this._timeout = setTimeout(handler, timeout);\n  }\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true);\n  }\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element);\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute];\n      }\n    }\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    };\n    config = this._mergeConfigObj(config);\n    config = this._configAfterMerge(config);\n    this._typeCheckConfig(config);\n    return config;\n  }\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container);\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      };\n    }\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString();\n    }\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString();\n    }\n    return config;\n  }\n  _getDelegateConfig() {\n    const config = {};\n    for (const [key, value] of Object.entries(this._config)) {\n      if (this.constructor.Default[key] !== value) {\n        config[key] = value;\n      }\n    }\n    config.selector = false;\n    config.trigger = 'manual';\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config;\n  }\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy();\n      this._popper = null;\n    }\n    if (this.tip) {\n      this.tip.remove();\n      this.tip = null;\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config]();\n    });\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$3 = 'popover';\nconst SELECTOR_TITLE = '.popover-header';\nconst SELECTOR_CONTENT = '.popover-body';\nconst Default$2 = {\n  ...Tooltip.Default,\n  content: '',\n  offset: [0, 8],\n  placement: 'right',\n  template: '<div class=\"popover\" role=\"tooltip\">' + '<div class=\"popover-arrow\"></div>' + '<h3 class=\"popover-header\"></h3>' + '<div class=\"popover-body\"></div>' + '</div>',\n  trigger: 'click'\n};\nconst DefaultType$2 = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n};\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default$2;\n  }\n  static get DefaultType() {\n    return DefaultType$2;\n  }\n  static get NAME() {\n    return NAME$3;\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent();\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    };\n  }\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content);\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config]();\n    });\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$2 = 'scrollspy';\nconst DATA_KEY$2 = 'bs.scrollspy';\nconst EVENT_KEY$2 = `.${DATA_KEY$2}`;\nconst DATA_API_KEY = '.data-api';\nconst EVENT_ACTIVATE = `activate${EVENT_KEY$2}`;\nconst EVENT_CLICK = `click${EVENT_KEY$2}`;\nconst EVENT_LOAD_DATA_API$1 = `load${EVENT_KEY$2}${DATA_API_KEY}`;\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item';\nconst CLASS_NAME_ACTIVE$1 = 'active';\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]';\nconst SELECTOR_TARGET_LINKS = '[href]';\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group';\nconst SELECTOR_NAV_LINKS = '.nav-link';\nconst SELECTOR_NAV_ITEMS = '.nav-item';\nconst SELECTOR_LIST_ITEMS = '.list-group-item';\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`;\nconst SELECTOR_DROPDOWN = '.dropdown';\nconst SELECTOR_DROPDOWN_TOGGLE$1 = '.dropdown-toggle';\nconst Default$1 = {\n  offset: null,\n  // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null,\n  threshold: [0.1, 0.5, 1]\n};\nconst DefaultType$1 = {\n  offset: '(number|null)',\n  // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element',\n  threshold: 'array'\n};\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map();\n    this._observableSections = new Map();\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element;\n    this._activeTarget = null;\n    this._observer = null;\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    };\n    this.refresh(); // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default$1;\n  }\n  static get DefaultType() {\n    return DefaultType$1;\n  }\n  static get NAME() {\n    return NAME$2;\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables();\n    this._maybeEnableSmoothScroll();\n    if (this._observer) {\n      this._observer.disconnect();\n    } else {\n      this._observer = this._getNewObserver();\n    }\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section);\n    }\n  }\n  dispose() {\n    this._observer.disconnect();\n    super.dispose();\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body;\n\n    // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n    config.rootMargin = config.offset ? `${config.offset}px 0px -30%` : config.rootMargin;\n    if (typeof config.threshold === 'string') {\n      config.threshold = config.threshold.split(',').map(value => Number.parseFloat(value));\n    }\n    return config;\n  }\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return;\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK);\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash);\n      if (observableSection) {\n        event.preventDefault();\n        const root = this._rootElement || window;\n        const height = observableSection.offsetTop - this._element.offsetTop;\n        if (root.scrollTo) {\n          root.scrollTo({\n            top: height,\n            behavior: 'smooth'\n          });\n          return;\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height;\n      }\n    });\n  }\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: this._config.threshold,\n      rootMargin: this._config.rootMargin\n    };\n    return new IntersectionObserver(entries => this._observerCallback(entries), options);\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`);\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop;\n      this._process(targetElement(entry));\n    };\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop;\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop;\n    this._previousScrollData.parentScrollTop = parentScrollTop;\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null;\n        this._clearActiveClass(targetElement(entry));\n        continue;\n      }\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop;\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry);\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return;\n        }\n        continue;\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry);\n      }\n    }\n  }\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map();\n    this._observableSections = new Map();\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target);\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue;\n      }\n      const observableSection = SelectorEngine.findOne(decodeURI(anchor.hash), this._element);\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(decodeURI(anchor.hash), anchor);\n        this._observableSections.set(anchor.hash, observableSection);\n      }\n    }\n  }\n  _process(target) {\n    if (this._activeTarget === target) {\n      return;\n    }\n    this._clearActiveClass(this._config.target);\n    this._activeTarget = target;\n    target.classList.add(CLASS_NAME_ACTIVE$1);\n    this._activateParents(target);\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, {\n      relatedTarget: target\n    });\n  }\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE$1, target.closest(SELECTOR_DROPDOWN)).classList.add(CLASS_NAME_ACTIVE$1);\n      return;\n    }\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE$1);\n      }\n    }\n  }\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE$1);\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE$1}`, parent);\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE$1);\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config]();\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API$1, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy);\n  }\n});\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$1 = 'tab';\nconst DATA_KEY$1 = 'bs.tab';\nconst EVENT_KEY$1 = `.${DATA_KEY$1}`;\nconst EVENT_HIDE$1 = `hide${EVENT_KEY$1}`;\nconst EVENT_HIDDEN$1 = `hidden${EVENT_KEY$1}`;\nconst EVENT_SHOW$1 = `show${EVENT_KEY$1}`;\nconst EVENT_SHOWN$1 = `shown${EVENT_KEY$1}`;\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY$1}`;\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY$1}`;\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY$1}`;\nconst ARROW_LEFT_KEY = 'ArrowLeft';\nconst ARROW_RIGHT_KEY = 'ArrowRight';\nconst ARROW_UP_KEY = 'ArrowUp';\nconst ARROW_DOWN_KEY = 'ArrowDown';\nconst HOME_KEY = 'Home';\nconst END_KEY = 'End';\nconst CLASS_NAME_ACTIVE = 'active';\nconst CLASS_NAME_FADE$1 = 'fade';\nconst CLASS_NAME_SHOW$1 = 'show';\nconst CLASS_DROPDOWN = 'dropdown';\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle';\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu';\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = `:not(${SELECTOR_DROPDOWN_TOGGLE})`;\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]';\nconst SELECTOR_OUTER = '.nav-item, .list-group-item';\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`;\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'; // TODO: could only be `tab` in v6\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`;\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`;\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element);\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL);\n    if (!this._parent) {\n      return;\n      // TODO: should throw exception in v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren());\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event));\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME$1;\n  }\n\n  // Public\n  show() {\n    // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element;\n    if (this._elemIsActive(innerElem)) {\n      return;\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem();\n    const hideEvent = active ? EventHandler.trigger(active, EVENT_HIDE$1, {\n      relatedTarget: innerElem\n    }) : null;\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW$1, {\n      relatedTarget: active\n    });\n    if (showEvent.defaultPrevented || hideEvent && hideEvent.defaultPrevented) {\n      return;\n    }\n    this._deactivate(active, innerElem);\n    this._activate(innerElem, active);\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return;\n    }\n    element.classList.add(CLASS_NAME_ACTIVE);\n    this._activate(SelectorEngine.getElementFromSelector(element)); // Search and activate/show the proper section\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.add(CLASS_NAME_SHOW$1);\n        return;\n      }\n      element.removeAttribute('tabindex');\n      element.setAttribute('aria-selected', true);\n      this._toggleDropDown(element, true);\n      EventHandler.trigger(element, EVENT_SHOWN$1, {\n        relatedTarget: relatedElem\n      });\n    };\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE$1));\n  }\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return;\n    }\n    element.classList.remove(CLASS_NAME_ACTIVE);\n    element.blur();\n    this._deactivate(SelectorEngine.getElementFromSelector(element)); // Search and deactivate the shown section too\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.remove(CLASS_NAME_SHOW$1);\n        return;\n      }\n      element.setAttribute('aria-selected', false);\n      element.setAttribute('tabindex', '-1');\n      this._toggleDropDown(element, false);\n      EventHandler.trigger(element, EVENT_HIDDEN$1, {\n        relatedTarget: relatedElem\n      });\n    };\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE$1));\n  }\n  _keydown(event) {\n    if (![ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY, HOME_KEY, END_KEY].includes(event.key)) {\n      return;\n    }\n    event.stopPropagation(); // stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault();\n    const children = this._getChildren().filter(element => !isDisabled(element));\n    let nextActiveElement;\n    if ([HOME_KEY, END_KEY].includes(event.key)) {\n      nextActiveElement = children[event.key === HOME_KEY ? 0 : children.length - 1];\n    } else {\n      const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key);\n      nextActiveElement = getNextActiveElement(children, event.target, isNext, true);\n    }\n    if (nextActiveElement) {\n      nextActiveElement.focus({\n        preventScroll: true\n      });\n      Tab.getOrCreateInstance(nextActiveElement).show();\n    }\n  }\n  _getChildren() {\n    // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent);\n  }\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null;\n  }\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist');\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child);\n    }\n  }\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child);\n    const isActive = this._elemIsActive(child);\n    const outerElem = this._getOuterElement(child);\n    child.setAttribute('aria-selected', isActive);\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation');\n    }\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1');\n    }\n    this._setAttributeIfNotExists(child, 'role', 'tab');\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child);\n  }\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = SelectorEngine.getElementFromSelector(child);\n    if (!target) {\n      return;\n    }\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel');\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `${child.id}`);\n    }\n  }\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element);\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return;\n    }\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem);\n      if (element) {\n        element.classList.toggle(className, open);\n      }\n    };\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE);\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW$1);\n    outerElem.setAttribute('aria-expanded', open);\n  }\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value);\n    }\n  }\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE);\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem);\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem;\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config]();\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault();\n  }\n  if (isDisabled(this)) {\n    return;\n  }\n  Tab.getOrCreateInstance(this).show();\n});\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element);\n  }\n});\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME = 'toast';\nconst DATA_KEY = 'bs.toast';\nconst EVENT_KEY = `.${DATA_KEY}`;\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`;\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`;\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`;\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`;\nconst EVENT_HIDE = `hide${EVENT_KEY}`;\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`;\nconst EVENT_SHOW = `show${EVENT_KEY}`;\nconst EVENT_SHOWN = `shown${EVENT_KEY}`;\nconst CLASS_NAME_FADE = 'fade';\nconst CLASS_NAME_HIDE = 'hide'; // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show';\nconst CLASS_NAME_SHOWING = 'showing';\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n};\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n};\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n    this._timeout = null;\n    this._hasMouseInteraction = false;\n    this._hasKeyboardInteraction = false;\n    this._setListeners();\n  }\n\n  // Getters\n  static get Default() {\n    return Default;\n  }\n  static get DefaultType() {\n    return DefaultType;\n  }\n  static get NAME() {\n    return NAME;\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW);\n    if (showEvent.defaultPrevented) {\n      return;\n    }\n    this._clearTimeout();\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE);\n    }\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING);\n      EventHandler.trigger(this._element, EVENT_SHOWN);\n      this._maybeScheduleHide();\n    };\n    this._element.classList.remove(CLASS_NAME_HIDE); // @deprecated\n    reflow(this._element);\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING);\n    this._queueCallback(complete, this._element, this._config.animation);\n  }\n  hide() {\n    if (!this.isShown()) {\n      return;\n    }\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE);\n    if (hideEvent.defaultPrevented) {\n      return;\n    }\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE); // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW);\n      EventHandler.trigger(this._element, EVENT_HIDDEN);\n    };\n    this._element.classList.add(CLASS_NAME_SHOWING);\n    this._queueCallback(complete, this._element, this._config.animation);\n  }\n  dispose() {\n    this._clearTimeout();\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW);\n    }\n    super.dispose();\n  }\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW);\n  }\n\n  // Private\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return;\n    }\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return;\n    }\n    this._timeout = setTimeout(() => {\n      this.hide();\n    }, this._config.delay);\n  }\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        {\n          this._hasMouseInteraction = isInteracting;\n          break;\n        }\n      case 'focusin':\n      case 'focusout':\n        {\n          this._hasKeyboardInteraction = isInteracting;\n          break;\n        }\n    }\n    if (isInteracting) {\n      this._clearTimeout();\n      return;\n    }\n    const nextElement = event.relatedTarget;\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return;\n    }\n    this._maybeScheduleHide();\n  }\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true));\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false));\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true));\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false));\n  }\n  _clearTimeout() {\n    clearTimeout(this._timeout);\n    this._timeout = null;\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config);\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`);\n        }\n        data[config](this);\n      }\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast);\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast);\n\nexport { Alert, Button, Carousel, Collapse, Dropdown, Modal, Offcanvas, Popover, ScrollSpy, Tab, Toast, Tooltip };\n//# sourceMappingURL=bootstrap.esm.js.map\n", "import { Dropdown } from './bootstrap';\n\n/*\nCore dropdowns\n */\nlet dropdownTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"dropdown\"]'));\ndropdownTriggerList.map(function (dropdownTriggerEl) {\n\tlet options = {\n\t\tboundary: dropdownTriggerEl.getAttribute('data-bs-boundary') === 'viewport' ? document.querySelector('.btn') : 'clippingParents',\n\t}\n\treturn new Dropdown(dropdownTriggerEl, options);\n});\n", "import { Tooltip } from './bootstrap';\n\nlet tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));\ntooltipTriggerList.map(function (tooltipTriggerEl) {\n\tlet options = {\n\t\tdelay: {show: 50, hide: 50},\n\t\thtml: tooltipTriggerEl.getAttribute(\"data-bs-html\") === \"true\" ?? false,\n\t\tplacement: tooltipTriggerEl.getAttribute('data-bs-placement') ?? 'auto'\n\t};\n\treturn new Tooltip(tooltipTriggerEl, options);\n});", "import { Popover } from './bootstrap';\n\n/*\nCore popovers\n */\nlet popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"popover\"]'));\npopoverTriggerList.map(function (popoverTriggerEl) {\n\tlet options = {\n\t\tdelay: {show: 50, hide: 50},\n\t\thtml: popoverTriggerEl.getAttribute('data-bs-html') === \"true\" ?? false,\n\t\tplacement: popoverTriggerEl.getAttribute('data-bs-placement') ?? 'auto'\n\t};\n\treturn new Popover(popoverTriggerEl, options);\n});", "/*\nSwitch icons\n */\nlet switchesTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"switch-icon\"]'));\nswitchesTriggerList.map(function (switchTriggerEl) {\n\tswitchTriggerEl.addEventListener('click', (e) => {\n\t\te.stopPropagation();\n\n\t\tswitchTriggerEl.classList.toggle('active');\n\t});\n});", "import { Toast } from './bootstrap';\n\n/*\nToasts\n */\nlet toastsTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"toast\"]'));\ntoastsTriggerList.map(function (toastTriggerEl) {\n\tif (!toastTriggerEl.hasAttribute('data-bs-target')) {\n\t\treturn;\n\t}\n\n\tconst toastEl = new Toast(toastTriggerEl.getAttribute('data-bs-target'));\n\n\ttoastTriggerEl.addEventListener('click', () => {\n\t\ttoastEl.show()\n\t});\n});", "export const prefix = 'tblr-'\n\nexport const hexToRgba = (hex, opacity) => {\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex)\n\n  return result ? `rgba(${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}, ${opacity})` : null\n}\n\nexport const getColor = (color, opacity = 1) => {\n  const c = getComputedStyle(document.body).getPropertyValue(`--${prefix}${color}`).trim()\n\n  if (opacity !== 1) {\n\t return hexToRgba(c, opacity)\n  }\n\n  return c\n}"], "mappings": ";;;;;;wDAIA,MAAMA,WAAWC,SAASC,iBAAiB,+BACvCF,WAASG,QACZH,WAASI,SAAQ,SAAUC,GAC1BC,UAAYA,SAASD,EACtB,ICRD,MAAML,SAAWC,SAASC,iBAAiB,kBAEvCF,SAASG,QACZH,SAASI,SAAQ,SAAUC,GAC1B,IAAIE,EAAU,GACd,IACC,MAAMC,EAAcH,EAAQI,aAAa,gBAAkBC,KAAKC,MAAMN,EAAQI,aAAa,iBAAmB,GAC9GF,EAAUK,OAAOC,OAAO,CAACC,iBAAmB,GAAON,EAEpD,CAAE,MAAOO,GAAO,CAEhB,MAAMC,EAAQC,SAASZ,EAAQa,UAAW,IAEpCC,EAAU,IAAIC,OAAOD,QAAQE,QAAQhB,EAASW,EAAOT,GACtDY,EAAQJ,OACZI,EAAQG,OAEV,ICbD,IAAIC,gBAAkB,GAAGC,MAAMC,KAAKxB,SAASC,iBAAiB,gBAC9DqB,gBAAgBG,KAAI,SAAUC,GAC7BC,OAAS,IAAIA,MAAMD,EAAQ,CAC1BE,KAAMF,EAAOG,QAAQD,KACrBE,KAAyC,SAAnCJ,EAAOG,QAAQ,iBAEvB,ICVO,IAAIE,IAAM,MACNC,OAAS,SACTC,MAAQ,QACRC,KAAO,OACPC,KAAO,OACPC,eAAiB,CAACL,IAAKC,OAAQC,MAAOC,MACtCb,MAAQ,QACRgB,IAAM,MACNC,gBAAkB,kBAClBC,SAAW,WACXC,OAAS,SACTC,UAAY,YACZC,oBAAmCN,eAAeO,QAAO,SAAUC,EAAKC,GACjF,OAAOD,EAAIE,OAAO,CAACD,EAAY,IAAMxB,MAAOwB,EAAY,IAAMR,KAChE,GAAG,IACQU,WAA0B,GAAGD,OAAOV,eAAgB,CAACD,OAAOQ,QAAO,SAAUC,EAAKC,GAC3F,OAAOD,EAAIE,OAAO,CAACD,EAAWA,EAAY,IAAMxB,MAAOwB,EAAY,IAAMR,KAC3E,GAAG,IAEQW,WAAa,aACbC,KAAO,OACPC,UAAY,YAEZC,WAAa,aACbC,KAAO,OACPC,UAAY,YAEZC,YAAc,cACdC,MAAQ,QACRC,WAAa,aACbC,eAAiB,CAACT,WAAYC,KAAMC,UAAWC,WAAYC,KAAMC,UAAWC,YAAaC,MAAOC,YC9B5F,SAASE,YAAYtD,GAClC,OAAOA,GAAWA,EAAQuD,UAAY,IAAIC,cAAgB,IAC5D,CCFe,SAASC,UAAUC,GAChC,GAAY,MAARA,EACF,OAAO3C,OAGT,GAAwB,oBAApB2C,EAAKC,WAAkC,CACzC,IAAIC,EAAgBF,EAAKE,cACzB,OAAOA,GAAgBA,EAAcC,aAAwB9C,MAC/D,CAEA,OAAO2C,CACT,CCTA,SAASI,YAAUJ,GAEjB,OAAOA,aADUD,UAAUC,GAAMK,SACIL,aAAgBK,OACvD,CAEA,SAASC,cAAcN,GAErB,OAAOA,aADUD,UAAUC,GAAMO,aACIP,aAAgBO,WACvD,CAEA,SAASC,aAAaR,GAEpB,MAA0B,oBAAfS,aAKJT,aADUD,UAAUC,GAAMS,YACIT,aAAgBS,WACvD,CChBA,SAASC,YAAYC,GACnB,IAAIC,EAAQD,EAAKC,MACjB/D,OAAOgE,KAAKD,EAAM3E,UAAUI,SAAQ,SAAUyE,GAC5C,IAAIC,EAAQH,EAAMI,OAAOF,IAAS,GAC9BG,EAAaL,EAAMK,WAAWH,IAAS,GACvCxE,EAAUsE,EAAM3E,SAAS6E,GAExBR,cAAchE,IAAasD,YAAYtD,KAO5CO,OAAOC,OAAOR,EAAQyE,MAAOA,GAC7BlE,OAAOgE,KAAKI,GAAY5E,SAAQ,SAAUyE,GACxC,IAAI7D,EAAQgE,EAAWH,IAET,IAAV7D,EACFX,EAAQ4E,gBAAgBJ,GAExBxE,EAAQ6E,aAAaL,GAAgB,IAAV7D,EAAiB,GAAKA,EAErD,IACF,GACF,CAEA,SAASmE,SAAOC,GACd,IAAIT,EAAQS,EAAMT,MACdU,EAAgB,CAClB5C,OAAQ,CACN6C,SAAUX,EAAMpE,QAAQgF,SACxBpD,KAAM,IACNH,IAAK,IACLwD,OAAQ,KAEVC,MAAO,CACLH,SAAU,YAEZ5C,UAAW,IASb,OAPA9B,OAAOC,OAAO8D,EAAM3E,SAASyC,OAAOqC,MAAOO,EAAc5C,QACzDkC,EAAMI,OAASM,EAEXV,EAAM3E,SAASyF,OACjB7E,OAAOC,OAAO8D,EAAM3E,SAASyF,MAAMX,MAAOO,EAAcI,OAGnD,WACL7E,OAAOgE,KAAKD,EAAM3E,UAAUI,SAAQ,SAAUyE,GAC5C,IAAIxE,EAAUsE,EAAM3E,SAAS6E,GACzBG,EAAaL,EAAMK,WAAWH,IAAS,GAGvCC,EAFkBlE,OAAOgE,KAAKD,EAAMI,OAAOW,eAAeb,GAAQF,EAAMI,OAAOF,GAAQQ,EAAcR,IAE7EjC,QAAO,SAAUkC,EAAOa,GAElD,OADAb,EAAMa,GAAY,GACXb,CACT,GAAG,IAEET,cAAchE,IAAasD,YAAYtD,KAI5CO,OAAOC,OAAOR,EAAQyE,MAAOA,GAC7BlE,OAAOgE,KAAKI,GAAY5E,SAAQ,SAAUwF,GACxCvF,EAAQ4E,gBAAgBW,EAC1B,IACF,G,CAEJ,CAGA,MAAAC,cAAe,CACbhB,KAAM,cACNiB,SAAS,EACTC,MAAO,QACPC,GAAIvB,YACJU,OAAQA,SACRc,SAAU,CAAC,kBCjFE,SAASC,iBAAiBpD,GACvC,OAAOA,EAAUqD,MAAM,KAAK,EAC9B,CCHO,IAAIC,IAAMC,KAAKD,IACXE,IAAMD,KAAKC,IACXC,MAAQF,KAAKE,MCFT,SAASC,cACtB,IAAIC,EAASC,UAAUC,cAEvB,OAAc,MAAVF,GAAkBA,EAAOG,QAAUC,MAAMC,QAAQL,EAAOG,QACnDH,EAAOG,OAAOlF,KAAI,SAAUqF,GACjC,OAAOA,EAAKC,MAAQ,IAAMD,EAAKE,OACjC,IAAGC,KAAK,KAGHR,UAAUS,SACnB,CCTe,SAASC,mBACtB,OAAQ,iCAAiCC,KAAKb,cAChD,CCCe,SAASc,sBAAsBjH,EAASkH,EAAcC,QAC9C,IAAjBD,IACFA,GAAe,QAGO,IAApBC,IACFA,GAAkB,GAGpB,IAAIC,EAAapH,EAAQiH,wBACrBI,EAAS,EACTC,EAAS,EAETJ,GAAgBlD,cAAchE,KAChCqH,EAASrH,EAAQuH,YAAc,GAAIrB,MAAMkB,EAAWI,OAASxH,EAAQuH,aAAmB,EACxFD,EAAStH,EAAQyH,aAAe,GAAIvB,MAAMkB,EAAWM,QAAU1H,EAAQyH,cAAoB,GAG7F,IACIE,GADO7D,YAAU9D,GAAWyD,UAAUzD,GAAWe,QAC3B4G,eAEtBC,GAAoBb,oBAAsBI,EAC1CU,GAAKT,EAAWtF,MAAQ8F,GAAoBD,EAAiBA,EAAeG,WAAa,IAAMT,EAC/FU,GAAKX,EAAWzF,KAAOiG,GAAoBD,EAAiBA,EAAeK,UAAY,IAAMV,EAC7FE,EAAQJ,EAAWI,MAAQH,EAC3BK,EAASN,EAAWM,OAASJ,EACjC,MAAO,CACLE,MAAOA,EACPE,OAAQA,EACR/F,IAAKoG,EACLlG,MAAOgG,EAAIL,EACX5F,OAAQmG,EAAIL,EACZ5F,KAAM+F,EACNA,EAAGA,EACHE,EAAGA,EAEP,CCrCe,SAASE,cAAcjI,GACpC,IAAIoH,EAAaH,sBAAsBjH,GAGnCwH,EAAQxH,EAAQuH,YAChBG,EAAS1H,EAAQyH,aAUrB,OARIzB,KAAKkC,IAAId,EAAWI,MAAQA,IAAU,IACxCA,EAAQJ,EAAWI,OAGjBxB,KAAKkC,IAAId,EAAWM,OAASA,IAAW,IAC1CA,EAASN,EAAWM,QAGf,CACLG,EAAG7H,EAAQ8H,WACXC,EAAG/H,EAAQgI,UACXR,MAAOA,EACPE,OAAQA,EAEZ,CCvBe,SAASS,SAASC,EAAQC,GACvC,IAAIC,EAAWD,EAAME,aAAeF,EAAME,cAE1C,GAAIH,EAAOD,SAASE,GAClB,OAAO,EAEJ,GAAIC,GAAYpE,aAAaoE,GAAW,CACzC,IAAIE,EAAOH,EAEX,EAAG,CACD,GAAIG,GAAQJ,EAAOK,WAAWD,GAC5B,OAAO,EAITA,EAAOA,EAAKE,YAAcF,EAAKG,IACjC,OAASH,EACX,CAGF,OAAO,CACT,CCrBe,SAASI,mBAAiB5I,GACvC,OAAOyD,UAAUzD,GAAS4I,iBAAiB5I,EAC7C,CCFe,SAAS6I,eAAe7I,GACrC,MAAO,CAAC,QAAS,KAAM,MAAM8I,QAAQxF,YAAYtD,KAAa,CAChE,CCFe,SAAS+I,mBAAmB/I,GAEzC,QAAS8D,YAAU9D,GAAWA,EAAQ4D,cACtC5D,EAAQJ,WAAamB,OAAOnB,UAAUoJ,eACxC,CCFe,SAASC,cAAcjJ,GACpC,MAA6B,SAAzBsD,YAAYtD,GACPA,EAMPA,EAAQkJ,cACRlJ,EAAQ0I,aACRxE,aAAalE,GAAWA,EAAQ2I,KAAO,OAEvCI,mBAAmB/I,EAGvB,CCVA,SAASmJ,oBAAoBnJ,GAC3B,OAAKgE,cAAchE,IACoB,UAAvC4I,mBAAiB5I,GAASiF,SAInBjF,EAAQoJ,aAHN,IAIX,CAIA,SAASC,mBAAmBrJ,GAC1B,IAAIsJ,EAAY,WAAWtC,KAAKb,eAGhC,GAFW,WAAWa,KAAKb,gBAEfnC,cAAchE,IAII,UAFX4I,mBAAiB5I,GAEnBiF,SACb,OAAO,KAIX,IAAIsE,EAAcN,cAAcjJ,GAMhC,IAJIkE,aAAaqF,KACfA,EAAcA,EAAYZ,MAGrB3E,cAAcuF,IAAgB,CAAC,OAAQ,QAAQT,QAAQxF,YAAYiG,IAAgB,GAAG,CAC3F,IAAIC,EAAMZ,mBAAiBW,GAI3B,GAAsB,SAAlBC,EAAIC,WAA4C,SAApBD,EAAIE,aAA0C,UAAhBF,EAAIG,UAAgF,IAAzD,CAAC,YAAa,eAAeb,QAAQU,EAAII,aAAsBN,GAAgC,WAAnBE,EAAII,YAA2BN,GAAaE,EAAIK,QAAyB,SAAfL,EAAIK,OACjO,OAAON,EAEPA,EAAcA,EAAYb,UAE9B,CAEA,OAAO,IACT,CAIe,SAASoB,gBAAgB9J,GAItC,IAHA,IAAIe,EAAS0C,UAAUzD,GACnBoJ,EAAeD,oBAAoBnJ,GAEhCoJ,GAAgBP,eAAeO,IAA6D,WAA5CR,mBAAiBQ,GAAcnE,UACpFmE,EAAeD,oBAAoBC,GAGrC,OAAIA,IAA+C,SAA9B9F,YAAY8F,IAA0D,SAA9B9F,YAAY8F,IAAwE,WAA5CR,mBAAiBQ,GAAcnE,UAC3HlE,EAGFqI,GAAgBC,mBAAmBrJ,IAAYe,CACxD,CCpEe,SAASgJ,yBAAyBtH,GAC/C,MAAO,CAAC,MAAO,UAAUqG,QAAQrG,IAAc,EAAI,IAAM,GAC3D,CCDO,SAASuH,OAAO/D,EAAKtF,EAAOoF,GACjC,OAAOkE,IAAQhE,EAAKiE,IAAQvJ,EAAOoF,GACrC,CACO,SAASoE,eAAelE,EAAKtF,EAAOoF,GACzC,IAAIqE,EAAIJ,OAAO/D,EAAKtF,EAAOoF,GAC3B,OAAOqE,EAAIrE,EAAMA,EAAMqE,CACzB,CCPe,SAASC,qBACtB,MAAO,CACL1I,IAAK,EACLE,MAAO,EACPD,OAAQ,EACRE,KAAM,EAEV,CCNe,SAASwI,mBAAmBC,GACzC,OAAOhK,OAAOC,OAAO,GAAI6J,qBAAsBE,EACjD,CCHe,SAASC,gBAAgB7J,EAAO4D,GAC7C,OAAOA,EAAKhC,QAAO,SAAUkI,EAASC,GAEpC,OADAD,EAAQC,GAAO/J,EACR8J,C,GACN,GACL,CCKA,IAAIE,gBAAkB,SAAyBC,EAAStG,GAItD,OAAOgG,mBAAsC,iBAH7CM,EAA6B,mBAAZA,EAAyBA,EAAQrK,OAAOC,OAAO,GAAI8D,EAAMuG,MAAO,CAC/EpI,UAAW6B,EAAM7B,aACbmI,GACkDA,EAAUJ,gBAAgBI,EAAS5I,gBAC7F,EAEA,SAASoD,MAAMf,GACb,IAAIyG,EAEAxG,EAAQD,EAAKC,MACbE,EAAOH,EAAKG,KACZtE,EAAUmE,EAAKnE,QACf6K,EAAezG,EAAM3E,SAASyF,MAC9B4F,EAAgB1G,EAAM2G,cAAcD,cACpCE,EAAgBrF,iBAAiBvB,EAAM7B,WACvC0I,EAAOpB,yBAAyBmB,GAEhCE,EADa,CAACtJ,KAAMD,OAAOiH,QAAQoC,IAAkB,EAClC,SAAW,QAElC,GAAKH,GAAiBC,EAAtB,CAIA,IAAIT,EAAgBI,gBAAgBzK,EAAQ0K,QAAStG,GACjD+G,EAAYpD,cAAc8C,GAC1BO,EAAmB,MAATH,EAAexJ,IAAMG,KAC/ByJ,EAAmB,MAATJ,EAAevJ,OAASC,MAClC2J,EAAUlH,EAAMuG,MAAMxI,UAAU+I,GAAO9G,EAAMuG,MAAMxI,UAAU8I,GAAQH,EAAcG,GAAQ7G,EAAMuG,MAAMzI,OAAOgJ,GAC9GK,EAAYT,EAAcG,GAAQ7G,EAAMuG,MAAMxI,UAAU8I,GACxDO,EAAoB5B,gBAAgBiB,GACpCY,EAAaD,EAA6B,MAATP,EAAeO,EAAkBE,cAAgB,EAAIF,EAAkBG,aAAe,EAAI,EAC3HC,EAAoBN,EAAU,EAAIC,EAAY,EAG9CxF,EAAMsE,EAAce,GACpBvF,EAAM4F,EAAaN,EAAUD,GAAOb,EAAcgB,GAClDQ,EAASJ,EAAa,EAAIN,EAAUD,GAAO,EAAIU,EAC/CE,EAAShC,OAAO/D,EAAK8F,EAAQhG,GAE7BkG,EAAWd,EACf7G,EAAM2G,cAAczG,KAASsG,EAAwB,IAA0BmB,GAAYD,EAAQlB,EAAsBoB,aAAeF,EAASD,EAAQjB,EAnBzJ,CAoBF,CAEA,SAAShG,SAAOC,GACd,IAAIT,EAAQS,EAAMT,MAEd6H,EADUpH,EAAM7E,QACWF,QAC3B+K,OAAoC,IAArBoB,EAA8B,sBAAwBA,EAErD,MAAhBpB,IAKwB,iBAAjBA,IACTA,EAAezG,EAAM3E,SAASyC,OAAOgK,cAAcrB,MAOhD5C,SAAS7D,EAAM3E,SAASyC,OAAQ2I,KAIrCzG,EAAM3E,SAASyF,MAAQ2F,EACzB,CAGA,MAAAsB,QAAe,CACb7H,KAAM,QACNiB,SAAS,EACTC,MAAO,OACPC,GAAIP,MACJN,OAAQA,SACRc,SAAU,CAAC,iBACX0G,iBAAkB,CAAC,oBCxFN,SAASC,aAAa9J,GACnC,OAAOA,EAAUqD,MAAM,KAAK,EAC9B,CCOA,IAAI0G,WAAa,CACf7K,IAAK,OACLE,MAAO,OACPD,OAAQ,OACRE,KAAM,QAKR,SAAS2K,kBAAkBpI,EAAMqI,GAC/B,IAAI7E,EAAIxD,EAAKwD,EACTE,EAAI1D,EAAK0D,EACT4E,EAAMD,EAAIE,kBAAoB,EAClC,MAAO,CACL/E,EAAG3B,MAAM2B,EAAI8E,GAAOA,GAAO,EAC3B5E,EAAG7B,MAAM6B,EAAI4E,GAAOA,GAAO,EAE/B,CAEO,SAASE,YAAY9H,GAC1B,IAAI+H,EAEA1K,EAAS2C,EAAM3C,OACf2K,EAAahI,EAAMgI,WACnBtK,EAAYsC,EAAMtC,UAClBuK,EAAYjI,EAAMiI,UAClBC,EAAUlI,EAAMkI,QAChBhI,EAAWF,EAAME,SACjBiI,EAAkBnI,EAAMmI,gBACxBC,EAAWpI,EAAMoI,SACjBC,EAAerI,EAAMqI,aACrBC,EAAUtI,EAAMsI,QAChBC,EAAaL,EAAQpF,EACrBA,OAAmB,IAAfyF,EAAwB,EAAIA,EAChCC,EAAaN,EAAQlF,EACrBA,OAAmB,IAAfwF,EAAwB,EAAIA,EAEhCC,EAAgC,mBAAjBJ,EAA8BA,EAAa,CAC5DvF,EAAGA,EACHE,EAAGA,IACA,CACHF,EAAGA,EACHE,EAAGA,GAGLF,EAAI2F,EAAM3F,EACVE,EAAIyF,EAAMzF,EACV,IAAI0F,EAAOR,EAAQ5H,eAAe,KAC9BqI,EAAOT,EAAQ5H,eAAe,KAC9BsI,EAAQ7L,KACR8L,EAAQjM,IACR+K,EAAM3L,OAEV,GAAIoM,EAAU,CACZ,IAAI/D,EAAeU,gBAAgB1H,GAC/ByL,EAAa,eACbC,EAAY,cAEZ1E,IAAiB3F,UAAUrB,IAGmB,WAA5CwG,mBAFJQ,EAAeL,mBAAmB3G,IAEC6C,UAAsC,aAAbA,IAC1D4I,EAAa,eACbC,EAAY,gBAOZrL,IAAcd,MAAQc,IAAcX,MAAQW,IAAcZ,QAAUmL,IAAc/K,OACpF2L,EAAQhM,OAGRmG,IAFcsF,GAAWjE,IAAiBsD,GAAOA,EAAI/E,eAAiB+E,EAAI/E,eAAeD,OACzF0B,EAAayE,IACEd,EAAWrF,OAC1BK,GAAKmF,EAAkB,GAAI,GAGzBzK,IAAcX,OAASW,IAAcd,KAAOc,IAAcb,QAAWoL,IAAc/K,OACrF0L,EAAQ9L,MAGRgG,IAFcwF,GAAWjE,IAAiBsD,GAAOA,EAAI/E,eAAiB+E,EAAI/E,eAAeH,MACzF4B,EAAa0E,IACEf,EAAWvF,MAC1BK,GAAKqF,EAAkB,GAAI,EAE/B,CAEA,IAgBMa,EAhBFC,EAAezN,OAAOC,OAAO,CAC/ByE,SAAUA,GACTkI,GAAYX,YAEXyB,GAAyB,IAAjBb,EAAwBX,kBAAkB,CACpD5E,EAAGA,EACHE,EAAGA,GACFtE,UAAUrB,IAAW,CACtByF,EAAGA,EACHE,EAAGA,GAML,OAHAF,EAAIoG,EAAMpG,EACVE,EAAIkG,EAAMlG,EAENmF,EAGK3M,OAAOC,OAAO,GAAIwN,IAAeD,EAAiB,IAAmBH,GAASF,EAAO,IAAM,GAAIK,EAAeJ,GAASF,EAAO,IAAM,GAAIM,EAAetE,WAAaiD,EAAIE,kBAAoB,IAAM,EAAI,aAAe/E,EAAI,OAASE,EAAI,MAAQ,eAAiBF,EAAI,OAASE,EAAI,SAAUgG,IAG5RxN,OAAOC,OAAO,GAAIwN,IAAelB,EAAkB,IAAoBc,GAASF,EAAO3F,EAAI,KAAO,GAAI+E,EAAgBa,GAASF,EAAO5F,EAAI,KAAO,GAAIiF,EAAgBrD,UAAY,GAAIqD,GAC9L,CAEA,SAASoB,cAAcC,GACrB,IAAI7J,EAAQ6J,EAAM7J,MACdpE,EAAUiO,EAAMjO,QAChBkO,EAAwBlO,EAAQgN,gBAChCA,OAA4C,IAA1BkB,GAA0CA,EAC5DC,EAAoBnO,EAAQiN,SAC5BA,OAAiC,IAAtBkB,GAAsCA,EACjDC,EAAwBpO,EAAQkN,aAChCA,OAAyC,IAA1BkB,GAA0CA,EACzDN,EAAe,CACjBvL,UAAWoD,iBAAiBvB,EAAM7B,WAClCuK,UAAWT,aAAajI,EAAM7B,WAC9BL,OAAQkC,EAAM3E,SAASyC,OACvB2K,WAAYzI,EAAMuG,MAAMzI,OACxB8K,gBAAiBA,EACjBG,QAAoC,UAA3B/I,EAAMpE,QAAQgF,UAGgB,MAArCZ,EAAM2G,cAAcD,gBACtB1G,EAAMI,OAAOtC,OAAS7B,OAAOC,OAAO,GAAI8D,EAAMI,OAAOtC,OAAQyK,YAAYtM,OAAOC,OAAO,GAAIwN,EAAc,CACvGf,QAAS3I,EAAM2G,cAAcD,cAC7B/F,SAAUX,EAAMpE,QAAQgF,SACxBiI,SAAUA,EACVC,aAAcA,OAIe,MAA7B9I,EAAM2G,cAAc7F,QACtBd,EAAMI,OAAOU,MAAQ7E,OAAOC,OAAO,GAAI8D,EAAMI,OAAOU,MAAOyH,YAAYtM,OAAOC,OAAO,GAAIwN,EAAc,CACrGf,QAAS3I,EAAM2G,cAAc7F,MAC7BH,SAAU,WACVkI,UAAU,EACVC,aAAcA,OAIlB9I,EAAMK,WAAWvC,OAAS7B,OAAOC,OAAO,GAAI8D,EAAMK,WAAWvC,OAAQ,CACnE,wBAAyBkC,EAAM7B,WAEnC,CAGA,MAAA8L,gBAAe,CACb/J,KAAM,gBACNiB,SAAS,EACTC,MAAO,cACPC,GAAIuI,cACJM,KAAM,ICrKR,IAAIC,QAAU,CACZA,SAAS,GAGX,SAAS3J,OAAOT,GACd,IAAIC,EAAQD,EAAKC,MACboK,EAAWrK,EAAKqK,SAChBxO,EAAUmE,EAAKnE,QACfyO,EAAkBzO,EAAQ0O,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAkB3O,EAAQ4O,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7C9N,EAAS0C,UAAUa,EAAM3E,SAASyC,QAClC2M,EAAgB,GAAGrM,OAAO4B,EAAMyK,cAAc1M,UAAWiC,EAAMyK,cAAc3M,QAYjF,OAVIwM,GACFG,EAAchP,SAAQ,SAAUiP,GAC9BA,EAAaC,iBAAiB,SAAUP,EAASQ,OAAQT,QAC3D,IAGEK,GACF/N,EAAOkO,iBAAiB,SAAUP,EAASQ,OAAQT,SAG9C,WACDG,GACFG,EAAchP,SAAQ,SAAUiP,GAC9BA,EAAaG,oBAAoB,SAAUT,EAASQ,OAAQT,QAC9D,IAGEK,GACF/N,EAAOoO,oBAAoB,SAAUT,EAASQ,OAAQT,Q,CAG5D,CAGA,MAAAW,eAAe,CACb5K,KAAM,iBACNiB,SAAS,EACTC,MAAO,QACPC,GAAI,WAAc,EAClBb,OAAQA,OACR0J,KAAM,IC/CR,IAAIa,OAAO,CACTvN,KAAM,QACND,MAAO,OACPD,OAAQ,MACRD,IAAK,UAEQ,SAAS2N,qBAAqB7M,GAC3C,OAAOA,EAAU8M,QAAQ,0BAA0B,SAAUC,GAC3D,OAAOH,OAAKG,EACd,GACF,CCVA,IAAIH,KAAO,CACTpO,MAAO,MACPgB,IAAK,SAEQ,SAASwN,8BAA8BhN,GACpD,OAAOA,EAAU8M,QAAQ,cAAc,SAAUC,GAC/C,OAAOH,KAAKG,EACd,GACF,CCPe,SAASE,gBAAgBhM,GACtC,IAAIgJ,EAAMjJ,UAAUC,GAGpB,MAAO,CACLiM,WAHejD,EAAIkD,YAInBC,UAHcnD,EAAIoD,YAKtB,CCNe,SAASC,oBAAoB/P,GAQ1C,OAAOiH,sBAAsB8B,mBAAmB/I,IAAU8B,KAAO4N,gBAAgB1P,GAAS2P,UAC5F,CCRe,SAASK,gBAAgBhQ,EAASkF,GAC/C,IAAIwH,EAAMjJ,UAAUzD,GAChBiQ,EAAOlH,mBAAmB/I,GAC1B2H,EAAiB+E,EAAI/E,eACrBH,EAAQyI,EAAKpE,YACbnE,EAASuI,EAAKrE,aACd/D,EAAI,EACJE,EAAI,EAER,GAAIJ,EAAgB,CAClBH,EAAQG,EAAeH,MACvBE,EAASC,EAAeD,OACxB,IAAIwI,EAAiBnJ,oBAEjBmJ,IAAmBA,GAA+B,UAAbhL,KACvC2C,EAAIF,EAAeG,WACnBC,EAAIJ,EAAeK,UAEvB,CAEA,MAAO,CACLR,MAAOA,EACPE,OAAQA,EACRG,EAAGA,EAAIkI,oBAAoB/P,GAC3B+H,EAAGA,EAEP,CCvBe,SAASoI,gBAAgBnQ,GACtC,IAAIoQ,EAEAH,EAAOlH,mBAAmB/I,GAC1BqQ,EAAYX,gBAAgB1P,GAC5BsQ,EAA0D,OAAlDF,EAAwBpQ,EAAQ4D,oBAAyB,EAASwM,EAAsBE,KAChG9I,EAAQzB,IAAIkK,EAAKM,YAAaN,EAAKpE,YAAayE,EAAOA,EAAKC,YAAc,EAAGD,EAAOA,EAAKzE,YAAc,GACvGnE,EAAS3B,IAAIkK,EAAKO,aAAcP,EAAKrE,aAAc0E,EAAOA,EAAKE,aAAe,EAAGF,EAAOA,EAAK1E,aAAe,GAC5G/D,GAAKwI,EAAUV,WAAaI,oBAAoB/P,GAChD+H,GAAKsI,EAAUR,UAMnB,MAJiD,QAA7CjH,mBAAiB0H,GAAQL,GAAMQ,YACjC5I,GAAK9B,IAAIkK,EAAKpE,YAAayE,EAAOA,EAAKzE,YAAc,GAAKrE,GAGrD,CACLA,MAAOA,EACPE,OAAQA,EACRG,EAAGA,EACHE,EAAGA,EAEP,CC3Be,SAAS2I,eAAe1Q,GAErC,IAAI2Q,EAAoB/H,mBAAiB5I,GACrC4Q,EAAWD,EAAkBC,SAC7BC,EAAYF,EAAkBE,UAC9BC,EAAYH,EAAkBG,UAElC,MAAO,6BAA6B9J,KAAK4J,EAAWE,EAAYD,EAClE,CCLe,SAASE,gBAAgBrN,GACtC,MAAI,CAAC,OAAQ,OAAQ,aAAaoF,QAAQxF,YAAYI,KAAU,EAEvDA,EAAKE,cAAc0M,KAGxBtM,cAAcN,IAASgN,eAAehN,GACjCA,EAGFqN,gBAAgB9H,cAAcvF,GACvC,CCJe,SAASsN,kBAAkBhR,EAASiR,GACjD,IAAIb,OAES,IAATa,IACFA,EAAO,IAGT,IAAIjC,EAAe+B,gBAAgB/Q,GAC/BkR,EAASlC,KAAqE,OAAlDoB,EAAwBpQ,EAAQ4D,oBAAyB,EAASwM,EAAsBE,MACpH5D,EAAMjJ,UAAUuL,GAChBmC,EAASD,EAAS,CAACxE,GAAKhK,OAAOgK,EAAI/E,gBAAkB,GAAI+I,eAAe1B,GAAgBA,EAAe,IAAMA,EAC7GoC,EAAcH,EAAKvO,OAAOyO,GAC9B,OAAOD,EAASE,EAChBA,EAAY1O,OAAOsO,kBAAkB/H,cAAckI,IACrD,CCzBe,SAASE,iBAAiBC,GACvC,OAAO/Q,OAAOC,OAAO,GAAI8Q,EAAM,CAC7BxP,KAAMwP,EAAKzJ,EACXlG,IAAK2P,EAAKvJ,EACVlG,MAAOyP,EAAKzJ,EAAIyJ,EAAK9J,MACrB5F,OAAQ0P,EAAKvJ,EAAIuJ,EAAK5J,QAE1B,CCQA,SAAS6J,2BAA2BvR,EAASkF,GAC3C,IAAIoM,EAAOrK,sBAAsBjH,GAAS,EAAoB,UAAbkF,GASjD,OARAoM,EAAK3P,IAAM2P,EAAK3P,IAAM3B,EAAQwR,UAC9BF,EAAKxP,KAAOwP,EAAKxP,KAAO9B,EAAQyR,WAChCH,EAAK1P,OAAS0P,EAAK3P,IAAM3B,EAAQ4L,aACjC0F,EAAKzP,MAAQyP,EAAKxP,KAAO9B,EAAQ6L,YACjCyF,EAAK9J,MAAQxH,EAAQ6L,YACrByF,EAAK5J,OAAS1H,EAAQ4L,aACtB0F,EAAKzJ,EAAIyJ,EAAKxP,KACdwP,EAAKvJ,EAAIuJ,EAAK3P,IACP2P,CACT,CAEA,SAASI,2BAA2B1R,EAAS2R,EAAgBzM,GAC3D,OAAOyM,IAAmBxP,SAAWkP,iBAAiBrB,gBAAgBhQ,EAASkF,IAAapB,YAAU6N,GAAkBJ,2BAA2BI,EAAgBzM,GAAYmM,iBAAiBlB,gBAAgBpH,mBAAmB/I,IACrO,CAKA,SAAS4R,mBAAmB5R,GAC1B,IAAIkC,EAAkB8O,kBAAkB/H,cAAcjJ,IAElD6R,EADoB,CAAC,WAAY,SAAS/I,QAAQF,mBAAiB5I,GAASiF,WAAa,GACnDjB,cAAchE,GAAW8J,gBAAgB9J,GAAWA,EAE9F,OAAK8D,YAAU+N,GAKR3P,EAAgB2H,QAAO,SAAU8H,GACtC,OAAO7N,YAAU6N,IAAmBxJ,SAASwJ,EAAgBE,IAAmD,SAAhCvO,YAAYqO,EAC9F,IANS,EAOX,CAIe,SAASG,gBAAgB9R,EAAS+R,EAAUC,EAAc9M,GACvE,IAAI+M,EAAmC,oBAAbF,EAAiCH,mBAAmB5R,GAAW,GAAG0C,OAAOqP,GAC/F7P,EAAkB,GAAGQ,OAAOuP,EAAqB,CAACD,IAClDE,EAAsBhQ,EAAgB,GACtCiQ,EAAejQ,EAAgBK,QAAO,SAAU6P,EAAST,GAC3D,IAAIL,EAAOI,2BAA2B1R,EAAS2R,EAAgBzM,GAK/D,OAJAkN,EAAQzQ,IAAMoE,IAAIuL,EAAK3P,IAAKyQ,EAAQzQ,KACpCyQ,EAAQvQ,MAAQoE,IAAIqL,EAAKzP,MAAOuQ,EAAQvQ,OACxCuQ,EAAQxQ,OAASqE,IAAIqL,EAAK1P,OAAQwQ,EAAQxQ,QAC1CwQ,EAAQtQ,KAAOiE,IAAIuL,EAAKxP,KAAMsQ,EAAQtQ,MAC/BsQ,C,GACNV,2BAA2B1R,EAASkS,EAAqBhN,IAK5D,OAJAiN,EAAa3K,MAAQ2K,EAAatQ,MAAQsQ,EAAarQ,KACvDqQ,EAAazK,OAASyK,EAAavQ,OAASuQ,EAAaxQ,IACzDwQ,EAAatK,EAAIsK,EAAarQ,KAC9BqQ,EAAapK,EAAIoK,EAAaxQ,IACvBwQ,CACT,CCjEe,SAASE,eAAehO,GACrC,IAOI4I,EAPA5K,EAAYgC,EAAKhC,UACjBrC,EAAUqE,EAAKrE,QACfyC,EAAY4B,EAAK5B,UACjByI,EAAgBzI,EAAYoD,iBAAiBpD,GAAa,KAC1DuK,EAAYvK,EAAY8J,aAAa9J,GAAa,KAClD6P,EAAUjQ,EAAUwF,EAAIxF,EAAUmF,MAAQ,EAAIxH,EAAQwH,MAAQ,EAC9D+K,EAAUlQ,EAAU0F,EAAI1F,EAAUqF,OAAS,EAAI1H,EAAQ0H,OAAS,EAGpE,OAAQwD,GACN,KAAKvJ,IACHsL,EAAU,CACRpF,EAAGyK,EACHvK,EAAG1F,EAAU0F,EAAI/H,EAAQ0H,QAE3B,MAEF,KAAK9F,OACHqL,EAAU,CACRpF,EAAGyK,EACHvK,EAAG1F,EAAU0F,EAAI1F,EAAUqF,QAE7B,MAEF,KAAK7F,MACHoL,EAAU,CACRpF,EAAGxF,EAAUwF,EAAIxF,EAAUmF,MAC3BO,EAAGwK,GAEL,MAEF,KAAKzQ,KACHmL,EAAU,CACRpF,EAAGxF,EAAUwF,EAAI7H,EAAQwH,MACzBO,EAAGwK,GAEL,MAEF,QACEtF,EAAU,CACRpF,EAAGxF,EAAUwF,EACbE,EAAG1F,EAAU0F,GAInB,IAAIyK,EAAWtH,EAAgBnB,yBAAyBmB,GAAiB,KAEzE,GAAgB,MAAZsH,EAAkB,CACpB,IAAIpH,EAAmB,MAAboH,EAAmB,SAAW,QAExC,OAAQxF,GACN,KAAK/L,MACHgM,EAAQuF,GAAYvF,EAAQuF,IAAanQ,EAAU+I,GAAO,EAAIpL,EAAQoL,GAAO,GAC7E,MAEF,KAAKnJ,IACHgL,EAAQuF,GAAYvF,EAAQuF,IAAanQ,EAAU+I,GAAO,EAAIpL,EAAQoL,GAAO,GAKnF,CAEA,OAAO6B,CACT,CC3De,SAASwF,eAAenO,EAAOpE,QAC5B,IAAZA,IACFA,EAAU,IAGZ,IAAIwS,EAAWxS,EACXyS,EAAqBD,EAASjQ,UAC9BA,OAAmC,IAAvBkQ,EAAgCrO,EAAM7B,UAAYkQ,EAC9DC,EAAoBF,EAASxN,SAC7BA,OAAiC,IAAtB0N,EAA+BtO,EAAMY,SAAW0N,EAC3DC,EAAoBH,EAASX,SAC7BA,OAAiC,IAAtBc,EAA+B3Q,gBAAkB2Q,EAC5DC,EAAwBJ,EAASV,aACjCA,OAAyC,IAA1Bc,EAAmC3Q,SAAW2Q,EAC7DC,EAAwBL,EAASM,eACjCA,OAA2C,IAA1BD,EAAmC3Q,OAAS2Q,EAC7DE,EAAuBP,EAASQ,YAChCA,OAAuC,IAAzBD,GAA0CA,EACxDE,EAAmBT,EAAS9H,QAC5BA,OAA+B,IAArBuI,EAA8B,EAAIA,EAC5C5I,EAAgBD,mBAAsC,iBAAZM,EAAuBA,EAAUJ,gBAAgBI,EAAS5I,iBACpGoR,EAAaJ,IAAmB5Q,OAASC,UAAYD,OACrD2K,EAAazI,EAAMuG,MAAMzI,OACzBpC,EAAUsE,EAAM3E,SAASuT,EAAcE,EAAaJ,GACpDK,EAAqBvB,gBAAgBhO,YAAU9D,GAAWA,EAAUA,EAAQsT,gBAAkBvK,mBAAmBzE,EAAM3E,SAASyC,QAAS2P,EAAUC,EAAc9M,GACjKqO,EAAsBtM,sBAAsB3C,EAAM3E,SAAS0C,WAC3D2I,EAAgBqH,eAAe,CACjChQ,UAAWkR,EACXvT,QAAS+M,EAETtK,UAAWA,IAET+Q,EAAmBnC,iBAAiB9Q,OAAOC,OAAO,GAAIuM,EAAY/B,IAClEyI,EAAoBT,IAAmB5Q,OAASoR,EAAmBD,EAGnEG,EAAkB,CACpB/R,IAAK0R,EAAmB1R,IAAM8R,EAAkB9R,IAAM4I,EAAc5I,IACpEC,OAAQ6R,EAAkB7R,OAASyR,EAAmBzR,OAAS2I,EAAc3I,OAC7EE,KAAMuR,EAAmBvR,KAAO2R,EAAkB3R,KAAOyI,EAAczI,KACvED,MAAO4R,EAAkB5R,MAAQwR,EAAmBxR,MAAQ0I,EAAc1I,OAExE8R,EAAarP,EAAM2G,cAAce,OAErC,GAAIgH,IAAmB5Q,QAAUuR,EAAY,CAC3C,IAAI3H,EAAS2H,EAAWlR,GACxBlC,OAAOgE,KAAKmP,GAAiB3T,SAAQ,SAAU2K,GAC7C,IAAIkJ,EAAW,CAAC/R,MAAOD,QAAQkH,QAAQ4B,IAAQ,EAAI,GAAI,EACnDS,EAAO,CAACxJ,IAAKC,QAAQkH,QAAQ4B,IAAQ,EAAI,IAAM,IACnDgJ,EAAgBhJ,IAAQsB,EAAOb,GAAQyI,CACzC,GACF,CAEA,OAAOF,CACT,CC5De,SAASG,qBAAqBvP,EAAOpE,QAClC,IAAZA,IACFA,EAAU,IAGZ,IAAIwS,EAAWxS,EACXuC,EAAYiQ,EAASjQ,UACrBsP,EAAWW,EAASX,SACpBC,EAAeU,EAASV,aACxBpH,EAAU8H,EAAS9H,QACnBkJ,EAAiBpB,EAASoB,eAC1BC,EAAwBrB,EAASsB,sBACjCA,OAAkD,IAA1BD,EAAmCE,WAAgBF,EAC3E/G,EAAYT,aAAa9J,GACzBE,EAAaqK,EAAY8G,EAAiBxR,oBAAsBA,oBAAoBuH,QAAO,SAAUpH,GACvG,OAAO8J,aAAa9J,KAAeuK,C,IAChChL,eACDkS,EAAoBvR,EAAWkH,QAAO,SAAUpH,GAClD,OAAOuR,EAAsBlL,QAAQrG,IAAc,CACrD,IAEiC,IAA7ByR,EAAkBpU,SACpBoU,EAAoBvR,GAItB,IAAIwR,EAAYD,EAAkB3R,QAAO,SAAUC,EAAKC,GAOtD,OANAD,EAAIC,GAAagQ,eAAenO,EAAO,CACrC7B,UAAWA,EACXsP,SAAUA,EACVC,aAAcA,EACdpH,QAASA,IACR/E,iBAAiBpD,IACbD,C,GACN,IACH,OAAOjC,OAAOgE,KAAK4P,GAAWC,MAAK,SAAUC,EAAGC,GAC9C,OAAOH,EAAUE,GAAKF,EAAUG,EAClC,GACF,CClCA,SAASC,8BAA8B9R,GACrC,GAAIoD,iBAAiBpD,KAAeV,KAClC,MAAO,GAGT,IAAIyS,EAAoBlF,qBAAqB7M,GAC7C,MAAO,CAACgN,8BAA8BhN,GAAY+R,EAAmB/E,8BAA8B+E,GACrG,CAEA,SAASC,KAAKpQ,GACZ,IAAIC,EAAQD,EAAKC,MACbpE,EAAUmE,EAAKnE,QACfsE,EAAOH,EAAKG,KAEhB,IAAIF,EAAM2G,cAAczG,GAAMkQ,MAA9B,CAoCA,IAhCA,IAAIC,EAAoBzU,EAAQsS,SAC5BoC,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmB3U,EAAQ4U,QAC3BC,OAAoC,IAArBF,GAAqCA,EACpDG,EAA8B9U,EAAQ+U,mBACtCrK,EAAU1K,EAAQ0K,QAClBmH,EAAW7R,EAAQ6R,SACnBC,EAAe9R,EAAQ8R,aACvBkB,EAAchT,EAAQgT,YACtBgC,EAAwBhV,EAAQ4T,eAChCA,OAA2C,IAA1BoB,GAA0CA,EAC3DlB,EAAwB9T,EAAQ8T,sBAChCmB,EAAqB7Q,EAAMpE,QAAQuC,UACnCyI,EAAgBrF,iBAAiBsP,GAEjCF,EAAqBD,IADH9J,IAAkBiK,GACqCrB,EAA8DS,8BAA8BY,GAA3E,CAAC7F,qBAAqB6F,KAChHxS,EAAa,CAACwS,GAAoBzS,OAAOuS,GAAoB1S,QAAO,SAAUC,EAAKC,GACrF,OAAOD,EAAIE,OAAOmD,iBAAiBpD,KAAeV,KAAO8R,qBAAqBvP,EAAO,CACnF7B,UAAWA,EACXsP,SAAUA,EACVC,aAAcA,EACdpH,QAASA,EACTkJ,eAAgBA,EAChBE,sBAAuBA,IACpBvR,E,GACJ,IACC2S,EAAgB9Q,EAAMuG,MAAMxI,UAC5B0K,EAAazI,EAAMuG,MAAMzI,OACzBiT,EAAY,IAAIC,IAChBC,GAAqB,EACrBC,EAAwB7S,EAAW,GAE9B8S,EAAI,EAAGA,EAAI9S,EAAW7C,OAAQ2V,IAAK,CAC1C,IAAIhT,EAAYE,EAAW8S,GAEvBC,EAAiB7P,iBAAiBpD,GAElCkT,EAAmBpJ,aAAa9J,KAAexB,MAC/C2U,EAAa,CAACjU,IAAKC,QAAQkH,QAAQ4M,IAAmB,EACtDtK,EAAMwK,EAAa,QAAU,SAC7BhF,EAAW6B,eAAenO,EAAO,CACnC7B,UAAWA,EACXsP,SAAUA,EACVC,aAAcA,EACdkB,YAAaA,EACbtI,QAASA,IAEPiL,EAAoBD,EAAaD,EAAmB9T,MAAQC,KAAO6T,EAAmB/T,OAASD,IAE/FyT,EAAchK,GAAO2B,EAAW3B,KAClCyK,EAAoBvG,qBAAqBuG,IAG3C,IAAIC,EAAmBxG,qBAAqBuG,GACxCE,EAAS,GAUb,GARInB,GACFmB,EAAOC,KAAKpF,EAAS8E,IAAmB,GAGtCX,GACFgB,EAAOC,KAAKpF,EAASiF,IAAsB,EAAGjF,EAASkF,IAAqB,GAG1EC,EAAOE,OAAM,SAAUC,GACzB,OAAOA,CACT,IAAI,CACFV,EAAwB/S,EACxB8S,GAAqB,EACrB,KACF,CAEAF,EAAUc,IAAI1T,EAAWsT,EAC3B,CAEA,GAAIR,EAqBF,IAnBA,IAEIa,EAAQ,SAAeC,GACzB,IAAIC,EAAmB3T,EAAW4T,MAAK,SAAU9T,GAC/C,IAAIsT,EAASV,EAAUmB,IAAI/T,GAE3B,GAAIsT,EACF,OAAOA,EAAO5U,MAAM,EAAGkV,GAAIJ,OAAM,SAAUC,GACzC,OAAOA,CACT,GAEJ,IAEA,GAAII,EAEF,OADAd,EAAwBc,EACjB,O,EAIFD,EAnBYvC,EAAiB,EAAI,EAmBZuC,EAAK,GAGpB,UAFFD,EAAMC,GADmBA,KAOpC/R,EAAM7B,YAAc+S,IACtBlR,EAAM2G,cAAczG,GAAMkQ,OAAQ,EAClCpQ,EAAM7B,UAAY+S,EAClBlR,EAAMmS,OAAQ,EA5GhB,CA8GF,CAGA,MAAAC,OAAe,CACblS,KAAM,OACNiB,SAAS,EACTC,MAAO,OACPC,GAAI8O,KACJnI,iBAAkB,CAAC,UACnBkC,KAAM,CACJkG,OAAO,IC7IX,SAASiC,eAAe/F,EAAUU,EAAMsF,GAQtC,YAPyB,IAArBA,IACFA,EAAmB,CACjB/O,EAAG,EACHE,EAAG,IAIA,CACLpG,IAAKiP,EAASjP,IAAM2P,EAAK5J,OAASkP,EAAiB7O,EACnDlG,MAAO+O,EAAS/O,MAAQyP,EAAK9J,MAAQoP,EAAiB/O,EACtDjG,OAAQgP,EAAShP,OAAS0P,EAAK5J,OAASkP,EAAiB7O,EACzDjG,KAAM8O,EAAS9O,KAAOwP,EAAK9J,MAAQoP,EAAiB/O,EAExD,CAEA,SAASgP,sBAAsBjG,GAC7B,MAAO,CAACjP,IAAKE,MAAOD,OAAQE,MAAMgV,MAAK,SAAUC,GAC/C,OAAOnG,EAASmG,IAAS,CAC3B,GACF,CAEA,SAASC,KAAK3S,GACZ,IAAIC,EAAQD,EAAKC,MACbE,EAAOH,EAAKG,KACZ4Q,EAAgB9Q,EAAMuG,MAAMxI,UAC5B0K,EAAazI,EAAMuG,MAAMzI,OACzBwU,EAAmBtS,EAAM2G,cAAcgM,gBACvCC,EAAoBzE,eAAenO,EAAO,CAC5C0O,eAAgB,cAEdmE,EAAoB1E,eAAenO,EAAO,CAC5C4O,aAAa,IAEXkE,EAA2BT,eAAeO,EAAmB9B,GAC7DiC,EAAsBV,eAAeQ,EAAmBpK,EAAY6J,GACpEU,EAAoBT,sBAAsBO,GAC1CG,EAAmBV,sBAAsBQ,GAC7C/S,EAAM2G,cAAczG,GAAQ,CAC1B4S,yBAA0BA,EAC1BC,oBAAqBA,EACrBC,kBAAmBA,EACnBC,iBAAkBA,GAEpBjT,EAAMK,WAAWvC,OAAS7B,OAAOC,OAAO,GAAI8D,EAAMK,WAAWvC,OAAQ,CACnE,+BAAgCkV,EAChC,sBAAuBC,GAE3B,CAGA,MAAAC,OAAe,CACbhT,KAAM,OACNiB,SAAS,EACTC,MAAO,OACP4G,iBAAkB,CAAC,mBACnB3G,GAAIqR,MCxDC,SAASS,wBAAwBhV,EAAWoI,EAAOmB,GACxD,IAAId,EAAgBrF,iBAAiBpD,GACjCiV,EAAiB,CAAC5V,KAAMH,KAAKmH,QAAQoC,IAAkB,GAAI,EAAK,EAEhE7G,EAAyB,mBAAX2H,EAAwBA,EAAOzL,OAAOC,OAAO,GAAIqK,EAAO,CACxEpI,UAAWA,KACPuJ,EACF2L,EAAWtT,EAAK,GAChBuT,EAAWvT,EAAK,GAIpB,OAFAsT,EAAWA,GAAY,EACvBC,GAAYA,GAAY,GAAKF,EACtB,CAAC5V,KAAMD,OAAOiH,QAAQoC,IAAkB,EAAI,CACjDrD,EAAG+P,EACH7P,EAAG4P,GACD,CACF9P,EAAG8P,EACH5P,EAAG6P,EAEP,CAEA,SAAS5L,OAAOjH,GACd,IAAIT,EAAQS,EAAMT,MACdpE,EAAU6E,EAAM7E,QAChBsE,EAAOO,EAAMP,KACbqT,EAAkB3X,EAAQ8L,OAC1BA,OAA6B,IAApB6L,EAA6B,CAAC,EAAG,GAAKA,EAC/CrJ,EAAO7L,WAAWJ,QAAO,SAAUC,EAAKC,GAE1C,OADAD,EAAIC,GAAagV,wBAAwBhV,EAAW6B,EAAMuG,MAAOmB,GAC1DxJ,C,GACN,IACCsV,EAAwBtJ,EAAKlK,EAAM7B,WACnCoF,EAAIiQ,EAAsBjQ,EAC1BE,EAAI+P,EAAsB/P,EAEW,MAArCzD,EAAM2G,cAAcD,gBACtB1G,EAAM2G,cAAcD,cAAcnD,GAAKA,EACvCvD,EAAM2G,cAAcD,cAAcjD,GAAKA,GAGzCzD,EAAM2G,cAAczG,GAAQgK,CAC9B,CAGA,MAAAuJ,SAAe,CACbvT,KAAM,SACNiB,SAAS,EACTC,MAAO,OACPE,SAAU,CAAC,iBACXD,GAAIqG,QClDN,SAAShB,cAAc3G,GACrB,IAAIC,EAAQD,EAAKC,MACbE,EAAOH,EAAKG,KAKhBF,EAAM2G,cAAczG,GAAQ6N,eAAe,CACzChQ,UAAWiC,EAAMuG,MAAMxI,UACvBrC,QAASsE,EAAMuG,MAAMzI,OAErBK,UAAW6B,EAAM7B,WAErB,CAGA,MAAAuV,gBAAe,CACbxT,KAAM,gBACNiB,SAAS,EACTC,MAAO,OACPC,GAAIqF,cACJwD,KAAM,ICvBO,SAASyJ,WAAW9M,GACjC,MAAgB,MAATA,EAAe,IAAM,GAC9B,CCUA,SAAS8L,gBAAgB5S,GACvB,IAAIC,EAAQD,EAAKC,MACbpE,EAAUmE,EAAKnE,QACfsE,EAAOH,EAAKG,KACZmQ,EAAoBzU,EAAQsS,SAC5BoC,OAAsC,IAAtBD,GAAsCA,EACtDE,EAAmB3U,EAAQ4U,QAC3BC,OAAoC,IAArBF,GAAsCA,EACrD9C,EAAW7R,EAAQ6R,SACnBC,EAAe9R,EAAQ8R,aACvBkB,EAAchT,EAAQgT,YACtBtI,EAAU1K,EAAQ0K,QAClBsN,EAAkBhY,EAAQiY,OAC1BA,OAA6B,IAApBD,GAAoCA,EAC7CE,EAAwBlY,EAAQmY,aAChCA,OAAyC,IAA1BD,EAAmC,EAAIA,EACtDxH,EAAW6B,eAAenO,EAAO,CACnCyN,SAAUA,EACVC,aAAcA,EACdpH,QAASA,EACTsI,YAAaA,IAEXhI,EAAgBrF,iBAAiBvB,EAAM7B,WACvCuK,EAAYT,aAAajI,EAAM7B,WAC/B6V,GAAmBtL,EACnBwF,EAAWzI,yBAAyBmB,GACpC4J,EAAUmD,WAAWzF,GACrBxH,EAAgB1G,EAAM2G,cAAcD,cACpCoK,EAAgB9Q,EAAMuG,MAAMxI,UAC5B0K,EAAazI,EAAMuG,MAAMzI,OACzBmW,EAA4C,mBAAjBF,EAA8BA,EAAa9X,OAAOC,OAAO,GAAI8D,EAAMuG,MAAO,CACvGpI,UAAW6B,EAAM7B,aACb4V,EACFG,EAA2D,iBAAtBD,EAAiC,CACxE/F,SAAU+F,EACVzD,QAASyD,GACPhY,OAAOC,OAAO,CAChBgS,SAAU,EACVsC,QAAS,GACRyD,GACCE,EAAsBnU,EAAM2G,cAAce,OAAS1H,EAAM2G,cAAce,OAAO1H,EAAM7B,WAAa,KACjG+L,EAAO,CACT3G,EAAG,EACHE,EAAG,GAGL,GAAKiD,EAAL,CAIA,GAAI4J,EAAe,CACjB,IAAI8D,EAEAC,EAAwB,MAAbnG,EAAmB7Q,IAAMG,KACpC8W,EAAuB,MAAbpG,EAAmB5Q,OAASC,MACtCuJ,EAAmB,MAAboH,EAAmB,SAAW,QACpCxG,EAAShB,EAAcwH,GACvBvM,EAAM+F,EAAS4E,EAAS+H,GACxB5S,EAAMiG,EAAS4E,EAASgI,GACxBC,EAAWV,GAAUpL,EAAW3B,GAAO,EAAI,EAC3C0N,EAAS9L,IAAc/L,MAAQmU,EAAchK,GAAO2B,EAAW3B,GAC/D2N,EAAS/L,IAAc/L,OAAS8L,EAAW3B,IAAQgK,EAAchK,GAGjEL,EAAezG,EAAM3E,SAASyF,MAC9BiG,EAAY8M,GAAUpN,EAAe9C,cAAc8C,GAAgB,CACrEvD,MAAO,EACPE,OAAQ,GAENsR,EAAqB1U,EAAM2G,cAAc,oBAAsB3G,EAAM2G,cAAc,oBAAoBL,QAAUP,qBACjH4O,EAAkBD,EAAmBL,GACrCO,EAAkBF,EAAmBJ,GAMrCO,EAAWnP,OAAO,EAAGoL,EAAchK,GAAMC,EAAUD,IACnDgO,EAAYd,EAAkBlD,EAAchK,GAAO,EAAIyN,EAAWM,EAAWF,EAAkBT,EAA4BhG,SAAWsG,EAASK,EAAWF,EAAkBT,EAA4BhG,SACxM6G,EAAYf,GAAmBlD,EAAchK,GAAO,EAAIyN,EAAWM,EAAWD,EAAkBV,EAA4BhG,SAAWuG,EAASI,EAAWD,EAAkBV,EAA4BhG,SACzM9G,EAAoBpH,EAAM3E,SAASyF,OAAS0E,gBAAgBxF,EAAM3E,SAASyF,OAC3EkU,EAAe5N,EAAiC,MAAb8G,EAAmB9G,EAAkB8F,WAAa,EAAI9F,EAAkB+F,YAAc,EAAI,EAC7H8H,EAAwH,OAAjGb,EAA+C,MAAvBD,OAA8B,EAASA,EAAoBjG,IAAqBkG,EAAwB,EAEvJc,EAAYxN,EAASqN,EAAYE,EACjCE,EAAkBzP,OAAOmO,EAASjO,IAAQjE,EAF9B+F,EAASoN,EAAYG,EAAsBD,GAEKrT,EAAK+F,EAAQmM,EAASlO,IAAQlE,EAAKyT,GAAazT,GAChHiF,EAAcwH,GAAYiH,EAC1BjL,EAAKgE,GAAYiH,EAAkBzN,CACrC,CAEA,GAAI+I,EAAc,CAChB,IAAI2E,EAEAC,EAAyB,MAAbnH,EAAmB7Q,IAAMG,KAErC8X,GAAwB,MAAbpH,EAAmB5Q,OAASC,MAEvCgY,GAAU7O,EAAc8J,GAExBgF,GAAmB,MAAZhF,EAAkB,SAAW,QAEpCiF,GAAOF,GAAUjJ,EAAS+I,GAE1BK,GAAOH,GAAUjJ,EAASgJ,IAE1BK,IAAsD,IAAvC,CAACtY,IAAKG,MAAMgH,QAAQoC,GAEnCgP,GAAyH,OAAjGR,EAAgD,MAAvBjB,OAA8B,EAASA,EAAoB3D,IAAoB4E,EAAyB,EAEzJS,GAAaF,GAAeF,GAAOF,GAAUzE,EAAc0E,IAAQ/M,EAAW+M,IAAQI,GAAuB1B,EAA4B1D,QAEzIsF,GAAaH,GAAeJ,GAAUzE,EAAc0E,IAAQ/M,EAAW+M,IAAQI,GAAuB1B,EAA4B1D,QAAUkF,GAE5IK,GAAmBlC,GAAU8B,GAAe9P,eAAegQ,GAAYN,GAASO,IAAcpQ,OAAOmO,EAASgC,GAAaJ,GAAMF,GAAS1B,EAASiC,GAAaJ,IAEpKhP,EAAc8J,GAAWuF,GACzB7L,EAAKsG,GAAWuF,GAAmBR,EACrC,CAEAvV,EAAM2G,cAAczG,GAAQgK,CAvE5B,CAwEF,CAGA,MAAA8L,kBAAe,CACb9V,KAAM,kBACNiB,SAAS,EACTC,MAAO,OACPC,GAAIsR,gBACJ3K,iBAAkB,CAAC,WC5IN,SAASiO,qBAAqBva,GAC3C,MAAO,CACL2P,WAAY3P,EAAQ2P,WACpBE,UAAW7P,EAAQ6P,UAEvB,CCDe,SAAS2K,cAAc9W,GACpC,OAAIA,IAASD,UAAUC,IAAUM,cAAcN,GAGtC6W,qBAAqB7W,GAFrBgM,gBAAgBhM,EAI3B,CCDA,SAAS+W,gBAAgBza,GACvB,IAAIsR,EAAOtR,EAAQiH,wBACfI,EAASnB,MAAMoL,EAAK9J,OAASxH,EAAQuH,aAAe,EACpDD,EAASpB,MAAMoL,EAAK5J,QAAU1H,EAAQyH,cAAgB,EAC1D,OAAkB,IAAXJ,GAA2B,IAAXC,CACzB,CAIe,SAASoT,iBAAiBC,EAAyBvR,EAAciE,QAC9D,IAAZA,IACFA,GAAU,GAGZ,IAAIuN,EAA0B5W,cAAcoF,GACxCyR,EAAuB7W,cAAcoF,IAAiBqR,gBAAgBrR,GACtEJ,EAAkBD,mBAAmBK,GACrCkI,EAAOrK,sBAAsB0T,EAAyBE,EAAsBxN,GAC5EuB,EAAS,CACXe,WAAY,EACZE,UAAW,GAET5C,EAAU,CACZpF,EAAG,EACHE,EAAG,GAkBL,OAfI6S,IAA4BA,IAA4BvN,MACxB,SAA9B/J,YAAY8F,IAChBsH,eAAe1H,MACb4F,EAAS4L,cAAcpR,IAGrBpF,cAAcoF,KAChB6D,EAAUhG,sBAAsBmC,GAAc,IACtCvB,GAAKuB,EAAaqI,WAC1BxE,EAAQlF,GAAKqB,EAAaoI,WACjBxI,IACTiE,EAAQpF,EAAIkI,oBAAoB/G,KAI7B,CACLnB,EAAGyJ,EAAKxP,KAAO8M,EAAOe,WAAa1C,EAAQpF,EAC3CE,EAAGuJ,EAAK3P,IAAMiN,EAAOiB,UAAY5C,EAAQlF,EACzCP,MAAO8J,EAAK9J,MACZE,OAAQ4J,EAAK5J,OAEjB,CCvDA,SAASoT,MAAMC,GACb,IAAI1Z,EAAM,IAAIiU,IACV0F,EAAU,IAAIC,IACdC,EAAS,GAKb,SAAS9G,EAAK+G,GACZH,EAAQI,IAAID,EAAS3W,MACN,GAAG9B,OAAOyY,EAASvV,UAAY,GAAIuV,EAAS7O,kBAAoB,IACtEvM,SAAQ,SAAUsb,GACzB,IAAKL,EAAQM,IAAID,GAAM,CACrB,IAAIE,EAAcla,EAAImV,IAAI6E,GAEtBE,GACFnH,EAAKmH,EAET,CACF,IACAL,EAAOlF,KAAKmF,EACd,CAQA,OAzBAJ,EAAUhb,SAAQ,SAAUob,GAC1B9Z,EAAI8U,IAAIgF,EAAS3W,KAAM2W,E,IAkBzBJ,EAAUhb,SAAQ,SAAUob,GACrBH,EAAQM,IAAIH,EAAS3W,OAExB4P,EAAK+G,EAET,IACOD,CACT,CAEe,SAASM,eAAeT,GAErC,IAAIU,EAAmBX,MAAMC,GAE7B,OAAO1X,eAAed,QAAO,SAAUC,EAAKkD,GAC1C,OAAOlD,EAAIE,OAAO+Y,EAAiB5R,QAAO,SAAUsR,GAClD,OAAOA,EAASzV,QAAUA,CAC5B,I,GACC,GACL,CC3Ce,SAASgW,SAAS/V,GAC/B,IAAIgW,EACJ,OAAO,WAUL,OATKA,IACHA,EAAU,IAAIC,SAAQ,SAAUC,GAC9BD,QAAQC,UAAUC,MAAK,WACrBH,OAAUI,EACVF,EAAQlW,IACV,GACF,KAGKgW,C,CAEX,CCde,SAASK,YAAYjB,GAClC,IAAIkB,EAASlB,EAAUxY,QAAO,SAAU0Z,EAAQC,GAC9C,IAAIC,EAAWF,EAAOC,EAAQ1X,MAK9B,OAJAyX,EAAOC,EAAQ1X,MAAQ2X,EAAW5b,OAAOC,OAAO,GAAI2b,EAAUD,EAAS,CACrEhc,QAASK,OAAOC,OAAO,GAAI2b,EAASjc,QAASgc,EAAQhc,SACrDsO,KAAMjO,OAAOC,OAAO,GAAI2b,EAAS3N,KAAM0N,EAAQ1N,QAC5C0N,EACED,CACT,GAAG,IAEH,OAAO1b,OAAOgE,KAAK0X,GAAQ5a,KAAI,SAAUqJ,GACvC,OAAOuR,EAAOvR,EAChB,GACF,CCJA,IAAI0R,gBAAkB,CACpB3Z,UAAW,SACXsY,UAAW,GACX7V,SAAU,YAGZ,SAASmX,mBACP,IAAK,IAAIvC,EAAOwC,UAAUxc,OAAQyc,EAAO,IAAI/V,MAAMsT,GAAO0C,EAAO,EAAGA,EAAO1C,EAAM0C,IAC/ED,EAAKC,GAAQF,UAAUE,GAGzB,OAAQD,EAAKzF,MAAK,SAAU9W,GAC1B,QAASA,GAAoD,mBAAlCA,EAAQiH,sBACrC,GACF,CAEO,SAASwV,gBAAgBC,QACL,IAArBA,IACFA,EAAmB,IAGrB,IAAIC,EAAoBD,EACpBE,EAAwBD,EAAkBE,iBAC1CA,OAA6C,IAA1BD,EAAmC,GAAKA,EAC3DE,EAAyBH,EAAkBI,eAC3CA,OAA4C,IAA3BD,EAAoCV,gBAAkBU,EAC3E,OAAO,SAAsBza,EAAWD,EAAQlC,QAC9B,IAAZA,IACFA,EAAU6c,GAGZ,IAAIzY,EAAQ,CACV7B,UAAW,SACXgZ,iBAAkB,GAClBvb,QAASK,OAAOC,OAAO,GAAI4b,gBAAiBW,GAC5C9R,cAAe,GACftL,SAAU,CACR0C,UAAWA,EACXD,OAAQA,GAEVuC,WAAY,GACZD,OAAQ,IAENsY,EAAmB,GACnBC,GAAc,EACdvO,EAAW,CACbpK,MAAOA,EACP4Y,WAAY,SAAoBC,GAC9B,IAAIjd,EAAsC,mBAArBid,EAAkCA,EAAiB7Y,EAAMpE,SAAWid,EACzFC,IACA9Y,EAAMpE,QAAUK,OAAOC,OAAO,GAAIuc,EAAgBzY,EAAMpE,QAASA,GACjEoE,EAAMyK,cAAgB,CACpB1M,UAAWyB,YAAUzB,GAAa2O,kBAAkB3O,GAAaA,EAAUiR,eAAiBtC,kBAAkB3O,EAAUiR,gBAAkB,GAC1IlR,OAAQ4O,kBAAkB5O,IAI5B,IAAIqZ,EAAmBD,eAAeQ,YAAY,GAAGtZ,OAAOma,EAAkBvY,EAAMpE,QAAQ6a,aAM5F,OAJAzW,EAAMmX,iBAAmBA,EAAiB5R,QAAO,SAAUwT,GACzD,OAAOA,EAAE5X,OACX,IA+FFnB,EAAMmX,iBAAiB1b,SAAQ,SAAUsE,GACvC,IAAIG,EAAOH,EAAKG,KACZ8Y,EAAejZ,EAAKnE,QACpBA,OAA2B,IAAjBod,EAA0B,GAAKA,EACzCxY,EAAST,EAAKS,OAElB,GAAsB,mBAAXA,EAAuB,CAChC,IAAIyY,EAAYzY,EAAO,CACrBR,MAAOA,EACPE,KAAMA,EACNkK,SAAUA,EACVxO,QAASA,IAKX8c,EAAiBhH,KAAKuH,GAFT,WAAkB,EAGjC,CACF,IA/GS7O,EAASQ,Q,EAOlBsO,YAAa,WACX,IAAIP,EAAJ,CAIA,IAAIQ,EAAkBnZ,EAAM3E,SACxB0C,EAAYob,EAAgBpb,UAC5BD,EAASqb,EAAgBrb,OAG7B,GAAKia,iBAAiBha,EAAWD,GAAjC,CAKAkC,EAAMuG,MAAQ,CACZxI,UAAWqY,iBAAiBrY,EAAWyH,gBAAgB1H,GAAoC,UAA3BkC,EAAMpE,QAAQgF,UAC9E9C,OAAQ6F,cAAc7F,IAOxBkC,EAAMmS,OAAQ,EACdnS,EAAM7B,UAAY6B,EAAMpE,QAAQuC,UAKhC6B,EAAMmX,iBAAiB1b,SAAQ,SAAUob,GACvC,OAAO7W,EAAM2G,cAAckQ,EAAS3W,MAAQjE,OAAOC,OAAO,GAAI2a,EAAS3M,KACzE,IAEA,IAAK,IAAIkP,EAAQ,EAAGA,EAAQpZ,EAAMmX,iBAAiB3b,OAAQ4d,IACzD,IAAoB,IAAhBpZ,EAAMmS,MAAV,CAMA,IAAIkH,EAAwBrZ,EAAMmX,iBAAiBiC,GAC/C/X,EAAKgY,EAAsBhY,GAC3BiY,EAAyBD,EAAsBzd,QAC/CwS,OAAsC,IAA3BkL,EAAoC,GAAKA,EACpDpZ,EAAOmZ,EAAsBnZ,KAEf,mBAAPmB,IACTrB,EAAQqB,EAAG,CACTrB,MAAOA,EACPpE,QAASwS,EACTlO,KAAMA,EACNkK,SAAUA,KACNpK,EAdR,MAHEA,EAAMmS,OAAQ,EACdiH,GAAQ,CAzBZ,CATA,C,EAwDFxO,OAAQwM,UAAS,WACf,OAAO,IAAIE,SAAQ,SAAUC,GAC3BnN,EAAS8O,cACT3B,EAAQvX,EACV,GACF,IACAuZ,QAAS,WACPT,IACAH,GAAc,CAChB,GAGF,IAAKZ,iBAAiBha,EAAWD,GAC/B,OAAOsM,EAmCT,SAAS0O,IACPJ,EAAiBjd,SAAQ,SAAU4F,GACjC,OAAOA,GACT,IACAqX,EAAmB,EACrB,CAEA,OAvCAtO,EAASwO,WAAWhd,GAAS4b,MAAK,SAAUxX,IACrC2Y,GAAe/c,EAAQ4d,eAC1B5d,EAAQ4d,cAAcxZ,E,IAqCnBoK,C,CAEX,CACO,IAAIqP,eAA4BtB,kBC/LnCI,mBAAmB,CAACzN,eAAgBpE,gBAAekD,gBAAe9J,eAClE2Z,eAA4BtB,gBAAgB,CAC9CI,iBAAkBA,qBCGhBA,iBAAmB,CAACzN,eAAgBpE,gBAAekD,gBAAe9J,cAAa4H,SAAQyI,OAAMwC,kBAAiB7R,QAAO4R,QACrH+G,aAA4BtB,gBAAgB,CAC9CI,iBAAkBA,mB,s4BCDdmB,WAAa,IAAI1I,IAEvB2I,KAAe,CACb9H,IAAInW,EAAS0K,EAAKgE,GACXsP,WAAW1C,IAAItb,IAClBge,WAAW7H,IAAInW,EAAS,IAAIsV,KAG9B,MAAM4I,EAAcF,WAAWxH,IAAIxW,GAI9Bke,EAAY5C,IAAI5Q,IAA6B,IAArBwT,EAAYC,KAMzCD,EAAY/H,IAAIzL,EAAKgE,GAJnB0P,QAAQ1d,MAAO,+EAA8E8F,MAAM6X,KAAKH,EAAY3Z,QAAQ,MAK/H,EAEDiS,IAAGA,CAACxW,EAAS0K,IACPsT,WAAW1C,IAAItb,IACVge,WAAWxH,IAAIxW,GAASwW,IAAI9L,IAG9B,KAGT4T,OAAOte,EAAS0K,GACd,IAAKsT,WAAW1C,IAAItb,GAClB,OAGF,MAAMke,EAAcF,WAAWxH,IAAIxW,GAEnCke,EAAYK,OAAO7T,GAGM,IAArBwT,EAAYC,MACdH,WAAWO,OAAOve,EAEtB,G;;;;;00pEChDF,IAAIwe,oBAAsB,GAAGrd,MAAMC,KAAKxB,SAASC,iBAAiB,gCAClE2e,oBAAoBnd,KAAI,SAAUod,GACjC,IAAIve,EAAU,CACb6R,SAAiE,aAAvD0M,EAAkBre,aAAa,oBAAqCR,SAASwM,cAAc,QAAU,mBAEhH,OAAO,IAAIsS,SAASD,EAAmBve,EACxC,ICTA,IAAIye,mBAAqB,GAAGxd,MAAMC,KAAKxB,SAASC,iBAAiB,+BACjE8e,mBAAmBtd,KAAI,SAAUud,GAChC,IAAI1e,EAAU,CACb2e,MAAO,CAACC,KAAM,GAAI9H,KAAM,IACxB/G,KAAwD,SAAlD2O,EAAiBxe,aAAa,kBAA8B,EAClEqC,UAAWmc,EAAiBxe,aAAa,sBAAwB,QAElE,OAAO,IAAI2e,QAAQH,EAAkB1e,EACtC,ICLA,IAAI8e,mBAAqB,GAAG7d,MAAMC,KAAKxB,SAASC,iBAAiB,+BACjEmf,mBAAmB3d,KAAI,SAAU4d,GAChC,IAAI/e,EAAU,CACb2e,MAAO,CAACC,KAAM,GAAI9H,KAAM,IACxB/G,KAAwD,SAAlDgP,EAAiB7e,aAAa,kBAA8B,EAClEqC,UAAWwc,EAAiB7e,aAAa,sBAAwB,QAElE,OAAO,IAAI8e,QAAQD,EAAkB/e,EACtC,ICVA,IAAIif,oBAAsB,GAAGhe,MAAMC,KAAKxB,SAASC,iBAAiB,mCAClEsf,oBAAoB9d,KAAI,SAAU+d,GACjCA,EAAgBnQ,iBAAiB,SAAUoQ,IAC1CA,EAAEC,kBAEFF,EAAgBG,UAAUC,OAAO,SAAS,GAE5C,ICLA,IAAIC,kBAAoB,GAAGte,MAAMC,KAAKxB,SAASC,iBAAiB,6BAChE4f,kBAAkBpe,KAAI,SAAUqe,GAC/B,IAAKA,EAAeC,aAAa,kBAChC,OAGD,MAAMC,EAAU,IAAIC,MAAMH,EAAetf,aAAa,mBAEtDsf,EAAezQ,iBAAiB,SAAS,KACxC2Q,EAAQd,MAAM,GAEhB,IChBO,MAAMgB,OAAS,QAETC,UAAYA,CAACC,EAAKC,KAC7B,MAAM/E,EAAS,4CAA4CgF,KAAKF,GAEhE,OAAO9E,EAAS,QAAQta,SAASsa,EAAO,GAAI,QAAQta,SAASsa,EAAO,GAAI,QAAQta,SAASsa,EAAO,GAAI,QAAQ+E,KAAa,IAAI,EAGlHE,SAAWA,CAACC,EAAOH,EAAU,KACxC,MAAMI,EAAIzX,iBAAiBhJ,SAAS0Q,MAAMgQ,iBAAiB,KAAKR,SAASM,KAASG,OAElF,OAAgB,IAAZN,EACGF,UAAUM,EAAGJ,GAGbI,CAAC,E", "ignoreList": []}