-- Migration для добавления системы редакторов
-- Дата: 2025-01-XX

-- 1. Добавляем роль редактора в пользователей
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS editor_specialization text;

-- 2. Создаем таблицу для управления передачами статей редакторам
CREATE TABLE IF NOT EXISTS public.editor_assignments (
    id serial primary key,
    submission_id integer NOT NULL REFERENCES public.submissions(id) ON DELETE CASCADE,
    editor_id integer NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    assigned_by integer NOT NULL REFERENCES public.users(id), -- кто назначил (админ)
    assigned_at bigint NOT NULL, -- когда назначено
    status text DEFAULT 'pending', -- pending, reviewed, rejected
    editor_comment text, -- ко<PERSON><PERSON><PERSON><PERSON>та<PERSON>ий редактора
    editor_file text, -- файл от редактора
    reviewed_at bigint, -- когда проверено
    created_at bigint DEFAULT EXTRACT(EPOCH FROM NOW()),
    updated_at bigint DEFAULT EXTRACT(EPOCH FROM NOW())
);

-- 3. Добавляем индексы для производительности
CREATE INDEX IF NOT EXISTS idx_editor_assignments_submission_id ON public.editor_assignments(submission_id);
CREATE INDEX IF NOT EXISTS idx_editor_assignments_editor_id ON public.editor_assignments(editor_id);
CREATE INDEX IF NOT EXISTS idx_editor_assignments_status ON public.editor_assignments(status);

-- 4. Добавляем поле для отслеживания статуса редакторской проверки в submissions
ALTER TABLE public.submissions ADD COLUMN IF NOT EXISTS editor_review_status text DEFAULT 'not_assigned'; 
-- not_assigned, assigned, in_review, reviewed, approved, rejected

-- 5. Создаем таблицу для уведомлений редакторов
CREATE TABLE IF NOT EXISTS public.editor_notifications (
    id serial primary key,
    editor_id integer NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    assignment_id integer NOT NULL REFERENCES public.editor_assignments(id) ON DELETE CASCADE,
    message text NOT NULL,
    is_read boolean DEFAULT false,
    created_at bigint DEFAULT EXTRACT(EPOCH FROM NOW())
);

-- 6. Добавляем индекс для уведомлений
CREATE INDEX IF NOT EXISTS idx_editor_notifications_editor_id ON public.editor_notifications(editor_id);
CREATE INDEX IF NOT EXISTS idx_editor_notifications_is_read ON public.editor_notifications(is_read);

-- 7. Вставляем тестовых редакторов (опционально)
INSERT INTO public.users (name, second_name, email, rolename, editor_specialization, created_at, register_time)
VALUES 
    ('Иван', 'Петров', '<EMAIL>', 'editor', 'Математика', EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),
    ('Мария', 'Сидорова', '<EMAIL>', 'editor', 'Физика', EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW()))
ON CONFLICT (email) DO NOTHING;

-- 8. Комментарии к таблицам
COMMENT ON TABLE public.editor_assignments IS 'Назначения статей редакторам для проверки';
COMMENT ON COLUMN public.editor_assignments.status IS 'Статус проверки: pending, reviewed, rejected';
COMMENT ON COLUMN public.editor_assignments.editor_comment IS 'Комментарий редактора по статье';
COMMENT ON COLUMN public.editor_assignments.editor_file IS 'Путь к файлу, прикрепленному редактором';

COMMENT ON TABLE public.editor_notifications IS 'Уведомления для редакторов о новых назначениях';
COMMENT ON COLUMN public.submissions.editor_review_status IS 'Статус редакторской проверки: not_assigned, assigned, in_review, reviewed, approved, rejected';
