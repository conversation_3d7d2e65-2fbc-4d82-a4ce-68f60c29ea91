{"version": 3, "file": "optgroup_columns.js", "sources": ["../../../src/constants.ts", "../../../src/vanilla.ts", "../../../src/plugins/optgroup_columns/plugin.ts"], "sourcesContent": ["export const KEY_A\t\t\t\t= 65;\nexport const KEY_RETURN\t\t\t= 13;\nexport const KEY_ESC\t\t\t= 27;\nexport const KEY_LEFT\t\t\t= 37;\nexport const KEY_UP\t\t\t\t= 38;\nexport const KEY_RIGHT\t\t\t= 39;\nexport const KEY_DOWN\t\t\t= 40;\nexport const KEY_BACKSPACE\t\t= 8;\nexport const KEY_DELETE\t\t\t= 46;\nexport const KEY_TAB\t\t\t= 9;\n\nexport const IS_MAC      \t\t= typeof navigator === 'undefined' ? false : /Mac/.test(navigator.userAgent);\nexport const KEY_SHORTCUT\t\t= IS_MAC ? 'metaKey' : 'ctrlKey'; // ctrl key or apple key for ma\n", "\nimport { iterate } from './utils.ts';\n\n/**\n * Return a dom element from either a dom query string, jQuery object, a dom element or html string\n * https://stackoverflow.com/questions/494143/creating-a-new-dom-element-from-an-html-string-using-built-in-dom-methods-or-pro/35385518#35385518\n *\n * param query should be {}\n */\nexport const getDom = ( query:any ):HTMLElement => {\n\n\tif( query.jquery ){\n\t\treturn query[0];\n\t}\n\n\tif( query instanceof HTMLElement ){\n\t\treturn query;\n\t}\n\n\tif( isHtmlString(query) ){\n\t\tvar tpl = document.createElement('template');\n\t\ttpl.innerHTML = query.trim(); // Never return a text node of whitespace as the result\n\t\treturn tpl.content.firstChild as HTMLElement;\n\t}\n\n\treturn document.querySelector(query);\n};\n\nexport const isHtmlString = (arg:any): boolean => {\n\tif( typeof arg === 'string' && arg.indexOf('<') > -1 ){\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nexport const escapeQuery = (query:string):string => {\n\treturn query.replace(/['\"\\\\]/g, '\\\\$&');\n}\n\n/**\n * Dispatch an event\n *\n */\nexport const triggerEvent = ( dom_el:HTMLElement, event_name:string ):void => {\n\tvar event = document.createEvent('HTMLEvents');\n\tevent.initEvent(event_name, true, false);\n\tdom_el.dispatchEvent(event)\n};\n\n/**\n * Apply CSS rules to a dom element\n *\n */\nexport const applyCSS = ( dom_el:HTMLElement, css:{ [key: string]: string|number }):void => {\n\tObject.assign(dom_el.style, css);\n}\n\n\n/**\n * Add css classes\n *\n */\nexport const addClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n\tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map( cls => {\n\t\t\tel.classList.add( cls );\n\t\t});\n\t});\n}\n\n/**\n * Remove css classes\n *\n */\n export const removeClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n \tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map(cls => {\n\t \t\tel.classList.remove( cls );\n\t\t});\n \t});\n }\n\n\n/**\n * Return arguments\n *\n */\nexport const classesArray = (args:string[]|string[][]):string[] => {\n\tvar classes:string[] = [];\n\titerate( args, (_classes) =>{\n\t\tif( typeof _classes === 'string' ){\n\t\t\t_classes = _classes.trim().split(/[\\t\\n\\f\\r\\s]/);\n\t\t}\n\t\tif( Array.isArray(_classes) ){\n\t\t\tclasses = classes.concat(_classes);\n\t\t}\n\t});\n\n\treturn classes.filter(Boolean);\n}\n\n\n/**\n * Create an array from arg if it's not already an array\n *\n */\nexport const castAsArray = (arg:any):Array<any> => {\n\tif( !Array.isArray(arg) ){\n \t\targ = [arg];\n \t}\n\treturn arg;\n}\n\n\n/**\n * Get the closest node to the evt.target matching the selector\n * Stops at wrapper\n *\n */\nexport const parentMatch = ( target:null|HTMLElement, selector:string, wrapper?:HTMLElement ):HTMLElement|void => {\n\n\tif( wrapper && !wrapper.contains(target) ){\n\t\treturn;\n\t}\n\n\twhile( target && target.matches ){\n\n\t\tif( target.matches(selector) ){\n\t\t\treturn target;\n\t\t}\n\n\t\ttarget = target.parentNode as HTMLElement;\n\t}\n}\n\n\n/**\n * Get the first or last item from an array\n *\n * > 0 - right (last)\n * <= 0 - left (first)\n *\n */\nexport const getTail = ( list:Array<any>|NodeList, direction:number=0 ):any => {\n\n\tif( direction > 0 ){\n\t\treturn list[list.length-1];\n\t}\n\n\treturn list[0];\n}\n\n/**\n * Return true if an object is empty\n *\n */\nexport const isEmptyObject = (obj:object):boolean => {\n\treturn (Object.keys(obj).length === 0);\n}\n\n\n/**\n * Get the index of an element amongst sibling nodes of the same type\n *\n */\nexport const nodeIndex = ( el:null|Element, amongst?:string ):number => {\n\tif (!el) return -1;\n\n\tamongst = amongst || el.nodeName;\n\n\tvar i = 0;\n\twhile( el = el.previousElementSibling ){\n\n\t\tif( el.matches(amongst) ){\n\t\t\ti++;\n\t\t}\n\t}\n\treturn i;\n}\n\n\n/**\n * Set attributes of an element\n *\n */\nexport const setAttr = (el:Element,attrs:{ [key: string]: null|string|number }) => {\n\titerate( attrs,(val,attr) => {\n\t\tif( val == null ){\n\t\t\tel.removeAttribute(attr as string);\n\t\t}else{\n\t\t\tel.setAttribute(attr as string, ''+val);\n\t\t}\n\t});\n}\n\n\n/**\n * Replace a node\n */\nexport const replaceNode = ( existing:Node, replacement:Node ) => {\n\tif( existing.parentNode ) existing.parentNode.replaceChild(replacement, existing);\n}\n", "/**\n * Plugin: \"optgroup_columns\" (Tom Select.js)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport type TomSelect from '../../tom-select.ts';\nimport * as constants from '../../constants.ts';\nimport { parentMatch, nodeIndex } from '../../vanilla.ts';\n\nexport default function(this:TomSelect) {\n\tvar self = this;\n\n\tvar orig_keydown = self.onKeyDown;\n\n\tself.hook('instead','onKeyDown',(evt:KeyboardEvent)=>{\n\t\tvar index, option, options, optgroup;\n\n\t\tif( !self.isOpen || !(evt.keyCode === constants.KEY_LEFT || evt.keyCode === constants.KEY_RIGHT)) {\n\t\t\treturn orig_keydown.call(self,evt);\n\t\t}\n\n\t\tself.ignoreHover\t= true;\n\t\toptgroup\t\t\t= parentMatch(self.activeOption,'[data-group]');\n\t\tindex\t\t\t\t= nodeIndex(self.activeOption,'[data-selectable]');\n\n\t\tif( !optgroup ){\n\t\t\treturn;\n\t\t}\n\n\t\tif( evt.keyCode === constants.KEY_LEFT ){\n\t\t\toptgroup = optgroup.previousSibling;\n\t\t} else {\n\t\t\toptgroup = optgroup.nextSibling;\n\t\t}\n\n\t\tif( !optgroup ){\n\t\t\treturn;\n\t\t}\n\n\t\toptions\t\t\t\t= (<HTMLOptGroupElement>optgroup).querySelectorAll('[data-selectable]');\n\t\toption\t\t\t\t= options[ Math.min(options.length - 1, index) ] as HTMLElement;\n\n\t\tif( option ){\n\t\t\tself.setActiveOption(option);\n\t\t}\n\n\t});\n\n};\n"], "names": ["KEY_LEFT", "KEY_RIGHT", "parentMatch", "target", "selector", "wrapper", "matches", "parentNode", "nodeIndex", "el", "amongst", "nodeName", "i", "previousElementSibling", "self", "orig_keydown", "onKeyDown", "hook", "evt", "index", "option", "options", "optgroup", "isOpen", "keyCode", "constants", "call", "ignoreHover", "activeOption", "previousSibling", "nextS<PERSON>ling", "querySelectorAll", "Math", "min", "length", "setActiveOption"], "mappings": ";;;;;;;;;;;CAGO,MAAMA,QAAQ,GAAK,EAAE;CAErB,MAAMC,SAAS,GAAK,EAAE;CAO+B;;CC8G5D;CACA;CACA;CACA;CACA;CACO,MAAMC,WAAW,GAAGA,CAAEC,MAAuB,EAAEC,QAAe,EAAEC,OAAoB,KAAuB;CAMjH,EAAA,OAAOF,MAAM,IAAIA,MAAM,CAACG,OAAO,EAAE;CAEhC,IAAA,IAAIH,MAAM,CAACG,OAAO,CAACF,QAAQ,CAAC,EAAE;CAC7B,MAAA,OAAOD,MAAM;CACd;KAEAA,MAAM,GAAGA,MAAM,CAACI,UAAyB;CAC1C;CACD,CAAC;;CA4BD;CACA;CACA;CACA;CACO,MAAMC,SAAS,GAAGA,CAAEC,EAAe,EAAEC,OAAe,KAAa;CACvE,EAAA,IAAI,CAACD,EAAE,EAAE,OAAO,CAAC,CAAC;CAElBC,EAAAA,OAAO,GAAGA,OAAO,IAAID,EAAE,CAACE,QAAQ;GAEhC,IAAIC,CAAC,GAAG,CAAC;CACT,EAAA,OAAOH,EAAE,GAAGA,EAAE,CAACI,sBAAsB,EAAE;CAEtC,IAAA,IAAIJ,EAAE,CAACH,OAAO,CAACI,OAAO,CAAC,EAAE;CACxBE,MAAAA,CAAC,EAAE;CACJ;CACD;CACA,EAAA,OAAOA,CAAC;CACT,CAAC;;CC1LD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAMe,eAAyB,IAAA;GACvC,IAAIE,IAAI,GAAG,IAAI;CAEf,EAAA,IAAIC,YAAY,GAAGD,IAAI,CAACE,SAAS;GAEjCF,IAAI,CAACG,IAAI,CAAC,SAAS,EAAC,WAAW,EAAEC,GAAiB,IAAG;CACpD,IAAA,IAAIC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,QAAQ;KAEpC,IAAI,CAACR,IAAI,CAACS,MAAM,IAAI,EAAEL,GAAG,CAACM,OAAO,KAAKC,QAAkB,IAAIP,GAAG,CAACM,OAAO,KAAKC,SAAmB,CAAC,EAAE;CACjG,MAAA,OAAOV,YAAY,CAACW,IAAI,CAACZ,IAAI,EAACI,GAAG,CAAC;CACnC;KAEAJ,IAAI,CAACa,WAAW,GAAG,IAAI;KACvBL,QAAQ,GAAKpB,WAAW,CAACY,IAAI,CAACc,YAAY,EAAC,cAAc,CAAC;KAC1DT,KAAK,GAAMX,SAAS,CAACM,IAAI,CAACc,YAAY,EAAC,mBAAmB,CAAC;KAE3D,IAAI,CAACN,QAAQ,EAAE;CACd,MAAA;CACD;CAEA,IAAA,IAAIJ,GAAG,CAACM,OAAO,KAAKC,QAAkB,EAAE;OACvCH,QAAQ,GAAGA,QAAQ,CAACO,eAAe;CACpC,KAAC,MAAM;OACNP,QAAQ,GAAGA,QAAQ,CAACQ,WAAW;CAChC;KAEA,IAAI,CAACR,QAAQ,EAAE;CACd,MAAA;CACD;CAEAD,IAAAA,OAAO,GAA4BC,QAAQ,CAAES,gBAAgB,CAAC,mBAAmB,CAAC;CAClFX,IAAAA,MAAM,GAAMC,OAAO,CAAEW,IAAI,CAACC,GAAG,CAACZ,OAAO,CAACa,MAAM,GAAG,CAAC,EAAEf,KAAK,CAAC,CAAiB;CAEzE,IAAA,IAAIC,MAAM,EAAE;CACXN,MAAAA,IAAI,CAACqB,eAAe,CAACf,MAAM,CAAC;CAC7B;CAED,GAAC,CAAC;CAEH;;;;;;;;"}