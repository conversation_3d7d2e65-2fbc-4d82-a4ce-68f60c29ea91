{%  extends 'basic.html' %}


{% block content %}

<div class="page-header d-print-none">
  <div class="row align-items-center">
    <div class="col">
      <h2 class="page-title">Авторы</h2>
    </div>
    <div class="col-auto ms-auto">
      <div class="btn-list">
        <a href="#" class="btn btn-primary d-none d-sm-inline-block" data-bs-toggle="modal" data-bs-target="#modal-new-user">
          <i class="ti ti-plus"></i>
          Добавить автора
        </a>
      </div>
    </div>
  </div>
</div>

<form method="get" action="{{ url_for('authors') }}">
<div class="row mb-3">
  <div class="col-md-4">
    <div class="form-label">Имя</div>
    <input type="text" class="form-control" placeholder="Введите имя..." name="name" value="{{ search_name|default('') }}">
  </div>
  <div class="col-md-4">
    <div class="form-label">Автор (ORCID)</div>
    <input type="text" class="form-control" placeholder="Введите ORCID..." name="orcid" value="{{ search_orcid|default('') }}">
  </div>
  <div class="col-md-4">
    <div class="form-label">Наличие статей</div>
    <select class="form-select" name="has_articles">
      <option value="" {% if not has_articles %}selected{% endif %}>Все</option>
      <option value="true" {% if has_articles=='true' %}selected{% endif %}>Есть статьи</option>
      <option value="false" {% if has_articles=='false' %}selected{% endif %}>Нет статей</option>
    </select>
  </div>
  <div class="col-12 mt-3">
    <div class="d-flex justify-content-end">
      <button class="btn btn-ghost-danger me-2" type="button" onclick="window.location='{{ url_for('authors') }}';">
        <i class="ti ti-x pe-2"></i>
        Очистить
      </button>
      <button class="btn btn-primary" type="submit">
        <i class="ti ti-search pe-2"></i>
        Поиск
      </button>
    </div>
  </div>
</div>
</form>



<div class="card mt-4">
  <div class="table-responsive">
    <table class="table card-table table-hover">
      <thead>
        <tr>
          <th>ORCID</th>
          <th>Имя автора</th>
          <th>Связанный пользователь</th>
          <th class="text-center">Как Автор</th>
          <th class="text-center">Как Соавтор</th>
          <th class="w-1"></th>
        </tr>
      </thead>
      <tbody>
      {% for author in authors %}
      <tr>
        <td class="align-middle">{{ author.orcid }}</td>
        <td class="align-middle">{{ author.name }}</td>
        <td class="align-middle">
          {% if author.user_id and users_map[author.user_id] %}
            {{ users_map[author.user_id].name }} {{ users_map[author.user_id].second_name }} ({{ users_map[author.user_id].email }})
          {% else %}
            —
          {% endif %}
        </td>
        <td class="align-middle text-center">{{ author_stats[author.id].as_main }}</td>
        <td class="align-middle text-center">{{ author_stats[author.id].as_co }}</td>
        <td class="align-middle">
            <a href="{{ url_for('author_edit', author_id=author.id) }}" class="btn btn-icon btn-outline-primary bg-primary-lt btn-sm px-2 py-1">
              Редактировать
            </a>
        </td>
      </tr>
      {% endfor %}
      </tbody>
    </table>
  </div>
  <div class="card-footer d-flex align-items-center">
    <p class="m-0 text-secondary">Показано <span>{{ authors|length }}</span> из <span>{{ total_authors }}</span> записей</p>
    <ul class="pagination m-0 ms-auto">
      <li class="page-item {% if page <= 1 %}disabled{% endif %}">
        <a class="page-link" href="{{ url_for('authors', page=page-1, name=search_name, orcid=search_orcid, has_articles=has_articles) }}" tabindex="-1" aria-disabled="true">
          <i class="ti ti-chevron-left"></i>
        </a>
      </li>
      {% for p in range(1, total_pages+1) %}
        <li class="page-item {% if p == page %}active{% endif %}"><a class="page-link" href="{{ url_for('authors', page=p, name=search_name, orcid=search_orcid, has_articles=has_articles) }}">{{ p }}</a></li>
      {% endfor %}
      <li class="page-item {% if page >= total_pages %}disabled{% endif %}">
        <a class="page-link" href="{{ url_for('authors', page=page+1, name=search_name, orcid=search_orcid, has_articles=has_articles) }}">
          <i class="ti ti-chevron-right"></i>
        </a>
      </li>
    </ul>
  </div>
</div>




{% endblock %}

{% block scripts %}

{% endblock %}