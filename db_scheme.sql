CREATE TABLE public.author_profile (
    id serial primary key,
    user_id integer,
    name text,
    organization text,
    email text,
    "position" text,
    address_street text,
    address_country text,
    address_city text,
    address_zip text,
    phone text,
    orcid text,
    created_at integer,
    department text,
    updated_at integer);


CREATE TABLE public.editorial_board (
    id serial primary key,
    title text,
    full_name text NOT NULL,
    organization text,
    biography text,
    order_group integer,
    image text);


CREATE TABLE public.files (
    id serial primary key,
    name text,
    filepath text,
    upload_time bigint,
    comment text,
    filesize bigint,
    created_at integer);


CREATE TABLE public.fix_classifications (
    id serial primary key,
    name text NOT NULL,
    name_uz text NOT NULL,
    name_ru text NOT NULL);


CREATE TABLE public.fix_country (
    id serial primary key,
    name text,
    name_uz text,
    name_ru text,
    created_at integer);


CREATE TABLE public.issues (
    id serial primary key,
    title text,
    title_uz text,
    title_ru text,
    vol_no text,
    issue_no text,
    year integer,
    category text,
    shortinfo text,
    shortinfo_uz text,
    shortinfo_ru text,
    price double precision,
    price_uz double precision,
    price_ru double precision,
    subscription_enable boolean DEFAULT false,
    is_paid boolean DEFAULT false,
    cover_image text,
    created_at integer);


CREATE TABLE public.pages (
    id serial primary key,
    alias text,
    title text,
    title_uz text,
    title_ru text,
    content text,
    content_uz text,
    content_ru text,
    last_update bigint,
    created_at integer);


CREATE TABLE public.payments (
    id serial primary key,
    user_id integer,
    status text DEFAULT 'unpaid'::text,
    currency text DEFAULT 'usd'::text,
    payment_type text,
    payment_date bigint,
    amount double precision,
    ids integer[],
    proof text,
    note text,
    created_at integer);


CREATE TABLE public.publication_citations (
    id serial primary key,
    publication_id integer,
    title text,
    authors text,
    doi text,
    doi_link text,
    wos_link text,
    scopus_link text,
    gscholar_link text,
    created_at integer);


CREATE TABLE public.publication_figures (
    id serial primary key,
    publication_id integer,
    title text,
    filepath text,
    order_id integer,
    created_at integer);


CREATE TABLE public.publication_parts (
    id serial primary key,
    publication_id integer,
    title text,
    content text,
    order_id integer,
    created_at integer);


CREATE TABLE public.publication_refs (
    id serial primary key,
    publication_id integer,
    title text,
    authors text,
    doi text,
    doi_link text,
    wos_link text,
    scopus_link text,
    gscholar_link text,
    created_at integer);


CREATE TABLE public.publications (
    id serial primary key,
    title text,
    title_uz text,
    title_ru text,
    main_author_id integer,
    subauthor_ids integer[],
    issue_id integer,
    abstract text,
    abstract_uz text,
    abstract_ru text,
    doi text,
    doi_link text,
    keywords text[],
    additional text,
    stat_views integer,
    stat_alt integer,
    stat_crossref integer,
    stat_wos integer,
    stat_scopus integer,
    date_sent bigint,
    date_accept bigint,
    date_publish bigint,
    stage text,
    comments text,
    file_ids integer[],
    is_paid boolean DEFAULT false,
    price double precision,
    price_uz double precision,
    price_ru double precision,
    subscription_enable boolean DEFAULT false,
    created_at integer);


CREATE TABLE public.settings (
    id serial primary key,
    k text,
    v text,
    created_at integer);


CREATE TABLE public.submissions (
    id serial primary key,
    user_id integer,
    status text DEFAULT 'draft'::text,
    title text,
    abstract text,
    is_special boolean,
    is_dataset boolean,
    check_copyright boolean,
    keywords text[],
    classifications text[],
    check_ethical boolean,
    check_consent boolean,
    check_acknowledgements boolean,
    is_used_previous boolean,
    word_count integer,
    is_corresponding_author boolean,
    main_author_id integer,
    sub_author_ids integer[],
    is_competing_interests boolean,
    notes text,
    file_authors text,
    file_anonymized text,
    created_date bigint,
    updated_at integer);


CREATE TABLE public.translations (
    id serial primary key,
    alias text,
    content text,
    content_uz text,
    content_ru text,
    created_at integer);


CREATE TABLE public.users (
    id serial primary key,
    name text,
    second_name text,
    father_name text,
    email text,
    password text,
    country_id integer,
    region text,
    rolename text DEFAULT 'user'::text,
    is_blocked boolean DEFAULT false,
    is_notify boolean DEFAULT false,
    accept_rules_time bigint,
    last_online bigint,
    created_at integer,
    register_time integer,
    token text,
    avatar text,
    subscription_end_date bigint);