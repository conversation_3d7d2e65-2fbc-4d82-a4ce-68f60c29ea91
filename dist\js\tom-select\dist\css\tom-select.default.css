/**
 * tom-select.css (v2.4.2)
 * Copyright (c) contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this
 * file except in compliance with the License. You may obtain a copy of the License at:
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF
 * ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 *
 */
.ts-control {
  border: 1px solid #d0d0d0;
  padding: 8px 8px;
  width: 100%;
  overflow: hidden;
  position: relative;
  z-index: 1;
  box-sizing: border-box;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  display: flex;
  flex-wrap: wrap;
}
.ts-wrapper.multi.has-items .ts-control {
  padding: calc(8px - 2px - 1px) 8px calc(8px - 2px - 3px - 1px);
}
.full .ts-control {
  background-color: #fff;
}
.disabled .ts-control, .disabled .ts-control * {
  cursor: default !important;
}
.focus .ts-control {
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);
}
.ts-control > * {
  vertical-align: baseline;
  display: inline-block;
}
.ts-wrapper.multi .ts-control > div {
  cursor: pointer;
  margin: 0 3px 3px 0;
  padding: 2px 6px;
  background: #1da7ee;
  color: #fff;
  border: 1px solid #0073bb;
}
.ts-wrapper.multi .ts-control > div.active {
  background: #92c836;
  color: #fff;
  border: 1px solid #00578d;
}
.ts-wrapper.multi.disabled .ts-control > div, .ts-wrapper.multi.disabled .ts-control > div.active {
  color: white;
  background: #d2d2d2;
  border: 1px solid #aaaaaa;
}
.ts-control > input {
  flex: 1 1 auto;
  min-width: 7rem;
  display: inline-block !important;
  padding: 0 !important;
  min-height: 0 !important;
  max-height: none !important;
  max-width: 100% !important;
  margin: 0 !important;
  text-indent: 0 !important;
  border: 0 none !important;
  background: none !important;
  line-height: inherit !important;
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
      -ms-user-select: auto !important;
          user-select: auto !important;
  box-shadow: none !important;
}
.ts-control > input::-ms-clear {
  display: none;
}
.ts-control > input:focus {
  outline: none !important;
}
.has-items .ts-control > input {
  margin: 0 4px !important;
}
.ts-control.rtl {
  text-align: right;
}
.ts-control.rtl.single .ts-control:after {
  left: 15px;
  right: auto;
}
.ts-control.rtl .ts-control > input {
  margin: 0 4px 0 -2px !important;
}
.disabled .ts-control {
  opacity: 0.5;
  background-color: #fafafa;
}
.input-hidden .ts-control > input {
  opacity: 0;
  position: absolute;
  left: -10000px;
}

.ts-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  z-index: 10;
  border: 1px solid #d0d0d0;
  background: #fff;
  margin: 0.25rem 0 0;
  border-top: 0 none;
  box-sizing: border-box;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0 0 3px 3px;
}
.ts-dropdown [data-selectable] {
  cursor: pointer;
  overflow: hidden;
}
.ts-dropdown [data-selectable] .highlight {
  background: rgba(125, 168, 208, 0.2);
  border-radius: 1px;
}
.ts-dropdown .option,
.ts-dropdown .optgroup-header,
.ts-dropdown .no-results,
.ts-dropdown .create {
  padding: 5px 8px;
}
.ts-dropdown .option, .ts-dropdown [data-disabled], .ts-dropdown [data-disabled] [data-selectable].option {
  cursor: inherit;
  opacity: 0.5;
}
.ts-dropdown [data-selectable].option {
  opacity: 1;
  cursor: pointer;
}
.ts-dropdown .optgroup:first-child .optgroup-header {
  border-top: 0 none;
}
.ts-dropdown .optgroup-header {
  color: #303030;
  background: #fff;
  cursor: default;
}
.ts-dropdown .active {
  background-color: #f5fafd;
  color: #495c68;
}
.ts-dropdown .active.create {
  color: #495c68;
}
.ts-dropdown .create {
  color: rgba(48, 48, 48, 0.5);
}
.ts-dropdown .spinner {
  display: inline-block;
  width: 30px;
  height: 30px;
  margin: 5px 8px;
}
.ts-dropdown .spinner::after {
  content: " ";
  display: block;
  width: 24px;
  height: 24px;
  margin: 3px;
  border-radius: 50%;
  border: 5px solid #d0d0d0;
  border-color: #d0d0d0 transparent #d0d0d0 transparent;
  animation: lds-dual-ring 1.2s linear infinite;
}
@keyframes lds-dual-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.ts-dropdown-content {
  overflow: hidden auto;
  max-height: 200px;
  scroll-behavior: smooth;
}

.ts-wrapper.plugin-drag_drop .ts-dragging {
  color: transparent !important;
}
.ts-wrapper.plugin-drag_drop .ts-dragging > * {
  visibility: hidden !important;
}

.plugin-checkbox_options:not(.rtl) .option input {
  margin-right: 0.5rem;
}

.plugin-checkbox_options.rtl .option input {
  margin-left: 0.5rem;
}

/* stylelint-disable function-name-case */
.plugin-clear_button {
  --ts-pr-clear-button: 1em;
}
.plugin-clear_button .clear-button {
  opacity: 0;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: calc(8px - 6px);
  margin-right: 0 !important;
  background: transparent !important;
  transition: opacity 0.5s;
  cursor: pointer;
}
.plugin-clear_button.form-select .clear-button, .plugin-clear_button.single .clear-button {
  right: max(var(--ts-pr-caret), 8px);
}
.plugin-clear_button.focus.has-items .clear-button, .plugin-clear_button:not(.disabled):hover.has-items .clear-button {
  opacity: 1;
}

.ts-wrapper .dropdown-header {
  position: relative;
  padding: 10px 8px;
  border-bottom: 1px solid #d0d0d0;
  background: color-mix(#fff, #d0d0d0, 85%);
  border-radius: 3px 3px 0 0;
}
.ts-wrapper .dropdown-header-close {
  position: absolute;
  right: 8px;
  top: 50%;
  color: #303030;
  opacity: 0.4;
  margin-top: -12px;
  line-height: 20px;
  font-size: 20px !important;
}
.ts-wrapper .dropdown-header-close:hover {
  color: black;
}

.plugin-dropdown_input.focus.dropdown-active .ts-control {
  box-shadow: none;
  border: 1px solid #d0d0d0;
}
.plugin-dropdown_input .dropdown-input {
  border: 1px solid #d0d0d0;
  border-width: 0 0 1px;
  display: block;
  padding: 8px 8px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);
  width: 100%;
  background: transparent;
}
.plugin-dropdown_input .items-placeholder {
  border: 0 none !important;
  box-shadow: none !important;
  width: 100%;
}
.plugin-dropdown_input.has-items .items-placeholder, .plugin-dropdown_input.dropdown-active .items-placeholder {
  display: none !important;
}

.ts-wrapper.plugin-input_autogrow.has-items .ts-control > input {
  min-width: 0;
}
.ts-wrapper.plugin-input_autogrow.has-items.focus .ts-control > input {
  flex: none;
  min-width: 4px;
}
.ts-wrapper.plugin-input_autogrow.has-items.focus .ts-control > input::-ms-input-placeholder {
  color: transparent;
}
.ts-wrapper.plugin-input_autogrow.has-items.focus .ts-control > input::placeholder {
  color: transparent;
}

.ts-dropdown.plugin-optgroup_columns .ts-dropdown-content {
  display: flex;
}
.ts-dropdown.plugin-optgroup_columns .optgroup {
  border-right: 1px solid #f2f2f2;
  border-top: 0 none;
  flex-grow: 1;
  flex-basis: 0;
  min-width: 0;
}
.ts-dropdown.plugin-optgroup_columns .optgroup:last-child {
  border-right: 0 none;
}
.ts-dropdown.plugin-optgroup_columns .optgroup::before {
  display: none;
}
.ts-dropdown.plugin-optgroup_columns .optgroup-header {
  border-top: 0 none;
}

.ts-wrapper.plugin-remove_button .item {
  display: inline-flex;
  align-items: center;
}
.ts-wrapper.plugin-remove_button .item .remove {
  color: inherit;
  text-decoration: none;
  vertical-align: middle;
  display: inline-block;
  padding: 0 6px;
  border-radius: 0 2px 2px 0;
  box-sizing: border-box;
}
.ts-wrapper.plugin-remove_button .item .remove:hover {
  background: rgba(0, 0, 0, 0.05);
}
.ts-wrapper.plugin-remove_button.disabled .item .remove:hover {
  background: none;
}
.ts-wrapper.plugin-remove_button .remove-single {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 23px;
}

.ts-wrapper.plugin-remove_button:not(.rtl) .item {
  padding-right: 0 !important;
}
.ts-wrapper.plugin-remove_button:not(.rtl) .item .remove {
  border-left: 1px solid #0073bb;
  margin-left: 6px;
}
.ts-wrapper.plugin-remove_button:not(.rtl) .item.active .remove {
  border-left-color: #00578d;
}
.ts-wrapper.plugin-remove_button:not(.rtl).disabled .item .remove {
  border-left-color: #aaaaaa;
}

.ts-wrapper.plugin-remove_button.rtl .item {
  padding-left: 0 !important;
}
.ts-wrapper.plugin-remove_button.rtl .item .remove {
  border-right: 1px solid #0073bb;
  margin-right: 6px;
}
.ts-wrapper.plugin-remove_button.rtl .item.active .remove {
  border-right-color: #00578d;
}
.ts-wrapper.plugin-remove_button.rtl.disabled .item .remove {
  border-right-color: #aaaaaa;
}

:root {
  --ts-pr-clear-button: 0px;
  --ts-pr-caret: 0px;
  --ts-pr-min: .75rem;
}

.ts-wrapper.single .ts-control, .ts-wrapper.single .ts-control input {
  cursor: pointer;
}

.ts-control:not(.rtl) {
  padding-right: max(var(--ts-pr-min), var(--ts-pr-clear-button) + var(--ts-pr-caret)) !important;
}

.ts-control.rtl {
  padding-left: max(var(--ts-pr-min), var(--ts-pr-clear-button) + var(--ts-pr-caret)) !important;
}

.ts-wrapper {
  position: relative;
}

.ts-dropdown,
.ts-control,
.ts-control input {
  color: #303030;
  font-family: inherit;
  font-size: 13px;
  line-height: 18px;
}

.ts-control,
.ts-wrapper.single.input-active .ts-control {
  background: #fff;
  cursor: text;
}

.ts-hidden-accessible {
  border: 0 !important;
  clip: rect(0 0 0 0) !important;
  -webkit-clip-path: inset(50%) !important;
          clip-path: inset(50%) !important;
  overflow: hidden !important;
  padding: 0 !important;
  position: absolute !important;
  width: 1px !important;
  white-space: nowrap !important;
}

.ts-wrapper.single .ts-control {
  --ts-pr-caret: 2rem;
}
.ts-wrapper.single .ts-control::after {
  content: " ";
  display: block;
  position: absolute;
  top: 50%;
  margin-top: -3px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 5px 0 5px;
  border-color: #808080 transparent transparent transparent;
}
.ts-wrapper.single .ts-control:not(.rtl)::after {
  right: 15px;
}
.ts-wrapper.single .ts-control.rtl::after {
  left: 15px;
}
.ts-wrapper.single.dropdown-active .ts-control::after {
  margin-top: -4px;
  border-width: 0 5px 5px 5px;
  border-color: transparent transparent #808080 transparent;
}
.ts-wrapper.single.input-active .ts-control, .ts-wrapper.single.input-active .ts-control input {
  cursor: text;
}

.ts-wrapper {
  display: flex;
  min-height: 36px;
}
.ts-wrapper.multi.has-items .ts-control {
  padding-left: 5px;
  --ts-pr-min: $padding-x;
}
.ts-wrapper.multi .ts-control [data-value] {
  text-shadow: 0 1px 0 rgba(0, 51, 83, 0.3);
  border-radius: 3px;
  background-color: color-mix(#1da7ee, #178ee9, 60%);
  background-image: linear-gradient(to bottom, #1da7ee, #178ee9);
  background-repeat: repeat-x;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2), inset 0 1px rgba(255, 255, 255, 0.03);
}
.ts-wrapper.multi .ts-control [data-value].active {
  background-color: color-mix(#008fd8, #0075cf, 60%);
  background-image: linear-gradient(to bottom, #008fd8, #0075cf);
  background-repeat: repeat-x;
}
.ts-wrapper.multi.disabled .ts-control [data-value] {
  color: #999;
  text-shadow: none;
  background: none;
  box-shadow: none;
}
.ts-wrapper.multi.disabled .ts-control [data-value], .ts-wrapper.multi.disabled .ts-control [data-value] .remove {
  border-color: #e6e6e6;
}
.ts-wrapper.multi.disabled .ts-control [data-value] .remove {
  background: none;
}
.ts-wrapper.single .ts-control {
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.8);
  background-color: color-mix(#fefefe, #f2f2f2, 60%);
  background-image: linear-gradient(to bottom, #fefefe, #f2f2f2);
  background-repeat: repeat-x;
}

.ts-wrapper.single .ts-control, .ts-dropdown.single {
  border-color: #b8b8b8;
}

.dropdown-active .ts-control {
  border-radius: 3px 3px 0 0;
}

.ts-dropdown .optgroup-header {
  padding-top: 7px;
  font-weight: bold;
  font-size: 0.85em;
}
.ts-dropdown .optgroup {
  border-top: 1px solid #f0f0f0;
}
.ts-dropdown .optgroup:first-child {
  border-top: 0 none;
}
/*# sourceMappingURL=tom-select.default.css.map */