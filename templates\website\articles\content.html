{% extends 'basic.html' %}

{% block content %}
<div class="page-header d-print-none mb-4">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="page-title">{{ article.title or 'Статья' }}</h2>
        </div>
    </div>
</div>

<!-- Табы навигации -->
<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" data-bs-toggle="tabs" role="tablist">
            <li class="nav-item" role="presentation">
                <a href="#tabs-content" class="nav-link active" data-bs-toggle="tab" aria-selected="true" role="tab">Контент</a>
            </li>
            <li class="nav-item" role="presentation">
                <a href="#tabs-references" class="nav-link" data-bs-toggle="tab" aria-selected="false" role="tab" tabindex="-1">Референсы</a>
            </li>
            <li class="nav-item" role="presentation">
                <a href="#tabs-citations" class="nav-link" data-bs-toggle="tab" aria-selected="false" role="tab" tabindex="-1">Цитирования</a>
            </li>
        </ul>
    </div>
    
    <div class="tab-content">
        <!-- Таб контента -->
        <div class="tab-pane active show" id="tabs-content" role="tabpanel">
            <div class="card-header ">
                <div class="d-flex align-items-center justify-content-between w-100">
                    <h3 class="card-title">Блоки контента</h3>
                    <button class="btn btn-primary" id="add-block-btn">
                        <i class="ti ti-plus"></i>
                        Добавить блок
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table card-table table-hover" id="content-table">
                    <thead>
                        <tr>
                            <th style="width: 80px;">Порядок</th>
                            <th style="width: 80px;">Тип</th>
                            <th>Название</th>
                            <th class="w-1"></th>
                        </tr>
                    </thead>
                    <tbody id="content-tbody">
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Таб референсов -->
        <div class="tab-pane" id="tabs-references" role="tabpanel">
            <div class="card-header">
                <div class="d-flex align-items-center justify-content-between w-100">
                    <h3 class="card-title">Референсы</h3>
                    <button class="btn btn-primary" id="add-reference-btn">
                        <i class="ti ti-plus"></i>
                        Добавить референс
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table card-table table-hover" id="references-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Название</th>
                            <th>Авторы</th>
                            <th>DOI</th>
                            <th class="w-1"></th>
                        </tr>
                    </thead>
                    <tbody id="references-tbody">
                        <tr>
                            <td colspan="5" class="text-center text-muted">Загрузка...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Таб цитирований -->
        <div class="tab-pane" id="tabs-citations" role="tabpanel">
            <div class="card-header">
                <div class="d-flex align-items-center justify-content-between w-100">
                    <h3 class="card-title">Цитирования</h3>
                    <button class="btn btn-primary" id="add-citation-btn">
                        <i class="ti ti-plus"></i>
                        Добавить цитирование
                    </button>
                </div>
            </div>
            <div class="table-responsive">
                <table class="table card-table table-hover" id="citations-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Название</th>
                            <th>Авторы</th>
                            <th>Журнал</th>
                            <th>Год</th>
                            <th class="w-1"></th>
                        </tr>
                    </thead>
                    <tbody id="citations-tbody">
                        <tr>
                            <td colspan="6" class="text-center text-muted">Загрузка...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Модалка для добавления/редактирования блока -->
<div class="modal fade" id="blockModal" tabindex="-1" aria-labelledby="blockModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="blockModalLabel">Блок контента</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <form id="block-form" method="post" enctype="multipart/form-data" action="">
      <div class="modal-body">
          <input type="hidden" id="block-id" name="block_id">
          <input type="hidden" id="block-index" name="block_index">
          <input type="hidden" name="article_id" value="{{ article_id }}">
          <div class="mb-3">
            <label for="block-type" class="form-label">Тип блока</label>
            <select class="form-select" id="block-type" name="block_type" required>
              <option value="text">Текст</option>
              <option value="image">Изображение</option>
            </select>
          </div>
          <div class="mb-3">
            <label for="block-title" class="form-label">Название блока</label>
            <input type="text" class="form-control" id="block-title" name="block_title" maxlength="255" required>
          </div>
          <div class="mb-3 block-field block-text">
            <label for="block-text" class="form-label">Текст</label>
            <div class="mb-2 text-muted small">[[число]] — это ссылка на референс, ссылка будет автоматически подставлена при отображении статьи.</div>
            <div class="d-flex align-items-center mb-2">
              <button type="button" class="btn btn-outline-primary btn-sm me-2" id="custom-insert-btn" title="Вставить ссылку на референс">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icon-tabler-book-plus" viewBox="0 0 24 24"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 6h6"/><path d="M9 12h6"/><path d="M9 18h6"/><path d="M15 15v6"/><path d="M12 18h6"/><path d="M5 4v16a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2z"/></svg>
                Вставить ссылку на референс
              </button>
            </div>
            <textarea class="form-control" id="block-text" name="block_text" rows="10"></textarea>
          </div>
          <div class="mb-3 block-field block-image" style="display:none;">
            <label for="block-image" class="form-label">Изображение</label>
            <input type="file" class="form-control" id="block-image" name="block_image" accept="image/*">
            <div class="mt-2" id="block-image-preview"></div>
            <label for="block-image-desc" class="form-label mt-2">Описание изображения</label>
            <input type="text" class="form-control" id="block-image-desc" name="block_image_desc" maxlength="255">
          </div>
          <div class="mb-3 block-field block-table" style="display:none;">
            <label for="block-table" class="form-label">Таблица (вставьте HTML или текст)</label>
            <textarea class="form-control" id="block-table" name="block_table" rows="6"></textarea>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
          <button type="submit" class="btn btn-primary" id="save-block-btn">Сохранить</button>
        </div>
      </form>
    </div>
  </div>
</div>

<!-- Модалка для добавления/редактирования референса -->
<div class="modal fade" id="referenceModal" tabindex="-1" aria-labelledby="referenceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="referenceModalLabel">Референс</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="reference-form">
                <div class="modal-body">
                    <input type="hidden" id="reference-id" name="reference_id">
                    <div class="mb-3">
                        <label for="reference-title" class="form-label">Название *</label>
                        <input type="text" class="form-control" id="reference-title" name="title" required maxlength="500">
                    </div>
                    <div class="mb-3">
                        <label for="reference-authors" class="form-label">Авторы</label>
                        <input type="text" class="form-control" id="reference-authors" name="authors" maxlength="500">
                    </div>
                    <div class="mb-3">
                        <label for="reference-doi" class="form-label">DOI</label>
                        <input type="text" class="form-control" id="reference-doi" name="doi" maxlength="255">
                    </div>
                    <div class="mb-3">
                        <label for="reference-doi-link" class="form-label">DOI ссылка</label>
                        <input type="url" class="form-control" id="reference-doi-link" name="doi_link" maxlength="255" readonly>
                        <div class="form-text">Автоматически генерируется на основе DOI</div>
                    </div>
                    <div class="mb-3">
                        <label for="reference-wos-link" class="form-label">WOS ссылка</label>
                        <input type="url" class="form-control" id="reference-wos-link" name="wos_link" maxlength="255">
                    </div>
                    <div class="mb-3">
                        <label for="reference-scopus-link" class="form-label">Scopus ссылка</label>
                        <input type="url" class="form-control" id="reference-scopus-link" name="scopus_link" maxlength="255">
                    </div>
                    <div class="mb-3">
                        <label for="reference-gscholar-link" class="form-label">Google Scholar ссылка</label>
                        <input type="url" class="form-control" id="reference-gscholar-link" name="gscholar_link" maxlength="255">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                    <button type="submit" class="btn btn-primary">Сохранить</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Модалка для добавления/редактирования цитирования -->
<div class="modal fade" id="citationModal" tabindex="-1" aria-labelledby="citationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="citationModalLabel">Цитирование</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="citation-form">
                <div class="modal-body">
                    <input type="hidden" id="citation-id" name="citation_id">
                    <div class="mb-3">
                        <label for="citation-title" class="form-label">Название *</label>
                        <input type="text" class="form-control" id="citation-title" name="title" required maxlength="500">
                    </div>
                    <div class="mb-3">
                        <label for="citation-authors" class="form-label">Авторы</label>
                        <input type="text" class="form-control" id="citation-authors" name="authors" maxlength="500">
                    </div>
                    <div class="mb-3">
                        <label for="citation-journal" class="form-label">Журнал</label>
                        <input type="text" class="form-control" id="citation-journal" name="journal" maxlength="255">
                    </div>
                    <div class="mb-3">
                        <label for="citation-year" class="form-label">Год</label>
                        <input type="number" class="form-control" id="citation-year" name="year" min="1900" max="2100">
                    </div>
                    <div class="mb-3">
                        <label for="citation-doi" class="form-label">DOI</label>
                        <input type="text" class="form-control" id="citation-doi" name="doi" maxlength="255">
                    </div>
                    <div class="mb-3">
                        <label for="citation-doi-link" class="form-label">DOI ссылка</label>
                        <input type="url" class="form-control" id="citation-doi-link" name="doi_link" maxlength="255" readonly>
                        <div class="form-text">Автоматически генерируется на основе DOI</div>
                    </div>
                    <div class="mb-3">
                        <label for="citation-wos-link" class="form-label">WOS ссылка</label>
                        <input type="url" class="form-control" id="citation-wos-link" name="wos_link" maxlength="255">
                    </div>
                    <div class="mb-3">
                        <label for="citation-scopus-link" class="form-label">Scopus ссылка</label>
                        <input type="url" class="form-control" id="citation-scopus-link" name="scopus_link" maxlength="255">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
                    <button type="submit" class="btn btn-primary">Сохранить</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
// Получаем данные из template
const articleId = parseInt('{{ article_id }}');
const contentListData = JSON.parse('{{ content_list|tojson|safe }}');

let contentList = contentListData;
let ckeditorInstance = null;

// Добавляем SVG иконку (книга с плюсом)
const bookPlusIcon = '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icon-tabler-book-plus" viewBox="0 0 24 24"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 6h6"/><path d="M9 12h6"/><path d="M9 18h6"/><path d="M15 15v6"/><path d="M12 18h6"/><path d="M5 4v16a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2z"/></svg>';

// Создаём модалку для вставки цитирования
const customModalHtml = `
<div class="modal fade" id="customInsertModal" tabindex="-1" aria-labelledby="customInsertModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content border">
      <div class="modal-header">
        <h5 class="modal-title" id="customInsertModalLabel">Вставка ссылки на референс</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <label for="custom-insert-search" class="form-label">Поиск референса</label>
        <input type="text" class="form-control mb-2" id="custom-insert-search" placeholder="Введите название или автора...">
        <div id="custom-insert-results" style="max-height:200px;overflow-y:auto;"></div>
        <hr>
        <div class="mb-2"><b>Добавить новый референс к статье</b></div>
        <input type="text" class="form-control mb-2" id="custom-insert-title" placeholder="Название" maxlength="500">
        <input type="text" class="form-control mb-2" id="custom-insert-authors" placeholder="Авторы" maxlength="500">
        <input type="text" class="form-control mb-2" id="custom-insert-doi" placeholder="DOI" maxlength="255">
        <input type="text" class="form-control mb-2" id="custom-insert-doi-link" placeholder="DOI ссылка" maxlength="255">
        <input type="text" class="form-control mb-2" id="custom-insert-wos-link" placeholder="WOS ссылка" maxlength="255">
        <input type="text" class="form-control mb-2" id="custom-insert-scopus-link" placeholder="Scopus ссылка" maxlength="255">
        <input type="text" class="form-control mb-2" id="custom-insert-gscholar-link" placeholder="Google Scholar ссылка" maxlength="255">
        <button type="button" class="btn btn-primary w-100" id="custom-insert-add">Добавить</button>
        <div class="text-danger small mt-2" id="custom-insert-add-error" style="display:none;"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Отмена</button>
      </div>
    </div>
  </div>
</div>`;
document.body.insertAdjacentHTML('beforeend', customModalHtml);

ClassicEditor.create(document.getElementById('block-text'), {
    toolbar: [
        'heading','bold','italic','link','bulletedList','numberedList','blockQuote','undo','redo','insertTable','codeBlock'
    ]
}).then(editor => { ckeditorInstance = editor; })
  .catch(error => { ckeditorInstance = null; });

document.getElementById('custom-insert-btn').onclick = function() {
    const modal = new bootstrap.Modal(document.getElementById('customInsertModal'));
    document.getElementById('custom-insert-search').value = '';
    document.getElementById('custom-insert-results').innerHTML = '<div class="text-muted">Введите запрос для поиска референсов</div>';
    document.getElementById('custom-insert-title').value = '';
    document.getElementById('custom-insert-authors').value = '';
    document.getElementById('custom-insert-doi').value = '';
    document.getElementById('custom-insert-doi-link').value = '';
    document.getElementById('custom-insert-wos-link').value = '';
    document.getElementById('custom-insert-scopus-link').value = '';
    document.getElementById('custom-insert-gscholar-link').value = '';
    document.getElementById('custom-insert-add-error').style.display = 'none';
    modal.show();
};

document.getElementById('custom-insert-search').addEventListener('input', function() {
    const q = this.value.trim();
    const resultsDiv = document.getElementById('custom-insert-results');
    
    if (!q) { 
        // Если поиск пустой, показываем подсказку
        resultsDiv.innerHTML = '<div class="text-muted">Введите запрос для поиска референсов</div>';
        return; 
    }
    
    resultsDiv.innerHTML = 'Поиск...';
    fetch('/fmadmin/api/references/search?q=${encodeURIComponent(q)}&article_id=${articleId}`)
      .then(r => r.json())
      .then(data => {
        if (!data.length) { 
            resultsDiv.innerHTML = '<div class="text-muted">Ничего не найдено</div>'; 
            return; 
        }
        resultsDiv.innerHTML = data.map(ref =>
          `<div class='list-group-item list-group-item-action' style='cursor:pointer' data-id='${ref.id}'>
            <div><b>${ref.title || ''}</b></div>
            <div class='small text-muted'>${ref.authors || ''}${ref.doi ? ' | DOI: ' + ref.doi : ''}</div>
          </div>`
        ).join('');
        Array.from(resultsDiv.querySelectorAll('[data-id]')).forEach(el => {
          el.onclick = function() {
            const id = this.getAttribute('data-id');
            const modal = bootstrap.Modal.getInstance(document.getElementById('customInsertModal'));
            if (ckeditorInstance && id) {
              ckeditorInstance.execute('insertText', { text: `[[${id}]]` });
            }
            modal.hide();
          };
        });
      })
      .catch(error => {
        console.error('Error searching references:', error);
        resultsDiv.innerHTML = '<div class="text-danger">Ошибка поиска</div>';
      });
});

document.getElementById('custom-insert-add').onclick = function() {
    const title = document.getElementById('custom-insert-title').value.trim();
    const authors = document.getElementById('custom-insert-authors').value.trim();
    const doi = document.getElementById('custom-insert-doi').value.trim();
    const doi_link = document.getElementById('custom-insert-doi-link').value.trim();
    const wos_link = document.getElementById('custom-insert-wos-link').value.trim();
    const scopus_link = document.getElementById('custom-insert-scopus-link').value.trim();
    const gscholar_link = document.getElementById('custom-insert-gscholar-link').value.trim();
    const errorDiv = document.getElementById('custom-insert-add-error');
    errorDiv.style.display = 'none';
    if (!title) {
        errorDiv.textContent = 'Название обязательно';
        errorDiv.style.display = '';
        return;
    }
    fetch('/fmadmin/api/references', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
            article_id: articleId,
            title, 
            authors, 
            doi, 
            doi_link, 
            wos_link, 
            scopus_link, 
            gscholar_link 
        })
    })
    .then(r => r.json())
    .then(data => {
        if (data && data.id) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('customInsertModal'));
            if (ckeditorInstance) {
                ckeditorInstance.execute('insertText', { text: '[[' + String(data.id) + ']]' });
            }
            modal.hide();
        } else {
            errorDiv.textContent = data && data.error ? data.error : 'Ошибка добавления';
            errorDiv.style.display = '';
        }
    })
    .catch(() => {
        errorDiv.textContent = 'Ошибка добавления';
        errorDiv.style.display = '';
    });
};

function renderTable() {
    const tbody = document.getElementById('content-tbody');
    tbody.innerHTML = '';
    
    if (contentList.length === 0) {
        tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">Нет блоков контента</td></tr>';
        return;
    }
    
    contentList.forEach((block, idx) => {
        let typeLabel = '';
        if (block.type === 'text') typeLabel = 'Текст';
        if (block.type === 'image') typeLabel = 'Изображение';
        if (block.type === 'table') typeLabel = 'Таблица';
        let preview = '';
        if (block.type === 'text') preview = block.text ? block.text.replace(/<[^>]+>/g, '').substring(0, 100) : '';
        if (block.type === 'image') preview = block.image ? `<img src='${block.image}' style='max-width:80px;max-height:60px;'>` : '';
        if (block.type === 'table') preview = block.table ? block.table.substring(0, 100) : '';
        tbody.innerHTML += `
        <tr>
            <td class="align-middle text-center">${block.order_id}
                <form method='post' style='display:inline;'>
                    <input type='hidden' name='move_block_id' value='${block.id}'>
                    <input type='hidden' name='move_dir' value='up'>
                    <button type='submit' class='btn btn-link p-0' title='Вверх' ${idx === 0 ? 'disabled' : ''}>&uarr;</button>
                </form>
                <form method='post' style='display:inline;'>
                    <input type='hidden' name='move_block_id' value='${block.id}'>
                    <input type='hidden' name='move_dir' value='down'>
                    <button type='submit' class='btn btn-link p-0' title='Вниз' ${idx === contentList.length-1 ? 'disabled' : ''}>&darr;</button>
                </form>
            </td>
            <td class="align-middle">${typeLabel}</td>
            <td class="align-middle">${block.title || ''}</td>
            <td class="align-middle">
                <div class="d-flex">
                    <button class="btn btn-icon btn-outline-primary bg-primary-lt btn-sm px-2 py-1 me-2" onclick="editBlock(${idx})" title="Редактировать"><i class="ti ti-edit"></i> Редактировать</button>
                    <button class="btn btn-icon btn-outline-danger bg-danger-lt btn-sm px-2 py-1" onclick="deleteBlock(${idx})" title="Удалить"><i class="ti ti-trash"></i> Удалить</button>
                </div>
            </td>
        </tr>`;
    });
}

function resetModal() {
    document.getElementById('block-form').reset();
    document.getElementById('block-id').value = '';
    document.getElementById('block-index').value = '';
    document.getElementById('block-type').value = 'text';
    document.getElementById('block-title').value = '';
    if (ckeditorInstance) ckeditorInstance.setData('');
    document.getElementById('block-image').value = '';
    document.getElementById('block-image-desc').value = '';
    document.getElementById('block-table').value = '';
    document.getElementById('block-image-preview').innerHTML = '';
    showBlockFields('text');
}

function showBlockFields(type) {
    document.querySelectorAll('.block-field').forEach(e => e.style.display = 'none');
    if (type === 'text') document.querySelector('.block-text').style.display = '';
    if (type === 'image') document.querySelector('.block-image').style.display = '';
    if (type === 'table') document.querySelector('.block-table').style.display = '';
    if (type === 'text' && ckeditorInstance) ckeditorInstance.resize && ckeditorInstance.resize();
}

document.getElementById('block-type').addEventListener('change', function() {
    showBlockFields(this.value);
});

document.getElementById('add-block-btn').addEventListener('click', function() {
    resetModal();
    var modal = new bootstrap.Modal(document.getElementById('blockModal'));
    modal.show();
});

function editBlock(idx) {
    const block = contentList[idx];
    resetModal();
    document.getElementById('block-index').value = idx;
    document.getElementById('block-id').value = block.id || '';
    document.getElementById('block-type').value = block.type;
    document.getElementById('block-title').value = block.title || '';
    showBlockFields(block.type);
    if (block.type === 'text' && ckeditorInstance) ckeditorInstance.setData(block.text || '');
    if (block.type === 'image') {
        document.getElementById('block-image-desc').value = block.image_desc || '';
        if (block.image) document.getElementById('block-image-preview').innerHTML = `<img src='${block.image}' style='max-width:100%;max-height:200px;'>`;
    }
    if (block.type === 'table') document.getElementById('block-table').value = block.table || '';
    var modal = new bootstrap.Modal(document.getElementById('blockModal'));
    modal.show();
}

function deleteBlock(idx) {
    if (confirm('Удалить блок?')) {
        const block = contentList[idx];
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '';
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'delete_block_id';
        input.value = block.id;
        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }
}

// Функции для загрузки данных табов
function loadReferences() {
    const tbody = document.getElementById('references-tbody');
    tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">Загрузка...</td></tr>';
    
    fetch('/fmadmin/api/articles/${articleId}/references`)
        .then(response => response.json())
        .then(data => {
            if (data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">Нет референсов</td></tr>';
                return;
            }
            
            tbody.innerHTML = data.map(ref => {
                const doiLink = ref.doi ? `https://doi.org/${ref.doi}` : (ref.doi_link || '');
                return `
                <tr>
                    <td class="align-middle">${ref.id}</td>
                    <td class="align-middle">${ref.title || ''}</td>
                    <td class="align-middle">${ref.authors || ''}</td>
                    <td class="align-middle">
                        ${ref.doi ? `<a href="${doiLink}" target="_blank">${ref.doi}</a>` : ''}
                    </td>
                    <td class="align-middle">
                        <div class="btn-group">
                            ${doiLink ? `<a href="${doiLink}" target="_blank" class="btn btn-sm btn-outline-primary">DOI</a>` : ''}
                            ${ref.wos_link ? `<a href="${ref.wos_link}" target="_blank" class="btn btn-sm btn-outline-info">WOS</a>` : ''}
                            ${ref.scopus_link ? `<a href="${ref.scopus_link}" target="_blank" class="btn btn-sm btn-outline-warning">Scopus</a>` : ''}
                            ${ref.gscholar_link ? `<a href="${ref.gscholar_link}" target="_blank" class="btn btn-sm btn-outline-success">Scholar</a>` : ''}
                        </div>
                        <div class="btn-group ms-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="editReference(${ref.id})">Редактировать</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteReference(${ref.id})">Удалить</button>
                        </div>
                    </td>
                </tr>
                `;
            }).join('');
        })
        .catch(error => {
            console.error('Error loading references:', error);
            tbody.innerHTML = '<tr><td colspan="5" class="text-center text-danger">Ошибка загрузки</td></tr>';
        });
}

function loadCitations() {
    const tbody = document.getElementById('citations-tbody');
    tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">Загрузка...</td></tr>';
    
    fetch('/fmadmin/api/articles/${articleId}/citations`)
        .then(response => response.json())
        .then(data => {
            if (data.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">Нет цитирований</td></tr>';
                return;
            }
            
            tbody.innerHTML = data.map(citation => {
                const doiLink = citation.doi ? `https://doi.org/${citation.doi}` : (citation.doi_link || '');
                return `
                <tr>
                    <td class="align-middle">${citation.id}</td>
                    <td class="align-middle">${citation.title || ''}</td>
                    <td class="align-middle">${citation.authors || ''}</td>
                    <td class="align-middle">${citation.journal || ''}</td>
                    <td class="align-middle">${citation.year || ''}</td>
                    <td class="align-middle">
                        <div class="btn-group">
                            ${doiLink ? `<a href="${doiLink}" target="_blank" class="btn btn-sm btn-outline-primary">DOI</a>` : ''}
                            ${citation.wos_link ? `<a href="${citation.wos_link}" target="_blank" class="btn btn-sm btn-outline-info">WOS</a>` : ''}
                            ${citation.scopus_link ? `<a href="${citation.scopus_link}" target="_blank" class="btn btn-sm btn-outline-warning">Scopus</a>` : ''}
                        </div>
                        <div class="btn-group ms-2">
                            <button class="btn btn-sm btn-outline-primary" onclick="editCitation(${citation.id})">Редактировать</button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCitation(${citation.id})">Удалить</button>
                        </div>
                    </td>
                </tr>
                `;
            }).join('');
        })
        .catch(error => {
            console.error('Error loading citations:', error);
            tbody.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Ошибка загрузки</td></tr>';
        });
}

// Функции для работы с референсами
function addReference() {
    resetReferenceModal();
    document.getElementById('referenceModalLabel').textContent = 'Добавить референс';
    const modal = new bootstrap.Modal(document.getElementById('referenceModal'));
    modal.show();
}

function editReference(referenceId) {
    fetch('/fmadmin/api/references/${referenceId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('reference-id').value = data.id;
            document.getElementById('reference-title').value = data.title || '';
            document.getElementById('reference-authors').value = data.authors || '';
            document.getElementById('reference-doi').value = data.doi || '';
            document.getElementById('reference-doi-link').value = data.doi ? 'https://doi.org/' + data.doi : '';
            document.getElementById('reference-wos-link').value = data.wos_link || '';
            document.getElementById('reference-scopus-link').value = data.scopus_link || '';
            document.getElementById('reference-gscholar-link').value = data.gscholar_link || '';
            document.getElementById('referenceModalLabel').textContent = 'Редактировать референс';
            const modal = new bootstrap.Modal(document.getElementById('referenceModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Error loading reference:', error);
            alert('Ошибка загрузки референса');
        });
}

function deleteReference(referenceId) {
    if (!confirm('Удалить этот референс?')) return;
    
    fetch('/fmadmin/api/references/${referenceId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadReferences();
        } else {
            alert('Ошибка удаления: ' + (data.error || 'Неизвестная ошибка'));
        }
    })
    .catch(error => {
        console.error('Error deleting reference:', error);
        alert('Ошибка удаления референса');
    });
}

function resetReferenceModal() {
    document.getElementById('reference-form').reset();
    document.getElementById('reference-id').value = '';
    document.getElementById('reference-doi-link').value = '';
}

// Функции для работы с цитированиями
function addCitation() {
    resetCitationModal();
    document.getElementById('citationModalLabel').textContent = 'Добавить цитирование';
    const modal = new bootstrap.Modal(document.getElementById('citationModal'));
    modal.show();
}

function editCitation(citationId) {
    fetch('/fmadmin/api/citations/${citationId}`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('citation-id').value = data.id;
            document.getElementById('citation-title').value = data.title || '';
            document.getElementById('citation-authors').value = data.authors || '';
            document.getElementById('citation-journal').value = data.journal || '';
            document.getElementById('citation-year').value = data.year || '';
            document.getElementById('citation-doi').value = data.doi || '';
            document.getElementById('citation-doi-link').value = data.doi ? 'https://doi.org/' + data.doi : '';
            document.getElementById('citation-wos-link').value = data.wos_link || '';
            document.getElementById('citation-scopus-link').value = data.scopus_link || '';
            document.getElementById('citationModalLabel').textContent = 'Редактировать цитирование';
            const modal = new bootstrap.Modal(document.getElementById('citationModal'));
            modal.show();
        })
        .catch(error => {
            console.error('Error loading citation:', error);
            alert('Ошибка загрузки цитирования');
        });
}

function deleteCitation(citationId) {
    if (!confirm('Удалить это цитирование?')) return;
    
    fetch('/fmadmin/api/citations/${citationId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            loadCitations();
        } else {
            alert('Ошибка удаления: ' + (data.error || 'Неизвестная ошибка'));
        }
    })
    .catch(error => {
        console.error('Error deleting citation:', error);
        alert('Ошибка удаления цитирования');
    });
}

function resetCitationModal() {
    document.getElementById('citation-form').reset();
    document.getElementById('citation-id').value = '';
    document.getElementById('citation-doi-link').value = '';
}

// Обработчики событий
document.addEventListener('DOMContentLoaded', function() {
    // Обработчики табов
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(event) {
            const targetTab = event.target.getAttribute('href');
            if (targetTab === '#tabs-references') {
                loadReferences();
            } else if (targetTab === '#tabs-citations') {
                loadCitations();
            }
        });
    });
    
    // Обработчики кнопок добавления
    document.getElementById('add-reference-btn').addEventListener('click', addReference);
    document.getElementById('add-citation-btn').addEventListener('click', addCitation);
    
    // Обработчики форм
    document.getElementById('reference-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const referenceId = formData.get('reference_id');
        const data = {
            article_id: articleId,
            title: formData.get('title'),
            authors: formData.get('authors'),
            doi: formData.get('doi'),
            doi_link: formData.get('doi_link'),
            wos_link: formData.get('wos_link'),
            scopus_link: formData.get('scopus_link'),
            gscholar_link: formData.get('gscholar_link')
        };
        
        const apiUrl = referenceId ? '/fmadmin/api/references/${referenceId}' : '/fmadmin/api/references';
        const apiMethod = referenceId ? 'PUT' : 'POST';
        
        fetch(apiUrl, {
            method: apiMethod,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success || data.id) {
                const modal = bootstrap.Modal.getInstance(document.getElementById('referenceModal'));
                modal.hide();
                loadReferences();
            } else {
                alert('Ошибка сохранения: ' + (data.error || 'Неизвестная ошибка'));
            }
        })
        .catch(error => {
            console.error('Error saving reference:', error);
            alert('Ошибка сохранения референса');
        });
    });
    
    document.getElementById('citation-form').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const citationId = formData.get('citation_id');
        const data = {
            article_id: articleId,
            title: formData.get('title'),
            authors: formData.get('authors'),
            journal: formData.get('journal'),
            year: formData.get('year') ? parseInt(formData.get('year')) : null,
            doi: formData.get('doi'),
            doi_link: formData.get('doi_link'),
            wos_link: formData.get('wos_link'),
            scopus_link: formData.get('scopus_link')
        };
        
        const apiUrl = citationId ? '/fmadmin/api/citations/${citationId}' : '/fmadmin/api/citations';
        const apiMethod = citationId ? 'PUT' : 'POST';
        
        fetch(apiUrl, {
            method: apiMethod,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success || data.id) {
                const modal = bootstrap.Modal.getInstance(document.getElementById('citationModal'));
                modal.hide();
                loadCitations();
            } else {
                alert('Ошибка сохранения: ' + (data.error || 'Неизвестная ошибка'));
            }
        })
        .catch(error => {
            console.error('Error saving citation:', error);
            alert('Ошибка сохранения цитирования');
        });
    });
    
    // Автогенерация DOI ссылок
    function generateDoiLink(doi) {
        if (doi && doi.trim()) {
            return 'https://doi.org/' + doi.trim();
        }
        return '';
    }
    
    // Обработчики для автогенерации DOI ссылок
    document.getElementById('reference-doi').addEventListener('input', function() {
        const doiLink = generateDoiLink(this.value);
        document.getElementById('reference-doi-link').value = doiLink;
    });
    
    document.getElementById('citation-doi').addEventListener('input', function() {
        const doiLink = generateDoiLink(this.value);
        document.getElementById('citation-doi-link').value = doiLink;
    });
});

renderTable();
</script>
{% endblock %}
