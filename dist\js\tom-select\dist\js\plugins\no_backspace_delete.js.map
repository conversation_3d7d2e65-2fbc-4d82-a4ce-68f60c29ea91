{"version": 3, "file": "no_backspace_delete.js", "sources": ["../../../src/plugins/no_backspace_delete/plugin.ts"], "sourcesContent": ["/**\n * Plugin: \"input_autogrow\" (Tom <PERSON>)\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport type TomSelect from '../../tom-select.ts';\n\nexport default function(this:TomSelect) {\n\tvar self = this;\n\tvar orig_deleteSelection = self.deleteSelection;\n\n\tthis.hook('instead','deleteSelection',(evt:KeyboardEvent) => {\n\n\t\tif( self.activeItems.length ){\n\t\t\treturn orig_deleteSelection.call(self, evt);\n\t\t}\n\n\t\treturn false;\n\t});\n\n};\n"], "names": ["self", "orig_deleteSelection", "deleteSelection", "hook", "evt", "activeItems", "length", "call"], "mappings": ";;;;;;;;;;;CAAA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAIe,eAAyB,IAAA;GACvC,IAAIA,IAAI,GAAG,IAAI;CACf,EAAA,IAAIC,oBAAoB,GAAGD,IAAI,CAACE,eAAe;GAE/C,IAAI,CAACC,IAAI,CAAC,SAAS,EAAC,iBAAiB,EAAEC,GAAiB,IAAK;CAE5D,IAAA,IAAIJ,IAAI,CAACK,WAAW,CAACC,MAAM,EAAE;CAC5B,MAAA,OAAOL,oBAAoB,CAACM,IAAI,CAACP,IAAI,EAAEI,GAAG,CAAC;CAC5C;CAEA,IAAA,OAAO,KAAK;CACb,GAAC,CAAC;CAEH;;;;;;;;"}