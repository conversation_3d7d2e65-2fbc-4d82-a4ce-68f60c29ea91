{% extends "basic.html" %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          {% if editor.id == 0 %}
            Новый редактор
          {% else %}
            Редактирование редактора: {{ editor.name }} {{ editor.second_name or '' }}
          {% endif %}
        </h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{{ url_for('editors') }}" class="btn btn-secondary">
            <i class="ti ti-arrow-left"></i>
            Назад к списку
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="row row-deck row-cards">
      <div class="col-12">
        <form method="POST" action="{{ url_for('editor_edit', editor_id=editor.id) }}">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">Основная информация</h3>
            </div>
            <div class="card-body">
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label required">Имя</label>
                    <input type="text" class="form-control" name="name" value="{{ editor.name or '' }}" required>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">Фамилия</label>
                    <input type="text" class="form-control" name="second_name" value="{{ editor.second_name or '' }}">
                  </div>
                </div>
              </div>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">Отчество</label>
                    <input type="text" class="form-control" name="father_name" value="{{ editor.father_name or '' }}">
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label required">Email</label>
                    <input type="email" class="form-control" name="email" value="{{ editor.email or '' }}" required>
                  </div>
                </div>
              </div>
              
              <div class="row">
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">Специализация</label>
                    <input type="text" class="form-control" name="editor_specialization" value="{{ editor.editor_specialization or '' }}" placeholder="Например: Математика, Физика, Химия">
                    <small class="form-hint">Укажите область научных интересов редактора</small>
                  </div>
                </div>
                {% if editor.id == 0 %}
                <div class="col-md-6">
                  <div class="mb-3">
                    <label class="form-label">Пароль</label>
                    <input type="text" class="form-control" name="password" value="{{ password or '' }}" readonly>
                    <small class="form-hint">Автоматически сгенерированный пароль</small>
                  </div>
                </div>
                {% endif %}
              </div>
            </div>
            
            <div class="card-footer text-end">
              <div class="d-flex">
                <a href="{{ url_for('editors') }}" class="btn btn-link">Отмена</a>
                <button type="submit" class="btn btn-primary ms-auto">
                  {% if editor.id == 0 %}
                    Создать редактора
                  {% else %}
                    Сохранить изменения
                  {% endif %}
                </button>
              </div>
            </div>
          </div>
        </form>
        
        {% if editor.id != 0 %}
        <div class="card mt-3">
          <div class="card-header">
            <h3 class="card-title">Статистика</h3>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                  <div class="card-body">
                    <div class="row align-items-center">
                      <div class="col-auto">
                        <span class="bg-primary text-white avatar">
                          <i class="ti ti-files"></i>
                        </span>
                      </div>
                      <div class="col">
                        <div class="font-weight-medium">
                          Всего назначений
                        </div>
                        <div class="text-secondary">
                          0 статей
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                  <div class="card-body">
                    <div class="row align-items-center">
                      <div class="col-auto">
                        <span class="bg-warning text-white avatar">
                          <i class="ti ti-clock"></i>
                        </span>
                      </div>
                      <div class="col">
                        <div class="font-weight-medium">
                          Ожидает проверки
                        </div>
                        <div class="text-secondary">
                          0 статей
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                  <div class="card-body">
                    <div class="row align-items-center">
                      <div class="col-auto">
                        <span class="bg-success text-white avatar">
                          <i class="ti ti-check"></i>
                        </span>
                      </div>
                      <div class="col">
                        <div class="font-weight-medium">
                          Проверено
                        </div>
                        <div class="text-secondary">
                          0 статей
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-sm-6 col-lg-3">
                <div class="card card-sm">
                  <div class="card-body">
                    <div class="row align-items-center">
                      <div class="col-auto">
                        <span class="bg-danger text-white avatar">
                          <i class="ti ti-x"></i>
                        </span>
                      </div>
                      <div class="col">
                        <div class="font-weight-medium">
                          Отклонено
                        </div>
                        <div class="text-secondary">
                          0 статей
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>
{% endblock %}
