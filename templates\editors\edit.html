{% extends "basic.html" %}

{% block content %}
<div class="page-header d-print-none">
  <div class="row align-items-center">
    <div class="col">
      <h2 class="page-title">
        {% if editor.id == 0 %}
          Новый редактор
        {% else %}
          {{ editor.name }} {{ editor.second_name or '' }}
        {% endif %}
      </h2>
      {% if editor.id != 0 %}
      <div class="page-subtitle">
        <div class="row">
          <div class="col-auto">
            <span class="badge bg-blue-lt">{{ editor.editor_specialization or 'Специализация не указана' }}</span>
          </div>
          <div class="col-auto">
            <span class="text-secondary">{{ editor.email }}</span>
          </div>
        </div>
      </div>
      {% endif %}
    </div>
    <div class="col-auto ms-auto">
      <div class="btn-list">
        <a href="{{ url_for('editors') }}" class="btn btn-secondary">
          <i class="ti ti-arrow-left"></i>
          Назад к списку
        </a>
        {% if editor.id != 0 %}
        <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#editEditorModal">
          <i class="ti ti-edit"></i>
          Редактировать
        </a>
        {% endif %}
      </div>
    </div>
  </div>
</div>

{% if editor.id == 0 %}
<!-- Форма создания нового редактора -->
<form method="POST" action="{{ url_for('editor_edit', editor_id=editor.id) }}">
  <div class="card">
    <div class="card-header">
      <h3 class="card-title">Основная информация</h3>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label required">Имя</label>
            <input type="text" class="form-control" name="name" value="{{ editor.name or '' }}" required>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Фамилия</label>
            <input type="text" class="form-control" name="second_name" value="{{ editor.second_name or '' }}">
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Отчество</label>
            <input type="text" class="form-control" name="father_name" value="{{ editor.father_name or '' }}">
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label required">Email</label>
            <input type="email" class="form-control" name="email" value="{{ editor.email or '' }}" required>
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Специализация</label>
            <input type="text" class="form-control" name="editor_specialization" value="{{ editor.editor_specialization or '' }}" placeholder="Например: Математика, Физика, Химия">
            <small class="form-hint">Укажите область научных интересов редактора</small>
          </div>
        </div>
        <div class="col-md-6">
          <div class="mb-3">
            <label class="form-label">Пароль</label>
            <input type="text" class="form-control" name="password" value="{{ password or '' }}" readonly>
            <small class="form-hint">Автоматически сгенерированный пароль</small>
          </div>
        </div>
      </div>
    </div>

    <div class="card-footer text-end">
      <div class="d-flex">
        <a href="{{ url_for('editors') }}" class="btn btn-link">Отмена</a>
        <button type="submit" class="btn btn-primary ms-auto">
          Создать редактора
        </button>
      </div>
    </div>
  </div>
</form>

{% else %}
<!-- Просмотр существующего редактора -->
<div class="row row-deck row-cards">
  <!-- Статистические карточки -->
  <div class="col-sm-6 col-lg-3">
    <div class="card card-sm">
      <div class="card-body">
        <div class="row align-items-center">
          <div class="col-auto">
            <span class="bg-primary text-white avatar">
              <i class="ti ti-files"></i>
            </span>
          </div>
          <div class="col">
            <div class="font-weight-medium">
              Всего назначений
            </div>
            <div class="text-secondary">
              {{ editor_stats.total or 0 }} статей
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-lg-3">
    <div class="card card-sm">
      <div class="card-body">
        <div class="row align-items-center">
          <div class="col-auto">
            <span class="bg-warning text-white avatar">
              <i class="ti ti-clock"></i>
            </span>
          </div>
          <div class="col">
            <div class="font-weight-medium">
              Ожидает проверки
            </div>
            <div class="text-secondary">
              {{ editor_stats.pending or 0 }} статей
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-lg-3">
    <div class="card card-sm">
      <div class="card-body">
        <div class="row align-items-center">
          <div class="col-auto">
            <span class="bg-success text-white avatar">
              <i class="ti ti-check"></i>
            </span>
          </div>
          <div class="col">
            <div class="font-weight-medium">
              Проверено
            </div>
            <div class="text-secondary">
              {{ editor_stats.reviewed or 0 }} статей
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-sm-6 col-lg-3">
    <div class="card card-sm">
      <div class="card-body">
        <div class="row align-items-center">
          <div class="col-auto">
            <span class="bg-danger text-white avatar">
              <i class="ti ti-x"></i>
            </span>
          </div>
          <div class="col">
            <div class="font-weight-medium">
              Отклонено
            </div>
            <div class="text-secondary">
              {{ editor_stats.rejected or 0 }} статей
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- Таблица назначений -->
<div class="card mt-4">
  <div class="card-header">
    <h3 class="card-title">Назначения редактора</h3>
  </div>
  {% if editor_assignments %}
  <div class="table-responsive">
    <table class="table card-table table-hover">
      <thead>
        <tr>
          <th>Статья</th>
          <th>Назначено</th>
          <th>Статус</th>
          <th>Проверено</th>
          <th class="w-1"></th>
        </tr>
      </thead>
      <tbody>
        {% for assignment in editor_assignments %}
        {% set submission = submissions_map.get(assignment.submission_id) %}
        <tr>
          <td class="align-middle">
            <div class="font-weight-medium">{{ submission.title if submission else 'Статья не найдена' }}</div>
            {% if submission %}
            <div class="text-secondary small">ID: {{ submission.id }}</div>
            {% endif %}
          </td>
          <td class="align-middle">
            <div class="text-secondary small">
              {{ moment(assignment.assigned_at).format('DD.MM.YYYY HH:mm') if assignment.assigned_at else '-' }}
            </div>
          </td>
          <td class="align-middle">
            {% if assignment.status == 'pending' %}
            <span class="badge bg-warning-lt">Ожидает проверки</span>
            {% elif assignment.status == 'reviewed' %}
            <span class="badge bg-success-lt">Проверено</span>
            {% elif assignment.status == 'rejected' %}
            <span class="badge bg-danger-lt">Отклонено</span>
            {% else %}
            <span class="badge bg-secondary-lt">{{ assignment.status }}</span>
            {% endif %}
          </td>
          <td class="align-middle">
            {% if assignment.reviewed_at %}
            <div class="text-secondary small">
              {{ moment(assignment.reviewed_at).format('DD.MM.YYYY HH:mm') }}
            </div>
            {% else %}
            <span class="text-secondary">-</span>
            {% endif %}
          </td>
          <td class="align-middle">
            <a href="{{ url_for('review_assignment', assignment_id=assignment.id) }}" class="btn btn-icon btn-outline-primary bg-primary-lt btn-sm px-2 py-1">
              {% if assignment.status == 'pending' %}Проверить{% else %}Просмотр{% endif %}
            </a>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  {% else %}
  <div class="card-body">
    <div class="empty">
      <div class="empty-img"><img src="/dist/img/undraw_printing_invoices_5r4r.svg" height="128" alt=""></div>
      <p class="empty-title">Нет назначений</p>
      <p class="empty-subtitle text-secondary">
        Этому редактору пока не назначены статьи для проверки.
      </p>
    </div>
  </div>
  {% endif %}
</div>

{% endif %}

<!-- Модальное окно редактирования -->
{% if editor.id != 0 %}
<div class="modal modal-blur fade" id="editEditorModal" tabindex="-1" role="dialog" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
    <div class="modal-content">
      <form method="POST" action="{{ url_for('editor_edit', editor_id=editor.id) }}">
        <div class="modal-header">
          <h5 class="modal-title">Редактирование редактора</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label required">Имя</label>
                <input type="text" class="form-control" name="name" value="{{ editor.name or '' }}" required>
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">Фамилия</label>
                <input type="text" class="form-control" name="second_name" value="{{ editor.second_name or '' }}">
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label">Отчество</label>
                <input type="text" class="form-control" name="father_name" value="{{ editor.father_name or '' }}">
              </div>
            </div>
            <div class="col-md-6">
              <div class="mb-3">
                <label class="form-label required">Email</label>
                <input type="email" class="form-control" name="email" value="{{ editor.email or '' }}" required>
              </div>
            </div>
          </div>

          <div class="mb-3">
            <label class="form-label">Специализация</label>
            <input type="text" class="form-control" name="editor_specialization" value="{{ editor.editor_specialization or '' }}" placeholder="Например: Математика, Физика, Химия">
            <small class="form-hint">Укажите область научных интересов редактора</small>
          </div>
        </div>
        <div class="modal-footer">
          <a href="#" class="btn btn-link link-secondary" data-bs-dismiss="modal">
            Отмена
          </a>
          <button type="submit" class="btn btn-primary ms-auto">
            Сохранить изменения
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endif %}

<script>
function moment(timestamp) {
  if (!timestamp) return { format: () => '-' };
  const date = new Date(timestamp * 1000);
  return {
    format: (format) => {
      if (format === 'DD.MM.YYYY HH:mm') {
        return date.toLocaleString('ru-RU', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
      return date.toLocaleDateString('ru-RU');
    }
  };
}
</script>
{% endblock %}
