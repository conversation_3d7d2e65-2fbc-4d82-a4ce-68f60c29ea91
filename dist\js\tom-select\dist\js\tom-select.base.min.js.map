{"version": 3, "file": "tom-select.base.min.js", "sources": ["../../src/contrib/microevent.ts", "../../node_modules/@orchidjs/unicode-variants/dist/esm/regex.js", "../../node_modules/@orchidjs/unicode-variants/dist/esm/strings.js", "../../node_modules/@orchidjs/unicode-variants/dist/esm/index.js", "../../node_modules/@orchidjs/sifter/dist/esm/utils.js", "../../node_modules/@orchidjs/sifter/dist/esm/sifter.js", "../../src/utils.ts", "../../src/vanilla.ts", "../../src/contrib/highlight.ts", "../../src/constants.ts", "../../src/defaults.ts", "../../src/getSettings.ts", "../../src/tom-select.ts", "../../src/contrib/microplugin.ts"], "sourcesContent": ["/**\n * MicroEvent - to make any js object an event emitter\n *\n * - pure javascript - server compatible, browser compatible\n * - dont rely on the browser doms\n * - super simple - you get it immediatly, no mistery, no magic involved\n *\n * <AUTHOR> (https://github.com/jero<PERSON>)\n */\n\ntype TCallback = (...args:any) => any;\n\n/**\n * Execute callback for each event in space separated list of event names\n *\n */\nfunction forEvents(events:string,callback:(event:string)=>any){\n\tevents.split(/\\s+/).forEach((event) =>{\n\t\tcallback(event);\n\t});\n}\n\nexport default class MicroEvent{\n\n\tpublic _events: {[key:string]:TCallback[]};\n\n\tconstructor(){\n\t\tthis._events = {};\n\t}\n\n\ton(events:string, fct:TCallback){\n\t\tforEvents(events,(event) => {\n\t\t\tconst event_array = this._events[event] || [];\n\t\t\tevent_array.push(fct);\n\t\t\tthis._events[event] = event_array;\n\t\t});\n\t}\n\n\toff(events:string, fct:TCallback){\n\t\tvar n = arguments.length;\n\t\tif( n === 0 ){\n\t\t\tthis._events = {};\n\t\t\treturn;\n\t\t}\n\n\t\tforEvents(events,(event) => {\n\n\t\t\tif (n === 1){\n\t\t\t\tdelete this._events[event];\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tconst event_array = this._events[event];\n\t\t\tif( event_array === undefined ) return;\n\n\t\t\tevent_array.splice(event_array.indexOf(fct), 1);\n\t\t\tthis._events[event] = event_array;\n\t\t});\n\t}\n\n\ttrigger(events:string, ...args:any){\n\t\tvar self = this;\n\n\t\tforEvents(events,(event) => {\n\t\t\tconst event_array = self._events[event];\n\t\t\tif( event_array === undefined ) return;\n\t\t\tevent_array.forEach(fct => {\n\t\t\t\tfct.apply(self, args );\n\t\t\t});\n\n\t\t});\n\t}\n};\n", "/**\n * Convert array of strings to a regular expression\n *\tex ['ab','a'] => (?:ab|a)\n * \tex ['a','b'] => [ab]\n */\nexport const arrayToPattern = (chars) => {\n    chars = chars.filter(Boolean);\n    if (chars.length < 2) {\n        return chars[0] || '';\n    }\n    return (maxValueLength(chars) == 1) ? '[' + chars.join('') + ']' : '(?:' + chars.join('|') + ')';\n};\nexport const sequencePattern = (array) => {\n    if (!hasDuplicates(array)) {\n        return array.join('');\n    }\n    let pattern = '';\n    let prev_char_count = 0;\n    const prev_pattern = () => {\n        if (prev_char_count > 1) {\n            pattern += '{' + prev_char_count + '}';\n        }\n    };\n    array.forEach((char, i) => {\n        if (char === array[i - 1]) {\n            prev_char_count++;\n            return;\n        }\n        prev_pattern();\n        pattern += char;\n        prev_char_count = 1;\n    });\n    prev_pattern();\n    return pattern;\n};\n/**\n * Convert array of strings to a regular expression\n *\tex ['ab','a'] => (?:ab|a)\n * \tex ['a','b'] => [ab]\n */\nexport const setToPattern = (chars) => {\n    let array = Array.from(chars);\n    return arrayToPattern(array);\n};\n/**\n * https://stackoverflow.com/questions/7376598/in-javascript-how-do-i-check-if-an-array-has-duplicate-values\n */\nexport const hasDuplicates = (array) => {\n    return (new Set(array)).size !== array.length;\n};\n/**\n * https://stackoverflow.com/questions/63006601/why-does-u-throw-an-invalid-escape-error\n */\nexport const escape_regex = (str) => {\n    return (str + '').replace(/([\\$\\(\\)\\*\\+\\.\\?\\[\\]\\^\\{\\|\\}\\\\])/gu, '\\\\$1');\n};\n/**\n * Return the max length of array values\n */\nexport const maxValueLength = (array) => {\n    return array.reduce((longest, value) => Math.max(longest, unicodeLength(value)), 0);\n};\nexport const unicodeLength = (str) => {\n    return Array.from(str).length;\n};\n//# sourceMappingURL=regex.js.map", "/**\n * Get all possible combinations of substrings that add up to the given string\n * https://stackoverflow.com/questions/30169587/find-all-the-combination-of-substrings-that-add-up-to-the-given-string\n */\nexport const allSubstrings = (input) => {\n    if (input.length === 1)\n        return [[input]];\n    let result = [];\n    const start = input.substring(1);\n    const suba = allSubstrings(start);\n    suba.forEach(function (subresult) {\n        let tmp = subresult.slice(0);\n        tmp[0] = input.charAt(0) + tmp[0];\n        result.push(tmp);\n        tmp = subresult.slice(0);\n        tmp.unshift(input.charAt(0));\n        result.push(tmp);\n    });\n    return result;\n};\n//# sourceMappingURL=strings.js.map", "import { setToPattern, arrayToPattern, escape_regex, sequencePattern } from \"./regex.js\";\nimport { allSubstrings } from \"./strings.js\";\nexport const code_points = [[0, 65535]];\nconst accent_pat = '[\\u0300-\\u036F\\u{b7}\\u{2be}\\u{2bc}]';\nexport let unicode_map;\nlet multi_char_reg;\nconst max_char_length = 3;\nconst latin_convert = {};\nconst latin_condensed = {\n    '/': '⁄∕',\n    '0': '߀',\n    \"a\": \"ⱥɐɑ\",\n    \"aa\": \"ꜳ\",\n    \"ae\": \"æǽǣ\",\n    \"ao\": \"ꜵ\",\n    \"au\": \"ꜷ\",\n    \"av\": \"ꜹꜻ\",\n    \"ay\": \"ꜽ\",\n    \"b\": \"ƀɓƃ\",\n    \"c\": \"ꜿƈȼↄ\",\n    \"d\": \"đɗɖᴅƌꮷԁɦ\",\n    \"e\": \"ɛǝᴇɇ\",\n    \"f\": \"ꝼƒ\",\n    \"g\": \"ǥɠꞡᵹꝿɢ\",\n    \"h\": \"ħⱨⱶɥ\",\n    \"i\": \"ɨı\",\n    \"j\": \"ɉȷ\",\n    \"k\": \"ƙⱪꝁꝃꝅꞣ\",\n    \"l\": \"łƚɫⱡꝉꝇꞁɭ\",\n    \"m\": \"ɱɯϻ\",\n    \"n\": \"ꞥƞɲꞑᴎлԉ\",\n    \"o\": \"øǿɔɵꝋꝍᴑ\",\n    \"oe\": \"œ\",\n    \"oi\": \"ƣ\",\n    \"oo\": \"ꝏ\",\n    \"ou\": \"ȣ\",\n    \"p\": \"ƥᵽꝑꝓꝕρ\",\n    \"q\": \"ꝗꝙɋ\",\n    \"r\": \"ɍɽꝛꞧꞃ\",\n    \"s\": \"ßȿꞩꞅʂ\",\n    \"t\": \"ŧƭʈⱦꞇ\",\n    \"th\": \"þ\",\n    \"tz\": \"ꜩ\",\n    \"u\": \"ʉ\",\n    \"v\": \"ʋꝟʌ\",\n    \"vy\": \"ꝡ\",\n    \"w\": \"ⱳ\",\n    \"y\": \"ƴɏỿ\",\n    \"z\": \"ƶȥɀⱬꝣ\",\n    \"hv\": \"ƕ\"\n};\nfor (let latin in latin_condensed) {\n    let unicode = latin_condensed[latin] || '';\n    for (let i = 0; i < unicode.length; i++) {\n        let char = unicode.substring(i, i + 1);\n        latin_convert[char] = latin;\n    }\n}\nconst convert_pat = new RegExp(Object.keys(latin_convert).join('|') + '|' + accent_pat, 'gu');\n/**\n * Initialize the unicode_map from the give code point ranges\n */\nexport const initialize = (_code_points) => {\n    if (unicode_map !== undefined)\n        return;\n    unicode_map = generateMap(_code_points || code_points);\n};\n/**\n * Helper method for normalize a string\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/normalize\n */\nexport const normalize = (str, form = 'NFKD') => str.normalize(form);\n/**\n * Remove accents without reordering string\n * calling str.normalize('NFKD') on \\u{594}\\u{595}\\u{596} becomes \\u{596}\\u{594}\\u{595}\n * via https://github.com/krisk/Fuse/issues/133#issuecomment-318692703\n */\nexport const asciifold = (str) => {\n    return Array.from(str).reduce(\n    /**\n     * @param {string} result\n     * @param {string} char\n     */\n    (result, char) => {\n        return result + _asciifold(char);\n    }, '');\n};\nexport const _asciifold = (str) => {\n    str = normalize(str)\n        .toLowerCase()\n        .replace(convert_pat, (/** @type {string} */ char) => {\n        return latin_convert[char] || '';\n    });\n    //return str;\n    return normalize(str, 'NFC');\n};\n/**\n * Generate a list of unicode variants from the list of code points\n */\nexport function* generator(code_points) {\n    for (const [code_point_min, code_point_max] of code_points) {\n        for (let i = code_point_min; i <= code_point_max; i++) {\n            let composed = String.fromCharCode(i);\n            let folded = asciifold(composed);\n            if (folded == composed.toLowerCase()) {\n                continue;\n            }\n            // skip when folded is a string longer than 3 characters long\n            // bc the resulting regex patterns will be long\n            // eg:\n            // folded صلى الله عليه وسلم length 18 code point 65018\n            // folded جل جلاله length 8 code point 65019\n            if (folded.length > max_char_length) {\n                continue;\n            }\n            if (folded.length == 0) {\n                continue;\n            }\n            yield { folded: folded, composed: composed, code_point: i };\n        }\n    }\n}\n/**\n * Generate a unicode map from the list of code points\n */\nexport const generateSets = (code_points) => {\n    const unicode_sets = {};\n    const addMatching = (folded, to_add) => {\n        /** @type {Set<string>} */\n        const folded_set = unicode_sets[folded] || new Set();\n        const patt = new RegExp('^' + setToPattern(folded_set) + '$', 'iu');\n        if (to_add.match(patt)) {\n            return;\n        }\n        folded_set.add(escape_regex(to_add));\n        unicode_sets[folded] = folded_set;\n    };\n    for (let value of generator(code_points)) {\n        addMatching(value.folded, value.folded);\n        addMatching(value.folded, value.composed);\n    }\n    return unicode_sets;\n};\n/**\n * Generate a unicode map from the list of code points\n * ae => (?:(?:ae|Æ|Ǽ|Ǣ)|(?:A|Ⓐ|Ａ...)(?:E|ɛ|Ⓔ...))\n */\nexport const generateMap = (code_points) => {\n    const unicode_sets = generateSets(code_points);\n    const unicode_map = {};\n    let multi_char = [];\n    for (let folded in unicode_sets) {\n        let set = unicode_sets[folded];\n        if (set) {\n            unicode_map[folded] = setToPattern(set);\n        }\n        if (folded.length > 1) {\n            multi_char.push(escape_regex(folded));\n        }\n    }\n    multi_char.sort((a, b) => b.length - a.length);\n    const multi_char_patt = arrayToPattern(multi_char);\n    multi_char_reg = new RegExp('^' + multi_char_patt, 'u');\n    return unicode_map;\n};\n/**\n * Map each element of an array from its folded value to all possible unicode matches\n */\nexport const mapSequence = (strings, min_replacement = 1) => {\n    let chars_replaced = 0;\n    strings = strings.map((str) => {\n        if (unicode_map[str]) {\n            chars_replaced += str.length;\n        }\n        return unicode_map[str] || str;\n    });\n    if (chars_replaced >= min_replacement) {\n        return sequencePattern(strings);\n    }\n    return '';\n};\n/**\n * Convert a short string and split it into all possible patterns\n * Keep a pattern only if min_replacement is met\n *\n * 'abc'\n * \t\t=> [['abc'],['ab','c'],['a','bc'],['a','b','c']]\n *\t\t=> ['abc-pattern','ab-c-pattern'...]\n */\nexport const substringsToPattern = (str, min_replacement = 1) => {\n    min_replacement = Math.max(min_replacement, str.length - 1);\n    return arrayToPattern(allSubstrings(str).map((sub_pat) => {\n        return mapSequence(sub_pat, min_replacement);\n    }));\n};\n/**\n * Convert an array of sequences into a pattern\n * [{start:0,end:3,length:3,substr:'iii'}...] => (?:iii...)\n */\nconst sequencesToPattern = (sequences, all = true) => {\n    let min_replacement = sequences.length > 1 ? 1 : 0;\n    return arrayToPattern(sequences.map((sequence) => {\n        let seq = [];\n        const len = all ? sequence.length() : sequence.length() - 1;\n        for (let j = 0; j < len; j++) {\n            seq.push(substringsToPattern(sequence.substrs[j] || '', min_replacement));\n        }\n        return sequencePattern(seq);\n    }));\n};\n/**\n * Return true if the sequence is already in the sequences\n */\nconst inSequences = (needle_seq, sequences) => {\n    for (const seq of sequences) {\n        if (seq.start != needle_seq.start || seq.end != needle_seq.end) {\n            continue;\n        }\n        if (seq.substrs.join('') !== needle_seq.substrs.join('')) {\n            continue;\n        }\n        let needle_parts = needle_seq.parts;\n        const filter = (part) => {\n            for (const needle_part of needle_parts) {\n                if (needle_part.start === part.start && needle_part.substr === part.substr) {\n                    return false;\n                }\n                if (part.length == 1 || needle_part.length == 1) {\n                    continue;\n                }\n                // check for overlapping parts\n                // a = ['::=','==']\n                // b = ['::','===']\n                // a = ['r','sm']\n                // b = ['rs','m']\n                if (part.start < needle_part.start && part.end > needle_part.start) {\n                    return true;\n                }\n                if (needle_part.start < part.start && needle_part.end > part.start) {\n                    return true;\n                }\n            }\n            return false;\n        };\n        let filtered = seq.parts.filter(filter);\n        if (filtered.length > 0) {\n            continue;\n        }\n        return true;\n    }\n    return false;\n};\nclass Sequence {\n    parts;\n    substrs;\n    start;\n    end;\n    constructor() {\n        this.parts = [];\n        this.substrs = [];\n        this.start = 0;\n        this.end = 0;\n    }\n    add(part) {\n        if (part) {\n            this.parts.push(part);\n            this.substrs.push(part.substr);\n            this.start = Math.min(part.start, this.start);\n            this.end = Math.max(part.end, this.end);\n        }\n    }\n    last() {\n        return this.parts[this.parts.length - 1];\n    }\n    length() {\n        return this.parts.length;\n    }\n    clone(position, last_piece) {\n        let clone = new Sequence();\n        let parts = JSON.parse(JSON.stringify(this.parts));\n        let last_part = parts.pop();\n        for (const part of parts) {\n            clone.add(part);\n        }\n        let last_substr = last_piece.substr.substring(0, position - last_part.start);\n        let clone_last_len = last_substr.length;\n        clone.add({ start: last_part.start, end: last_part.start + clone_last_len, length: clone_last_len, substr: last_substr });\n        return clone;\n    }\n}\n/**\n * Expand a regular expression pattern to include unicode variants\n * \teg /a/ becomes /aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐɑAⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ/\n *\n * Issue:\n *  ﺊﺋ [ 'ﺊ = \\\\u{fe8a}', 'ﺋ = \\\\u{fe8b}' ]\n *\tbecomes:\tئئ [ 'ي = \\\\u{64a}', 'ٔ = \\\\u{654}', 'ي = \\\\u{64a}', 'ٔ = \\\\u{654}' ]\n *\n *\tİĲ = IIJ = ⅡJ\n *\n * \t1/2/4\n */\nexport const getPattern = (str) => {\n    initialize();\n    str = asciifold(str);\n    let pattern = '';\n    let sequences = [new Sequence()];\n    for (let i = 0; i < str.length; i++) {\n        let substr = str.substring(i);\n        let match = substr.match(multi_char_reg);\n        const char = str.substring(i, i + 1);\n        const match_str = match ? match[0] : null;\n        // loop through sequences\n        // add either the char or multi_match\n        let overlapping = [];\n        let added_types = new Set();\n        for (const sequence of sequences) {\n            const last_piece = sequence.last();\n            if (!last_piece || last_piece.length == 1 || last_piece.end <= i) {\n                // if we have a multi match\n                if (match_str) {\n                    const len = match_str.length;\n                    sequence.add({ start: i, end: i + len, length: len, substr: match_str });\n                    added_types.add('1');\n                }\n                else {\n                    sequence.add({ start: i, end: i + 1, length: 1, substr: char });\n                    added_types.add('2');\n                }\n            }\n            else if (match_str) {\n                let clone = sequence.clone(i, last_piece);\n                const len = match_str.length;\n                clone.add({ start: i, end: i + len, length: len, substr: match_str });\n                overlapping.push(clone);\n            }\n            else {\n                // don't add char\n                // adding would create invalid patterns: 234 => [2,34,4]\n                added_types.add('3');\n            }\n        }\n        // if we have overlapping\n        if (overlapping.length > 0) {\n            // ['ii','iii'] before ['i','i','iii']\n            overlapping = overlapping.sort((a, b) => {\n                return a.length() - b.length();\n            });\n            for (let clone of overlapping) {\n                // don't add if we already have an equivalent sequence\n                if (inSequences(clone, sequences)) {\n                    continue;\n                }\n                sequences.push(clone);\n            }\n            continue;\n        }\n        // if we haven't done anything unique\n        // clean up the patterns\n        // helps keep patterns smaller\n        // if str = 'r₨㎧aarss', pattern will be 446 instead of 655\n        if (i > 0 && added_types.size == 1 && !added_types.has('3')) {\n            pattern += sequencesToPattern(sequences, false);\n            let new_seq = new Sequence();\n            const old_seq = sequences[0];\n            if (old_seq) {\n                new_seq.add(old_seq.last());\n            }\n            sequences = [new_seq];\n        }\n    }\n    pattern += sequencesToPattern(sequences, true);\n    return pattern;\n};\nexport { escape_regex };\n//# sourceMappingURL=index.js.map", "import { asciifold } from '@orchidjs/unicode-variants';\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\nexport const getAttr = (obj, name) => {\n    if (!obj)\n        return;\n    return obj[name];\n};\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\nexport const getAttrNesting = (obj, name) => {\n    if (!obj)\n        return;\n    var part, names = name.split(\".\");\n    while ((part = names.shift()) && (obj = obj[part]))\n        ;\n    return obj;\n};\n/**\n * Calculates how close of a match the\n * given value is against a search token.\n *\n */\nexport const scoreValue = (value, token, weight) => {\n    var score, pos;\n    if (!value)\n        return 0;\n    value = value + '';\n    if (token.regex == null)\n        return 0;\n    pos = value.search(token.regex);\n    if (pos === -1)\n        return 0;\n    score = token.string.length / value.length;\n    if (pos === 0)\n        score += 0.5;\n    return score * weight;\n};\n/**\n * Cast object property to an array if it exists and has a value\n *\n */\nexport const propToArray = (obj, key) => {\n    var value = obj[key];\n    if (typeof value == 'function')\n        return value;\n    if (value && !Array.isArray(value)) {\n        obj[key] = [value];\n    }\n};\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\nexport const iterate = (object, callback) => {\n    if (Array.isArray(object)) {\n        object.forEach(callback);\n    }\n    else {\n        for (var key in object) {\n            if (object.hasOwnProperty(key)) {\n                callback(object[key], key);\n            }\n        }\n    }\n};\nexport const cmp = (a, b) => {\n    if (typeof a === 'number' && typeof b === 'number') {\n        return a > b ? 1 : (a < b ? -1 : 0);\n    }\n    a = asciifold(a + '').toLowerCase();\n    b = asciifold(b + '').toLowerCase();\n    if (a > b)\n        return 1;\n    if (b > a)\n        return -1;\n    return 0;\n};\n//# sourceMappingURL=utils.js.map", "/**\n * sifter.js\n * Copyright (c) 2013–2020 <PERSON> & contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n * <AUTHOR> <<EMAIL>>\n */\nimport { scoreValue, getAttr, getAttrNesting, propToArray, iterate, cmp } from \"./utils.js\";\nimport { getPattern, escape_regex } from '@orchidjs/unicode-variants';\nclass Sifter {\n    items; // []|{};\n    settings;\n    /**\n     * Textually searches arrays and hashes of objects\n     * by property (or multiple properties). Designed\n     * specifically for autocomplete.\n     *\n     */\n    constructor(items, settings) {\n        this.items = items;\n        this.settings = settings || { diacritics: true };\n    }\n    ;\n    /**\n     * Splits a search string into an array of individual\n     * regexps to be used to match results.\n     *\n     */\n    tokenize(query, respect_word_boundaries, weights) {\n        if (!query || !query.length)\n            return [];\n        const tokens = [];\n        const words = query.split(/\\s+/);\n        var field_regex;\n        if (weights) {\n            field_regex = new RegExp('^(' + Object.keys(weights).map(escape_regex).join('|') + ')\\:(.*)$');\n        }\n        words.forEach((word) => {\n            let field_match;\n            let field = null;\n            let regex = null;\n            // look for \"field:query\" tokens\n            if (field_regex && (field_match = word.match(field_regex))) {\n                field = field_match[1];\n                word = field_match[2];\n            }\n            if (word.length > 0) {\n                if (this.settings.diacritics) {\n                    regex = getPattern(word) || null;\n                }\n                else {\n                    regex = escape_regex(word);\n                }\n                if (regex && respect_word_boundaries)\n                    regex = \"\\\\b\" + regex;\n            }\n            tokens.push({\n                string: word,\n                regex: regex ? new RegExp(regex, 'iu') : null,\n                field: field,\n            });\n        });\n        return tokens;\n    }\n    ;\n    /**\n     * Returns a function to be used to score individual results.\n     *\n     * Good matches will have a higher score than poor matches.\n     * If an item is not a match, 0 will be returned by the function.\n     *\n     * @returns {T.ScoreFn}\n     */\n    getScoreFunction(query, options) {\n        var search = this.prepareSearch(query, options);\n        return this._getScoreFunction(search);\n    }\n    /**\n     * @returns {T.ScoreFn}\n     *\n     */\n    _getScoreFunction(search) {\n        const tokens = search.tokens, token_count = tokens.length;\n        if (!token_count) {\n            return function () { return 0; };\n        }\n        const fields = search.options.fields, weights = search.weights, field_count = fields.length, getAttrFn = search.getAttrFn;\n        if (!field_count) {\n            return function () { return 1; };\n        }\n        /**\n         * Calculates the score of an object\n         * against the search query.\n         *\n         */\n        const scoreObject = (function () {\n            if (field_count === 1) {\n                return function (token, data) {\n                    const field = fields[0].field;\n                    return scoreValue(getAttrFn(data, field), token, weights[field] || 1);\n                };\n            }\n            return function (token, data) {\n                var sum = 0;\n                // is the token specific to a field?\n                if (token.field) {\n                    const value = getAttrFn(data, token.field);\n                    if (!token.regex && value) {\n                        sum += (1 / field_count);\n                    }\n                    else {\n                        sum += scoreValue(value, token, 1);\n                    }\n                }\n                else {\n                    iterate(weights, (weight, field) => {\n                        sum += scoreValue(getAttrFn(data, field), token, weight);\n                    });\n                }\n                return sum / field_count;\n            };\n        })();\n        if (token_count === 1) {\n            return function (data) {\n                return scoreObject(tokens[0], data);\n            };\n        }\n        if (search.options.conjunction === 'and') {\n            return function (data) {\n                var score, sum = 0;\n                for (let token of tokens) {\n                    score = scoreObject(token, data);\n                    if (score <= 0)\n                        return 0;\n                    sum += score;\n                }\n                return sum / token_count;\n            };\n        }\n        else {\n            return function (data) {\n                var sum = 0;\n                iterate(tokens, (token) => {\n                    sum += scoreObject(token, data);\n                });\n                return sum / token_count;\n            };\n        }\n    }\n    ;\n    /**\n     * Returns a function that can be used to compare two\n     * results, for sorting purposes. If no sorting should\n     * be performed, `null` will be returned.\n     *\n     * @return function(a,b)\n     */\n    getSortFunction(query, options) {\n        var search = this.prepareSearch(query, options);\n        return this._getSortFunction(search);\n    }\n    _getSortFunction(search) {\n        var implicit_score, sort_flds = [];\n        const self = this, options = search.options, sort = (!search.query && options.sort_empty) ? options.sort_empty : options.sort;\n        if (typeof sort == 'function') {\n            return sort.bind(this);\n        }\n        /**\n         * Fetches the specified sort field value\n         * from a search result item.\n         *\n         */\n        const get_field = function (name, result) {\n            if (name === '$score')\n                return result.score;\n            return search.getAttrFn(self.items[result.id], name);\n        };\n        // parse options\n        if (sort) {\n            for (let s of sort) {\n                if (search.query || s.field !== '$score') {\n                    sort_flds.push(s);\n                }\n            }\n        }\n        // the \"$score\" field is implied to be the primary\n        // sort field, unless it's manually specified\n        if (search.query) {\n            implicit_score = true;\n            for (let fld of sort_flds) {\n                if (fld.field === '$score') {\n                    implicit_score = false;\n                    break;\n                }\n            }\n            if (implicit_score) {\n                sort_flds.unshift({ field: '$score', direction: 'desc' });\n            }\n            // without a search.query, all items will have the same score\n        }\n        else {\n            sort_flds = sort_flds.filter((fld) => fld.field !== '$score');\n        }\n        // build function\n        const sort_flds_count = sort_flds.length;\n        if (!sort_flds_count) {\n            return null;\n        }\n        return function (a, b) {\n            var result, field;\n            for (let sort_fld of sort_flds) {\n                field = sort_fld.field;\n                let multiplier = sort_fld.direction === 'desc' ? -1 : 1;\n                result = multiplier * cmp(get_field(field, a), get_field(field, b));\n                if (result)\n                    return result;\n            }\n            return 0;\n        };\n    }\n    ;\n    /**\n     * Parses a search query and returns an object\n     * with tokens and fields ready to be populated\n     * with results.\n     *\n     */\n    prepareSearch(query, optsUser) {\n        const weights = {};\n        var options = Object.assign({}, optsUser);\n        propToArray(options, 'sort');\n        propToArray(options, 'sort_empty');\n        // convert fields to new format\n        if (options.fields) {\n            propToArray(options, 'fields');\n            const fields = [];\n            options.fields.forEach((field) => {\n                if (typeof field == 'string') {\n                    field = { field: field, weight: 1 };\n                }\n                fields.push(field);\n                weights[field.field] = ('weight' in field) ? field.weight : 1;\n            });\n            options.fields = fields;\n        }\n        return {\n            options: options,\n            query: query.toLowerCase().trim(),\n            tokens: this.tokenize(query, options.respect_word_boundaries, weights),\n            total: 0,\n            items: [],\n            weights: weights,\n            getAttrFn: (options.nesting) ? getAttrNesting : getAttr,\n        };\n    }\n    ;\n    /**\n     * Searches through all items and returns a sorted array of matches.\n     *\n     */\n    search(query, options) {\n        var self = this, score, search;\n        search = this.prepareSearch(query, options);\n        options = search.options;\n        query = search.query;\n        // generate result scoring function\n        const fn_score = options.score || self._getScoreFunction(search);\n        // perform search and sort\n        if (query.length) {\n            iterate(self.items, (item, id) => {\n                score = fn_score(item);\n                if (options.filter === false || score > 0) {\n                    search.items.push({ 'score': score, 'id': id });\n                }\n            });\n        }\n        else {\n            iterate(self.items, (_, id) => {\n                search.items.push({ 'score': 1, 'id': id });\n            });\n        }\n        const fn_sort = self._getSortFunction(search);\n        if (fn_sort)\n            search.items.sort(fn_sort);\n        // apply limits\n        search.total = search.items.length;\n        if (typeof options.limit === 'number') {\n            search.items = search.items.slice(0, options.limit);\n        }\n        return search;\n    }\n    ;\n}\nexport { Sifter, scoreValue, getAttr, getAttrNesting, propToArray, iterate, cmp, getPattern };\nexport * from \"./types.js\";\n//# sourceMappingURL=sifter.js.map", "\nimport type TomSelect from './tom-select.ts';\nimport { TomLoadCallback } from './types/index.ts';\n\n\n/**\n * Converts a scalar to its best string representation\n * for hash keys and HTML attribute values.\n *\n * Transformations:\n *   'str'     -> 'str'\n *   null      -> ''\n *   undefined -> ''\n *   true      -> '1'\n *   false     -> '0'\n *   0         -> '0'\n *   1         -> '1'\n *\n */\nexport const hash_key = (value:undefined|null|boolean|string|number):string|null => {\n\tif (typeof value === 'undefined' || value === null) return null;\n\treturn get_hash(value);\n};\n\nexport const get_hash = (value:boolean|string|number):string => {\n\tif (typeof value === 'boolean') return value ? '1' : '0';\n\treturn value + '';\n};\n\n/**\n * Escapes a string for use within HTML.\n *\n */\nexport const escape_html = (str:string):string => {\n\treturn (str + '')\n\t\t.replace(/&/g, '&amp;')\n\t\t.replace(/</g, '&lt;')\n\t\t.replace(/>/g, '&gt;')\n\t\t.replace(/\"/g, '&quot;');\n};\n\n\n/**\n * use setTimeout if timeout > 0 \n */\nexport const timeout = (fn:()=>void,timeout:number): number | null => {\n\tif( timeout > 0 ){\n\t\treturn window.setTimeout(fn,timeout);\n\t}\n\n\tfn.call(null);\n\treturn null;\n}\n\n/**\n * Debounce the user provided load function\n *\n */\nexport const loadDebounce = (fn:(value:string,callback:TomLoadCallback) => void,delay:number) => {\n\tvar timeout: null|ReturnType<typeof setTimeout>;\n\treturn function(this:TomSelect, value:string,callback:TomLoadCallback) {\n\t\tvar self = this;\n\n\t\tif( timeout ){\n\t\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\t\tclearTimeout(timeout);\n\t\t}\n\t\ttimeout = setTimeout(function() {\n\t\t\ttimeout = null;\n\t\t\tself.loadedSearches[value] = true;\n\t\t\tfn.call(self, value, callback);\n\n\t\t}, delay);\n\t};\n};\n\n\n/**\n * Debounce all fired events types listed in `types`\n * while executing the provided `fn`.\n *\n */\nexport const debounce_events = ( self:TomSelect, types:string[], fn:() => void ) => {\n\tvar type:string;\n\tvar trigger = self.trigger;\n\tvar event_args:{ [key: string]: any } = {};\n\n\t// override trigger method\n\tself.trigger = function(){\n\t\tvar type = arguments[0];\n\t\tif (types.indexOf(type) !== -1) {\n\t\t\tevent_args[type] = arguments;\n\t\t} else {\n\t\t\treturn trigger.apply(self, arguments);\n\t\t}\n\t};\n\n\t// invoke provided function\n\tfn.apply(self, []);\n\tself.trigger = trigger;\n\n\t// trigger queued events\n\tfor( type of types ){\n\t\tif( type in event_args ){\n\t\t\ttrigger.apply(self, event_args[type]);\n\t\t}\n\t}\n};\n\n\n/**\n * Determines the current selection within a text input control.\n * Returns an object containing:\n *   - start\n *   - length\n *\n * Note: \"selectionStart, selectionEnd ... apply only to inputs of types text, search, URL, tel and password\"\n * \t- https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange\n */\nexport const getSelection = (input:HTMLInputElement):{ start: number; length: number } => {\n\treturn {\n\t\tstart\t: input.selectionStart || 0,\n\t\tlength\t: (input.selectionEnd||0) - (input.selectionStart||0),\n\t};\n};\n\n\n/**\n * Prevent default\n *\n */\nexport const preventDefault = (evt?:Event, stop:boolean=false):void => {\n\tif( evt ){\n\t\tevt.preventDefault();\n\t\tif( stop ){\n\t\t\tevt.stopPropagation();\n\t\t}\n\t}\n}\n\n\n/**\n * Add event helper\n *\n */\nexport const addEvent = (target:EventTarget, type:string, callback:EventListenerOrEventListenerObject, options?:object):void => {\n\ttarget.addEventListener(type,callback,options);\n};\n\n\n/**\n * Return true if the requested key is down\n * Will return false if more than one control character is pressed ( when [ctrl+shift+a] != [ctrl+a] )\n * The current evt may not always set ( eg calling advanceSelection() )\n *\n */\nexport const isKeyDown = ( key_name:keyof (KeyboardEvent|MouseEvent), evt?:KeyboardEvent|MouseEvent ) => {\n\n\tif( !evt ){\n\t\treturn false;\n\t}\n\n\tif( !evt[key_name] ){\n\t\treturn false;\n\t}\n\n\tvar count = (evt.altKey?1:0) + (evt.ctrlKey?1:0) + (evt.shiftKey?1:0) + (evt.metaKey?1:0);\n\n\tif( count === 1 ){\n\t\treturn true;\n\t}\n\n\treturn false;\n};\n\n\n/**\n * Get the id of an element\n * If the id attribute is not set, set the attribute with the given id\n *\n */\nexport const getId = (el:Element,id:string) => {\n\tconst existing_id = el.getAttribute('id');\n\tif( existing_id ){\n\t\treturn existing_id;\n\t}\n\n\tel.setAttribute('id',id);\n\treturn id;\n};\n\n\n/**\n * Returns a string with backslashes added before characters that need to be escaped.\n */\nexport const addSlashes = (str:string):string => {\n\treturn str.replace(/[\\\\\"']/g, '\\\\$&');\n};\n\n/**\n *\n */\nexport const append = ( parent:Element|DocumentFragment, node: string|Node|null|undefined ):void =>{\n\tif( node ) parent.append(node);\n};\n\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\nexport const iterate = (object:[]|{[key:string]:any}, callback:(value:any,key:any)=>any) => {\n\n\tif ( Array.isArray(object)) {\n\t\tobject.forEach(callback);\n\n\t}else{\n\n\t\tfor (var key in object) {\n\t\t\tif (object.hasOwnProperty(key)) {\n\t\t\t\tcallback(object[key], key);\n\t\t\t}\n\t\t}\n\t}\n};\n", "\nimport { iterate } from './utils.ts';\n\n/**\n * Return a dom element from either a dom query string, jQuery object, a dom element or html string\n * https://stackoverflow.com/questions/494143/creating-a-new-dom-element-from-an-html-string-using-built-in-dom-methods-or-pro/35385518#35385518\n *\n * param query should be {}\n */\nexport const getDom = ( query:any ):HTMLElement => {\n\n\tif( query.jquery ){\n\t\treturn query[0];\n\t}\n\n\tif( query instanceof HTMLElement ){\n\t\treturn query;\n\t}\n\n\tif( isHtmlString(query) ){\n\t\tvar tpl = document.createElement('template');\n\t\ttpl.innerHTML = query.trim(); // Never return a text node of whitespace as the result\n\t\treturn tpl.content.firstChild as HTMLElement;\n\t}\n\n\treturn document.querySelector(query);\n};\n\nexport const isHtmlString = (arg:any): boolean => {\n\tif( typeof arg === 'string' && arg.indexOf('<') > -1 ){\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nexport const escapeQuery = (query:string):string => {\n\treturn query.replace(/['\"\\\\]/g, '\\\\$&');\n}\n\n/**\n * Dispatch an event\n *\n */\nexport const triggerEvent = ( dom_el:HTMLElement, event_name:string ):void => {\n\tvar event = document.createEvent('HTMLEvents');\n\tevent.initEvent(event_name, true, false);\n\tdom_el.dispatchEvent(event)\n};\n\n/**\n * Apply CSS rules to a dom element\n *\n */\nexport const applyCSS = ( dom_el:HTMLElement, css:{ [key: string]: string|number }):void => {\n\tObject.assign(dom_el.style, css);\n}\n\n\n/**\n * Add css classes\n *\n */\nexport const addClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n\tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map( cls => {\n\t\t\tel.classList.add( cls );\n\t\t});\n\t});\n}\n\n/**\n * Remove css classes\n *\n */\n export const removeClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n \tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map(cls => {\n\t \t\tel.classList.remove( cls );\n\t\t});\n \t});\n }\n\n\n/**\n * Return arguments\n *\n */\nexport const classesArray = (args:string[]|string[][]):string[] => {\n\tvar classes:string[] = [];\n\titerate( args, (_classes) =>{\n\t\tif( typeof _classes === 'string' ){\n\t\t\t_classes = _classes.trim().split(/[\\t\\n\\f\\r\\s]/);\n\t\t}\n\t\tif( Array.isArray(_classes) ){\n\t\t\tclasses = classes.concat(_classes);\n\t\t}\n\t});\n\n\treturn classes.filter(Boolean);\n}\n\n\n/**\n * Create an array from arg if it's not already an array\n *\n */\nexport const castAsArray = (arg:any):Array<any> => {\n\tif( !Array.isArray(arg) ){\n \t\targ = [arg];\n \t}\n\treturn arg;\n}\n\n\n/**\n * Get the closest node to the evt.target matching the selector\n * Stops at wrapper\n *\n */\nexport const parentMatch = ( target:null|HTMLElement, selector:string, wrapper?:HTMLElement ):HTMLElement|void => {\n\n\tif( wrapper && !wrapper.contains(target) ){\n\t\treturn;\n\t}\n\n\twhile( target && target.matches ){\n\n\t\tif( target.matches(selector) ){\n\t\t\treturn target;\n\t\t}\n\n\t\ttarget = target.parentNode as HTMLElement;\n\t}\n}\n\n\n/**\n * Get the first or last item from an array\n *\n * > 0 - right (last)\n * <= 0 - left (first)\n *\n */\nexport const getTail = ( list:Array<any>|NodeList, direction:number=0 ):any => {\n\n\tif( direction > 0 ){\n\t\treturn list[list.length-1];\n\t}\n\n\treturn list[0];\n}\n\n/**\n * Return true if an object is empty\n *\n */\nexport const isEmptyObject = (obj:object):boolean => {\n\treturn (Object.keys(obj).length === 0);\n}\n\n\n/**\n * Get the index of an element amongst sibling nodes of the same type\n *\n */\nexport const nodeIndex = ( el:null|Element, amongst?:string ):number => {\n\tif (!el) return -1;\n\n\tamongst = amongst || el.nodeName;\n\n\tvar i = 0;\n\twhile( el = el.previousElementSibling ){\n\n\t\tif( el.matches(amongst) ){\n\t\t\ti++;\n\t\t}\n\t}\n\treturn i;\n}\n\n\n/**\n * Set attributes of an element\n *\n */\nexport const setAttr = (el:Element,attrs:{ [key: string]: null|string|number }) => {\n\titerate( attrs,(val,attr) => {\n\t\tif( val == null ){\n\t\t\tel.removeAttribute(attr as string);\n\t\t}else{\n\t\t\tel.setAttribute(attr as string, ''+val);\n\t\t}\n\t});\n}\n\n\n/**\n * Replace a node\n */\nexport const replaceNode = ( existing:Node, replacement:Node ) => {\n\tif( existing.parentNode ) existing.parentNode.replaceChild(replacement, existing);\n}\n", "/**\n * highlight v3 | MIT license | <PERSON> <<EMAIL>>\n * Highlights arbitrary terms in a node.\n *\n * - Modified by <PERSON> <<EMAIL>> 2011-6-24 (added regex)\n * - Modified by <PERSON> <<EMAIL>> 2012-8-27 (cleanup)\n */\n\nimport {replaceNode} from '../vanilla.ts';\n\n\nexport const highlight = (element:HTMLElement, regex:string|RegExp) => {\n\n\tif( regex === null ) return;\n\n\t// convet string to regex\n\tif( typeof regex === 'string' ){\n\n\t\tif( !regex.length ) return;\n\t\tregex = new RegExp(regex, 'i');\n\t}\n\n\n\t// Wrap matching part of text node with highlighting <span>, e.g.\n\t// Soccer  ->  <span class=\"highlight\">Soc</span>cer  for regex = /soc/i\n\tconst highlightText = ( node:Text ):number => {\n\n\t\tvar match = node.data.match(regex);\n\t\tif( match && node.data.length > 0 ){\n\t\t\tvar spannode\t\t= document.createElement('span');\n\t\t\tspannode.className\t= 'highlight';\n\t\t\tvar middlebit\t\t= node.splitText(match.index as number);\n\n\t\t\tmiddlebit.splitText(match[0]!.length);\n\t\t\tvar middleclone\t\t= middlebit.cloneNode(true);\n\n\t\t\tspannode.appendChild(middleclone);\n\t\t\treplaceNode(middlebit, spannode);\n\t\t\treturn 1;\n\t\t}\n\n\t\treturn 0;\n\t};\n\n\t// Recurse element node, looking for child text nodes to highlight, unless element\n\t// is childless, <script>, <style>, or already highlighted: <span class=\"hightlight\">\n\tconst highlightChildren = ( node:Element ):void => {\n\t\tif( node.nodeType === 1 && node.childNodes && !/(script|style)/i.test(node.tagName) && ( node.className !== 'highlight' || node.tagName !== 'SPAN' ) ){\n\t\t\tArray.from(node.childNodes).forEach(element => {\n\t\t\t\thighlightRecursive(element);\n\t\t\t});\n\t\t}\n\t};\n\n\n\tconst highlightRecursive = ( node:Node|Element ):number => {\n\n\t\tif( node.nodeType === 3 ){\n\t\t\treturn highlightText(node as Text);\n\t\t}\n\n\t\thighlightChildren(node as Element);\n\n\t\treturn 0;\n\t};\n\n\thighlightRecursive( element );\n};\n\n/**\n * removeHighlight fn copied from highlight v5 and\n * edited to remove with(), pass js strict mode, and use without jquery\n */\nexport const removeHighlight = (el:HTMLElement) => {\n\tvar elements = el.querySelectorAll(\"span.highlight\");\n\tArray.prototype.forEach.call(elements, function(el:HTMLElement){\n\t\tvar parent = el.parentNode as Node;\n\t\tparent.replaceChild(el.firstChild as Node, el);\n\t\tparent.normalize();\n\t});\n};\n", "export const KEY_A\t\t\t\t= 65;\nexport const KEY_RETURN\t\t\t= 13;\nexport const KEY_ESC\t\t\t= 27;\nexport const KEY_LEFT\t\t\t= 37;\nexport const KEY_UP\t\t\t\t= 38;\nexport const KEY_RIGHT\t\t\t= 39;\nexport const KEY_DOWN\t\t\t= 40;\nexport const KEY_BACKSPACE\t\t= 8;\nexport const KEY_DELETE\t\t\t= 46;\nexport const KEY_TAB\t\t\t= 9;\n\nexport const IS_MAC      \t\t= typeof navigator === 'undefined' ? false : /Mac/.test(navigator.userAgent);\nexport const KEY_SHORTCUT\t\t= IS_MAC ? 'metaKey' : 'ctrlKey'; // ctrl key or apple key for ma\n", "\nexport default {\n\toptions: [],\n\toptgroups: [],\n\n\tplugins: [],\n\tdelimiter: ',',\n\tsplitOn: null, // regexp or string for splitting up values from a paste command\n\tpersist: true,\n\tdiacritics: true,\n\tcreate: null,\n\tcreateOnBlur: false,\n\tcreateFilter: null,\n\thighlight: true,\n\topenOnFocus: true,\n\tshouldOpen: null,\n\tmaxOptions: 50,\n\tmaxItems: null,\n\thideSelected: null,\n\tduplicates: false,\n\taddPrecedence: false,\n\tselectOnTab: false,\n\tpreload: null,\n\tallowEmptyOption: false,\n\t//closeAfterSelect: false,\n\trefreshThrottle: 300,\n\n\n\tloadThrottle: 300,\n\tloadingClass: 'loading',\n\n\tdataAttr: null, //'data-data',\n\toptgroupField: 'optgroup',\n\tvalueField: 'value',\n\tlabelField: 'text',\n\tdisabledField: 'disabled',\n\toptgroupLabelField: 'label',\n\toptgroupValueField: 'value',\n\tlockOptgroupOrder: false,\n\n\tsortField: '$order',\n\tsearchField: ['text'],\n\tsearchConjunction: 'and',\n\n\tmode: null,\n\twrapperClass: 'ts-wrapper',\n\tcontrolClass: 'ts-control',\n\tdropdownClass: 'ts-dropdown',\n\tdropdownContentClass: 'ts-dropdown-content',\n\titemClass: 'item',\n\toptionClass: 'option',\n\n\tdropdownParent: null,\n\tcontrolInput: '<input type=\"text\" autocomplete=\"off\" size=\"1\" />',\n\n\tcopyClassesToDropdown: false,\n\n\tplaceholder: null,\n\thidePlaceholder: null,\n\n\tshouldLoad: function(query:string):boolean{\n\t\treturn query.length > 0;\n\t},\n\n\t/*\n\tload                 : null, // function(query, callback) { ... }\n\tscore                : null, // function(search) { ... }\n\tonInitialize         : null, // function() { ... }\n\tonChange             : null, // function(value) { ... }\n\tonItemAdd            : null, // function(value, $item) { ... }\n\tonItemRemove         : null, // function(value) { ... }\n\tonClear              : null, // function() { ... }\n\tonOptionAdd          : null, // function(value, data) { ... }\n\tonOptionRemove       : null, // function(value) { ... }\n\tonOptionClear        : null, // function() { ... }\n\tonOptionGroupAdd     : null, // function(id, data) { ... }\n\tonOptionGroupRemove  : null, // function(id) { ... }\n\tonOptionGroupClear   : null, // function() { ... }\n\tonDropdownOpen       : null, // function(dropdown) { ... }\n\tonDropdownClose      : null, // function(dropdown) { ... }\n\tonType               : null, // function(str) { ... }\n\tonDelete             : null, // function(values) { ... }\n\t*/\n\n\trender: {\n\t\t/*\n\t\titem: null,\n\t\toptgroup: null,\n\t\toptgroup_header: null,\n\t\toption: null,\n\t\toption_create: null\n\t\t*/\n\t}\n};\n", "import defaults from './defaults.ts';\nimport { hash_key, iterate } from './utils.ts';\nimport { TomOption, TomSettings, RecursivePartial } from './types/index.ts';\nimport { TomInput } from './types/index.ts';\n\n\nexport default function getSettings( input:TomInput, settings_user:RecursivePartial<TomSettings>):TomSettings{\n\tvar settings:TomSettings\t= Object.assign({}, defaults, settings_user);\n\n\tvar attr_data\t\t\t\t= settings.dataAttr;\n\tvar field_label\t\t\t\t= settings.labelField;\n\tvar field_value\t\t\t\t= settings.valueField;\n\tvar field_disabled\t\t\t= settings.disabledField;\n\tvar field_optgroup\t\t\t= settings.optgroupField;\n\tvar field_optgroup_label\t= settings.optgroupLabelField;\n\tvar field_optgroup_value\t= settings.optgroupValueField;\n\n\tvar tag_name\t\t\t\t= input.tagName.toLowerCase();\n\tvar placeholder\t\t\t\t= input.getAttribute('placeholder') || input.getAttribute('data-placeholder');\n\n\tif (!placeholder && !settings.allowEmptyOption) {\n\t\tlet option\t\t= input.querySelector('option[value=\"\"]');\n\t\tif( option ){\n\t\t\tplaceholder = option.textContent;\n\t\t}\n\n\t}\n\n\tvar settings_element:{\n\t\tplaceholder\t: null|string,\n\t\toptions\t\t: TomOption[],\n\t\toptgroups\t: TomOption[],\n\t\titems\t\t: string[],\n\t\tmaxItems\t: null|number,\n\t} = {\n\t\tplaceholder\t: placeholder,\n\t\toptions\t\t: [],\n\t\toptgroups\t: [],\n\t\titems\t\t: [],\n\t\tmaxItems\t: null,\n\t};\n\n\n\t/**\n\t * Initialize from a <select> element.\n\t *\n\t */\n\tvar init_select = () => {\n\t\tvar tagName;\n\t\tvar options = settings_element.options;\n\t\tvar optionsMap:{[key:string]:any} = {};\n\t\tvar group_count = 1;\n\t\tlet $order = 0;\n\n\t\tvar readData = (el:HTMLElement):TomOption => {\n\n\t\t\tvar data\t= Object.assign({},el.dataset); // get plain object from DOMStringMap\n\t\t\tvar json\t= attr_data && data[attr_data];\n\n\t\t\tif( typeof json === 'string' && json.length ){\n\t\t\t\tdata = Object.assign(data,JSON.parse(json));\n\t\t\t}\n\n\t\t\treturn data;\n\t\t};\n\n\t\tvar addOption = (option:HTMLOptionElement, group?:string) => {\n\n\t\t\tvar value = hash_key(option.value);\n\t\t\tif ( value == null ) return;\n\t\t\tif ( !value && !settings.allowEmptyOption) return;\n\n\t\t\t// if the option already exists, it's probably been\n\t\t\t// duplicated in another optgroup. in this case, push\n\t\t\t// the current group to the \"optgroup\" property on the\n\t\t\t// existing option so that it's rendered in both places.\n\t\t\tif (optionsMap.hasOwnProperty(value)) {\n\t\t\t\tif (group) {\n\t\t\t\t\tvar arr = optionsMap[value][field_optgroup];\n\t\t\t\t\tif (!arr) {\n\t\t\t\t\t\toptionsMap[value][field_optgroup] = group;\n\t\t\t\t\t} else if (!Array.isArray(arr)) {\n\t\t\t\t\t\toptionsMap[value][field_optgroup] = [arr, group];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tarr.push(group);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t}else{\n\n\t\t\t\tvar option_data             = readData(option);\n\t\t\t\toption_data[field_label]    = option_data[field_label] || option.textContent;\n\t\t\t\toption_data[field_value]    = option_data[field_value] || value;\n\t\t\t\toption_data[field_disabled] = option_data[field_disabled] || option.disabled;\n\t\t\t\toption_data[field_optgroup] = option_data[field_optgroup] || group;\n\t\t\t\toption_data.$option\t\t\t= option;\n\t\t\t\toption_data.$order\t\t\t= option_data.$order || ++$order;\n\n\t\t\t\toptionsMap[value] = option_data;\n\t\t\t\toptions.push(option_data);\n\t\t\t}\n\n\t\t\tif( option.selected ){\n\t\t\t\tsettings_element.items.push(value);\n\t\t\t}\n\t\t};\n\n\t\tvar addGroup = ( optgroup:HTMLOptGroupElement ) => {\n\t\t\tvar id:string, optgroup_data\n\n\t\t\toptgroup_data\t\t\t\t\t\t\t= readData(optgroup);\n\t\t\toptgroup_data[field_optgroup_label]\t\t= optgroup_data[field_optgroup_label] || optgroup.getAttribute('label') || '';\n\t\t\toptgroup_data[field_optgroup_value]\t\t= optgroup_data[field_optgroup_value] || group_count++;\n\t\t\toptgroup_data[field_disabled]\t\t\t= optgroup_data[field_disabled] || optgroup.disabled;\n\t\t\toptgroup_data.$order\t\t\t\t\t= optgroup_data.$order || ++$order;\n\n\t\t\tsettings_element.optgroups.push(optgroup_data);\n\n\t\t\tid = optgroup_data[field_optgroup_value];\n\n\t\t\titerate(optgroup.children, (option)=>{\n\t\t\t\taddOption(option as HTMLOptionElement, id);\n\t\t\t});\n\n\t\t};\n\n\t\tsettings_element.maxItems = input.hasAttribute('multiple') ? null : 1;\n\n\t\titerate(input.children,(child)=>{\n\t\t\ttagName = child.tagName.toLowerCase();\n\t\t\tif (tagName === 'optgroup') {\n\t\t\t\taddGroup(child as HTMLOptGroupElement);\n\t\t\t} else if (tagName === 'option') {\n\t\t\t\taddOption(child as HTMLOptionElement);\n\t\t\t}\n\t\t});\n\n\t};\n\n\n\t/**\n\t * Initialize from a <input type=\"text\"> element.\n\t *\n\t */\n\tvar init_textbox = () => {\n\t\tconst data_raw = input.getAttribute(attr_data);\n\n\t\tif (!data_raw) {\n\t\t\tvar value = input.value.trim() || '';\n\t\t\tif (!settings.allowEmptyOption && !value.length) return;\n\t\t\tconst values = value.split(settings.delimiter);\n\n\t\t\titerate( values, (value) => {\n\t\t\t\tconst option:TomOption = {};\n\t\t\t\toption[field_label] = value;\n\t\t\t\toption[field_value] = value;\n\t\t\t\tsettings_element.options.push(option);\n\t\t\t});\n\t\t\tsettings_element.items = values;\n\t\t} else {\n\t\t\tsettings_element.options = JSON.parse(data_raw);\n\t\t\titerate( settings_element.options, (opt) => {\n\t\t\t\tsettings_element.items.push(opt[field_value]);\n\t\t\t});\n\t\t}\n\t};\n\n\n\tif (tag_name === 'select') {\n\t\tinit_select();\n\t} else {\n\t\tinit_textbox();\n\t}\n\n\treturn Object.assign( {}, defaults, settings_element, settings_user) as TomSettings;\n};\n", "\nimport MicroEvent from './contrib/microevent.ts';\nimport MicroPlugin from './contrib/microplugin.ts';\nimport { Sifter } from '@orchidjs/sifter';\nimport { escape_regex } from '@orchidjs/unicode-variants';\nimport { TomInput, TomArgObject, TomOption, TomOptions, TomCreateFilter, TomCreateCallback, TomItem, TomSettings, TomTemplateNames, TomClearFilter, RecursivePartial } from './types/index.ts';\nimport {highlight, removeHighlight} from './contrib/highlight.ts';\nimport * as constants from './constants.ts';\nimport getSettings from './getSettings.ts';\nimport {\n\thash_key,\n\tget_hash,\n\tescape_html,\n\tdebounce_events,\n\tgetSelection,\n\tpreventDefault,\n\taddEvent,\n\tloadDebounce,\n\ttimeout,\n\tisKeyDown,\n\tgetId,\n\taddSlashes,\n\tappend,\n\titerate\n} from './utils.ts';\n\nimport {\n\tgetDom,\n\tisHtmlString,\n\tescapeQuery,\n\ttriggerEvent,\n\tapplyCSS,\n\taddClasses,\n\tremoveClasses,\n\tparentMatch,\n\tgetTail,\n\tisEmptyObject,\n\tnodeIndex,\n\tsetAttr,\n\treplaceNode\n} from './vanilla.ts';\n\nvar instance_i = 0;\n\nexport default class TomSelect extends MicroPlugin(MicroEvent){\n\n\tpublic control_input\t\t\t: HTMLInputElement;\n\tpublic wrapper\t\t\t\t\t: HTMLElement;\n\tpublic dropdown\t\t\t\t\t: HTMLElement;\n\tpublic control\t\t\t\t\t: HTMLElement;\n\tpublic dropdown_content\t\t\t: HTMLElement;\n\tpublic focus_node\t\t\t\t: HTMLElement;\n\n\tpublic order\t\t\t\t\t: number = 0;\n\tpublic settings\t\t\t\t\t: TomSettings;\n\tpublic input\t\t\t\t\t: TomInput;\n\tpublic tabIndex\t\t\t\t\t: number;\n\tpublic is_select_tag\t\t\t: boolean;\n\tpublic rtl\t\t\t\t\t\t: boolean;\n\tprivate inputId\t\t\t\t\t: string;\n\n\tprivate _destroy\t\t\t\t!: () => void;\n\tpublic sifter\t\t\t\t\t: Sifter;\n\n\n\tpublic isOpen\t\t\t\t\t: boolean = false;\n\tpublic isDisabled\t\t\t\t: boolean = false;\n\tpublic isReadOnly\t\t\t\t: boolean = false;\n\tpublic isRequired\t\t\t\t: boolean;\n\tpublic isInvalid\t\t\t\t: boolean = false; // @deprecated 1.8\n\tpublic isValid\t\t\t\t\t: boolean = true;\n\tpublic isLocked\t\t\t\t\t: boolean = false;\n\tpublic isFocused\t\t\t\t: boolean = false;\n\tpublic isInputHidden\t\t\t: boolean = false;\n\tpublic isSetup\t\t\t\t\t: boolean = false;\n\tpublic ignoreFocus\t\t\t\t: boolean = false;\n\tpublic ignoreHover\t\t\t\t: boolean = false;\n\tpublic hasOptions\t\t\t\t: boolean = false;\n\tpublic currentResults\t\t\t?: ReturnType<Sifter['search']>;\n\tpublic lastValue\t\t\t\t: string = '';\n\tpublic caretPos\t\t\t\t\t: number = 0;\n\tpublic loading\t\t\t\t\t: number = 0;\n\tpublic loadedSearches\t\t\t: { [key: string]: boolean } = {};\n\n\tpublic activeOption\t\t\t\t: null|HTMLElement = null;\n\tpublic activeItems\t\t\t\t: TomItem[] = [];\n\n\tpublic optgroups\t\t\t\t: TomOptions = {};\n\tpublic options\t\t\t\t\t: TomOptions = {};\n\tpublic userOptions\t\t\t\t: {[key:string]:boolean} = {};\n\tpublic items\t\t\t\t\t: string[] = [];\n\n\tprivate refreshTimeout\t\t\t: null|number = null;\n\n\n\tconstructor( input_arg: string|TomInput, user_settings:RecursivePartial<TomSettings> ){\n\t\tsuper();\n\n\t\tinstance_i++;\n\n\t\tvar dir;\n\t\tvar input\t\t\t\t= getDom( input_arg ) as TomInput;\n\n\t\tif( input.tomselect ){\n\t\t\tthrow new Error('Tom Select already initialized on this element');\n\t\t}\n\n\n\t\tinput.tomselect\t\t\t= this;\n\n\n\t\t// detect rtl environment\n\t\tvar computedStyle\t\t= window.getComputedStyle && window.getComputedStyle(input, null);\n\t\tdir\t\t\t\t\t\t= computedStyle.getPropertyValue('direction');\n\n\t\t// setup default state\n\t\tconst settings\t\t\t= getSettings( input, user_settings );\n\t\tthis.settings\t\t\t= settings;\n\t\tthis.input\t\t\t\t= input;\n\t\tthis.tabIndex\t\t\t= input.tabIndex || 0;\n\t\tthis.is_select_tag\t\t= input.tagName.toLowerCase() === 'select';\n\t\tthis.rtl\t\t\t\t= /rtl/i.test(dir);\n\t\tthis.inputId\t\t\t= getId(input, 'tomselect-'+instance_i);\n\t\tthis.isRequired\t\t\t= input.required;\n\n\n\t\t// search system\n\t\tthis.sifter = new Sifter(this.options, {diacritics: settings.diacritics});\n\n\t\t// option-dependent defaults\n\t\tsettings.mode = settings.mode || (settings.maxItems === 1 ? 'single' : 'multi');\n\t\tif (typeof settings.hideSelected !== 'boolean') {\n\t\t\tsettings.hideSelected = settings.mode === 'multi';\n\t\t}\n\n\t\tif( typeof settings.hidePlaceholder !== 'boolean' ){\n\t\t\tsettings.hidePlaceholder = settings.mode !== 'multi';\n\t\t}\n\n\t\t// set up createFilter callback\n\t\tvar filter = settings.createFilter;\n\t\tif( typeof filter !== 'function' ){\n\n\t\t\tif( typeof filter === 'string' ){\n\t\t\t\tfilter = new RegExp(filter);\n\t\t\t}\n\n\t\t\tif( filter instanceof RegExp ){\n\t\t\t\tsettings.createFilter = (input: string) => (filter as RegExp).test(input);\n\t\t\t}else{\n\t\t\t\tsettings.createFilter = (value: string) => {\n\t\t\t\t\treturn this.settings.duplicates || !this.options[value];\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\n\t\tthis.initializePlugins(settings.plugins);\n\t\tthis.setupCallbacks();\n\t\tthis.setupTemplates();\n\n\n\t\t// Create all elements\n\t\tconst wrapper\t\t\t= getDom('<div>');\n\t\tconst control\t\t\t= getDom('<div>');\n\t\tconst dropdown\t\t\t= this._render('dropdown');\n\t\tconst dropdown_content\t= getDom(`<div role=\"listbox\" tabindex=\"-1\">`);\n\n\t\tconst classes\t\t\t= this.input.getAttribute('class') || '';\n\t\tconst inputMode\t\t\t= settings.mode;\n\n\t\tvar control_input: HTMLInputElement;\n\n\n\t\taddClasses( wrapper, settings.wrapperClass, classes, inputMode);\n\n\n\t\taddClasses(control,settings.controlClass);\n\t\tappend( wrapper, control );\n\n\n\t\taddClasses(dropdown, settings.dropdownClass, inputMode);\n\t\tif( settings.copyClassesToDropdown ){\n\t\t\taddClasses( dropdown, classes);\n\t\t}\n\n\n\t\taddClasses(dropdown_content, settings.dropdownContentClass);\n\t\tappend( dropdown, dropdown_content );\n\n\t\tgetDom( settings.dropdownParent || wrapper ).appendChild( dropdown );\n\n\n\t\t// default controlInput\n\t\tif( isHtmlString(settings.controlInput) ){\n\t\t\tcontrol_input\t\t= getDom(settings.controlInput ) as HTMLInputElement;\n\n\t\t\t// set attributes\n\t\t\tvar attrs = ['autocorrect','autocapitalize','autocomplete','spellcheck'];\n\t\t\titerate(attrs,(attr:string) => {\n\t\t\t\tif( input.getAttribute(attr) ){\n\t\t\t\t\tsetAttr(control_input,{[attr]:input.getAttribute(attr)});\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tcontrol_input.tabIndex = -1;\n\t\t\tcontrol.appendChild( control_input );\n\t\t\tthis.focus_node\t\t= control_input;\n\n\t\t// dom element\n\t\t}else if( settings.controlInput ){\n\t\t\tcontrol_input\t\t= getDom( settings.controlInput ) as HTMLInputElement;\n\t\t\tthis.focus_node\t\t= control_input;\n\n\t\t}else{\n\t\t\tcontrol_input\t\t= getDom('<input/>') as HTMLInputElement;\n\t\t\tthis.focus_node\t\t= control;\n\t\t}\n\n\t\tthis.wrapper\t\t\t= wrapper;\n\t\tthis.dropdown\t\t\t= dropdown;\n\t\tthis.dropdown_content\t= dropdown_content;\n\t\tthis.control \t\t\t= control;\n\t\tthis.control_input\t\t= control_input;\n\n\t\tthis.setup();\n\t}\n\n\t/**\n\t * set up event bindings.\n\t *\n\t */\n\tsetup(){\n\n\t\tconst self = this;\n\t\tconst settings\t\t\t\t= self.settings;\n\t\tconst control_input\t\t\t= self.control_input;\n\t\tconst dropdown\t\t\t\t= self.dropdown;\n\t\tconst dropdown_content\t\t= self.dropdown_content;\n\t\tconst wrapper\t\t\t\t= self.wrapper;\n\t\tconst control\t\t\t\t= self.control;\n\t\tconst input\t\t\t\t\t= self.input;\n\t\tconst focus_node\t\t\t= self.focus_node;\n\t\tconst passive_event\t\t\t= { passive: true };\n\t\tconst listboxId\t\t\t\t= self.inputId +'-ts-dropdown';\n\n\n\t\tsetAttr(dropdown_content,{\n\t\t\tid: listboxId\n\t\t});\n\n\t\tsetAttr(focus_node,{\n\t\t\trole:'combobox',\n\t\t\t'aria-haspopup':'listbox',\n\t\t\t'aria-expanded':'false',\n\t\t\t'aria-controls':listboxId\n\t\t});\n\n\t\tconst control_id\t= getId(focus_node,self.inputId + '-ts-control');\n\t\tconst query\t\t\t= \"label[for='\"+escapeQuery(self.inputId)+\"']\";\n\t\tconst label\t\t\t= document.querySelector(query);\n\t\tconst label_click\t= self.focus.bind(self);\n\t\tif( label ){\n\t\t\taddEvent(label,'click', label_click );\n\t\t\tsetAttr(label,{for:control_id});\n\t\t\tconst label_id = getId(label,self.inputId+'-ts-label');\n\t\t\tsetAttr(focus_node,{'aria-labelledby':label_id});\n\t\t\tsetAttr(dropdown_content,{'aria-labelledby':label_id});\n\t\t}\n\n\t\twrapper.style.width = input.style.width;\n\n\t\tif (self.plugins.names.length) {\n\t\t\tconst classes_plugins = 'plugin-' + self.plugins.names.join(' plugin-');\n\t\t\taddClasses( [wrapper,dropdown], classes_plugins);\n\t\t}\n\n\t\tif ((settings.maxItems === null || settings.maxItems > 1) && self.is_select_tag ){\n\t\t\tsetAttr(input,{multiple:'multiple'});\n\t\t}\n\n\t\tif (settings.placeholder) {\n\t\t\tsetAttr(control_input,{placeholder:settings.placeholder});\n\t\t}\n\n\t\t// if splitOn was not passed in, construct it from the delimiter to allow pasting universally\n\t\tif (!settings.splitOn && settings.delimiter) {\n\t\t\tsettings.splitOn = new RegExp('\\\\s*' + escape_regex(settings.delimiter) + '+\\\\s*');\n\t\t}\n\n\t\t// debounce user defined load() if loadThrottle > 0\n\t\t// after initializePlugins() so plugins can create/modify user defined loaders\n\t\tif( settings.load && settings.loadThrottle ){\n\t\t\tsettings.load = loadDebounce(settings.load,settings.loadThrottle)\n\t\t}\n\n\t\taddEvent(dropdown,'mousemove', () => {\n\t\t\tself.ignoreHover = false;\n\t\t});\n\n\t\taddEvent(dropdown,'mouseenter', (e) => {\n\n\t\t\tvar target_match = parentMatch(e.target as HTMLElement, '[data-selectable]', dropdown);\n\t\t\tif( target_match ) self.onOptionHover( e as MouseEvent, target_match );\n\n\t\t}, {capture:true});\n\n\t\t// clicking on an option should select it\n\t\taddEvent(dropdown,'click',(evt) => {\n\t\t\tconst option = parentMatch(evt.target as HTMLElement, '[data-selectable]');\n\t\t\tif( option ){\n\t\t\t\tself.onOptionSelect( evt as MouseEvent, option );\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t}\n\t\t});\n\n\t\taddEvent(control,'click', (evt) => {\n\n\t\t\tvar target_match = parentMatch( evt.target as HTMLElement, '[data-ts-item]', control);\n\t\t\tif( target_match && self.onItemSelect(evt as MouseEvent, target_match as TomItem) ){\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// retain focus (see control_input mousedown)\n\t\t\tif( control_input.value != '' ){\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tself.onClick();\n\t\t\tpreventDefault(evt,true);\n\t\t});\n\n\n\t\t// keydown on focus_node for arrow_down/arrow_up\n\t\taddEvent(focus_node,'keydown',\t\t(e) => self.onKeyDown(e as KeyboardEvent) );\n\n\t\t// keypress and input/keyup\n\t\taddEvent(control_input,'keypress',\t(e) => self.onKeyPress(e as KeyboardEvent) );\n\t\taddEvent(control_input,'input',\t\t(e) => self.onInput(e as KeyboardEvent) );\n\t\taddEvent(focus_node,'blur', \t\t(e) => self.onBlur(e as FocusEvent) );\n\t\taddEvent(focus_node,'focus',\t\t(e) => self.onFocus(e as MouseEvent) );\n\t\taddEvent(control_input,'paste',\t\t(e) => self.onPaste(e as MouseEvent) );\n\n\n\t\tconst doc_mousedown = (evt:Event) => {\n\n\t\t\t// blur if target is outside of this instance\n\t\t\t// dropdown is not always inside wrapper\n\t\t\tconst target = evt.composedPath()[0];\n\t\t\tif( !wrapper.contains(target as HTMLElement) && !dropdown.contains(target as HTMLElement) ){\n\t\t\t\tif (self.isFocused) {\n\t\t\t\t\tself.blur();\n\t\t\t\t}\n\t\t\t\tself.inputState();\n\t\t\t\treturn;\n\t\t\t}\n\n\n\t\t\t// retain focus by preventing native handling. if the\n\t\t\t// event target is the input it should not be modified.\n\t\t\t// otherwise, text selection within the input won't work.\n\t\t\t// Fixes bug #212 which is no covered by tests\n\t\t\tif( target == control_input && self.isOpen ){\n\t\t\t\tevt.stopPropagation();\n\n\t\t\t// clicking anywhere in the control should not blur the control_input (which would close the dropdown)\n\t\t\t}else{\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t}\n\n\t\t};\n\n\t\tconst win_scroll = () => {\n\t\t\tif (self.isOpen) {\n\t\t\t\tself.positionDropdown();\n\t\t\t}\n\t\t};\n\n\n\t\taddEvent(document,'mousedown', doc_mousedown);\n\t\taddEvent(window,'scroll', win_scroll, passive_event);\n\t\taddEvent(window,'resize', win_scroll, passive_event);\n\n\t\tthis._destroy = () => {\n\t\t\tdocument.removeEventListener('mousedown',doc_mousedown);\n\t\t\twindow.removeEventListener('scroll',win_scroll);\n\t\t\twindow.removeEventListener('resize',win_scroll);\n\t\t\tif( label ) label.removeEventListener('click',label_click);\n\t\t};\n\n\t\t// store original html and tab index so that they can be\n\t\t// restored when the destroy() method is called.\n\t\tthis.revertSettings = {\n\t\t\tinnerHTML : input.innerHTML,\n\t\t\ttabIndex : input.tabIndex\n\t\t};\n\n\n\t\tinput.tabIndex = -1;\n\t\tinput.insertAdjacentElement('afterend', self.wrapper);\n\n\t\tself.sync(false);\n\t\tsettings.items = [];\n\t\tdelete settings.optgroups;\n\t\tdelete settings.options;\n\n\t\taddEvent(input,'invalid', () => {\n\t\t\tif( self.isValid ){\n\t\t\t\tself.isValid = false;\n\t\t\t\tself.isInvalid = true;\n\t\t\t\tself.refreshState();\n\t\t\t}\n\t\t});\n\n\t\tself.updateOriginalInput();\n\t\tself.refreshItems();\n\t\tself.close(false);\n\t\tself.inputState();\n\t\tself.isSetup = true;\n\n\t\tif( input.disabled ){\n\t\t\tself.disable();\n\t\t}else if( input.readOnly ){\n\t\t\tself.setReadOnly(true);\n\t\t}else{\n\t\t\tself.enable(); //sets tabIndex\n\t\t}\n\n\t\tself.on('change', this.onChange);\n\n\t\taddClasses(input,'tomselected','ts-hidden-accessible');\n\t\tself.trigger('initialize');\n\n\t\t// preload options\n\t\tif (settings.preload === true) {\n\t\t\tself.preload();\n\t\t}\n\n\t}\n\n\n\t/**\n\t * Register options and optgroups\n\t *\n\t */\n\tsetupOptions(options:TomOption[] = [], optgroups:TomOption[] = []){\n\n\t\t// build options table\n\t\tthis.addOptions(options);\n\n\n\t\t// build optgroup table\n\t\titerate( optgroups, (optgroup:TomOption) => {\n\t\t\tthis.registerOptionGroup(optgroup);\n\t\t});\n\t}\n\n\t/**\n\t * Sets up default rendering functions.\n\t */\n\tsetupTemplates() {\n\t\tvar self = this;\n\t\tvar field_label = self.settings.labelField;\n\t\tvar field_optgroup = self.settings.optgroupLabelField;\n\n\t\tvar templates = {\n\t\t\t'optgroup': (data:TomOption) => {\n\t\t\t\tlet optgroup = document.createElement('div');\n\t\t\t\toptgroup.className = 'optgroup';\n\t\t\t\toptgroup.appendChild(data.options);\n\t\t\t\treturn optgroup;\n\n\t\t\t},\n\t\t\t'optgroup_header': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div class=\"optgroup-header\">' + escape(data[field_optgroup]) + '</div>';\n\t\t\t},\n\t\t\t'option': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div>' + escape(data[field_label]) + '</div>';\n\t\t\t},\n\t\t\t'item': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div>' + escape(data[field_label]) + '</div>';\n\t\t\t},\n\t\t\t'option_create': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div class=\"create\">Add <strong>' + escape(data.input) + '</strong>&hellip;</div>';\n\t\t\t},\n\t\t\t'no_results':() => {\n\t\t\t\treturn '<div class=\"no-results\">No results found</div>';\n\t\t\t},\n\t\t\t'loading':() => {\n\t\t\t\treturn '<div class=\"spinner\"></div>';\n\t\t\t},\n\t\t\t'not_loading':() => {},\n\t\t\t'dropdown':() => {\n\t\t\t\treturn '<div></div>';\n\t\t\t}\n\t\t};\n\n\n\t\tself.settings.render = Object.assign({}, templates, self.settings.render);\n\t}\n\n\t/**\n\t * Maps fired events to callbacks provided\n\t * in the settings used when creating the control.\n\t */\n\tsetupCallbacks() {\n\t\tvar key, fn;\n\t\tvar callbacks:{[key:string]:string} = {\n\t\t\t'initialize'      : 'onInitialize',\n\t\t\t'change'          : 'onChange',\n\t\t\t'item_add'        : 'onItemAdd',\n\t\t\t'item_remove'     : 'onItemRemove',\n\t\t\t'item_select'     : 'onItemSelect',\n\t\t\t'clear'           : 'onClear',\n\t\t\t'option_add'      : 'onOptionAdd',\n\t\t\t'option_remove'   : 'onOptionRemove',\n\t\t\t'option_clear'    : 'onOptionClear',\n\t\t\t'optgroup_add'    : 'onOptionGroupAdd',\n\t\t\t'optgroup_remove' : 'onOptionGroupRemove',\n\t\t\t'optgroup_clear'  : 'onOptionGroupClear',\n\t\t\t'dropdown_open'   : 'onDropdownOpen',\n\t\t\t'dropdown_close'  : 'onDropdownClose',\n\t\t\t'type'            : 'onType',\n\t\t\t'load'            : 'onLoad',\n\t\t\t'focus'           : 'onFocus',\n\t\t\t'blur'            : 'onBlur'\n\t\t};\n\n\t\tfor (key in callbacks) {\n\n\t\t\tfn = this.settings[callbacks[key] as (keyof TomSettings)];\n\t\t\tif (fn) this.on(key, fn);\n\n\t\t}\n\t}\n\n\t/**\n\t * Sync the Tom Select instance with the original input or select\n\t *\n\t */\n\tsync(get_settings:boolean=true):void{\n\t\tconst self\t\t= this;\n\t\tconst settings\t= get_settings ? getSettings( self.input, {delimiter:self.settings.delimiter} as RecursivePartial<TomSettings> ) : self.settings;\n\n\t\tself.setupOptions(settings.options,settings.optgroups);\n\n\t\tself.setValue(settings.items||[],true); // silent prevents recursion\n\n\t\tself.lastQuery = null; // so updated options will be displayed in dropdown\n\t}\n\n\t/**\n\t * Triggered when the main control element\n\t * has a click event.\n\t *\n\t */\n\tonClick():void {\n\t\tvar self = this;\n\n\t\tif( self.activeItems.length > 0 ){\n\t\t\tself.clearActiveItems();\n\t\t\tself.focus();\n\t\t\treturn;\n\t\t}\n\n\t\tif( self.isFocused && self.isOpen ){\n\t\t\tself.blur();\n\t\t} else {\n\t\t\tself.focus();\n\t\t}\n\t}\n\n\t/**\n\t * @deprecated v1.7\n\t *\n\t */\n\tonMouseDown():void {}\n\n\t/**\n\t * Triggered when the value of the control has been changed.\n\t * This should propagate the event to the original DOM\n\t * input / select element.\n\t */\n\tonChange() {\n\t\ttriggerEvent(this.input, 'input');\n\t\ttriggerEvent(this.input, 'change');\n\t}\n\n\t/**\n\t * Triggered on <input> paste.\n\t *\n\t */\n\tonPaste(e:MouseEvent|KeyboardEvent):void {\n\t\tvar self = this;\n\n\t\tif( self.isInputHidden || self.isLocked ){\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\n\t\t// If a regex or string is included, this will split the pasted\n\t\t// input and create Items for each separate value\n\t\tif( !self.settings.splitOn ){\n\t\t\treturn;\n\t\t}\n\n\t\t// Wait for pasted text to be recognized in value\n\t\tsetTimeout(() => {\n\t\t\tvar pastedText = self.inputValue();\n\t\t\tif( !pastedText.match(self.settings.splitOn)){\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tvar splitInput = pastedText.trim().split(self.settings.splitOn);\n\t\t\titerate( splitInput, (piece:string) => {\n\n\t\t\t\tconst hash = hash_key(piece);\n\t\t\t\tif( hash ){\n\t\t\t\t\tif( this.options[piece] ){\n\t\t\t\t\t\tself.addItem(piece);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tself.createItem(piece);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}, 0);\n\n\t}\n\n\t/**\n\t * Triggered on <input> keypress.\n\t *\n\t */\n\tonKeyPress(e:KeyboardEvent):void {\n\t\tvar self = this;\n\t\tif(self.isLocked){\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\t\tvar character = String.fromCharCode(e.keyCode || e.which);\n\t\tif (self.settings.create && self.settings.mode === 'multi' && character === self.settings.delimiter) {\n\t\t\tself.createItem();\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\t}\n\n\t/**\n\t * Triggered on <input> keydown.\n\t *\n\t */\n\tonKeyDown(e:KeyboardEvent):void {\n\t\tvar self = this;\n\n\t\tself.ignoreHover = true;\n\n\t\tif (self.isLocked) {\n\t\t\tif (e.keyCode !== constants.KEY_TAB) {\n\t\t\t\tpreventDefault(e);\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\tswitch (e.keyCode) {\n\n\t\t\t// ctrl+A: select all\n\t\t\tcase constants.KEY_A:\n\t\t\t\tif( isKeyDown(constants.KEY_SHORTCUT,e) ){\n\t\t\t\t\tif( self.control_input.value == '' ){\n\t\t\t\t\t\tpreventDefault(e);\n\t\t\t\t\t\tself.selectAll();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbreak;\n\n\t\t\t// esc: close dropdown\n\t\t\tcase constants.KEY_ESC:\n\t\t\t\tif (self.isOpen) {\n\t\t\t\t\tpreventDefault(e,true);\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\t\t\t\tself.clearActiveItems();\n\t\t\t\treturn;\n\n\t\t\t// down: open dropdown or move selection down\n\t\t\tcase constants.KEY_DOWN:\n\t\t\t\tif (!self.isOpen && self.hasOptions) {\n\t\t\t\t\tself.open();\n\t\t\t\t} else if (self.activeOption) {\n\t\t\t\t\tlet next = self.getAdjacent(self.activeOption, 1);\n\t\t\t\t\tif (next) self.setActiveOption(next);\n\t\t\t\t}\n\t\t\t\tpreventDefault(e);\n\t\t\t\treturn;\n\n\t\t\t// up: move selection up\n\t\t\tcase constants.KEY_UP:\n\t\t\t\tif (self.activeOption) {\n\t\t\t\t\tlet prev = self.getAdjacent(self.activeOption, -1);\n\t\t\t\t\tif (prev) self.setActiveOption(prev);\n\t\t\t\t}\n\t\t\t\tpreventDefault(e);\n\t\t\t\treturn;\n\n\t\t\t// return: select active option\n\t\t\tcase constants.KEY_RETURN:\n\t\t\t\tif( self.canSelect(self.activeOption) ){\n\t\t\t\t\tself.onOptionSelect(e,self.activeOption!);\n\t\t\t\t\tpreventDefault(e);\n\n\t\t\t\t// if the option_create=null, the dropdown might be closed\n\t\t\t\t}else if (self.settings.create && self.createItem()) {\n\t\t\t\t\tpreventDefault(e);\n\n\t\t\t\t// don't submit form when searching for a value\n\t\t\t\t}else if( document.activeElement == self.control_input && self.isOpen ){\n\t\t\t\t\tpreventDefault(e);\n\t\t\t\t}\n\n\t\t\t\treturn;\n\n\t\t\t// left: modifiy item selection to the left\n\t\t\tcase constants.KEY_LEFT:\n\t\t\t\tself.advanceSelection(-1, e);\n\t\t\t\treturn;\n\n\t\t\t// right: modifiy item selection to the right\n\t\t\tcase constants.KEY_RIGHT:\n\t\t\t\tself.advanceSelection(1, e);\n\t\t\t\treturn;\n\n\t\t\t// tab: select active option and/or create item\n\t\t\tcase constants.KEY_TAB:\n\n\t\t\t\tif( self.settings.selectOnTab ){\n\t\t\t\t\tif( self.canSelect(self.activeOption) ){\n\t\t\t\t\t\tself.onOptionSelect(e,self.activeOption!);\n\n\t\t\t\t\t\t// prevent default [tab] behaviour of jump to the next field\n\t\t\t\t\t\t// if select isFull, then the dropdown won't be open and [tab] will work normally\n\t\t\t\t\t\tpreventDefault(e);\n\t\t\t\t\t}\n\t\t\t\t\tif (self.settings.create && self.createItem()) {\n\t\t\t\t\t\tpreventDefault(e);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn;\n\n\t\t\t// delete|backspace: delete items\n\t\t\tcase constants.KEY_BACKSPACE:\n\t\t\tcase constants.KEY_DELETE:\n\t\t\t\tself.deleteSelection(e);\n\t\t\t\treturn;\n\t\t}\n\n\t\t// don't enter text in the control_input when active items are selected\n\t\tif( self.isInputHidden && !isKeyDown(constants.KEY_SHORTCUT,e) ){\n\t\t\tpreventDefault(e);\n\t\t}\n\t}\n\n\t/**\n\t * Triggered on <input> keyup.\n\t *\n\t */\n\tonInput(e:MouseEvent|KeyboardEvent):void {\n\t\t\n\t\tif( this.isLocked ){\n\t\t\treturn;\n\t\t}\n\n\t\tconst value = this.inputValue();\n\t\tif( this.lastValue === value ) return;\n\t\tthis.lastValue = value;\n\t\t\n\t\tif( value == '' ){\n\t\t\tthis._onInput();\n\t\t\treturn;\n\t\t}\n\n\t\tif( this.refreshTimeout ){\n\t\t\twindow.clearTimeout(this.refreshTimeout);\n\t\t}\n\n\t\tthis.refreshTimeout = timeout(()=> {\n\t\t\tthis.refreshTimeout = null;\n\t\t\tthis._onInput();\n\t\t}, this.settings.refreshThrottle);\n\t}\n\n\t_onInput():void {\n\t\tconst value = this.lastValue;\n\n\t\tif( this.settings.shouldLoad.call(this,value) ){\n\t\t\tthis.load(value);\n\t\t}\n\n\t\tthis.refreshOptions();\n\t\tthis.trigger('type', value);\n\t}\n\n\t/**\n\t * Triggered when the user rolls over\n\t * an option in the autocomplete dropdown menu.\n\t *\n\t */\n\tonOptionHover( evt:MouseEvent|KeyboardEvent, option:HTMLElement ):void{\n\t\tif( this.ignoreHover ) return;\n\t\tthis.setActiveOption(option, false);\n\t}\n\n\t/**\n\t * Triggered on <input> focus.\n\t *\n\t */\n\tonFocus(e?:MouseEvent|KeyboardEvent):void {\n\t\tvar self = this;\n\t\tvar wasFocused = self.isFocused;\n\n\t\tif( self.isDisabled || self.isReadOnly ){\n\t\t\tself.blur();\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\n\t\tif (self.ignoreFocus) return;\n\t\tself.isFocused = true;\n\t\tif( self.settings.preload === 'focus' ) self.preload();\n\n\t\tif (!wasFocused) self.trigger('focus');\n\n\t\tif (!self.activeItems.length) {\n\t\t\tself.inputState();\n\t\t\tself.refreshOptions(!!self.settings.openOnFocus);\n\t\t}\n\n\t\tself.refreshState();\n\t}\n\n\t/**\n\t * Triggered on <input> blur.\n\t *\n\t */\n\tonBlur(e?:FocusEvent):void {\n\n\t\tif( document.hasFocus() === false ) return;\n\n\t\tvar self = this;\n\t\tif (!self.isFocused) return;\n\t\tself.isFocused = false;\n\t\tself.ignoreFocus = false;\n\n\t\tvar deactivate = () => {\n\t\t\tself.close();\n\t\t\tself.setActiveItem();\n\t\t\tself.setCaret(self.items.length);\n\t\t\tself.trigger('blur');\n\t\t};\n\n\t\tif (self.settings.create && self.settings.createOnBlur) {\n\t\t\tself.createItem(null, deactivate);\n\t\t} else {\n\t\t\tdeactivate();\n\t\t}\n\t}\n\n\n\t/**\n\t * Triggered when the user clicks on an option\n\t * in the autocomplete dropdown menu.\n\t *\n\t */\n\tonOptionSelect( evt:MouseEvent|KeyboardEvent, option:HTMLElement ){\n\t\tvar value, self = this;\n\n\n\t\t// should not be possible to trigger a option under a disabled optgroup\n\t\tif( option.parentElement && option.parentElement.matches('[data-disabled]') ){\n\t\t\treturn;\n\t\t}\n\n\n\t\tif( option.classList.contains('create') ){\n\t\t\tself.createItem(null, () => {\n\t\t\t\tif (self.settings.closeAfterSelect) {\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tvalue = option.dataset.value;\n\t\t\tif (typeof value !== 'undefined') {\n\t\t\t\tself.lastQuery = null;\n\t\t\t\tself.addItem(value);\n\t\t\t\tif (self.settings.closeAfterSelect) {\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\n\t\t\t\tif( !self.settings.hideSelected && evt.type && /click/.test(evt.type) ){\n\t\t\t\t\tself.setActiveOption(option);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Return true if the given option can be selected\n\t *\n\t */\n\tcanSelect(option:HTMLElement|null):boolean{\n\n\t\tif( this.isOpen && option && this.dropdown_content.contains(option) ) {\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * Triggered when the user clicks on an item\n\t * that has been selected.\n\t *\n\t */\n\tonItemSelect( evt?:MouseEvent, item?:TomItem ):boolean{\n\t\tvar self = this;\n\n\t\tif( !self.isLocked && self.settings.mode === 'multi' ){\n\t\t\tpreventDefault(evt);\n\t\t\tself.setActiveItem(item, evt);\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * Determines whether or not to invoke\n\t * the user-provided option provider / loader\n\t *\n\t * Note, there is a subtle difference between\n\t * this.canLoad() and this.settings.shouldLoad();\n\t *\n\t *\t- settings.shouldLoad() is a user-input validator.\n\t *\tWhen false is returned, the not_loading template\n\t *\twill be added to the dropdown\n\t *\n\t *\t- canLoad() is lower level validator that checks\n\t * \tthe Tom Select instance. There is no inherent user\n\t *\tfeedback when canLoad returns false\n\t *\n\t */\n\tcanLoad(value:string):boolean{\n\n\t\tif( !this.settings.load ) return false;\n\t\tif( this.loadedSearches.hasOwnProperty(value) ) return false;\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Invokes the user-provided option provider / loader.\n\t *\n\t */\n\tload(value:string):void {\n\t\tconst self = this;\n\n\t\tif( !self.canLoad(value) ) return;\n\n\t\taddClasses(self.wrapper,self.settings.loadingClass);\n\t\tself.loading++;\n\n\t\tconst callback = self.loadCallback.bind(self);\n\t\tself.settings.load.call(self, value, callback);\n\t}\n\n\t/**\n\t * Invoked by the user-provided option provider\n\t *\n\t */\n\tloadCallback( options:TomOption[], optgroups:TomOption[] ):void{\n\t\tconst self = this;\n\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\tself.lastQuery = null;\n\n\t\tself.clearActiveOption(); // when new results load, focus should be on first option\n\t\tself.setupOptions(options,optgroups);\n\n\t\tself.refreshOptions(self.isFocused && !self.isInputHidden);\n\n\t\tif (!self.loading) {\n\t\t\tremoveClasses(self.wrapper,self.settings.loadingClass);\n\t\t}\n\n\t\tself.trigger('load', options, optgroups);\n\t}\n\n\tpreload():void{\n\t\tvar classList = this.wrapper.classList;\n\t\tif( classList.contains('preloaded') ) return;\n\t\tclassList.add('preloaded');\n\t\tthis.load('');\n\t}\n\n\n\t/**\n\t * Sets the input field of the control to the specified value.\n\t *\n\t */\n\tsetTextboxValue(value:string = '') {\n\t\tvar input = this.control_input;\n\t\tvar changed = input.value !== value;\n\t\tif (changed) {\n\t\t\tinput.value = value;\n\t\t\ttriggerEvent(input,'update');\n\t\t\tthis.lastValue = value;\n\t\t}\n\t}\n\n\t/**\n\t * Returns the value of the control. If multiple items\n\t * can be selected (e.g. <select multiple>), this returns\n\t * an array. If only one item can be selected, this\n\t * returns a string.\n\t *\n\t */\n\tgetValue():string|string[] {\n\n\t\tif( this.is_select_tag && this.input.hasAttribute('multiple')) {\n\t\t\treturn this.items;\n\t\t}\n\n\t\treturn this.items.join(this.settings.delimiter);\n\t}\n\n\t/**\n\t * Resets the selected items to the given value.\n\t *\n\t */\n\tsetValue( value:string|string[], silent?:boolean ):void{\n\t\tvar events = silent ? [] : ['change'];\n\n\t\tdebounce_events(this, events,() => {\n\t\t\tthis.clear(silent);\n\t\t\tthis.addItems(value, silent);\n\t\t});\n\t}\n\n\n\t/**\n\t * Resets the number of max items to the given value\n\t *\n\t */\n\tsetMaxItems(value:null|number){\n\t\tif(value === 0) value = null; //reset to unlimited items.\n\t\tthis.settings.maxItems = value;\n\t\tthis.refreshState();\n\t}\n\n\t/**\n\t * Sets the selected item.\n\t *\n\t */\n\tsetActiveItem( item?:TomItem, e?:MouseEvent|KeyboardEvent ){\n\t\tvar self = this;\n\t\tvar eventName;\n\t\tvar i, begin, end, swap;\n\t\tvar last;\n\n\t\tif (self.settings.mode === 'single') return;\n\n\t\t// clear the active selection\n\t\tif( !item ){\n\t\t\tself.clearActiveItems();\n\t\t\tif (self.isFocused) {\n\t\t\t\tself.inputState();\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\t// modify selection\n\t\teventName = e && e.type.toLowerCase();\n\n\t\tif (eventName === 'click' && isKeyDown('shiftKey',e) && self.activeItems.length) {\n\t\t\tlast\t= self.getLastActive();\n\t\t\tbegin\t= Array.prototype.indexOf.call(self.control.children, last);\n\t\t\tend\t\t= Array.prototype.indexOf.call(self.control.children, item);\n\n\t\t\tif (begin > end) {\n\t\t\t\tswap  = begin;\n\t\t\t\tbegin = end;\n\t\t\t\tend   = swap;\n\t\t\t}\n\t\t\tfor (i = begin; i <= end; i++) {\n\t\t\t\titem = self.control.children[i] as TomItem;\n\t\t\t\tif (self.activeItems.indexOf(item) === -1) {\n\t\t\t\t\tself.setActiveItemClass(item);\n\t\t\t\t}\n\t\t\t}\n\t\t\tpreventDefault(e);\n\t\t} else if ((eventName === 'click' && isKeyDown(constants.KEY_SHORTCUT,e) ) || (eventName === 'keydown' && isKeyDown('shiftKey',e))) {\n\t\t\tif( item.classList.contains('active') ){\n\t\t\t\tself.removeActiveItem( item );\n\t\t\t} else {\n\t\t\t\tself.setActiveItemClass(item);\n\t\t\t}\n\t\t} else {\n\t\t\tself.clearActiveItems();\n\t\t\tself.setActiveItemClass(item);\n\t\t}\n\n\t\t// ensure control has focus\n\t\tself.inputState();\n\t\tif (!self.isFocused) {\n\t\t\tself.focus();\n\t\t}\n\t}\n\n\t/**\n\t * Set the active and last-active classes\n\t *\n\t */\n\tsetActiveItemClass( item:TomItem ){\n\t\tconst self = this;\n\t\tconst last_active = self.control.querySelector('.last-active');\n\t\tif( last_active ) removeClasses(last_active as HTMLElement,'last-active');\n\n\t\taddClasses(item,'active last-active');\n\t\tself.trigger('item_select', item);\n\t\tif( self.activeItems.indexOf(item) == -1 ){\n\t\t\tself.activeItems.push( item );\n\t\t}\n\t}\n\n\t/**\n\t * Remove active item\n\t *\n\t */\n\tremoveActiveItem( item:TomItem ){\n\t\tvar idx = this.activeItems.indexOf(item);\n\t\tthis.activeItems.splice(idx, 1);\n\t\tremoveClasses(item,'active');\n\t}\n\n\t/**\n\t * Clears all the active items\n\t *\n\t */\n\tclearActiveItems(){\n\t\tremoveClasses(this.activeItems,'active');\n\t\tthis.activeItems = [];\n\t}\n\n\t/**\n\t * Sets the selected item in the dropdown menu\n\t * of available options.\n\t *\n\t */\n\tsetActiveOption( option:null|HTMLElement,scroll:boolean=true ):void{\n\n\t\tif( option === this.activeOption ){\n\t\t\treturn;\n\t\t}\n\n\t\tthis.clearActiveOption();\n\t\tif( !option ) return;\n\n\t\tthis.activeOption = option;\n\t\tsetAttr(this.focus_node,{'aria-activedescendant':option.getAttribute('id')});\n\t\tsetAttr(option,{'aria-selected':'true'});\n\t\taddClasses(option,'active');\n\t\tif( scroll ) this.scrollToOption(option);\n\t}\n\n\t/**\n\t * Sets the dropdown_content scrollTop to display the option\n\t *\n\t */\n\tscrollToOption( option:null|HTMLElement, behavior?:string ):void{\n\n\t\tif( !option ) return;\n\n\t\tconst content\t\t= this.dropdown_content;\n\t\tconst height_menu\t= content.clientHeight;\n\t\tconst scrollTop\t\t= content.scrollTop || 0;\n\t\tconst height_item\t= option.offsetHeight;\n\t\tconst y\t\t\t\t= option.getBoundingClientRect().top - content.getBoundingClientRect().top + scrollTop;\n\n\t\tif (y + height_item > height_menu + scrollTop) {\n\t\t\tthis.scroll(y - height_menu + height_item, behavior);\n\n\t\t} else if (y < scrollTop) {\n\t\t\tthis.scroll(y, behavior);\n\t\t}\n\t}\n\n\t/**\n\t * Scroll the dropdown to the given position\n\t *\n\t */\n\tscroll( scrollTop:number, behavior?:string ):void{\n\t\tconst content = this.dropdown_content;\n\t\tif( behavior ){\n\t\t\tcontent.style.scrollBehavior = behavior;\n\t\t}\n\t\tcontent.scrollTop = scrollTop;\n\t\tcontent.style.scrollBehavior = '';\n\t}\n\n\t/**\n\t * Clears the active option\n\t *\n\t */\n\tclearActiveOption(){\n\t\tif( this.activeOption ){\n\t\t\tremoveClasses(this.activeOption,'active');\n\t\t\tsetAttr(this.activeOption,{'aria-selected':null});\n\t\t}\n\t\tthis.activeOption = null;\n\t\tsetAttr(this.focus_node,{'aria-activedescendant':null});\n\t}\n\n\n\t/**\n\t * Selects all items (CTRL + A).\n\t */\n\tselectAll() {\n\t\tconst self = this;\n\n\t\tif (self.settings.mode === 'single') return;\n\n\t\tconst activeItems = self.controlChildren();\n\n\t\tif( !activeItems.length ) return;\n\n\t\tself.inputState();\n\t\tself.close();\n\n\t\tself.activeItems = activeItems;\n\t\titerate( activeItems, (item:TomItem) => {\n\t\t\tself.setActiveItemClass(item);\n\t\t});\n\n\t}\n\n\t/**\n\t * Determines if the control_input should be in a hidden or visible state\n\t *\n\t */\n\tinputState(){\n\t\tvar self = this;\n\n\t\tif( !self.control.contains(self.control_input) ) return;\n\n\t\tsetAttr(self.control_input,{placeholder:self.settings.placeholder});\n\n\t\tif( self.activeItems.length > 0 || (!self.isFocused && self.settings.hidePlaceholder && self.items.length > 0) ){\n\t\t\tself.setTextboxValue();\n\t\t\tself.isInputHidden = true;\n\n\t\t}else{\n\n\t\t\tif( self.settings.hidePlaceholder && self.items.length > 0 ){\n\t\t\t\tsetAttr(self.control_input,{placeholder:''});\n\t\t\t}\n\t\t\tself.isInputHidden = false;\n\t\t}\n\n\t\tself.wrapper.classList.toggle('input-hidden', self.isInputHidden );\n\t}\n\n\t/**\n\t * Get the input value\n\t */\n\tinputValue(){\n\t\treturn this.control_input.value.trim();\n\t}\n\n\t/**\n\t * Gives the control focus.\n\t */\n\tfocus() {\n\t\tvar self = this;\n\t\tif( self.isDisabled || self.isReadOnly) return;\n\n\t\tself.ignoreFocus = true;\n\n\t\tif( self.control_input.offsetWidth ){\n\t\t\tself.control_input.focus();\n\t\t}else{\n\t\t\tself.focus_node.focus();\n\t\t}\n\n\t\tsetTimeout(() => {\n\t\t\tself.ignoreFocus = false;\n\t\t\tself.onFocus();\n\t\t}, 0);\n\t}\n\n\t/**\n\t * Forces the control out of focus.\n\t *\n\t */\n\tblur():void {\n\t\tthis.focus_node.blur();\n\t\tthis.onBlur();\n\t}\n\n\t/**\n\t * Returns a function that scores an object\n\t * to show how good of a match it is to the\n\t * provided query.\n\t *\n\t * @return {function}\n\t */\n\tgetScoreFunction(query:string) {\n\t\treturn this.sifter.getScoreFunction(query, this.getSearchOptions());\n\t}\n\n\t/**\n\t * Returns search options for sifter (the system\n\t * for scoring and sorting results).\n\t *\n\t * @see https://github.com/orchidjs/sifter.js\n\t * @return {object}\n\t */\n\tgetSearchOptions() {\n\t\tvar settings = this.settings;\n\t\tvar sort = settings.sortField;\n\t\tif (typeof settings.sortField === 'string') {\n\t\t\tsort = [{field: settings.sortField}];\n\t\t}\n\n\t\treturn {\n\t\t\tfields      : settings.searchField,\n\t\t\tconjunction : settings.searchConjunction,\n\t\t\tsort        : sort,\n\t\t\tnesting     : settings.nesting\n\t\t};\n\t}\n\n\t/**\n\t * Searches through available options and returns\n\t * a sorted array of matches.\n\t *\n\t */\n\tsearch(query:string) : ReturnType<Sifter['search']>{\n\t\tvar result, calculateScore;\n\t\tvar self     = this;\n\t\tvar options  = this.getSearchOptions();\n\n\t\t// validate user-provided result scoring function\n\t\tif ( self.settings.score ){\n\t\t\tcalculateScore = self.settings.score.call(self,query);\n\t\t\tif (typeof calculateScore !== 'function') {\n\t\t\t\tthrow new Error('Tom Select \"score\" setting must be a function that returns a function');\n\t\t\t}\n\t\t}\n\n\t\t// perform search\n\t\tif (query !== self.lastQuery) {\n\t\t\tself.lastQuery\t\t\t= query;\n\t\t\tresult\t\t\t\t\t= self.sifter.search(query, Object.assign(options, {score: calculateScore}));\n\t\t\tself.currentResults\t\t= result;\n\t\t} else {\n\t\t\tresult\t\t\t\t\t= Object.assign( {}, self.currentResults);\n\t\t}\n\n\t\t// filter out selected items\n\t\tif( self.settings.hideSelected ){\n\t\t\tresult.items = result.items.filter((item) => {\n\t\t\t\tlet hashed = hash_key(item.id);\n\t\t\t\treturn !(hashed && self.items.indexOf(hashed) !== -1 );\n\t\t\t});\n\t\t}\n\n\t\treturn result;\n\t}\n\n\t/**\n\t * Refreshes the list of available options shown\n\t * in the autocomplete dropdown menu.\n\t *\n\t */\n\trefreshOptions( triggerDropdown:boolean = true ){\n\t\tvar i, j, k, n, optgroup, optgroups, html:DocumentFragment, has_create_option, active_group;\n\t\tvar create;\n\n\t\ttype Group = {fragment:DocumentFragment,order:number,optgroup:string}\n\t\tconst groups: {[key:string]:number} = {};\n\t\tconst groups_order:Group[]\t= [];\n\n\t\tvar self\t\t\t\t\t= this;\n\t\tvar query\t\t\t\t\t= self.inputValue();\n\t\tconst same_query\t\t\t= query === self.lastQuery || (query == '' && self.lastQuery == null);\n\t\tvar results\t\t\t\t\t= self.search(query);\n\t\tvar active_option:HTMLElement|null = null;\n\t\tvar show_dropdown\t\t\t= self.settings.shouldOpen || false;\n\t\tvar dropdown_content\t\t= self.dropdown_content;\n\n\n\t\tif( same_query ){\n\t\t\tactive_option\t\t\t= self.activeOption;\n\n\t\t\tif( active_option ){\n\t\t\t\tactive_group = active_option.closest('[data-group]') as HTMLElement;\n\t\t\t}\n\t\t}\n\n\t\t// build markup\n\t\tn = results.items.length;\n\t\tif (typeof self.settings.maxOptions === 'number') {\n\t\t\tn = Math.min(n, self.settings.maxOptions);\n\t\t}\n\n\t\tif( n > 0 ){\n\t\t\tshow_dropdown = true;\n\t\t}\n\n\t\t// get fragment for group and the position of the group in group_order\n\t\tconst getGroupFragment = (optgroup:string,order:number):[number,DocumentFragment] => {\n\n\t\t\tlet group_order_i = groups[optgroup];\n\n\t\t\tif( group_order_i !== undefined ){\n\t\t\t\tlet order_group = groups_order[group_order_i];\n\t\t\t\tif( order_group !== undefined ){\n\t\t\t\t\treturn [group_order_i,order_group.fragment];\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet group_fragment = document.createDocumentFragment();\n\t\t\tgroup_order_i = groups_order.length;\n\t\t\tgroups_order.push({fragment:group_fragment,order,optgroup});\n\n\t\t\treturn [group_order_i,group_fragment]\n\t\t}\n\n\t\t// render and group available options individually\n\t\tfor (i = 0; i < n; i++) {\n\n\t\t\t// get option dom element\n\t\t\tlet item\t\t\t= results.items[i];\n\t\t\tif( !item ) continue;\n\n\t\t\tlet opt_value\t\t= item.id;\n\t\t\tlet option\t\t\t= self.options[opt_value];\n\n\t\t\tif( option === undefined ) continue;\n\n\t\t\tlet opt_hash\t\t= get_hash(opt_value);\n\t\t\tlet option_el\t\t= self.getOption(opt_hash,true) as HTMLElement;\n\n\t\t\t// toggle 'selected' class\n\t\t\tif( !self.settings.hideSelected ){\n\t\t\t\toption_el.classList.toggle('selected', self.items.includes(opt_hash) );\n\t\t\t}\n\n\t\t\toptgroup    = option[self.settings.optgroupField] || '';\n\t\t\toptgroups   = Array.isArray(optgroup) ? optgroup : [optgroup];\n\t\t\t\n\n\t\t\tfor (j = 0, k = optgroups && optgroups.length; j < k; j++) {\n\t\t\t\toptgroup = optgroups[j];\n\n\t\t\t\tlet order = option.$order;\n\t\t\t\tlet self_optgroup = self.optgroups[optgroup];\n\t\t\t\tif( self_optgroup === undefined ){\t\t\t\t\t\n\t\t\t\t\toptgroup = '';\n\t\t\t\t}else{\n\t\t\t\t\torder = self_optgroup.$order;\n\t\t\t\t}\n\n\t\t\t\tconst [group_order_i,group_fragment] = getGroupFragment(optgroup,order);\n\n\n\t\t\t\t// nodes can only have one parent, so if the option is in mutple groups, we need a clone\n\t\t\t\tif( j > 0 ){\n\t\t\t\t\toption_el = option_el.cloneNode(true) as HTMLElement;\n\t\t\t\t\tsetAttr(option_el,{id: option.$id+'-clone-'+j,'aria-selected':null});\n\t\t\t\t\toption_el.classList.add('ts-cloned');\n\t\t\t\t\tremoveClasses(option_el,'active');\n\n\n\t\t\t\t\t// make sure we keep the activeOption in the same group\n\t\t\t\t\tif( self.activeOption && self.activeOption.dataset.value == opt_value ){\n\t\t\t\t\t\tif( active_group && active_group.dataset.group === optgroup.toString() ){\n\t\t\t\t\t\t\tactive_option = option_el;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\t\n\t\t\t\t\n\t\t\t\tgroup_fragment.appendChild(option_el);\n\t\t\t\tif( optgroup != '' ){\n\t\t\t\t\tgroups[optgroup] = group_order_i;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// sort optgroups\n\t\tif( self.settings.lockOptgroupOrder ){\n\t\t\tgroups_order.sort((a, b) => {\n\t\t\t\treturn a.order - b.order;\n\t\t\t});\n\t\t}\n\n\t\t// render optgroup headers & join groups\n\t\thtml = document.createDocumentFragment();\n\t\titerate( groups_order, (group_order:Group) => {\n\n\t\t\tlet group_fragment = group_order.fragment;\n\t\t\tlet optgroup = group_order.optgroup\n\n\t\t\tif( !group_fragment || !group_fragment.children.length ) return;\n\n\t\t\tlet group_heading = self.optgroups[optgroup];\n\n\t\t\tif( group_heading !== undefined ){\n\n\t\t\t\tlet group_options = document.createDocumentFragment();\n\t\t\t\tlet header = self.render('optgroup_header', group_heading);\n\t\t\t\tappend( group_options, header );\n\t\t\t\tappend( group_options, group_fragment );\n\n\t\t\t\tlet group_html = self.render('optgroup', {group:group_heading,options:group_options} );\n\n\t\t\t\tappend( html, group_html );\n\n\t\t\t} else {\n\t\t\t\tappend( html, group_fragment );\n\t\t\t}\n\t\t});\n\n\t\tdropdown_content.innerHTML = '';\n\t\tappend( dropdown_content, html );\n\n\t\t// highlight matching terms inline\n\t\tif (self.settings.highlight) {\n\t\t\tremoveHighlight( dropdown_content );\n\t\t\tif (results.query.length && results.tokens.length) {\n\t\t\t\titerate( results.tokens, (tok) => {\n\t\t\t\t\thighlight( dropdown_content, tok.regex);\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\t// helper method for adding templates to dropdown\n\t\tvar add_template = (template:TomTemplateNames) => {\n\t\t\tlet content = self.render(template,{input:query});\n\t\t\tif( content ){\n\t\t\t\tshow_dropdown = true;\n\t\t\t\tdropdown_content.insertBefore(content, dropdown_content.firstChild);\n\t\t\t}\n\t\t\treturn content;\n\t\t};\n\n\n\t\t// add loading message\n\t\tif( self.loading ){\n\t\t\tadd_template('loading');\n\n\t\t// invalid query\n\t\t}else if( !self.settings.shouldLoad.call(self,query) ){\n\t\t\tadd_template('not_loading');\n\n\t\t// add no_results message\n\t\t}else if( results.items.length === 0 ){\n\t\t\tadd_template('no_results');\n\n\t\t}\n\n\n\n\t\t// add create option\n\t\thas_create_option = self.canCreate(query);\n\t\tif (has_create_option) {\n\t\t\tcreate = add_template('option_create');\n\t\t}\n\n\n\t\t// activate\n\t\tself.hasOptions = results.items.length > 0 || has_create_option;\n\t\tif( show_dropdown ){\n\n\t\t\tif (results.items.length > 0) {\n\n\t\t\t\tif( !active_option && self.settings.mode === 'single' && self.items[0] != undefined ){\n\t\t\t\t\tactive_option = self.getOption(self.items[0]);\n\t\t\t\t}\n\n\t\t\t\tif( !dropdown_content.contains(active_option)  ){\n\n\t\t\t\t\tlet active_index = 0;\n\t\t\t\t\tif( create && !self.settings.addPrecedence ){\n\t\t\t\t\t\tactive_index = 1;\n\t\t\t\t\t}\n\t\t\t\t\tactive_option = self.selectable()[active_index] as HTMLElement;\n\t\t\t\t}\n\n\t\t\t}else if( create ){\n\t\t\t\tactive_option = create;\n\t\t\t}\n\n\t\t\tif( triggerDropdown && !self.isOpen ){\n\t\t\t\tself.open();\n\t\t\t\tself.scrollToOption(active_option,'auto');\n\t\t\t}\n\t\t\tself.setActiveOption(active_option);\n\n\t\t}else{\n\t\t\tself.clearActiveOption();\n\t\t\tif( triggerDropdown && self.isOpen ){\n\t\t\t\tself.close(false); // if create_option=null, we want the dropdown to close but not reset the textbox value\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Return list of selectable options\n\t *\n\t */\n\tselectable():NodeList{\n\t\treturn this.dropdown_content.querySelectorAll('[data-selectable]');\n\t}\n\n\n\n\t/**\n\t * Adds an available option. If it already exists,\n\t * nothing will happen. Note: this does not refresh\n\t * the options list dropdown (use `refreshOptions`\n\t * for that).\n\t *\n\t * Usage:\n\t *\n\t *   this.addOption(data)\n\t *\n\t */\n\taddOption( data:TomOption, user_created = false ):false|string {\n\t\tconst self = this;\n\n\t\t// @deprecated 1.7.7\n\t\t// use addOptions( array, user_created ) for adding multiple options\n\t\tif( Array.isArray(data) ){\n\t\t\tself.addOptions( data, user_created);\n\t\t\treturn false;\n\t\t}\n\n\t\tconst key = hash_key(data[self.settings.valueField]);\n\t\tif( key === null || self.options.hasOwnProperty(key) ){\n\t\t\treturn false;\n\t\t}\n\n\t\tdata.$order\t\t\t= data.$order || ++self.order;\n\t\tdata.$id\t\t\t= self.inputId + '-opt-' + data.$order;\n\t\tself.options[key]\t= data;\n\t\tself.lastQuery\t\t= null;\n\n\t\tif( user_created ){\n\t\t\tself.userOptions[key] = user_created;\n\t\t\tself.trigger('option_add', key, data);\n\t\t}\n\n\t\treturn key;\n\t}\n\n\t/**\n\t * Add multiple options\n\t *\n\t */\n\taddOptions( data:TomOption[], user_created = false ):void{\n\t\titerate( data, (dat:TomOption) => {\n\t\t\tthis.addOption(dat, user_created);\n\t\t});\n\t}\n\n\t/**\n\t * @deprecated 1.7.7\n\t */\n\tregisterOption( data:TomOption ):false|string {\n\t\treturn this.addOption(data);\n\t}\n\n\t/**\n\t * Registers an option group to the pool of option groups.\n\t *\n\t * @return {boolean|string}\n\t */\n\tregisterOptionGroup(data:TomOption) {\n\t\tvar key = hash_key(data[this.settings.optgroupValueField]);\n\n\t\tif ( key === null ) return false;\n\n\t\tdata.$order = data.$order || ++this.order;\n\t\tthis.optgroups[key] = data;\n\t\treturn key;\n\t}\n\n\t/**\n\t * Registers a new optgroup for options\n\t * to be bucketed into.\n\t *\n\t */\n\taddOptionGroup(id:string, data:TomOption) {\n\t\tvar hashed_id;\n\t\tdata[this.settings.optgroupValueField] = id;\n\n\t\tif( hashed_id = this.registerOptionGroup(data) ){\n\t\t\tthis.trigger('optgroup_add', hashed_id, data);\n\t\t}\n\t}\n\n\t/**\n\t * Removes an existing option group.\n\t *\n\t */\n\tremoveOptionGroup(id:string) {\n\t\tif (this.optgroups.hasOwnProperty(id)) {\n\t\t\tdelete this.optgroups[id];\n\t\t\tthis.clearCache();\n\t\t\tthis.trigger('optgroup_remove', id);\n\t\t}\n\t}\n\n\t/**\n\t * Clears all existing option groups.\n\t */\n\tclearOptionGroups() {\n\t\tthis.optgroups = {};\n\t\tthis.clearCache();\n\t\tthis.trigger('optgroup_clear');\n\t}\n\n\t/**\n\t * Updates an option available for selection. If\n\t * it is visible in the selected items or options\n\t * dropdown, it will be re-rendered automatically.\n\t *\n\t */\n\tupdateOption(value:string, data:TomOption) {\n\t\tconst self = this;\n\t\tvar item_new;\n\t\tvar index_item;\n\n\t\tconst value_old\t\t= hash_key(value);\n\t\tconst value_new\t\t= hash_key(data[self.settings.valueField]);\n\n\t\t// sanity checks\n\t\tif( value_old === null ) return;\n\n\t\tconst data_old\t\t= self.options[value_old];\n\n\t\tif( data_old == undefined ) return;\n\t\tif( typeof value_new !== 'string' ) throw new Error('Value must be set in option data');\n\n\n\t\tconst option\t\t= self.getOption(value_old);\n\t\tconst item\t\t\t= self.getItem(value_old);\n\n\n\t\tdata.$order = data.$order || data_old.$order;\n\t\tdelete self.options[value_old];\n\n\t\t// invalidate render cache\n\t\t// don't remove existing node yet, we'll remove it after replacing it\n\t\tself.uncacheValue(value_new);\n\n\t\tself.options[value_new] = data;\n\n\t\t// update the option if it's in the dropdown\n\t\tif( option ){\n\t\t\tif( self.dropdown_content.contains(option) ){\n\n\t\t\t\tconst option_new\t= self._render('option', data);\n\t\t\t\treplaceNode(option, option_new);\n\n\t\t\t\tif( self.activeOption === option ){\n\t\t\t\t\tself.setActiveOption(option_new);\n\t\t\t\t}\n\t\t\t}\n\t\t\toption.remove();\n\t\t}\n\n\t\t// update the item if we have one\n\t\tif( item ){\n\t\t\tindex_item = self.items.indexOf(value_old);\n\t\t\tif (index_item !== -1) {\n\t\t\t\tself.items.splice(index_item, 1, value_new);\n\t\t\t}\n\n\t\t\titem_new\t= self._render('item', data);\n\n\t\t\tif( item.classList.contains('active') ) addClasses(item_new,'active');\n\n\t\t\treplaceNode( item, item_new);\n\t\t}\n\n\t\t// invalidate last query because we might have updated the sortField\n\t\tself.lastQuery = null;\n\t}\n\n\t/**\n\t * Removes a single option.\n\t *\n\t */\n\tremoveOption(value:string, silent?:boolean):void {\n\t\tconst self = this;\n\t\tvalue = get_hash(value);\n\n\t\tself.uncacheValue(value);\n\n\t\tdelete self.userOptions[value];\n\t\tdelete self.options[value];\n\t\tself.lastQuery = null;\n\t\tself.trigger('option_remove', value);\n\t\tself.removeItem(value, silent);\n\t}\n\n\t/**\n\t * Clears all options.\n\t */\n\tclearOptions(filter?:TomClearFilter ) {\n\n\t\tconst boundFilter = (filter || this.clearFilter).bind(this);\n\n\t\tthis.loadedSearches\t\t= {};\n\t\tthis.userOptions\t\t= {};\n\t\tthis.clearCache();\n\n\t\tconst selected:TomOptions\t= {};\n\t\titerate(this.options,(option:TomOption,key:string)=>{\n\t\t\tif( boundFilter(option,key as string) ){\n\t\t\t\tselected[key] = option;\n\t\t\t}\n\t\t});\n\n\t\tthis.options = this.sifter.items = selected;\n\t\tthis.lastQuery = null;\n\t\tthis.trigger('option_clear');\n\t}\n\n\t/**\n\t * Used by clearOptions() to decide whether or not an option should be removed\n\t * Return true to keep an option, false to remove\n\t *\n\t */\n\tclearFilter(option:TomOption,value:string){\n\t\tif( this.items.indexOf(value) >= 0 ){\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * Returns the dom element of the option\n\t * matching the given value.\n\t *\n\t */\n\tgetOption(value:undefined|null|boolean|string|number, create:boolean=false):null|HTMLElement {\n\n\t\tconst hashed = hash_key(value);\n\t\tif( hashed === null ) return null;\n\n\t\tconst option = this.options[hashed];\n\t\tif( option != undefined ){\n\n\t\t\tif( option.$div ){\n\t\t\t\treturn option.$div;\n\t\t\t}\n\n\t\t\tif( create ){\n\t\t\t\treturn this._render('option', option);\n\t\t\t}\n\t\t}\n\n\t\treturn null;\n\t}\n\n\t/**\n\t * Returns the dom element of the next or previous dom element of the same type\n\t * Note: adjacent options may not be adjacent DOM elements (optgroups)\n\t *\n\t */\n\tgetAdjacent( option:null|HTMLElement, direction:number, type:string = 'option' ) : HTMLElement|null{\n\t\tvar self = this, all;\n\n\t\tif( !option ){\n\t\t\treturn null;\n\t\t}\n\n\t\tif( type == 'item' ){\n\t\t\tall\t\t\t= self.controlChildren();\n\t\t}else{\n\t\t\tall\t\t\t= self.dropdown_content.querySelectorAll('[data-selectable]');\n\t\t}\n\n\t\tfor( let i = 0; i < all.length; i++ ){\n\t\t\tif( all[i] != option ){\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif( direction > 0 ){\n\t\t\t\treturn all[i+1] as HTMLElement;\n\t\t\t}\n\n\t\t\treturn all[i-1] as HTMLElement;\n\t\t}\n\t\treturn null;\n\t}\n\n\n\t/**\n\t * Returns the dom element of the item\n\t * matching the given value.\n\t *\n\t */\n\tgetItem(item:string|TomItem|null):null|TomItem {\n\n\t\tif( typeof item == 'object' ){\n\t\t\treturn item;\n\t\t}\n\n\t\tvar value = hash_key(item);\n\t\treturn value !== null\n\t\t\t? this.control.querySelector(`[data-value=\"${addSlashes(value)}\"]`)\n\t\t\t: null;\n\t}\n\n\t/**\n\t * \"Selects\" multiple items at once. Adds them to the list\n\t * at the current caret position.\n\t *\n\t */\n\taddItems( values:string|string[], silent?:boolean ):void{\n\t\tvar self = this;\n\n\t\tvar items = Array.isArray(values) ? values : [values];\n\t\titems = items.filter(x => self.items.indexOf(x) === -1);\n\t\tconst last_item = items[items.length - 1];\n\t\titems.forEach(item => {\n\t\t\tself.isPending = (item !== last_item);\n\t\t\tself.addItem(item, silent);\n\t\t});\n\t}\n\n\t/**\n\t * \"Selects\" an item. Adds it to the list\n\t * at the current caret position.\n\t *\n\t */\n\taddItem( value:string, silent?:boolean ):void{\n\t\tvar events = silent ? [] : ['change','dropdown_close'];\n\n\t\tdebounce_events(this, events, () => {\n\t\t\tvar item, wasFull;\n\t\t\tconst self = this;\n\t\t \tconst inputMode = self.settings.mode;\n\t\t\tconst hashed = hash_key(value);\n\n\t\t\tif( hashed && self.items.indexOf(hashed) !== -1 ){\n\n\t\t\t\tif( inputMode === 'single' ){\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\n\t\t\t\tif( inputMode === 'single' || !self.settings.duplicates ){\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (hashed === null || !self.options.hasOwnProperty(hashed)) return;\n\t\t\tif (inputMode === 'single') self.clear(silent);\n\t\t\tif (inputMode === 'multi' && self.isFull()) return;\n\n\t\t\titem = self._render('item', self.options[hashed]);\n\n\t\t\tif( self.control.contains(item) ){ // duplicates\n\t\t\t\titem = item.cloneNode(true) as HTMLElement;\n\t\t\t}\n\n\t\t\twasFull = self.isFull();\n\t\t\tself.items.splice(self.caretPos, 0, hashed);\n\t\t\tself.insertAtCaret(item);\n\n\t\t\tif (self.isSetup) {\n\n\t\t\t\t// update menu / remove the option (if this is not one item being added as part of series)\n\t\t\t\tif( !self.isPending && self.settings.hideSelected ){\n\t\t\t\t\tlet option = self.getOption(hashed);\n\t\t\t\t\tlet next = self.getAdjacent(option, 1);\n\t\t\t\t\tif( next ){\n\t\t\t\t\t\tself.setActiveOption(next);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// refreshOptions after setActiveOption(),\n\t\t\t\t// otherwise setActiveOption() will be called by refreshOptions() with the wrong value\n\t\t\t\tif( !self.isPending && !self.settings.closeAfterSelect ){\n\t\t\t\t\tself.refreshOptions(self.isFocused && inputMode !== 'single');\n\t\t\t\t}\n\n\t\t\t\t// hide the menu if the maximum number of items have been selected or no options are left\n\t\t\t\tif( self.settings.closeAfterSelect != false && self.isFull() ){\n\t\t\t\t\tself.close();\n\t\t\t\t} else if (!self.isPending) {\n\t\t\t\t\tself.positionDropdown();\n\t\t\t\t}\n\n\t\t\t\tself.trigger('item_add', hashed, item);\n\n\t\t\t\tif (!self.isPending) {\n\t\t\t\t\tself.updateOriginalInput({silent: silent});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (!self.isPending || (!wasFull && self.isFull())) {\n\t\t\t\tself.inputState();\n\t\t\t\tself.refreshState();\n\t\t\t}\n\n\t\t});\n\t}\n\n\t/**\n\t * Removes the selected item matching\n\t * the provided value.\n\t *\n\t */\n\tremoveItem( item:string|TomItem|null=null, silent?:boolean ){\n\t\tconst self\t\t= this;\n\t\titem\t\t\t= self.getItem(item);\n\n\t\tif( !item ) return;\n\n\t\tvar i,idx;\n\t\tconst value\t= item.dataset.value;\n\t\ti = nodeIndex(item);\n\n\t\titem.remove();\n\t\tif( item.classList.contains('active') ){\n\t\t\tidx = self.activeItems.indexOf(item);\n\t\t\tself.activeItems.splice(idx, 1);\n\t\t\tremoveClasses(item,'active');\n\t\t}\n\n\t\tself.items.splice(i, 1);\n\t\tself.lastQuery = null;\n\t\tif (!self.settings.persist && self.userOptions.hasOwnProperty(value)) {\n\t\t\tself.removeOption(value, silent);\n\t\t}\n\n\t\tif (i < self.caretPos) {\n\t\t\tself.setCaret(self.caretPos - 1);\n\t\t}\n\n\t\tself.updateOriginalInput({silent: silent});\n\t\tself.refreshState();\n\t\tself.positionDropdown();\n\t\tself.trigger('item_remove', value, item);\n\n\t}\n\n\t/**\n\t * Invokes the `create` method provided in the\n\t * TomSelect options that should provide the data\n\t * for the new item, given the user input.\n\t *\n\t * Once this completes, it will be added\n\t * to the item list.\n\t *\n\t */\n\tcreateItem( input:null|string=null, callback:TomCreateCallback = ()=>{} ):boolean{\n\n\t\t// triggerDropdown parameter @deprecated 2.1.1\n\t\tif( arguments.length === 3 ){\n\t\t\tcallback = arguments[2];\n\t\t}\n\t\tif( typeof callback != 'function' ){\n\t\t\tcallback = () => {};\n\t\t}\n\n\t\tvar self  = this;\n\t\tvar caret = self.caretPos;\n\t\tvar output;\n\t\tinput = input || self.inputValue();\n\n\t\tif (!self.canCreate(input)) {\n\t\t\tcallback();\n\t\t\treturn false;\n\t\t}\n\n\t\tself.lock();\n\n\t\tvar created = false;\n\t\tvar create = (data?:boolean|TomOption) => {\n\t\t\tself.unlock();\n\n\t\t\tif (!data || typeof data !== 'object') return callback();\n\t\t\tvar value = hash_key(data[self.settings.valueField]);\n\t\t\tif( typeof value !== 'string' ){\n\t\t\t\treturn callback();\n\t\t\t}\n\n\t\t\tself.setTextboxValue();\n\t\t\tself.addOption(data,true);\n\t\t\tself.setCaret(caret);\n\t\t\tself.addItem(value);\n\t\t\tcallback(data);\n\t\t\tcreated = true;\n\t\t};\n\n\t\tif( typeof self.settings.create === 'function' ){\n\t\t\toutput = self.settings.create.call(this, input, create);\n\t\t}else{\n\t\t\toutput = {\n\t\t\t\t[self.settings.labelField]: input,\n\t\t\t\t[self.settings.valueField]: input,\n\t\t\t};\n\t\t}\n\n\t\tif( !created ){\n\t\t\tcreate(output);\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Re-renders the selected item lists.\n\t */\n\trefreshItems() {\n\t\tvar self = this;\n\t\tself.lastQuery = null;\n\n\t\tif (self.isSetup) {\n\t\t\tself.addItems(self.items);\n\t\t}\n\n\t\tself.updateOriginalInput();\n\t\tself.refreshState();\n\t}\n\n\t/**\n\t * Updates all state-dependent attributes\n\t * and CSS classes.\n\t */\n\trefreshState() {\n\t\tconst self     = this;\n\n\t\tself.refreshValidityState();\n\n\t\tconst isFull\t= self.isFull();\n\t\tconst isLocked\t= self.isLocked;\n\n\t\tself.wrapper.classList.toggle('rtl',self.rtl);\n\n\n\t\tconst wrap_classList = self.wrapper.classList;\n\n\t\twrap_classList.toggle('focus', self.isFocused)\n\t\twrap_classList.toggle('disabled', self.isDisabled)\n\t\twrap_classList.toggle('readonly', self.isReadOnly)\n\t\twrap_classList.toggle('required', self.isRequired)\n\t\twrap_classList.toggle('invalid', !self.isValid)\n\t\twrap_classList.toggle('locked', isLocked)\n\t\twrap_classList.toggle('full', isFull)\n\t\twrap_classList.toggle('input-active', self.isFocused && !self.isInputHidden)\n\t\twrap_classList.toggle('dropdown-active', self.isOpen)\n\t\twrap_classList.toggle('has-options', isEmptyObject(self.options) )\n\t\twrap_classList.toggle('has-items', self.items.length > 0);\n\n\t}\n\n\n\t/**\n\t * Update the `required` attribute of both input and control input.\n\t *\n\t * The `required` property needs to be activated on the control input\n\t * for the error to be displayed at the right place. `required` also\n\t * needs to be temporarily deactivated on the input since the input is\n\t * hidden and can't show errors.\n\t */\n\trefreshValidityState() {\n\t\tvar self = this;\n\n\t\tif( !self.input.validity ){\n\t\t\treturn;\n\t\t}\n\n\t\tself.isValid = self.input.validity.valid;\n\t\tself.isInvalid = !self.isValid;\n\t}\n\n\t/**\n\t * Determines whether or not more items can be added\n\t * to the control without exceeding the user-defined maximum.\n\t *\n\t * @returns {boolean}\n\t */\n\tisFull() {\n\t\treturn this.settings.maxItems !== null && this.items.length >= this.settings.maxItems;\n\t}\n\n\t/**\n\t * Refreshes the original <select> or <input>\n\t * element to reflect the current state.\n\t *\n\t */\n\tupdateOriginalInput( opts:TomArgObject = {} ){\n\t\tconst self = this;\n\t\tvar option, label;\n\n\t\tconst empty_option = self.input.querySelector('option[value=\"\"]') as HTMLOptionElement;\n\n\t\tif( self.is_select_tag ){\n\n\t\t\tconst selected:HTMLOptionElement[]\t\t= [];\n\t\t\tconst has_selected:number\t\t\t\t= self.input.querySelectorAll('option:checked').length;\n\n\t\t\tfunction AddSelected(option_el:HTMLOptionElement|null, value:string, label:string):HTMLOptionElement{\n\n\t\t\t\tif( !option_el ){\n\t\t\t\t\toption_el = getDom('<option value=\"' + escape_html(value) + '\">' + escape_html(label) + '</option>') as HTMLOptionElement;\n\t\t\t\t}\n\n\t\t\t\t// don't move empty option from top of list\n\t\t\t\t// fixes bug in firefox https://bugzilla.mozilla.org/show_bug.cgi?id=1725293\n\t\t\t\tif( option_el != empty_option ){\n\t\t\t\t\tself.input.append(option_el);\n\t\t\t\t}\n\n\t\t\t\tselected.push(option_el);\n\n\t\t\t\t// marking empty option as selected can break validation\n\t\t\t\t// fixes https://github.com/orchidjs/tom-select/issues/303\n\t\t\t\tif( option_el != empty_option || has_selected > 0 ){\n\t\t\t\t\toption_el.selected = true;\n\t\t\t\t}\n\n\t\t\t\treturn option_el;\n\t\t\t}\n\n\t\t\t// unselect all selected options\n\t\t\tself.input.querySelectorAll('option:checked').forEach((option_el:Element) => {\n\t\t\t\t(<HTMLOptionElement>option_el).selected = false;\n\t\t\t});\n\n\n\t\t\t// nothing selected?\n\t\t\tif( self.items.length == 0 && self.settings.mode == 'single' ){\n\n\t\t\t\tAddSelected(empty_option, \"\", \"\");\n\n\t\t\t// order selected <option> tags for values in self.items\n\t\t\t}else{\n\n\t\t\t\tself.items.forEach((value)=>{\n\t\t\t\t\toption\t\t\t= self.options[value]!;\n\t\t\t\t\tlabel\t\t\t= option[self.settings.labelField] || '';\n\n\t\t\t\t\tif( selected.includes(option.$option) ){\n\t\t\t\t\t\tconst reuse_opt = self.input.querySelector(`option[value=\"${addSlashes(value)}\"]:not(:checked)`) as HTMLOptionElement;\n\t\t\t\t\t\tAddSelected(reuse_opt, value, label);\n\t\t\t\t\t}else{\n\t\t\t\t\t\toption.$option\t= AddSelected(option.$option, value, label);\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t}\n\n\t\t} else {\n\t\t\tself.input.value = self.getValue() as string;\n\t\t}\n\n\t\tif (self.isSetup) {\n\t\t\tif (!opts.silent) {\n\t\t\t\tself.trigger('change', self.getValue() );\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Shows the autocomplete dropdown containing\n\t * the available options.\n\t */\n\topen() {\n\t\tvar self = this;\n\n\t\tif (self.isLocked || self.isOpen || (self.settings.mode === 'multi' && self.isFull())) return;\n\t\tself.isOpen = true;\n\t\tsetAttr(self.focus_node,{'aria-expanded': 'true'});\n\t\tself.refreshState();\n\t\tapplyCSS(self.dropdown,{visibility: 'hidden', display: 'block'});\n\t\tself.positionDropdown();\n\t\tapplyCSS(self.dropdown,{visibility: 'visible', display: 'block'});\n\t\tself.focus();\n\t\tself.trigger('dropdown_open', self.dropdown);\n\t}\n\n\t/**\n\t * Closes the autocomplete dropdown menu.\n\t */\n\tclose(setTextboxValue=true) {\n\t\tvar self = this;\n\t\tvar trigger = self.isOpen;\n\n\t\tif( setTextboxValue ){\n\n\t\t\t// before blur() to prevent form onchange event\n\t\t\tself.setTextboxValue();\n\n\t\t\tif (self.settings.mode === 'single' && self.items.length) {\n\t\t\t\tself.inputState();\n\t\t\t}\n\t\t}\n\n\t\tself.isOpen = false;\n\t\tsetAttr(self.focus_node,{'aria-expanded': 'false'});\n\t\tapplyCSS(self.dropdown,{display: 'none'});\n\t\tif( self.settings.hideSelected ){\n\t\t\tself.clearActiveOption();\n\t\t}\n\t\tself.refreshState();\n\n\t\tif (trigger) self.trigger('dropdown_close', self.dropdown);\n\t}\n\n\t/**\n\t * Calculates and applies the appropriate\n\t * position of the dropdown if dropdownParent = 'body'.\n\t * Otherwise, position is determined by css\n\t */\n\tpositionDropdown(){\n\n\t\tif( this.settings.dropdownParent !== 'body' ){\n\t\t\treturn;\n\t\t}\n\n\t\tvar context\t\t\t= this.control;\n\t\tvar rect\t\t\t= context.getBoundingClientRect();\n\t\tvar top\t\t\t\t= context.offsetHeight + rect.top  + window.scrollY;\n\t\tvar left\t\t\t= rect.left + window.scrollX;\n\n\n\t\tapplyCSS(this.dropdown,{\n\t\t\twidth : rect.width + 'px',\n\t\t\ttop   : top + 'px',\n\t\t\tleft  : left + 'px'\n\t\t});\n\n\t}\n\n\t/**\n\t * Resets / clears all selected items\n\t * from the control.\n\t *\n\t */\n\tclear(silent?:boolean) {\n\t\tvar self = this;\n\n\t\tif (!self.items.length) return;\n\n\t\tvar items = self.controlChildren();\n\t\titerate(items,(item:TomItem)=>{\n\t\t\tself.removeItem(item,true);\n\t\t});\n\n\t\tself.inputState();\n\t\tif( !silent ) self.updateOriginalInput();\n\t\tself.trigger('clear');\n\t}\n\n\t/**\n\t * A helper method for inserting an element\n\t * at the current caret position.\n\t *\n\t */\n\tinsertAtCaret(el:HTMLElement) {\n\t\tconst self\t\t= this;\n\t\tconst caret\t\t= self.caretPos;\n\t\tconst target\t= self.control;\n\n\t\ttarget.insertBefore(el, target.children[caret] || null);\n\t\tself.setCaret(caret + 1);\n\t}\n\n\t/**\n\t * Removes the current selected item(s).\n\t *\n\t */\n\tdeleteSelection(e:KeyboardEvent):boolean {\n\t\tvar direction, selection, caret, tail;\n\t\tvar self = this;\n\n\t\tdirection = (e && e.keyCode === constants.KEY_BACKSPACE) ? -1 : 1;\n\t\tselection = getSelection(self.control_input);\n\n\n\t\t// determine items that will be removed\n\t\tconst rm_items:TomItem[]\t= [];\n\n\t\tif (self.activeItems.length) {\n\n\t\t\ttail = getTail(self.activeItems, direction);\n\t\t\tcaret = nodeIndex(tail);\n\n\t\t\tif (direction > 0) { caret++; }\n\n\t\t\titerate(self.activeItems, (item:TomItem) => rm_items.push(item) );\n\n\t\t} else if ((self.isFocused || self.settings.mode === 'single') && self.items.length) {\n\t\t\tconst items = self.controlChildren();\n\t\t\tlet rm_item;\n\t\t\tif( direction < 0 && selection.start === 0 && selection.length === 0 ){\n\t\t\t\trm_item = items[self.caretPos - 1];\n\n\t\t\t}else if( direction > 0 && selection.start === self.inputValue().length ){\n\t\t\t\trm_item = items[self.caretPos];\n\t\t\t}\n\n\t\t\tif( rm_item !== undefined ){\n\t\t\t\trm_items.push( rm_item );\n\t\t\t}\n\t\t}\n\n\t\tif( !self.shouldDelete(rm_items,e) ){\n\t\t\treturn false;\n\t\t}\n\n\t\tpreventDefault(e,true);\n\n\t\t// perform removal\n\t\tif (typeof caret !== 'undefined') {\n\t\t\tself.setCaret(caret);\n\t\t}\n\n\t\twhile( rm_items.length ){\n\t\t\tself.removeItem(rm_items.pop());\n\t\t}\n\n\t\tself.inputState();\n\t\tself.positionDropdown();\n\t\tself.refreshOptions(false);\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Return true if the items should be deleted\n\t */\n\tshouldDelete(items:TomItem[],evt:MouseEvent|KeyboardEvent){\n\n\t\tconst values = items.map(item => item.dataset.value);\n\n\t\t// allow the callback to abort\n\t\tif( !values.length || (typeof this.settings.onDelete === 'function' && this.settings.onDelete(values,evt) === false) ){\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Selects the previous / next item (depending on the `direction` argument).\n\t *\n\t * > 0 - right\n\t * < 0 - left\n\t *\n\t */\n\tadvanceSelection(direction:number, e?:MouseEvent|KeyboardEvent) {\n\t\tvar last_active, adjacent, self = this;\n\n\t\tif (self.rtl) direction *= -1;\n\t\tif( self.inputValue().length ) return;\n\n\n\t\t// add or remove to active items\n\t\tif( isKeyDown(constants.KEY_SHORTCUT,e) || isKeyDown('shiftKey',e) ){\n\n\t\t\tlast_active\t\t\t= self.getLastActive(direction);\n\t\t\tif( last_active ){\n\n\t\t\t\tif( !last_active.classList.contains('active') ){\n\t\t\t\t\tadjacent\t\t\t= last_active;\n\t\t\t\t}else{\n\t\t\t\t\tadjacent\t\t\t= self.getAdjacent(last_active,direction,'item');\n\t\t\t\t}\n\n\t\t\t// if no active item, get items adjacent to the control input\n\t\t\t}else if( direction > 0 ){\n\t\t\t\tadjacent\t\t\t= self.control_input.nextElementSibling;\n\t\t\t}else{\n\t\t\t\tadjacent\t\t\t= self.control_input.previousElementSibling;\n\t\t\t}\n\n\n\t\t\tif( adjacent ){\n\t\t\t\tif( adjacent.classList.contains('active') ){\n\t\t\t\t\tself.removeActiveItem(last_active);\n\t\t\t\t}\n\t\t\t\tself.setActiveItemClass(adjacent); // mark as last_active !! after removeActiveItem() on last_active\n\t\t\t}\n\n\t\t// move caret to the left or right\n\t\t}else{\n\t\t\tself.moveCaret(direction);\n\t\t}\n\t}\n\n\tmoveCaret(direction:number){}\n\n\t/**\n\t * Get the last active item\n\t *\n\t */\n\tgetLastActive(direction?:number){\n\n\t\tlet last_active = this.control.querySelector('.last-active');\n\t\tif( last_active ){\n\t\t\treturn last_active;\n\t\t}\n\n\n\t\tvar result = this.control.querySelectorAll('.active');\n\t\tif( result ){\n\t\t\treturn getTail(result,direction);\n\t\t}\n\t}\n\n\n\t/**\n\t * Moves the caret to the specified index.\n\t *\n\t * The input must be moved by leaving it in place and moving the\n\t * siblings, due to the fact that focus cannot be restored once lost\n\t * on mobile webkit devices\n\t *\n\t */\n\tsetCaret(new_pos:number) {\n\t\tthis.caretPos = this.items.length;\n\t}\n\n\t/**\n\t * Return list of item dom elements\n\t *\n\t */\n\tcontrolChildren():TomItem[]{\n\t\treturn Array.from( this.control.querySelectorAll('[data-ts-item]') ) as TomItem[];\n\t}\n\n\t/**\n\t * Disables user input on the control. Used while\n\t * items are being asynchronously created.\n\t */\n\tlock() {\n\t\tthis.setLocked(true);\n\t}\n\n\t/**\n\t * Re-enables user input on the control.\n\t */\n\tunlock() {\n\t\tthis.setLocked(false);\n\t}\n\n\t/**\n\t * Disable or enable user input on the control\n\t */\n\tsetLocked( lock:boolean = this.isReadOnly || this.isDisabled ){\n\t\tthis.isLocked = lock;\n\t\tthis.refreshState();\n\t}\n\n\t/**\n\t * Disables user input on the control completely.\n\t * While disabled, it cannot receive focus.\n\t */\n\tdisable() {\n\t\tthis.setDisabled(true);\n\t\tthis.close();\n\t}\n\n\t/**\n\t * Enables the control so that it can respond\n\t * to focus and user input.\n\t */\n\tenable() {\n\t\tthis.setDisabled(false);\n\t}\n\n\tsetDisabled(disabled:boolean){\n\t\tthis.focus_node.tabIndex\t\t= disabled ? -1 : this.tabIndex;\n\t\tthis.isDisabled\t\t\t\t\t= disabled;\n\t\tthis.input.disabled\t\t\t\t= disabled;\n\t\tthis.control_input.disabled\t\t= disabled;\n\t\tthis.setLocked();\n\t}\n\n\tsetReadOnly(isReadOnly:boolean){\n\t\tthis.isReadOnly\t\t\t\t\t= isReadOnly;\n\t\tthis.input.readOnly\t\t\t\t= isReadOnly;\n\t\tthis.control_input.readOnly\t\t= isReadOnly;\n\t\tthis.setLocked();\n\t}\n\n\t/**\n\t * Completely destroys the control and\n\t * unbinds all event listeners so that it can\n\t * be garbage collected.\n\t */\n\tdestroy() {\n\t\tvar self = this;\n\t\tvar revertSettings = self.revertSettings;\n\n\t\tself.trigger('destroy');\n\t\tself.off();\n\t\tself.wrapper.remove();\n\t\tself.dropdown.remove();\n\n\t\tself.input.innerHTML = revertSettings.innerHTML;\n\t\tself.input.tabIndex = revertSettings.tabIndex;\n\n\t\tremoveClasses(self.input,'tomselected','ts-hidden-accessible');\n\n\t\tself._destroy();\n\n\t\tdelete self.input.tomselect;\n\t}\n\n\t/**\n\t * A helper method for rendering \"item\" and\n\t * \"option\" templates, given the data.\n\t *\n\t */\n\trender( templateName:TomTemplateNames, data?:any ):null|HTMLElement{\n\t\tvar id, html;\n\t\tconst self = this;\n\n\t\tif( typeof this.settings.render[templateName] !== 'function' ){\n\t\t\treturn null;\n\t\t}\n\n\t\t// render markup\n\t\thtml = self.settings.render[templateName].call(this, data, escape_html);\n\n\t\tif( !html ){\n\t\t\treturn null;\n\t\t}\n\n\t\thtml = getDom( html );\n\n\t\t// add mandatory attributes\n\t\tif (templateName === 'option' || templateName === 'option_create') {\n\n\t\t\tif( data[self.settings.disabledField] ){\n\t\t\t\tsetAttr(html,{'aria-disabled':'true'});\n\t\t\t}else{\n\t\t\t\tsetAttr(html,{'data-selectable': ''});\n\t\t\t}\n\n\t\t}else if (templateName === 'optgroup') {\n\t\t\tid = data.group[self.settings.optgroupValueField];\n\t\t\tsetAttr(html,{'data-group': id});\n\t\t\tif(data.group[self.settings.disabledField]) {\n\t\t\t\tsetAttr(html,{'data-disabled': ''});\n\t\t\t}\n\t\t}\n\n\t\tif (templateName === 'option' || templateName === 'item') {\n\t\t\tconst value\t= get_hash(data[self.settings.valueField]);\n\t\t\tsetAttr(html,{'data-value': value });\n\n\n\t\t\t// make sure we have some classes if a template is overwritten\n\t\t\tif( templateName === 'item' ){\n\t\t\t\taddClasses(html,self.settings.itemClass);\n\t\t\t\tsetAttr(html,{'data-ts-item':''});\n\t\t\t}else{\n\t\t\t\taddClasses(html,self.settings.optionClass);\n\t\t\t\tsetAttr(html,{\n\t\t\t\t\trole:'option',\n\t\t\t\t\tid:data.$id\n\t\t\t\t});\n\n\t\t\t\t// update cache\n\t\t\t\tdata.$div = html;\n\t\t\t\tself.options[value] = data;\n\t\t\t}\n\n\n\t\t}\n\n\t\treturn html;\n\n\t}\n\n\n\t/**\n\t * Type guarded rendering\n\t *\n\t */\n\t_render( templateName:TomTemplateNames, data?:any ):HTMLElement{\n\t\tconst html = this.render(templateName, data);\n\n\t\tif( html == null ){\n\t\t\tthrow 'HTMLElement expected';\n\t\t}\n\t\treturn html;\n\t}\n\n\n\t/**\n\t * Clears the render cache for a template. If\n\t * no template is given, clears all render\n\t * caches.\n\t *\n\t */\n\tclearCache():void{\n\n\t\titerate(this.options, (option:TomOption)=>{\n\t\t\tif( option.$div ){\n\t\t\t\toption.$div.remove();\n\t\t\t\tdelete option.$div;\n\t\t\t}\n\t\t});\n\n\t}\n\n\t/**\n\t * Removes a value from item and option caches\n\t *\n\t */\n\tuncacheValue(value:string){\n\n\t\tconst option_el\t\t\t= this.getOption(value);\n\t\tif( option_el ) option_el.remove();\n\n\t}\n\n\t/**\n\t * Determines whether or not to display the\n\t * create item prompt, given a user input.\n\t *\n\t */\n\tcanCreate( input:string ):boolean {\n\t\treturn this.settings.create && (input.length > 0) && (this.settings.createFilter as TomCreateFilter ).call(this, input);\n\t}\n\n\n\t/**\n\t * Wraps this.`method` so that `new_fn` can be invoked 'before', 'after', or 'instead' of the original method\n\t *\n\t * this.hook('instead','onKeyDown',function( arg1, arg2 ...){\n\t *\n\t * });\n\t */\n\thook( when:string, method:string, new_fn:any ){\n\t\tvar self = this;\n\t\tvar orig_method = self[method];\n\n\n\t\tself[method] = function(){\n\t\t\tvar result, result_new;\n\n\t\t\tif( when === 'after' ){\n\t\t\t\tresult = orig_method.apply(self, arguments);\n\t\t\t}\n\n\t\t\tresult_new = new_fn.apply(self, arguments );\n\n\t\t\tif( when === 'instead' ){\n\t\t\t\treturn result_new;\n\t\t\t}\n\n\t\t\tif( when === 'before' ){\n\t\t\t\tresult = orig_method.apply(self, arguments);\n\t\t\t}\n\n\t\t\treturn result;\n\t\t};\n\n\t}\n\n};\n", "/**\n * microplugin.js\n * Copyright (c) 2013 <PERSON> & <PERSON>\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n * <AUTHOR> <<EMAIL>>\n */\n\ntype TSettings = {\n\t[key:string]:any\n}\n\ntype TPlugins = {\n\tnames: string[],\n\tsettings: TSettings,\n\trequested: {[key:string]:boolean},\n\tloaded: {[key:string]:any}\n};\n\nexport type TPluginItem = {name:string,options:{}};\nexport type TPluginHash = {[key:string]:{}};\n\n\n\n\nexport default function MicroPlugin(Interface: any ){\n\n\tInterface.plugins = {};\n\n\treturn class extends Interface{\n\n\t\tpublic plugins:TPlugins = {\n\t\t\tnames     : [],\n\t\t\tsettings  : {},\n\t\t\trequested : {},\n\t\t\tloaded    : {}\n\t\t};\n\n\t\t/**\n\t\t * Registers a plugin.\n\t\t *\n\t\t * @param {function} fn\n\t\t */\n\t\tstatic define(name:string, fn:(this:any,settings:TSettings)=>any){\n\t\t\tInterface.plugins[name] = {\n\t\t\t\t'name' : name,\n\t\t\t\t'fn'   : fn\n\t\t\t};\n\t\t}\n\n\n\t\t/**\n\t\t * Initializes the listed plugins (with options).\n\t\t * Acceptable formats:\n\t\t *\n\t\t * List (without options):\n\t\t *   ['a', 'b', 'c']\n\t\t *\n\t\t * List (with options):\n\t\t *   [{'name': 'a', options: {}}, {'name': 'b', options: {}}]\n\t\t *\n\t\t * Hash (with options):\n\t\t *   {'a': { ... }, 'b': { ... }, 'c': { ... }}\n\t\t *\n\t\t * @param {array|object} plugins\n\t\t */\n\t\tinitializePlugins(plugins:string[]|TPluginItem[]|TPluginHash) {\n\t\t\tvar key, name;\n\t\t\tconst self  = this;\n\t\t\tconst queue:string[] = [];\n\n\t\t\tif (Array.isArray(plugins)) {\n\t\t\t\tplugins.forEach((plugin:string|TPluginItem)=>{\n\t\t\t\t\tif (typeof plugin === 'string') {\n\t\t\t\t\t\tqueue.push(plugin);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.plugins.settings[plugin.name] = plugin.options;\n\t\t\t\t\t\tqueue.push(plugin.name);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else if (plugins) {\n\t\t\t\tfor (key in plugins) {\n\t\t\t\t\tif (plugins.hasOwnProperty(key)) {\n\t\t\t\t\t\tself.plugins.settings[key] = plugins[key];\n\t\t\t\t\t\tqueue.push(key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\twhile( name = queue.shift() ){\n\t\t\t\tself.require(name);\n\t\t\t}\n\t\t}\n\n\t\tloadPlugin(name:string) {\n\t\t\tvar self    = this;\n\t\t\tvar plugins = self.plugins;\n\t\t\tvar plugin  = Interface.plugins[name];\n\n\t\t\tif (!Interface.plugins.hasOwnProperty(name)) {\n\t\t\t\tthrow new Error('Unable to find \"' +  name + '\" plugin');\n\t\t\t}\n\n\t\t\tplugins.requested[name] = true;\n\t\t\tplugins.loaded[name] = plugin.fn.apply(self, [self.plugins.settings[name] || {}]);\n\t\t\tplugins.names.push(name);\n\t\t}\n\n\t\t/**\n\t\t * Initializes a plugin.\n\t\t *\n\t\t */\n\t\trequire(name:string) {\n\t\t\tvar self = this;\n\t\t\tvar plugins = self.plugins;\n\n\t\t\tif (!self.plugins.loaded.hasOwnProperty(name)) {\n\t\t\t\tif (plugins.requested[name]) {\n\t\t\t\t\tthrow new Error('Plugin has circular dependency (\"' + name + '\")');\n\t\t\t\t}\n\t\t\t\tself.loadPlugin(name);\n\t\t\t}\n\n\t\t\treturn plugins.loaded[name];\n\t\t}\n\n\t};\n\n}\n"], "names": ["forEvents", "events", "callback", "split", "for<PERSON>ach", "event", "MicroEvent", "constructor", "this", "_events", "on", "fct", "event_array", "push", "off", "n", "arguments", "length", "undefined", "splice", "indexOf", "trigger", "args", "self", "apply", "arrayToPattern", "chars", "filter", "Boolean", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "join", "sequencePattern", "array", "hasDuplicates", "pattern", "prev_char_count", "prev_pattern", "char", "i", "setToPattern", "Array", "from", "Set", "size", "escape_regex", "str", "replace", "reduce", "longest", "value", "Math", "max", "unicodeLength", "allSubstrings", "input", "result", "start", "substring", "subresult", "tmp", "slice", "char<PERSON>t", "unshift", "code_points", "unicode_map", "multi_char_reg", "latin_convert", "latin_condensed", "a", "aa", "ae", "ao", "au", "av", "ay", "b", "c", "d", "e", "f", "g", "h", "j", "k", "l", "m", "o", "oe", "oi", "oo", "ou", "p", "q", "r", "s", "t", "th", "tz", "u", "v", "vy", "w", "y", "z", "hv", "latin", "unicode", "convert_pat", "RegExp", "Object", "keys", "normalize", "form", "<PERSON><PERSON><PERSON><PERSON>", "_asciifold", "toLowerCase", "generateSets", "unicode_sets", "addMatching", "folded", "to_add", "folded_set", "patt", "match", "add", "code_point_min", "code_point_max", "composed", "String", "fromCharCode", "code_point", "generator", "generateMap", "multi_char", "set", "sort", "multi_char_patt", "substringsToPattern", "min_replacement", "map", "sub_pat", "strings", "chars_replaced", "mapSequence", "sequencesToPattern", "sequences", "all", "sequence", "seq", "len", "substrs", "inSequences", "needle_seq", "end", "needle_parts", "parts", "part", "needle_part", "substr", "Sequence", "min", "last", "clone", "position", "last_piece", "JSON", "parse", "stringify", "last_part", "pop", "last_substr", "clone_last_len", "getPattern", "match_str", "overlapping", "added_types", "has", "new_seq", "old_seq", "getAttr", "obj", "name", "getAttrNesting", "names", "shift", "scoreValue", "token", "weight", "score", "pos", "regex", "search", "string", "propToArray", "key", "isArray", "iterate", "object", "hasOwnProperty", "cmp", "Sifter", "items", "settings", "diacritics", "tokenize", "query", "respect_word_boundaries", "weights", "tokens", "words", "field_regex", "word", "field_match", "field", "getScoreFunction", "options", "prepareSearch", "_getScoreFunction", "token_count", "fields", "field_count", "getAttrFn", "scoreObject", "data", "sum", "conjunction", "getSortFunction", "_getSortFunction", "implicit_score", "sort_flds", "sort_empty", "bind", "get_field", "id", "fld", "direction", "sort_fld", "optsUser", "assign", "trim", "total", "fn_score", "item", "_", "fn_sort", "limit", "hash_key", "get_hash", "escape_html", "loadDebounce", "fn", "delay", "timeout", "loading", "clearTimeout", "setTimeout", "loadedSearches", "call", "debounce_events", "types", "type", "event_args", "preventDefault", "evt", "stop", "stopPropagation", "addEvent", "target", "addEventListener", "isKeyDown", "key_name", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "metaKey", "getId", "el", "existing_id", "getAttribute", "setAttribute", "addSlashes", "append", "parent", "node", "getDom", "j<PERSON>y", "HTMLElement", "isHtmlString", "tpl", "document", "createElement", "innerHTML", "content", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "arg", "triggerEvent", "dom_el", "event_name", "createEvent", "initEvent", "dispatchEvent", "applyCSS", "css", "style", "addClasses", "elmts", "classes", "norm_classes", "classesArray", "cast<PERSON><PERSON><PERSON><PERSON>", "cls", "classList", "removeClasses", "remove", "_classes", "concat", "parentMatch", "selector", "wrapper", "contains", "matches", "parentNode", "getTail", "list", "nodeIndex", "amongst", "nodeName", "previousElementSibling", "setAttr", "attrs", "val", "attr", "removeAttribute", "replaceNode", "existing", "replacement", "<PERSON><PERSON><PERSON><PERSON>", "highlight", "element", "highlightRecursive", "nodeType", "spannode", "className", "middlebit", "splitText", "index", "middle<PERSON>lone", "cloneNode", "append<PERSON><PERSON><PERSON>", "highlightText", "childNodes", "test", "tagName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "KEY_SHORTCUT", "navigator", "userAgent", "defaults", "optgroups", "plugins", "delimiter", "splitOn", "persist", "create", "createOnBlur", "createFilter", "openOnFocus", "shouldOpen", "maxOptions", "maxItems", "hideSelected", "duplicates", "addPrecedence", "selectOnTab", "preload", "allowEmptyOption", "refreshThrottle", "loadThrottle", "loadingClass", "dataAttr", "optgroupField", "valueField", "labelField", "<PERSON><PERSON><PERSON>", "optgroupLabelField", "optgroupValueField", "lockOptgroupOrder", "sortField", "searchField", "searchConjunction", "mode", "wrapperClass", "controlClass", "dropdownClass", "dropdownContentClass", "itemClass", "optionClass", "dropdownParent", "controlInput", "copyClassesToDropdown", "placeholder", "hidePlaceholder", "shouldLoad", "render", "getSettings", "settings_user", "attr_data", "field_label", "field_value", "field_disabled", "field_optgroup", "field_optgroup_label", "field_optgroup_value", "tag_name", "option", "textContent", "settings_element", "init_select", "optionsMap", "group_count", "$order", "readData", "dataset", "json", "addOption", "group", "arr", "option_data", "disabled", "$option", "selected", "hasAttribute", "children", "child", "optgroup", "optgroup_data", "init_textbox", "data_raw", "opt", "values", "instance_i", "TomSelect", "Interface", "super", "requested", "loaded", "define", "initializePlugins", "queue", "plugin", "require", "loadPlugin", "Error", "MicroPlugin", "input_arg", "user_settings", "dir", "order", "isOpen", "isDisabled", "isReadOnly", "isInvalid", "<PERSON><PERSON><PERSON><PERSON>", "isLocked", "isFocused", "isInputHidden", "isSetup", "ignoreFocus", "ignoreHover", "hasOptions", "lastValue", "caretPos", "activeOption", "activeItems", "userOptions", "refreshTimeout", "tomselect", "window", "getComputedStyle", "getPropertyValue", "tabIndex", "is_select_tag", "rtl", "inputId", "isRequired", "required", "sifter", "setupCallbacks", "setupTemplates", "control", "dropdown", "_render", "dropdown_content", "inputMode", "control_input", "focus_node", "setup", "passive_event", "passive", "listboxId", "role", "control_id", "escape<PERSON><PERSON>y", "label", "label_click", "focus", "for", "label_id", "width", "classes_plugins", "multiple", "load", "target_match", "onOptionHover", "capture", "onOptionSelect", "onItemSelect", "onClick", "onKeyDown", "onKeyPress", "onInput", "onBlur", "onFocus", "onPaste", "doc_mousedown", "<PERSON><PERSON><PERSON>", "blur", "inputState", "win_scroll", "positionDropdown", "_destroy", "removeEventListener", "revertSettings", "insertAdjacentElement", "sync", "refreshState", "updateOriginalInput", "refreshItems", "close", "disable", "readOnly", "setReadOnly", "enable", "onChange", "setupOptions", "addOptions", "registerOptionGroup", "templates", "optgroup_header", "escape", "option_create", "no_results", "not_loading", "callbacks", "initialize", "change", "item_add", "item_remove", "item_select", "clear", "option_add", "option_remove", "option_clear", "optgroup_add", "optgroup_remove", "optgroup_clear", "dropdown_open", "dropdown_close", "get_settings", "setValue", "last<PERSON><PERSON>y", "clearActiveItems", "onMouseDown", "pastedText", "inputValue", "splitInput", "piece", "addItem", "createItem", "character", "keyCode", "which", "constants", "selectAll", "open", "next", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setActiveOption", "prev", "canSelect", "activeElement", "advanceSelection", "deleteSelection", "_onInput", "refreshOptions", "wasFocused", "hasFocus", "deactivate", "setActiveItem", "setCaret", "parentElement", "closeAfterSelect", "canLoad", "loadCallback", "clearActiveOption", "setTextboxValue", "getValue", "silent", "addItems", "setMaxItems", "eventName", "begin", "swap", "getLastActive", "prototype", "setActiveItemClass", "removeActiveItem", "last_active", "idx", "scroll", "scrollToOption", "behavior", "height_menu", "clientHeight", "scrollTop", "height_item", "offsetHeight", "getBoundingClientRect", "top", "scroll<PERSON>eh<PERSON>or", "controlChildren", "toggle", "offsetWidth", "getSearchOptions", "nesting", "calculateScore", "currentResults", "hashed", "triggerDropdown", "html", "has_create_option", "active_group", "groups", "groups_order", "same_query", "results", "active_option", "show_dropdown", "closest", "getGroupFragment", "group_order_i", "order_group", "fragment", "group_fragment", "createDocumentFragment", "opt_value", "opt_hash", "option_el", "getOption", "includes", "self_optgroup", "$id", "toString", "elements", "group_order", "group_heading", "group_options", "header", "group_html", "querySelectorAll", "tok", "add_template", "template", "insertBefore", "canCreate", "active_index", "selectable", "user_created", "dat", "registerOption", "addOptionGroup", "hashed_id", "removeOptionGroup", "clearCache", "clearOptionGroups", "updateOption", "item_new", "index_item", "value_old", "value_new", "data_old", "getItem", "uncacheValue", "option_new", "removeOption", "removeItem", "clearOptions", "boundFilter", "clearFilter", "$div", "last_item", "x", "isPending", "<PERSON><PERSON><PERSON>", "isFull", "insertAtCaret", "output", "caret", "lock", "created", "unlock", "refreshValidityState", "wrap_classList", "validity", "valid", "opts", "empty_option", "has_selected", "AddSelected", "visibility", "display", "context", "rect", "scrollY", "left", "scrollX", "selection", "tail", "selectionStart", "selectionEnd", "rm_items", "rm_item", "shouldDelete", "onDelete", "adjacent", "nextElement<PERSON><PERSON>ling", "moveCaret", "new_pos", "setLocked", "setDisabled", "destroy", "templateName", "hook", "when", "method", "new_fn", "orig_method", "result_new"], "mappings": ";;;;;AAgBA,SAASA,EAAUC,EAAcC,GAChCD,EAAOE,MAAM,OAAOC,SAASC,IAC5BH,EAASG,EAAM,GAEjB,CAEe,MAAMC,EAIpBC,WAAAA,GACCC,KAAKC,QAAU,CAAE,CAClB,CAEAC,EAAAA,CAAGT,EAAeU,GACjBX,EAAUC,GAAQI,IACjB,MAAMO,EAAcJ,KAAKC,QAAQJ,IAAU;AAC3CO,EAAYC,KAAKF,GACjBH,KAAKC,QAAQJ,GAASO,CAAW,GAEnC,CAEAE,GAAAA,CAAIb,EAAeU,GAClB,IAAII,EAAIC,UAAUC;AACR,IAANF,EAKJf,EAAUC,GAAQI,IAEjB,GAAU,IAANU,EAEH,mBADOP,KAAKC,QAAQJ;AAIrB,MAAMO,EAAcJ,KAAKC,QAAQJ;KACba,IAAhBN,IAEJA,EAAYO,OAAOP,EAAYQ,QAAQT,GAAM,GAC7CH,KAAKC,QAAQJ,GAASO,EAAW,IAfjCJ,KAAKC,QAAU,CAAE,CAiBnB,CAEAY,OAAAA,CAAQpB,KAAkBqB,GACzB,IAAIC,EAAOf;AAEXR,EAAUC,GAAQI,IACjB,MAAMO,EAAcW,EAAKd,QAAQJ;KACba,IAAhBN,GACJA,EAAYR,SAAQO,IACnBA,EAAIa,MAAMD,EAAMD,EAAM,GACrB,GAGJ,EClEM,MAAMG,EAAkBC,IAC3BA,EAAQA,EAAMC,OAAOC,UACXX,OAAS,EACRS,EAAM,IAAM,GAEU,GAAzBG,EAAeH,GAAe,IAAMA,EAAMI,KAAK,IAAM,IAAM,MAAQJ,EAAMI,KAAK,KAAO,IAEpFC,EAAmBC,IAC5B,IAAKC,EAAcD,GACf,OAAOA,EAAMF,KAAK;AAEtB,IAAII,EAAU,GACVC,EAAkB;AACtB,MAAMC,EAAe,KACbD,EAAkB,IAClBD,GAAW,IAAMC,EAAkB,IAC/C;AAYI,OAVAH,EAAM5B,SAAQ,CAACiC,EAAMC,KACbD,IAASL,EAAMM,EAAI,IAIvBF,IACAF,GAAWG,EACXF,EAAkB,GALdA,GAKe,IAEvBC,IACOF,CAAO,EAOLK,EAAgBb,IACzB,IAAIM,EAAQQ,MAAMC,KAAKf;AACvB,OAAOD,EAAeO,EAAM,EAKnBC,EAAiBD,GACnB,IAAKU,IAAIV,GAAQW,OAASX,EAAMf,OAK9B2B,EAAgBC,IACjBA,EAAM,IAAIC,QAAQ,qCAAsC,QAKvDjB,EAAkBG,GACpBA,EAAMe,QAAO,CAACC,EAASC,IAAUC,KAAKC,IAAIH,EAASI,EAAcH,KAAS,GAExEG,EAAiBP,GACnBL,MAAMC,KAAKI,GAAK5B,OC3DdoC,EAAiBC,IAC1B,GAAqB,IAAjBA,EAAMrC,OACN,MAAO,CAAC,CAACqC;AACb,IAAIC,EAAS;AACb,MAAMC,EAAQF,EAAMG,UAAU;AAU9B,OATaJ,EAAcG,GACtBpD,SAAQ,SAAUsD,GACnB,IAAIC,EAAMD,EAAUE,MAAM;AAC1BD,EAAI,GAAKL,EAAMO,OAAO,GAAKF,EAAI,GAC/BJ,EAAO1C,KAAK8C,GACZA,EAAMD,EAAUE,MAAM,GACtBD,EAAIG,QAAQR,EAAMO,OAAO,IACzBN,EAAO1C,KAAK8C,EACpB,IACWJ,CAAM,EChBJQ,EAAc,CAAC,CAAC,EAAG;AAEzB,IAAIC,EACPC;AACJ,MACMC,EAAgB,CAAE,EAClBC,EAAkB,CACpB,IAAK,KACL,EAAK,IACLC,EAAK,MACLC,GAAM,IACNC,GAAM,MACNC,GAAM,IACNC,GAAM,IACNC,GAAM,KACNC,GAAM,IACNC,EAAK,MACLC,EAAK,OACLC,EAAK,WACLC,EAAK,OACLC,EAAK,KACLC,EAAK,SACLC,EAAK,OACL3C,EAAK,KACL4C,EAAK,KACLC,EAAK,SACLC,EAAK,WACLC,EAAK,MACLtE,EAAK,UACLuE,EAAK,UACLC,GAAM,IACNC,GAAM,IACNC,GAAM,IACNC,GAAM,IACNC,EAAK,SACLC,EAAK,MACLC,EAAK,QACLC,EAAK,QACLC,EAAK,QACLC,GAAM,IACNC,GAAM,IACNC,EAAK,IACLC,EAAK,MACLC,GAAM,IACNC,EAAK,IACLC,EAAK,MACLC,EAAK,QACLC,GAAM;AAEV,IAAK,IAAIC,KAAStC,EAAiB,CAC/B,IAAIuC,EAAUvC,EAAgBsC,IAAU;AACxC,IAAK,IAAInE,EAAI,EAAGA,EAAIoE,EAAQzF,OAAQqB,IAAK,CACrC,IAAID,EAAOqE,EAAQjD,UAAUnB,EAAGA,EAAI;AACpC4B,EAAc7B,GAAQoE,CAC9B,CACA,CACA,MAAME,EAAc,IAAIC,OAAOC,OAAOC,KAAK5C,GAAepC,KAAK,KAAhC+E,YAAyD,MAa3EE,EAAY,CAAClE,EAAKmE,EAAO,SAAWnE,EAAIkE,UAAUC,GAMlDC,EAAapE,GACfL,MAAMC,KAAKI,GAAKE,QAKvB,CAACQ,EAAQlB,IACEkB,EAAS2D,EAAW7E,IAC5B,IAEM6E,EAAcrE,IACvBA,EAAMkE,EAAUlE,GACXsE,cACArE,QAAQ6D,GAAoCtE,GACtC6B,EAAc7B,IAAS,KAG3B0E,EAAUlE,EAAK;AA+BnB,MAAMuE,EAAgBrD,IACzB,MAAMsD,EAAe,CAAE,EACjBC,EAAc,CAACC,EAAQC,KAEzB,MAAMC,EAAaJ,EAAaE,IAAW,IAAI7E,IACzCgF,EAAO,IAAId,OAAO,IAAMrE,EAAakF,GAAc,IAAK;AAC1DD,EAAOG,MAAMD,KAGjBD,EAAWG,IAAIhF,EAAa4E,IAC5BH,EAAaE,GAAUE,EAAU;AAErC,IAAK,IAAIxE,KAtCN,UAAoBc,GACvB,IAAK,MAAO8D,EAAgBC,KAAmB/D,EAC3C,IAAK,IAAIzB,EAAIuF,EAAgBvF,GAAKwF,EAAgBxF,IAAK,CACnD,IAAIyF,EAAWC,OAAOC,aAAa3F,GAC/BiF,EAASN,EAAUc;AACnBR,GAAUQ,EAASZ,gBAQnBI,EAAOtG,OA1GC,GA6GS,GAAjBsG,EAAOtG,cAGL,CAAEsG,OAAQA,EAAQQ,SAAUA,EAAUG,WAAY5F,IACpE,CAEA,CAgBsB6F,CAAUpE,GACxBuD,EAAYrE,EAAMsE,OAAQtE,EAAMsE,QAChCD,EAAYrE,EAAMsE,OAAQtE,EAAM8E;AAEpC,OAAOV,CAAY,EAMVe,EAAerE,IACxB,MAAMsD,EAAeD,EAAarD,GAC5BC,EAAc,CAAE;AACtB,IAAIqE,EAAa;AACjB,IAAK,IAAId,KAAUF,EAAc,CAC7B,IAAIiB,EAAMjB,EAAaE;AACnBe,IACAtE,EAAYuD,GAAUhF,EAAa+F,IAEnCf,EAAOtG,OAAS,GAChBoH,EAAWxH,KAAK+B,EAAa2E,GAEzC,CACIc,EAAWE,MAAK,CAACnE,EAAGO,IAAMA,EAAE1D,OAASmD,EAAEnD;AACvC,MAAMuH,EAAkB/G,EAAe4G;AAEvC,OADApE,EAAiB,IAAI2C,OAAO,IAAM4B,EAAiB,KAC5CxE,CAAW,EA0BTyE,EAAsB,CAAC5F,EAAK6F,EAAkB,KACvDA,EAAkBxF,KAAKC,IAAIuF,EAAiB7F,EAAI5B,OAAS,GAClDQ,EAAe4B,EAAcR,GAAK8F,KAAKC,GAvBvB,EAACC,EAASH,EAAkB,KACnD,IAAII,EAAiB;AAOrB,OANAD,EAAUA,EAAQF,KAAK9F,IACfmB,EAAYnB,KACZiG,GAAkBjG,EAAI5B,QAEnB+C,EAAYnB,IAAQA,KAE3BiG,GAAkBJ,EACX3G,EAAgB8G,GAEpB,EAAE,EAaEE,CAAYH,EAASF,OAO9BM,EAAqB,CAACC,EAAWC,GAAM,KACzC,IAAIR,EAAkBO,EAAUhI,OAAS,EAAI,EAAI;AACjD,OAAOQ,EAAewH,EAAUN,KAAKQ,IACjC,IAAIC,EAAM;AACV,MAAMC,EAAMH,EAAMC,EAASlI,SAAWkI,EAASlI,SAAW;AAC1D,IAAK,IAAIiE,EAAI,EAAGA,EAAImE,EAAKnE,IACrBkE,EAAIvI,KAAK4H,EAAoBU,EAASG,QAAQpE,IAAM,GAAIwD;AAE5D,OAAO3G,EAAgBqH,EAAI,IAC5B,EAKDG,EAAc,CAACC,EAAYP,KAC7B,IAAK,MAAMG,KAAOH,EAAW,CACzB,GAAIG,EAAI5F,OAASgG,EAAWhG,OAAS4F,EAAIK,KAAOD,EAAWC,IACvD;AAEJ,GAAIL,EAAIE,QAAQxH,KAAK,MAAQ0H,EAAWF,QAAQxH,KAAK,IACjD;AAEJ,IAAI4H,EAAeF,EAAWG;AAC9B,MAAMhI,EAAUiI,IACZ,IAAK,MAAMC,KAAeH,EAAc,CACpC,GAAIG,EAAYrG,QAAUoG,EAAKpG,OAASqG,EAAYC,SAAWF,EAAKE,OAChE,OAAO;AAEX,GAAmB,GAAfF,EAAK3I,QAAqC,GAAtB4I,EAAY5I,OAApC,CAQA,GAAI2I,EAAKpG,MAAQqG,EAAYrG,OAASoG,EAAKH,IAAMI,EAAYrG,MACzD,OAAO;AAEX,GAAIqG,EAAYrG,MAAQoG,EAAKpG,OAASqG,EAAYJ,IAAMG,EAAKpG,MACzD,OAAO,CAV3B,CAYA,CACY,OAAO,CAAK;AAGhB,KADe4F,EAAIO,MAAMhI,OAAOA,GACnBV,OAAS,GAGtB,OAAO,CACf,CACI,OAAO,CAAK;AAEhB,MAAM8I,EACFJ;AACAL;AACA9F;AACAiG;AACA,WAAAlJ,GACIC,KAAKmJ,MAAQ,GACbnJ,KAAK8I,QAAU,GACf9I,KAAKgD,MAAQ,EACbhD,KAAKiJ,IAAM,CACnB,CACI,GAAA7B,CAAIgC,GACIA,IACApJ,KAAKmJ,MAAM9I,KAAK+I,GAChBpJ,KAAK8I,QAAQzI,KAAK+I,EAAKE,QACvBtJ,KAAKgD,MAAQN,KAAK8G,IAAIJ,EAAKpG,MAAOhD,KAAKgD,OACvChD,KAAKiJ,IAAMvG,KAAKC,IAAIyG,EAAKH,IAAKjJ,KAAKiJ,KAE/C,CACI,IAAAQ,GACI,OAAOzJ,KAAKmJ,MAAMnJ,KAAKmJ,MAAM1I,OAAS,EAC9C,CACI,MAAAA,GACI,OAAOT,KAAKmJ,MAAM1I,MAC1B,CACI,KAAAiJ,CAAMC,EAAUC,GACZ,IAAIF,EAAQ,IAAIH,EACZJ,EAAQU,KAAKC,MAAMD,KAAKE,UAAU/J,KAAKmJ,QACvCa,EAAYb,EAAMc;AACtB,IAAK,MAAMb,KAAQD,EACfO,EAAMtC,IAAIgC;AAEd,IAAIc,EAAcN,EAAWN,OAAOrG,UAAU,EAAG0G,EAAWK,EAAUhH,OAClEmH,EAAiBD,EAAYzJ;AAEjC,OADAiJ,EAAMtC,IAAI,CAAEpE,MAAOgH,EAAUhH,MAAOiG,IAAKe,EAAUhH,MAAQmH,EAAgB1J,OAAQ0J,EAAgBb,OAAQY,IACpGR,CACf,EAcO,MAAMU,EAAc/H,SA/OH3B,IAAhB8C,IAEJA,EAAcoE,EAA4BrE,IA+O1ClB,EAAMoE,EAAUpE;AAChB,IAAIX,EAAU,GACV+G,EAAY,CAAC,IAAIc;AACrB,IAAK,IAAIzH,EAAI,EAAGA,EAAIO,EAAI5B,OAAQqB,IAAK,CACjC,IACIqF,EADS9E,EAAIY,UAAUnB,GACRqF,MAAM1D;AACzB,MAAM5B,EAAOQ,EAAIY,UAAUnB,EAAGA,EAAI,GAC5BuI,EAAYlD,EAAQA,EAAM,GAAK;AAGrC,IAAImD,EAAc,GACdC,EAAc,IAAIrI;AACtB,IAAK,MAAMyG,KAAYF,EAAW,CAC9B,MAAMmB,EAAajB,EAASc;AAC5B,IAAKG,GAAmC,GAArBA,EAAWnJ,QAAemJ,EAAWX,KAAOnH,EAE3D,GAAIuI,EAAW,CACX,MAAMxB,EAAMwB,EAAU5J;AACtBkI,EAASvB,IAAI,CAAEpE,MAAOlB,EAAGmH,IAAKnH,EAAI+G,EAAKpI,OAAQoI,EAAKS,OAAQe,IAC5DE,EAAYnD,IAAI,IACpC,MAEoBuB,EAASvB,IAAI,CAAEpE,MAAOlB,EAAGmH,IAAKnH,EAAI,EAAGrB,OAAQ,EAAG6I,OAAQzH,IACxD0I,EAAYnD,IAAI;KAGnB,GAAIiD,EAAW,CAChB,IAAIX,EAAQf,EAASe,MAAM5H,EAAG8H;AAC9B,MAAMf,EAAMwB,EAAU5J;AACtBiJ,EAAMtC,IAAI,CAAEpE,MAAOlB,EAAGmH,IAAKnH,EAAI+G,EAAKpI,OAAQoI,EAAKS,OAAQe,IACzDC,EAAYjK,KAAKqJ,EACjC,MAIgBa,EAAYnD,IAAI,IAEhC,CAEQ,GAAIkD,EAAY7J,OAAS,EAAzB,CAEI6J,EAAcA,EAAYvC,MAAK,CAACnE,EAAGO,IACxBP,EAAEnD,SAAW0D,EAAE1D;AAE1B,IAAK,IAAIiJ,KAASY,EAEVvB,EAAYW,EAAOjB,IAGvBA,EAAUpI,KAAKqJ,EAG/B,MAKQ,GAAI5H,EAAI,GAAyB,GAApByI,EAAYpI,OAAcoI,EAAYC,IAAI,KAAM,CACzD9I,GAAW8G,EAAmBC,GAAW;AACzC,IAAIgC,EAAU,IAAIlB;AAClB,MAAMmB,EAAUjC,EAAU;AACtBiC,GACAD,EAAQrD,IAAIsD,EAAQjB,QAExBhB,EAAY,CAACgC,EACzB,CACA,CAEI,OADA/I,GAAW8G,EAAmBC,GAAW,GAClC/G,CAAO,EC7WLiJ,EAAU,CAACC,EAAKC,KACzB,GAAKD,EAEL,OAAOA,EAAIC,EAAK,EAQPC,EAAiB,CAACF,EAAKC,KAChC,GAAKD,EAAL,CAGA,IADA,IAAIxB,EAAM2B,EAAQF,EAAKlL,MAAM,MACrByJ,EAAO2B,EAAMC,WAAaJ,EAAMA,EAAIxB,MAE5C,OAAOwB,CAJH,CAIM,EAODK,EAAa,CAACxI,EAAOyI,EAAOC,KACrC,IAAIC,EAAOC;AACX,OAAK5I,GAELA,GAAgB,GACG,MAAfyI,EAAMI,QAGG,KADbD,EAAM5I,EAAM8I,OAAOL,EAAMI,QADd,GAIXF,EAAQF,EAAMM,OAAO/K,OAASgC,EAAMhC,OACxB,IAAR4K,IACAD,GAAS,IACNA,EAAQD,IAVJ,CAUU,EAMZM,EAAc,CAACb,EAAKc,KAC7B,IAAIjJ,EAAQmI,EAAIc;AAChB,GAAoB,mBAATjJ,EACP,OAAOA;AACPA,IAAUT,MAAM2J,QAAQlJ,KACxBmI,EAAIc,GAAO,CAACjJ,GACpB,EAYamJ,EAAU,CAACC,EAAQnM,KAC5B,GAAIsC,MAAM2J,QAAQE,GACdA,EAAOjM,QAAQF;KAGf,IAAK,IAAIgM,KAAOG,EACRA,EAAOC,eAAeJ,IACtBhM,EAASmM,EAAOH,GAAMA,EAGtC,EAEaK,EAAM,CAACnI,EAAGO,IACF,iBAANP,GAA+B,iBAANO,EACzBP,EAAIO,EAAI,EAAKP,EAAIO,GAAK,EAAI,GAErCP,EAAI6C,EAAU7C,EAAI,IAAI+C,gBACtBxC,EAAIsC,EAAUtC,EAAI,IAAIwC,eAEX,EACPxC,EAAIP,GACI,EACL;ACzEX,MAAMoI,EACFC;AACAC;AAOA,WAAAnM,CAAYkM,EAAOC,GACflM,KAAKiM,MAAQA,EACbjM,KAAKkM,SAAWA,GAAY,CAAEC,YAAY,EAClD,CAOI,QAAAC,CAASC,EAAOC,EAAyBC,GACrC,IAAKF,IAAUA,EAAM5L,OACjB,MAAO;AACX,MAAM+L,EAAS,GACTC,EAAQJ,EAAM1M,MAAM;AAC1B,IAAI+M;AA6BJ,OA5BIH,IACAG,EAAc,IAAItG,OAAO,KAAOC,OAAOC,KAAKiG,GAASpE,IAAI/F,GAAcd,KAAK,KAAO,YAEvFmL,EAAM7M,SAAS+M,IACX,IAAIC,EACAC,EAAQ,KACRvB,EAAQ;AAERoB,IAAgBE,EAAcD,EAAKxF,MAAMuF,MACzCG,EAAQD,EAAY,GACpBD,EAAOC,EAAY,IAEnBD,EAAKlM,OAAS,IAEV6K,EADAtL,KAAKkM,SAASC,WACN/B,EAAWuC,IAAS,KAGpBvK,EAAauK,GAErBrB,GAASgB,IACThB,EAAQ,MAAQA,IAExBkB,EAAOnM,KAAK,CACRmL,OAAQmB,EACRrB,MAAOA,EAAQ,IAAIlF,OAAOkF,EAAO,MAAQ,KACzCuB,MAAOA,GACT,IAECL,CACf,CAUI,gBAAAM,CAAiBT,EAAOU,GACpB,IAAIxB,EAASvL,KAAKgN,cAAcX,EAAOU;AACvC,OAAO/M,KAAKiN,kBAAkB1B,EACtC,CAKI,iBAAA0B,CAAkB1B,GACd,MAAMiB,EAASjB,EAAOiB,OAAQU,EAAcV,EAAO/L;AACnD,IAAKyM,EACD,OAAO,WAAc,OAAO,CAAI;AAEpC,MAAMC,EAAS5B,EAAOwB,QAAQI,OAAQZ,EAAUhB,EAAOgB,QAASa,EAAcD,EAAO1M,OAAQ4M,EAAY9B,EAAO8B;AAChH,IAAKD,EACD,OAAO,WAAc,OAAO,CAAI;AAOpC,MAAME,EACkB,IAAhBF,EACO,SAAUlC,EAAOqC,GACpB,MAAMV,EAAQM,EAAO,GAAGN;AACxB,OAAO5B,EAAWoC,EAAUE,EAAMV,GAAQ3B,EAAOqB,EAAQM,IAAU,EACtE,EAEE,SAAU3B,EAAOqC,GACpB,IAAIC,EAAM;AAEV,GAAItC,EAAM2B,MAAO,CACb,MAAMpK,EAAQ4K,EAAUE,EAAMrC,EAAM2B;CAC/B3B,EAAMI,OAAS7I,EAChB+K,GAAQ,EAAIJ,EAGZI,GAAOvC,EAAWxI,EAAOyI,EAAO,EAExD,MAEoBU,EAAQW,GAAS,CAACpB,EAAQ0B,KACtBW,GAAOvC,EAAWoC,EAAUE,EAAMV,GAAQ3B,EAAOC,EAAO;AAGhE,OAAOqC,EAAMJ,CAChB;AAEL,OAAoB,IAAhBF,EACO,SAAUK,GACb,OAAOD,EAAYd,EAAO,GAAIe,EACjC,EAE8B,QAA/BhC,EAAOwB,QAAQU,YACR,SAAUF,GACb,IAAInC,EAAOoC,EAAM;AACjB,IAAK,IAAItC,KAASsB,EAAQ,CAEtB,IADApB,EAAQkC,EAAYpC,EAAOqC,KACd,EACT,OAAO;AACXC,GAAOpC,CAC3B,CACgB,OAAOoC,EAAMN,CAChB,EAGM,SAAUK,GACb,IAAIC,EAAM;AAIV,OAHA5B,EAAQY,GAAStB,IACbsC,GAAOF,EAAYpC,EAAOqC,EAAK,IAE5BC,EAAMN,CAChB,CAEb,CASI,eAAAQ,CAAgBrB,EAAOU,GACnB,IAAIxB,EAASvL,KAAKgN,cAAcX,EAAOU;AACvC,OAAO/M,KAAK2N,iBAAiBpC,EACrC,CACI,gBAAAoC,CAAiBpC,GACb,IAAIqC,EAAgBC,EAAY;AAChC,MAAM9M,EAAOf,KAAM+M,EAAUxB,EAAOwB,QAAShF,GAASwD,EAAOc,OAASU,EAAQe,WAAcf,EAAQe,WAAaf,EAAQhF;AACzH,GAAmB,mBAARA,EACP,OAAOA,EAAKgG,KAAK/N;AAOrB,MAAMgO,EAAY,SAAUnD,EAAM9H,GAC9B,MAAa,WAAT8H,EACO9H,EAAOqI,MACXG,EAAO8B,UAAUtM,EAAKkL,MAAMlJ,EAAOkL,IAAKpD,EAClD;AAED,GAAI9C,EACA,IAAK,IAAIzC,KAAKyC,GACNwD,EAAOc,OAAqB,WAAZ/G,EAAEuH,QAClBgB,EAAUxN,KAAKiF;AAM3B,GAAIiG,EAAOc,MAAO,CACduB,GAAiB;AACjB,IAAK,IAAIM,KAAOL,EACZ,GAAkB,WAAdK,EAAIrB,MAAoB,CACxBe,GAAiB;AACjB,KACpB,CAEgBA,GACAC,EAAUvK,QAAQ,CAAEuJ,MAAO,SAAUsB,UAAW,QAGhE,MAEYN,EAAYA,EAAU1M,QAAQ+M,GAAsB,WAAdA,EAAIrB;AAI9C,OADwBgB,EAAUpN,OAI3B,SAAUmD,EAAGO,GAChB,IAAIpB,EAAQ8J;AACZ,IAAK,IAAIuB,KAAYP,EAAW,CAI5B,GAHAhB,EAAQuB,EAASvB,MAEjB9J,GADwC,SAAvBqL,EAASD,WAAwB,EAAI,GAChCpC,EAAIiC,EAAUnB,EAAOjJ,GAAIoK,EAAUnB,EAAO1I,IAE5D,OAAOpB,CAC3B,CACY,OAAO,CACV,EAZU,IAanB,CAQI,aAAAiK,CAAcX,EAAOgC,GACjB,MAAM9B,EAAU,CAAE;AAClB,IAAIQ,EAAU1G,OAAOiI,OAAO,CAAA,EAAID;AAIhC,GAHA5C,EAAYsB,EAAS,QACrBtB,EAAYsB,EAAS,cAEjBA,EAAQI,OAAQ,CAChB1B,EAAYsB,EAAS;AACrB,MAAMI,EAAS;AACfJ,EAAQI,OAAOvN,SAASiN,IACA,iBAATA,IACPA,EAAQ,CAAEA,MAAOA,EAAO1B,OAAQ,IAEpCgC,EAAO9M,KAAKwM,GACZN,EAAQM,EAAMA,OAAU,WAAYA,EAASA,EAAM1B,OAAS,CAAC,IAEjE4B,EAAQI,OAASA,CAC7B,CACQ,MAAO,CACHJ,QAASA,EACTV,MAAOA,EAAM1F,cAAc4H,OAC3B/B,OAAQxM,KAAKoM,SAASC,EAAOU,EAAQT,wBAAyBC,GAC9DiC,MAAO,EACPvC,MAAO,GACPM,QAASA,EACTc,UAAYN,EAAe,QAAIjC,EAAiBH,EAE5D,CAMI,MAAAY,CAAOc,EAAOU,GACV,IAAiB3B,EAAOG,EAApBxK,EAAOf;AACXuL,EAASvL,KAAKgN,cAAcX,EAAOU,GACnCA,EAAUxB,EAAOwB,QACjBV,EAAQd,EAAOc;AAEf,MAAMoC,EAAW1B,EAAQ3B,OAASrK,EAAKkM,kBAAkB1B;AAErDc,EAAM5L,OACNmL,EAAQ7K,EAAKkL,OAAO,CAACyC,EAAMT,KACvB7C,EAAQqD,EAASC,KACM,IAAnB3B,EAAQ5L,QAAoBiK,EAAQ,IACpCG,EAAOU,MAAM5L,KAAK,CAAE+K,MAASA,EAAO6C,GAAMA,GAC9D,IAIYrC,EAAQ7K,EAAKkL,OAAO,CAAC0C,EAAGV,KACpB1C,EAAOU,MAAM5L,KAAK,CAAE+K,MAAS,EAAG6C,GAAMA,GAAK;AAGnD,MAAMW,EAAU7N,EAAK4M,iBAAiBpC;AAQtC,OAPIqD,GACArD,EAAOU,MAAMlE,KAAK6G,GAEtBrD,EAAOiD,MAAQjD,EAAOU,MAAMxL,OACC,iBAAlBsM,EAAQ8B,QACftD,EAAOU,MAAQV,EAAOU,MAAM7I,MAAM,EAAG2J,EAAQ8B,QAE1CtD,CACf,ECvRO,MAAMuD,EAAYrM,GACpB,MAAOA,EAAgD,KACpDsM,EAAStM,GAGJsM,EAAYtM,GACH,kBAAVA,EAA4BA,EAAQ,IAAM,IAC9CA,EAAQ,GAOHuM,EAAe3M,IACnBA,EAAM,IACZC,QAAQ,KAAM,SACdA,QAAQ,KAAM,QACdA,QAAQ,KAAM,QACdA,QAAQ,KAAM,UAoBJ2M,EAAeA,CAACC,EAAmDC,KAC/E,IAAIC;AACJ,OAAO,SAAyB3M,EAAa/C,GAC5C,IAAIqB,EAAOf;AAEPoP,IACHrO,EAAKsO,QAAU3M,KAAKC,IAAI5B,EAAKsO,QAAU,EAAG,GAC1CC,aAAaF,IAEdA,EAAUG,YAAW,WACpBH,EAAU,KACVrO,EAAKyO,eAAe/M,IAAS,EAC7ByM,EAAGO,KAAK1O,EAAM0B,EAAO/C,EAErB,GAAEyP,EACH,CAAA,EASWO,EAAkBA,CAAE3O,EAAgB4O,EAAgBT,KAChE,IAAIU,EACA/O,EAAUE,EAAKF,QACfgP,EAAoC,CAAE;AAiB1C,IAAKD,KAdL7O,EAAKF,QAAU,WACd,IAAI+O,EAAOpP,UAAU;AACrB,IAA6B,IAAzBmP,EAAM/O,QAAQgP,GAGjB,OAAO/O,EAAQG,MAAMD,EAAMP;AAF3BqP,EAAWD,GAAQpP,SAIpB,EAGD0O,EAAGlO,MAAMD,EAAM,IACfA,EAAKF,QAAUA,EAGF8O,GACRC,KAAQC,GACXhP,EAAQG,MAAMD,EAAM8O,EAAWD,GAEjC,EAyBYE,EAAiBA,CAACC,EAAYC,GAAa,KACnDD,IACHA,EAAID,iBACAE,GACHD,EAAIE,kBAEN,EAQYC,EAAWA,CAACC,EAAoBP,EAAalQ,EAA6CqN,KACtGoD,EAAOC,iBAAiBR,EAAKlQ,EAASqN,EAAQ,EAUlCsD,EAAYA,CAAEC,EAA2CP,MAEhEA,MAIAA,EAAIO,IAMK,KAFDP,EAAIQ,OAAO,EAAE,IAAMR,EAAIS,QAAQ,EAAE,IAAMT,EAAIU,SAAS,EAAE,IAAMV,EAAIW,QAAQ,EAAE,IAe3EC,EAAQA,CAACC,EAAW3C,KAChC,MAAM4C,EAAcD,EAAGE,aAAa;AACpC,OAAID,IAIJD,EAAGG,aAAa,KAAK9C,GACdA,EAAE,EAOG+C,EAAc3O,GACnBA,EAAIC,QAAQ,UAAW,QAMlB2O,EAASA,CAAEC,EAAiCC,KACpDA,GAAOD,EAAOD,OAAOE,EAAK,EAalBvF,EAAUA,CAACC,EAA8BnM,KAErD,GAAKsC,MAAM2J,QAAQE,GAClBA,EAAOjM,QAAQF;KAIf,IAAK,IAAIgM,KAAOG,EACXA,EAAOC,eAAeJ,IACzBhM,EAASmM,EAAOH,GAAMA,EAGzB,EC3NY0F,EAAW/E,IAEvB,GAAIA,EAAMgF,OACT,OAAOhF,EAAM;AAGd,GAAIA,aAAiBiF,YACpB,OAAOjF;AAGR,GAAIkF,EAAalF,GAAQ,CACxB,IAAImF,EAAMC,SAASC,cAAc;AAEjC,OADAF,EAAIG,UAAYtF,EAAMkC,OACfiD,EAAII,QAAQC,UACpB,CAEA,OAAOJ,SAASK,cAAczF,EAAM,EAGxBkF,EAAgBQ,GACT,iBAARA,GAAoBA,EAAInR,QAAQ,MAAQ,EAcvCoR,EAAeA,CAAEC,EAAoBC,KACjD,IAAIrS,EAAQ4R,SAASU,YAAY;AACjCtS,EAAMuS,UAAUF,GAAY,GAAM,GAClCD,EAAOI,cAAcxS,EAAM,EAOfyS,EAAWA,CAAEL,EAAoBM,KAC7ClM,OAAOiI,OAAO2D,EAAOO,MAAOD,EAAI,EAQpBE,EAAaA,CAAEC,KAAoCC,KAE/D,IAAIC,EAAgBC,EAAaF,IACjCD,EAAWI,EAAYJ,IAEjBvK,KAAKyI,IACVgC,EAAazK,KAAK4K,IACjBnC,EAAGoC,UAAU5L,IAAK2L,EAAK,GACtB,GACD,EAOWE,EAAgBA,CAAEP,KAAoCC,KAElE,IAAIC,EAAgBC,EAAaF,IAClCD,EAAWI,EAAYJ,IAEjBvK,KAAKyI,IACVgC,EAAazK,KAAI4K,IACfnC,EAAGoC,UAAUE,OAAQH,EAAK,GAC1B,GACA,EAQSF,EAAgB/R,IAC5B,IAAI6R,EAAmB;AAUvB,OATA/G,EAAS9K,GAAOqS,IACS,iBAAbA,IACVA,EAAWA,EAAS5E,OAAO5O,MAAM,iBAE9BqC,MAAM2J,QAAQwH,KACjBR,EAAUA,EAAQS,OAAOD,GAC1B,IAGMR,EAAQxR,OAAOC,QAAQ,EAQlB0R,EAAef,IACtB/P,MAAM2J,QAAQoG,KACjBA,EAAM,CAACA,IAEFA,GASKsB,EAAcA,CAAElD,EAAyBmD,EAAiBC,KAEtE,IAAIA,GAAYA,EAAQC,SAASrD,GAIjC,KAAOA,GAAUA,EAAOsD,SAAS,CAEhC,GAAItD,EAAOsD,QAAQH,GAClB,OAAOnD;AAGRA,EAASA,EAAOuD,UACjB,GAWYC,GAAUA,CAAEC,EAA0BzF,EAAiB,IAE/DA,EAAY,EACRyF,EAAKA,EAAKnT,OAAO,GAGlBmT,EAAK,GAgBAC,GAAYA,CAAEjD,EAAiBkD,KAC3C,IAAKlD,EAAI,OAAQ;AAEjBkD,EAAUA,GAAWlD,EAAGmD;AAGxB,IADA,IAAIjS,EAAI,EACD8O,EAAKA,EAAGoD,wBAEVpD,EAAG6C,QAAQK,IACdhS;AAGF,OAAOA,CAAC,EAQImS,GAAUA,CAACrD,EAAWsD,KAClCtI,EAASsI,GAAM,CAACC,EAAIC,KACR,MAAPD,EACHvD,EAAGyD,gBAAgBD,GAEnBxD,EAAGG,aAAaqD,EAAgB,GAAGD,EACpC,GACC,EAOUG,GAAcA,CAAEC,EAAeC,KACvCD,EAASb,YAAaa,EAASb,WAAWe,aAAaD,EAAaD,EAAS,ECrMrEG,GAAYA,CAACC,EAAqBrJ,KAE9C,GAAc,OAAVA,EAAiB;AAGrB,GAAqB,iBAAVA,EAAoB,CAE9B,IAAKA,EAAM7K,OAAS;AACpB6K,EAAQ,IAAIlF,OAAOkF,EAAO,IAC3B,CAKA,MA8BMsJ,EAAuBzD,GAEN,IAAlBA,EAAK0D,SAhCc1D,KAEvB,IAAIhK,EAAQgK,EAAK5D,KAAKpG,MAAMmE;AAC5B,GAAInE,GAASgK,EAAK5D,KAAK9M,OAAS,EAAG,CAClC,IAAIqU,EAAYrD,SAASC,cAAc;AACvCoD,EAASC,UAAY;AACrB,IAAIC,EAAa7D,EAAK8D,UAAU9N,EAAM+N;AAEtCF,EAAUC,UAAU9N,EAAM,GAAI1G;AAC9B,IAAI0U,EAAeH,EAAUI,WAAU;AAIvC,OAFAN,EAASO,YAAYF,GACrBb,GAAYU,EAAWF,GAChB,CACR,CAEA,OAAO,CAAC,EAiBAQ,CAAcnE,IAZKA,KACL,IAAlBA,EAAK0D,WAAkB1D,EAAKoE,YAAe,kBAAkBC,KAAKrE,EAAKsE,UAAiC,cAAnBtE,EAAK4D,WAA8C,SAAjB5D,EAAKsE,SAC/HzT,MAAMC,KAAKkP,EAAKoE,YAAY3V,SAAQ+U,IACnCC,EAAmBD,EAAQ,GAE7B,EAUAe,CAAkBvE,GAEX;AAGRyD,EAAoBD,EAAS,ECtDjBgB,GADqC,oBAAdC,WAAoC,MAAMJ,KAAKI,UAAUC,WACvD,UAAY;ACXnC,IAAAC,GAAA,CACd/I,QAAS,GACTgJ,UAAW,GAEXC,QAAS,GACTC,UAAW,IACXC,QAAS,KACTC,SAAS,EACThK,YAAY,EACZiK,OAAQ,KACRC,cAAc,EACdC,aAAc,KACd5B,WAAW,EACX6B,aAAa,EACbC,WAAY,KACZC,WAAY,GACZC,SAAU,KACVC,aAAc,KACdC,YAAY,EACZC,eAAe,EACfC,aAAa,EACbC,QAAS,KACTC,kBAAkB,EAElBC,gBAAiB,IAGjBC,aAAc,IACdC,aAAc,UAEdC,SAAU,KACVC,cAAe,WACfC,WAAY,QACZC,WAAY,OACZC,cAAe,WACfC,mBAAoB,QACpBC,mBAAoB,QACpBC,mBAAmB,EAEnBC,UAAW,SACXC,YAAa,CAAC,QACdC,kBAAmB,MAEnBC,KAAM,KACNC,aAAc,aACdC,aAAc,aACdC,cAAe,cACfC,qBAAsB,sBACtBC,UAAW,OACXC,YAAa,SAEbC,eAAgB,KAChBC,aAAc,oDAEdC,uBAAuB,EAEvBC,YAAa,KACbC,gBAAiB,KAEjBC,WAAY,SAAStM,GACpB,OAAOA,EAAM5L,OAAS,CACtB,EAsBDmY,OAAQ,CACP;AC/Ea,SAASC,GAAa/V,EAAgBgW,GACpD,IAAI5M,EAAuB7F,OAAOiI,OAAO,CAAA,EAAIwH,GAAUgD,GAEnDC,EAAe7M,EAASkL,SACxB4B,EAAiB9M,EAASqL,WAC1B0B,EAAiB/M,EAASoL,WAC1B4B,EAAmBhN,EAASsL,cAC5B2B,EAAmBjN,EAASmL,cAC5B+B,EAAuBlN,EAASuL,mBAChC4B,EAAuBnN,EAASwL,mBAEhC4B,EAAcxW,EAAM2S,QAAQ9O,cAC5B8R,EAAiB3V,EAAMgO,aAAa,gBAAkBhO,EAAMgO,aAAa;AAE7E,IAAK2H,IAAgBvM,EAAS8K,iBAAkB,CAC/C,IAAIuC,EAAUzW,EAAMgP,cAAc;AAC9ByH,IACHd,EAAcc,EAAOC,YAGvB,CAEA,IAAIC,EAMA,CACHhB,YAAcA,EACd1L,QAAW,GACXgJ,UAAY,GACZ9J,MAAS,GACTyK,SAAW;AAuIZ,MANiB,WAAb4C,EAzHcI,MACjB,IAAIjE,EACA1I,EAAU0M,EAAiB1M,QAC3B4M,EAAgC,CAAE,EAClCC,EAAc;AAClB,IAAIC,EAAS;AAEb,IAAIC,EAAYlJ,IAEf,IAAIrD,EAAOlH,OAAOiI,OAAO,CAAA,EAAGsC,EAAGmJ,SAC3BC,EAAOjB,GAAaxL,EAAKwL;AAM7B,MAJoB,iBAATiB,GAAqBA,EAAKvZ,SACpC8M,EAAOlH,OAAOiI,OAAOf,EAAK1D,KAAKC,MAAMkQ,KAG/BzM,CAAI,EAGR0M,EAAYA,CAACV,EAA0BW,KAE1C,IAAIzX,EAAQqM,EAASyK,EAAO9W;AAC5B,GAAc,MAATA,IACCA,GAAUyJ,EAAS8K,kBAAzB,CAMA,GAAI2C,EAAW7N,eAAerJ,IAC7B,GAAIyX,EAAO,CACV,IAAIC,EAAMR,EAAWlX,GAAO0W;AACvBgB,EAEOnY,MAAM2J,QAAQwO,GAGzBA,EAAI9Z,KAAK6Z,GAFTP,EAAWlX,GAAO0W,GAAkB,CAACgB,EAAKD,GAF1CP,EAAWlX,GAAO0W,GAAkBe,CAMtC,MAEI,CAEJ,IAAIE,EAA0BN,EAASP;AACvCa,EAAYpB,GAAkBoB,EAAYpB,IAAgBO,EAAOC,YACjEY,EAAYnB,GAAkBmB,EAAYnB,IAAgBxW,EAC1D2X,EAAYlB,GAAkBkB,EAAYlB,IAAmBK,EAAOc,SACpED,EAAYjB,GAAkBiB,EAAYjB,IAAmBe,EAC7DE,EAAYE,QAAYf,EACxBa,EAAYP,OAAWO,EAAYP,UAAYA,EAE/CF,EAAWlX,GAAS2X,EACpBrN,EAAQ1M,KAAK+Z,EACd,CAEIb,EAAOgB,UACVd,EAAiBxN,MAAM5L,KAAKoC,EAjCc,CAkC3C;AAsBDgX,EAAiB/C,SAAW5T,EAAM0X,aAAa,YAAc,KAAO,EAEpE5O,EAAQ9I,EAAM2X,UAAUC,IArBPC,MACZ1M,EAAW2M;AAsBC,cADhBnF,EAAUiF,EAAMjF,QAAQ9O,iBAnBxBiU,EAAsBd,EAHNa,EAwBND,IApBItB,GAAyBwB,EAAcxB,IAAyBuB,EAAS7J,aAAa,UAAY,GAChH8J,EAAcvB,GAAyBuB,EAAcvB,IAAyBO,IAC9EgB,EAAc1B,GAAoB0B,EAAc1B,IAAmByB,EAASN,SAC5EO,EAAcf,OAAae,EAAcf,UAAYA,EAErDJ,EAAiB1D,UAAU1V,KAAKua,GAEhC3M,EAAK2M,EAAcvB,GAEnBzN,EAAQ+O,EAASF,UAAWlB,IAC3BU,EAAUV,EAA6BtL,EAAG,KAWpB,WAAZwH,GACVwE,EAAUS,EACX,GACC,EAkCFhB,GAzBkBmB,MAClB,MAAMC,EAAWhY,EAAMgO,aAAaiI;AAEpC,GAAK+B,EAaJrB,EAAiB1M,QAAUlD,KAAKC,MAAMgR,GACtClP,EAAS6N,EAAiB1M,SAAUgO,IACnCtB,EAAiBxN,MAAM5L,KAAK0a,EAAI9B,GAAa;IAfhC,CACd,IAAIxW,EAAQK,EAAML,MAAM8L,QAAU;AAClC,IAAKrC,EAAS8K,mBAAqBvU,EAAMhC,OAAQ;AACjD,MAAMua,EAASvY,EAAM9C,MAAMuM,EAAS+J;AAEpCrK,EAASoP,GAASvY,IACjB,MAAM8W,EAAmB,CAAE;AAC3BA,EAAOP,GAAevW,EACtB8W,EAAON,GAAexW,EACtBgX,EAAiB1M,QAAQ1M,KAAKkZ,EAAO,IAEtCE,EAAiBxN,MAAQ+O,CAC1B,CAKA,EAOAH,GAGMxU,OAAOiI,OAAQ,CAAA,EAAIwH,GAAU2D,EAAkBX,EACvD,CCrIA,IAAImC,GAAa;AAEF,MAAMC,WCXN,SAAqBC,GAInC,OAFAA,EAAUnF,QAAU,CAAE,EAEf,cAAcmF,EAASpb,WAAAA,IAAAe,GAAAsa,SAAAta,GAAAd,KAEtBgW,QAAmB,CACzBjL,MAAY,GACZmB,SAAY,CAAE,EACdmP,UAAY,CAAE,EACdC,OAAY,CAAA,EACZ,CAOD,aAAOC,CAAO1Q,EAAaqE,GAC1BiM,EAAUnF,QAAQnL,GAAQ,CACzBA,KAASA,EACTqE,GAASA,EAEX,CAkBAsM,iBAAAA,CAAkBxF,GACjB,IAAItK,EAAKb;AACT,MAAM9J,EAAQf,KACRyb,EAAiB;AAEvB,GAAIzZ,MAAM2J,QAAQqK,GACjBA,EAAQpW,SAAS8b,IACM,iBAAXA,EACVD,EAAMpb,KAAKqb,IAEX3a,EAAKiV,QAAQ9J,SAASwP,EAAO7Q,MAAQ6Q,EAAO3O,QAC5C0O,EAAMpb,KAAKqb,EAAO7Q,MACnB;KAEK,GAAImL,EACV,IAAKtK,KAAOsK,EACPA,EAAQlK,eAAeJ,KAC1B3K,EAAKiV,QAAQ9J,SAASR,GAAOsK,EAAQtK,GACrC+P,EAAMpb,KAAKqL;AAKd,KAAOb,EAAO4Q,EAAMzQ,SACnBjK,EAAK4a,QAAQ9Q,EAEf,CAEA+Q,UAAAA,CAAW/Q,GACV,IAAI9J,EAAUf,KACVgW,EAAUjV,EAAKiV,QACf0F,EAAUP,EAAUnF,QAAQnL;AAEhC,IAAKsQ,EAAUnF,QAAQlK,eAAejB,GACrC,MAAM,IAAIgR,MAAM,mBAAsBhR,EAAO;AAG9CmL,EAAQqF,UAAUxQ,IAAQ,EAC1BmL,EAAQsF,OAAOzQ,GAAQ6Q,EAAOxM,GAAGlO,MAAMD,EAAM,CAACA,EAAKiV,QAAQ9J,SAASrB,IAAS,CAAE,IAC/EmL,EAAQjL,MAAM1K,KAAKwK,EACpB,CAMA8Q,OAAAA,CAAQ9Q,GACP,IAAI9J,EAAOf,KACPgW,EAAUjV,EAAKiV;AAEnB,IAAKjV,EAAKiV,QAAQsF,OAAOxP,eAAejB,GAAO,CAC9C,GAAImL,EAAQqF,UAAUxQ,GACrB,MAAM,IAAIgR,MAAM,oCAAsChR,EAAO;AAE9D9J,EAAK6a,WAAW/Q,EACjB,CAEA,OAAOmL,EAAQsF,OAAOzQ,EACvB,EAIF,CD5FuCiR,CAAYhc,IAmDlDC,WAAAA,CAAagc,EAA4BC,GAKxC,IAAIC;AAJJb,QAAQpb,KA3CFkc,MAAqB,EAAClc,KAYtBmc,QAAuB,EAAKnc,KAC5Boc,YAA0B,EAAKpc,KAC/Bqc,YAA0B,EAAKrc,KAE/Bsc,WAAyB,EAAOtc,KAChCuc,SAAwB,EAAIvc,KAC5Bwc,UAAyB,EAAKxc,KAC9Byc,WAAyB,EAAKzc,KAC9B0c,eAA4B,EAAK1c,KACjC2c,SAAwB,EAAK3c,KAC7B4c,aAA2B,EAAK5c,KAChC6c,aAA2B,EAAK7c,KAChC8c,YAA0B,EAAK9c,KAE/B+c,UAAwB,GAAE/c,KAC1Bgd,SAAwB,EAAChd,KACzBqP,QAAuB,EAACrP,KACxBwP,eAAgD,CAAE,EAAAxP,KAElDid,aAAqC,KAAIjd,KACzCkd,YAA6B,GAAEld,KAE/B+V,UAA4B,CAAE,EAAA/V,KAC9B+M,QAA2B,CAAE,EAAA/M,KAC7Bmd,YAA0C,CAAE,EAAAnd,KAC5CiM,MAAuB,GAAEjM,KAExBod,eAAiC,KAMxCnC;AAGA,IAAInY,EAAWsO,EAAQ2K;AAEvB,GAAIjZ,EAAMua,UACT,MAAM,IAAIxB,MAAM;AAIjB/Y,EAAMua,UAAcrd,KAKpBic,GADqBqB,OAAOC,kBAAoBD,OAAOC,iBAAiBza,EAAO,OACtD0a,iBAAiB;AAG1C,MAAMtR,EAAa2M,GAAa/V,EAAOkZ;AACvChc,KAAKkM,SAAaA,EAClBlM,KAAK8C,MAAWA,EAChB9C,KAAKyd,SAAa3a,EAAM2a,UAAY,EACpCzd,KAAK0d,cAAiD,WAAhC5a,EAAM2S,QAAQ9O,cACpC3G,KAAK2d,IAAS,OAAOnI,KAAKyG,GAC1Bjc,KAAK4d,QAAYjN,EAAM7N,EAAO,aAAamY,IAC3Cjb,KAAK6d,WAAe/a,EAAMgb,SAI1B9d,KAAK+d,OAAS,IAAI/R,EAAOhM,KAAK+M,QAAS,CAACZ,WAAYD,EAASC,aAG7DD,EAAS6L,KAAO7L,EAAS6L,OAA+B,IAAtB7L,EAASwK,SAAiB,SAAW,SAClC,kBAA1BxK,EAASyK,eACnBzK,EAASyK,aAAiC,UAAlBzK,EAAS6L,MAGM,kBAA7B7L,EAASwM,kBACnBxM,EAASwM,gBAAoC,UAAlBxM,EAAS6L;AAIrC,IAAI5W,EAAS+K,EAASoK;AACA,mBAAXnV,IAEY,iBAAXA,IACVA,EAAS,IAAIiF,OAAOjF,IAGjBA,aAAkBiF,OACrB8F,EAASoK,aAAgBxT,GAAmB3B,EAAkBqU,KAAK1S,GAEnEoJ,EAASoK,aAAgB7T,GACjBzC,KAAKkM,SAAS0K,aAAe5W,KAAK+M,QAAQtK,IAMpDzC,KAAKwb,kBAAkBtP,EAAS8J,SAChChW,KAAKge,iBACLhe,KAAKie;AAIL,MAAM1K,EAAYnC,EAAO,SACnB8M,EAAY9M,EAAO,SACnB+M,EAAane,KAAKoe,QAAQ,YAC1BC,EAAmBjN,EAAO,sCAE1BuB,EAAY3S,KAAK8C,MAAMgO,aAAa,UAAY,GAChDwN,EAAcpS,EAAS6L;AAE7B,IAAIwG;AAuBJ,GApBA9L,EAAYc,EAASrH,EAAS8L,aAAcrF,EAAS2L,GAGrD7L,EAAWyL,EAAQhS,EAAS+L,cAC5BhH,EAAQsC,EAAS2K,GAGjBzL,EAAW0L,EAAUjS,EAASgM,cAAeoG,GACzCpS,EAASsM,uBACZ/F,EAAY0L,EAAUxL,GAIvBF,EAAW4L,EAAkBnS,EAASiM,sBACtClH,EAAQkN,EAAUE,GAElBjN,EAAQlF,EAASoM,gBAAkB/E,GAAU8B,YAAa8I,GAItD5M,EAAarF,EAASqM,cAAe,CACxCgG,EAAiBnN,EAAOlF,EAASqM;AAIjC3M,EADY,CAAC,cAAc,iBAAiB,eAAe,eAC5CwI,IACVtR,EAAMgO,aAAasD,IACtBH,GAAQsK,EAAc,CAACnK,CAACA,GAAMtR,EAAMgO,aAAasD,IAClD,IAGDmK,EAAcd,UAAY,EAC1BS,EAAQ7I,YAAakJ,GACrBve,KAAKwe,WAAcD,CAGpB,MAAUrS,EAASqM,cAClBgG,EAAiBnN,EAAQlF,EAASqM,cAClCvY,KAAKwe,WAAcD,IAGnBA,EAAiBnN,EAAO,YACxBpR,KAAKwe,WAAcN;AAGpBle,KAAKuT,QAAYA,EACjBvT,KAAKme,SAAaA,EAClBne,KAAKqe,iBAAmBA,EACxBre,KAAKke,QAAaA,EAClBle,KAAKue,cAAiBA,EAEtBve,KAAKye,OACN,CAMAA,KAAAA,GAEC,MAAM1d,EAAOf,KACPkM,EAAcnL,EAAKmL,SACnBqS,EAAkBxd,EAAKwd,cACvBJ,EAAcpd,EAAKod,SACnBE,EAAoBtd,EAAKsd,iBACzB9K,EAAaxS,EAAKwS,QAClB2K,EAAand,EAAKmd,QAClBpb,EAAY/B,EAAK+B,MACjB0b,EAAezd,EAAKyd,WACpBE,EAAkB,CAAEC,SAAS,GAC7BC,EAAe7d,EAAK6c,QAAS;AAGnC3J,GAAQoK,EAAiB,CACxBpQ,GAAI2Q,IAGL3K,GAAQuK,EAAW,CAClBK,KAAK,WACL,gBAAgB,UAChB,gBAAgB,QAChB,gBAAgBD;AAGjB,MAAME,EAAanO,EAAM6N,EAAWzd,EAAK6c,QAAU,eAC7CvR,EAAU,cLhOUA,IACpBA,EAAM/J,QAAQ,UAAW,QK+NDyc,CAAYhe,EAAK6c,SAAS,KAClDoB,EAAUvN,SAASK,cAAczF,GACjC4S,EAAcle,EAAKme,MAAMnR,KAAKhN;AACpC,GAAIie,EAAO,CACV9O,EAAS8O,EAAM,QAASC,GACxBhL,GAAQ+K,EAAM,CAACG,IAAIL;AACnB,MAAMM,EAAWzO,EAAMqO,EAAMje,EAAK6c,QAAQ;AAC1C3J,GAAQuK,EAAW,CAAC,kBAAkBY,IACtCnL,GAAQoK,EAAiB,CAAC,kBAAkBe,GAC7C,CAIA,GAFA7L,EAAQf,MAAM6M,MAAQvc,EAAM0P,MAAM6M,MAE9Bte,EAAKiV,QAAQjL,MAAMtK,OAAQ,CAC9B,MAAM6e,EAAkB,UAAYve,EAAKiV,QAAQjL,MAAMzJ,KAAK;AAC5DmR,EAAY,CAACc,EAAQ4K,GAAWmB,EACjC,EAE2B,OAAtBpT,EAASwK,UAAqBxK,EAASwK,SAAW,IAAM3V,EAAK2c,eACjEzJ,GAAQnR,EAAM,CAACyc,SAAS,aAGrBrT,EAASuM,aACZxE,GAAQsK,EAAc,CAAC9F,YAAYvM,EAASuM,eAIxCvM,EAASgK,SAAWhK,EAAS+J,YACjC/J,EAASgK,QAAU,IAAI9P,OAAO,OAAShE,EAAa8J,EAAS+J,WAAa,UAKvE/J,EAASsT,MAAQtT,EAASgL,eAC7BhL,EAASsT,KAAOvQ,EAAa/C,EAASsT,KAAKtT,EAASgL,eAGrDhH,EAASiO,EAAS,aAAa,KAC9Bpd,EAAK8b,aAAc,CAAK,IAGzB3M,EAASiO,EAAS,cAAe7Z,IAEhC,IAAImb,EAAepM,EAAY/O,EAAE6L,OAAuB,oBAAqBgO;AACzEsB,GAAe1e,EAAK2e,cAAepb,EAAiBmb,EAAc,GAEpE,CAACE,SAAQ,IAGZzP,EAASiO,EAAS,SAASpO,IAC1B,MAAMwJ,EAASlG,EAAYtD,EAAII,OAAuB;AAClDoJ,IACHxY,EAAK6e,eAAgB7P,EAAmBwJ,GACxCzJ,EAAeC,GAAI,GACpB,IAGDG,EAASgO,EAAQ,SAAUnO,IAE1B,IAAI0P,EAAepM,EAAatD,EAAII,OAAuB,iBAAkB+N;AACzEuB,GAAgB1e,EAAK8e,aAAa9P,EAAmB0P,GACxD3P,EAAeC,GAAI,GAKO,IAAvBwO,EAAc9b,QAIlB1B,EAAK+e,UACLhQ,EAAeC,GAAI,GAAK,IAKzBG,EAASsO,EAAW,WAAala,GAAMvD,EAAKgf,UAAUzb,KAGtD4L,EAASqO,EAAc,YAAaja,GAAMvD,EAAKif,WAAW1b,KAC1D4L,EAASqO,EAAc,SAAWja,GAAMvD,EAAKkf,QAAQ3b,KACrD4L,EAASsO,EAAW,QAAWla,GAAMvD,EAAKmf,OAAO5b,KACjD4L,EAASsO,EAAW,SAAWla,GAAMvD,EAAKof,QAAQ7b,KAClD4L,EAASqO,EAAc,SAAWja,GAAMvD,EAAKqf,QAAQ9b;AAGrD,MAAM+b,EAAiBtQ,IAItB,MAAMI,EAASJ,EAAIuQ,eAAe;AAClC,IAAK/M,EAAQC,SAASrD,KAA2BgO,EAAS3K,SAASrD,GAKlE,OAJIpP,EAAK0b,WACR1b,EAAKwf,YAENxf,EAAKyf;AASFrQ,GAAUoO,GAAiBxd,EAAKob,OACnCpM,EAAIE,kBAIJH,EAAeC,GAAI,EACpB,EAIK0Q,EAAaA,KACd1f,EAAKob,QACRpb,EAAK2f,kBACN;AAIDxQ,EAASuB,SAAS,YAAa4O,GAC/BnQ,EAASoN,OAAO,SAAUmD,EAAY/B,GACtCxO,EAASoN,OAAO,SAAUmD,EAAY/B,GAEtC1e,KAAK2gB,SAAW,KACflP,SAASmP,oBAAoB,YAAYP,GACzC/C,OAAOsD,oBAAoB,SAASH,GACpCnD,OAAOsD,oBAAoB,SAASH,GAChCzB,GAAQA,EAAM4B,oBAAoB,QAAQ3B,EAAY,EAK3Djf,KAAK6gB,eAAiB,CACrBlP,UAAY7O,EAAM6O,UAClB8L,SAAW3a,EAAM2a,UAIlB3a,EAAM2a,UAAY,EAClB3a,EAAMge,sBAAsB,WAAY/f,EAAKwS,SAE7CxS,EAAKggB,MAAK,GACV7U,EAASD,MAAQ,UACVC,EAAS6J,iBACT7J,EAASa,QAEhBmD,EAASpN,EAAM,WAAW,KACrB/B,EAAKwb,UACRxb,EAAKwb,SAAU,EACfxb,EAAKub,WAAY,EACjBvb,EAAKigB,eACN,IAGDjgB,EAAKkgB,sBACLlgB,EAAKmgB,eACLngB,EAAKogB,OAAM,GACXpgB,EAAKyf,aACLzf,EAAK4b,SAAU,EAEX7Z,EAAMuX,SACTtZ,EAAKqgB,UACIte,EAAMue,SACftgB,EAAKugB,aAAY,GAEjBvgB,EAAKwgB,SAGNxgB,EAAKb,GAAG,SAAUF,KAAKwhB,UAEvB/O,EAAW3P,EAAM,cAAc,wBAC/B/B,EAAKF,QAAQ,eAGY,IAArBqL,EAAS6K,SACZhW,EAAKgW,SAGP,CAOA0K,YAAAA,CAAa1U,EAAsB,GAAIgJ,EAAwB,IAG9D/V,KAAK0hB,WAAW3U,GAIhBnB,EAASmK,GAAY4E,IACpB3a,KAAK2hB,oBAAoBhH,EAAS,GAEpC,CAKAsD,cAAAA,GACC,IAAIld,EAAOf,KACPgZ,EAAcjY,EAAKmL,SAASqL,WAC5B4B,EAAiBpY,EAAKmL,SAASuL,mBAE/BmK,EAAY,CACfjH,SAAapN,IACZ,IAAIoN,EAAWlJ,SAASC,cAAc;AAGtC,OAFAiJ,EAAS5F,UAAY,WACrB4F,EAAStF,YAAY9H,EAAKR,SACnB4N,CAAQ,EAGhBkH,gBAAmBA,CAACtU,EAAgBuU,IAC5B,gCAAkCA,EAAOvU,EAAK4L,IAAmB,SAEzEI,OAAUA,CAAChM,EAAgBuU,IACnB,QAAUA,EAAOvU,EAAKyL,IAAgB,SAE9CtK,KAAQA,CAACnB,EAAgBuU,IACjB,QAAUA,EAAOvU,EAAKyL,IAAgB,SAE9C+I,cAAiBA,CAACxU,EAAgBuU,IAC1B,mCAAqCA,EAAOvU,EAAKzK,OAAS,0BAElEkf,WAAaA,IACL,iDAER3S,QAAUA,IACF,8BAER4S,YAAcA,OACd9D,SAAWA,IACH;AAKTpd,EAAKmL,SAAS0M,OAASvS,OAAOiI,OAAO,CAAE,EAAEsT,EAAW7gB,EAAKmL,SAAS0M,OACnE,CAMAoF,cAAAA,GACC,IAAItS,EAAKwD,EACLgT,EAAkC,CACrCC,WAAoB,eACpBC,OAAoB,WACpBC,SAAoB,YACpBC,YAAoB,eACpBC,YAAoB,eACpBC,MAAoB,UACpBC,WAAoB,cACpBC,cAAoB,iBACpBC,aAAoB,gBACpBC,aAAoB,mBACpBC,gBAAoB,sBACpBC,eAAoB,qBACpBC,cAAoB,iBACpBC,eAAoB,kBACpBpT,KAAoB,SACpB4P,KAAoB,SACpBN,MAAoB,UACpBqB,KAAoB;AAGrB,IAAK7U,KAAOwW,GAEXhT,EAAKlP,KAAKkM,SAASgW,EAAUxW,MACrB1L,KAAKE,GAAGwL,EAAKwD,EAGvB,CAMA6R,IAAAA,CAAKkC,GAAqB,GACzB,MAAMliB,EAAQf,KACRkM,EAAW+W,EAAepK,GAAa9X,EAAK+B,MAAO,CAACmT,UAAUlV,EAAKmL,SAAS+J,YAAgDlV,EAAKmL;AAEvInL,EAAK0gB,aAAavV,EAASa,QAAQb,EAAS6J,WAE5ChV,EAAKmiB,SAAShX,EAASD,OAAO,IAAG,GAEjClL,EAAKoiB,UAAY,IAClB,CAOArD,OAAAA,GACC,IAAI/e,EAAOf;AAEX,GAAIe,EAAKmc,YAAYzc,OAAS,EAG7B,OAFAM,EAAKqiB,wBACLriB,EAAKme;AAIFne,EAAK0b,WAAa1b,EAAKob,OAC1Bpb,EAAKwf,OAELxf,EAAKme,OAEP,CAMAmE,WAAAA,GAAmB,CAOnB7B,QAAAA,GACCxP,EAAahS,KAAK8C,MAAO,SACzBkP,EAAahS,KAAK8C,MAAO,SAC1B,CAMAsd,OAAAA,CAAQ9b,GACP,IAAIvD,EAAOf;AAEPe,EAAK2b,eAAiB3b,EAAKyb,SAC9B1M,EAAexL,GAMXvD,EAAKmL,SAASgK,SAKnB3G,YAAW,KACV,IAAI+T,EAAaviB,EAAKwiB;AACtB,GAAKD,EAAWnc,MAAMpG,EAAKmL,SAASgK,SAApC,CAIA,IAAIsN,EAAaF,EAAW/U,OAAO5O,MAAMoB,EAAKmL,SAASgK;AACvDtK,EAAS4X,GAAaC,IAER3U,EAAS2U,KAEjBzjB,KAAK+M,QAAQ0W,GAChB1iB,EAAK2iB,QAAQD,GAEb1iB,EAAK4iB,WAAWF,GAElB,GAZD,CAaE,GACA,EAEJ,CAMAzD,UAAAA,CAAW1b,GACV,IAAIvD,EAAOf;AACX,IAAGe,EAAKyb,SAAR,CAIA,IAAIoH,EAAYpc,OAAOC,aAAanD,EAAEuf,SAAWvf,EAAEwf;AACnD,OAAI/iB,EAAKmL,SAASkK,QAAiC,UAAvBrV,EAAKmL,SAAS6L,MAAoB6L,IAAc7iB,EAAKmL,SAAS+J,WACzFlV,EAAK4iB,kBACL7T,EAAexL,SAFhB,CAFA,CAFCwL,EAAexL,EASjB,CAMAyb,SAAAA,CAAUzb,GACT,IAAIvD,EAAOf;AAIX,GAFAe,EAAK8b,aAAc,EAEf9b,EAAKyb,SHxoBc,IGyoBlBlY,EAAEuf,SACL/T,EAAexL;IAFjB,CAOA,OAAQA,EAAEuf,SAGT,KH3pBqB,GG4pBpB,GAAIxT,EAAU0T,GAAuBzf,IACJ,IAA5BvD,EAAKwd,cAAc9b,MAGtB,OAFAqN,EAAexL,QACfvD,EAAKijB;AAIP;AAGD,KHpqBsB,GG0qBrB,OALIjjB,EAAKob,SACRrM,EAAexL,GAAE,GACjBvD,EAAKogB,cAENpgB,EAAKqiB;AAIN,KHzqBuB,GG0qBtB,IAAKriB,EAAKob,QAAUpb,EAAK+b,WACxB/b,EAAKkjB;KACC,GAAIljB,EAAKkc,aAAc,CAC7B,IAAIiH,EAAOnjB,EAAKojB,YAAYpjB,EAAKkc,aAAc;AAC3CiH,GAAMnjB,EAAKqjB,gBAAgBF,EAChC,CAEA,YADApU,EAAexL;AAIhB,KHtrBsB,GGurBrB,GAAIvD,EAAKkc,aAAc,CACtB,IAAIoH,EAAOtjB,EAAKojB,YAAYpjB,EAAKkc,cAAe;AAC5CoH,GAAMtjB,EAAKqjB,gBAAgBC,EAChC,CAEA,YADAvU,EAAexL;AAIhB,KHlsByB,GGgtBxB,YAbIvD,EAAKujB,UAAUvjB,EAAKkc,eACvBlc,EAAK6e,eAAetb,EAAEvD,EAAKkc,cAC3BnN,EAAexL,KAGNvD,EAAKmL,SAASkK,QAAUrV,EAAK4iB,cAI7BlS,SAAS8S,eAAiBxjB,EAAKwd,eAAiBxd,EAAKob,SAH9DrM,EAAexL;AAUjB,KHjtBuB,GGmtBtB,YADAvD,EAAKyjB,kBAAkB,EAAGlgB;AAI3B,KHptBwB,GGstBvB,YADAvD,EAAKyjB,iBAAiB,EAAGlgB;AAI1B,KHrtBsB,EGmuBrB,YAZIvD,EAAKmL,SAAS4K,cACb/V,EAAKujB,UAAUvjB,EAAKkc,gBACvBlc,EAAK6e,eAAetb,EAAEvD,EAAKkc,cAI3BnN,EAAexL,IAEZvD,EAAKmL,SAASkK,QAAUrV,EAAK4iB,cAChC7T,EAAexL;AAMlB,KHxuB2B,EGyuB3B,KHxuByB,GG0uBxB,YADAvD,EAAK0jB,gBAAgBngB,GAKnBvD,EAAK2b,gBAAkBrM,EAAU0T,GAAuBzf,IAC3DwL,EAAexL,EAjGhB,CAmGD,CAMA2b,OAAAA,CAAQ3b,GAEP,GAAItE,KAAKwc,SACR;AAGD,MAAM/Z,EAAQzC,KAAKujB;AACfvjB,KAAK+c,YAActa,IACvBzC,KAAK+c,UAAYta,EAEJ,IAATA,GAKAzC,KAAKod,gBACRE,OAAOhO,aAAatP,KAAKod,gBAG1Bpd,KAAKod,eNruBgBhO,EAACF,EAAYE,IAC/BA,EAAU,EACNkO,OAAO/N,WAAWL,EAAGE,IAG7BF,EAAGO,KAAK,MACD,MM+tBgBL,EAAQ,KAC7BpP,KAAKod,eAAiB,KACtBpd,KAAK0kB,UAAU,GACb1kB,KAAKkM,SAAS+K,kBAXhBjX,KAAK0kB,WAYP,CAEAA,QAAAA,GACC,MAAMjiB,EAAQzC,KAAK+c;AAEf/c,KAAKkM,SAASyM,WAAWlJ,KAAKzP,KAAKyC,IACtCzC,KAAKwf,KAAK/c,GAGXzC,KAAK2kB,iBACL3kB,KAAKa,QAAQ,OAAQ4B,EACtB,CAOAid,aAAAA,CAAe3P,EAA8BwJ,GACxCvZ,KAAK6c,aACT7c,KAAKokB,gBAAgB7K,GAAQ,EAC9B,CAMA4G,OAAAA,CAAQ7b,GACP,IAAIvD,EAAOf,KACP4kB,EAAa7jB,EAAK0b;AAEtB,GAAI1b,EAAKqb,YAAcrb,EAAKsb,WAG3B,OAFAtb,EAAKwf,YACLzQ,EAAexL;AAIZvD,EAAK6b,cACT7b,EAAK0b,WAAY,EACa,UAA1B1b,EAAKmL,SAAS6K,SAAsBhW,EAAKgW,UAExC6N,GAAY7jB,EAAKF,QAAQ,SAEzBE,EAAKmc,YAAYzc,SACrBM,EAAKyf,aACLzf,EAAK4jB,iBAAiB5jB,EAAKmL,SAASqK,cAGrCxV,EAAKigB,eACN,CAMAd,MAAAA,CAAO5b,GAEN,IAA4B,IAAxBmN,SAASoT,WAAb,CAEA,IAAI9jB,EAAOf;AACX,GAAKe,EAAK0b,UAAV,CACA1b,EAAK0b,WAAY,EACjB1b,EAAK6b,aAAc;AAEnB,IAAIkI,EAAaA,KAChB/jB,EAAKogB,QACLpgB,EAAKgkB,gBACLhkB,EAAKikB,SAASjkB,EAAKkL,MAAMxL,QACzBM,EAAKF,QAAQ,OAAO;AAGjBE,EAAKmL,SAASkK,QAAUrV,EAAKmL,SAASmK,aACzCtV,EAAK4iB,WAAW,KAAMmB,GAEtBA,GAdoB,CAHe,CAmBrC,CAQAlF,cAAAA,CAAgB7P,EAA8BwJ,GAC7C,IAAI9W,EAAO1B,EAAOf;AAIduZ,EAAO0L,eAAiB1L,EAAO0L,cAAcxR,QAAQ,qBAKrD8F,EAAOvG,UAAUQ,SAAS,UAC7BzS,EAAK4iB,WAAW,MAAM,KACjB5iB,EAAKmL,SAASgZ,kBACjBnkB,EAAKogB,OACN,SAIoB,KADrB1e,EAAQ8W,EAAOQ,QAAQtX,SAEtB1B,EAAKoiB,UAAY,KACjBpiB,EAAK2iB,QAAQjhB,GACT1B,EAAKmL,SAASgZ,kBACjBnkB,EAAKogB,SAGDpgB,EAAKmL,SAASyK,cAAgB5G,EAAIH,MAAQ,QAAQ4F,KAAKzF,EAAIH,OAC/D7O,EAAKqjB,gBAAgB7K,IAIzB,CAMA+K,SAAAA,CAAU/K,GAET,SAAIvZ,KAAKmc,QAAU5C,GAAUvZ,KAAKqe,iBAAiB7K,SAAS+F,GAI7D,CAOAsG,YAAAA,CAAc9P,EAAiBrB,GAC9B,IAAI3N,EAAOf;AAEX,OAAKe,EAAKyb,UAAmC,UAAvBzb,EAAKmL,SAAS6L,OACnCjI,EAAeC,GACfhP,EAAKgkB,cAAcrW,EAAMqB,IAClB,EAGT,CAkBAoV,OAAAA,CAAQ1iB,GAEP,QAAKzC,KAAKkM,SAASsT,OACfxf,KAAKwP,eAAe1D,eAAerJ,EAGxC,CAMA+c,IAAAA,CAAK/c,GACJ,MAAM1B,EAAOf;AAEb,IAAKe,EAAKokB,QAAQ1iB,GAAS;AAE3BgQ,EAAW1R,EAAKwS,QAAQxS,EAAKmL,SAASiL,cACtCpW,EAAKsO;AAEL,MAAM3P,EAAWqB,EAAKqkB,aAAarX,KAAKhN;AACxCA,EAAKmL,SAASsT,KAAK/P,KAAK1O,EAAM0B,EAAO/C,EACtC,CAMA0lB,YAAAA,CAAcrY,EAAqBgJ,GAClC,MAAMhV,EAAOf;AACbe,EAAKsO,QAAU3M,KAAKC,IAAI5B,EAAKsO,QAAU,EAAG,GAC1CtO,EAAKoiB,UAAY,KAEjBpiB,EAAKskB,oBACLtkB,EAAK0gB,aAAa1U,EAAQgJ,GAE1BhV,EAAK4jB,eAAe5jB,EAAK0b,YAAc1b,EAAK2b,eAEvC3b,EAAKsO,SACT4D,EAAclS,EAAKwS,QAAQxS,EAAKmL,SAASiL,cAG1CpW,EAAKF,QAAQ,OAAQkM,EAASgJ,EAC/B,CAEAgB,OAAAA,GACC,IAAI/D,EAAYhT,KAAKuT,QAAQP;AACzBA,EAAUQ,SAAS,eACvBR,EAAU5L,IAAI,aACdpH,KAAKwf,KAAK,IACX,CAOA8F,eAAAA,CAAgB7iB,EAAe,IAC9B,IAAIK,EAAQ9C,KAAKue;AACHzb,EAAML,QAAUA,IAE7BK,EAAML,MAAQA,EACduP,EAAalP,EAAM,UACnB9C,KAAK+c,UAAYta,EAEnB,CASA8iB,QAAAA,GAEC,OAAIvlB,KAAK0d,eAAiB1d,KAAK8C,MAAM0X,aAAa,YAC1Cxa,KAAKiM,MAGNjM,KAAKiM,MAAM3K,KAAKtB,KAAKkM,SAAS+J,UACtC,CAMAiN,QAAAA,CAAUzgB,EAAuB+iB,GAGhC9V,EAAgB1P,KAFHwlB,EAAS,GAAK,CAAC,WAEC,KAC5BxlB,KAAKwiB,MAAMgD,GACXxlB,KAAKylB,SAAShjB,EAAO+iB,EAAO,GAE9B,CAOAE,WAAAA,CAAYjjB,GACE,IAAVA,IAAaA,EAAQ,MACxBzC,KAAKkM,SAASwK,SAAWjU,EACzBzC,KAAKghB,cACN,CAMA+D,aAAAA,CAAerW,EAAepK,GAC7B,IACIqhB,EACA7jB,EAAG8jB,EAAO3c,EAAK4c,EACfpc,EAHA1I,EAAOf;AAKX,GAA2B,WAAvBe,EAAKmL,SAAS6L,KAAlB,CAGA,IAAKrJ,EAKJ,OAJA3N,EAAKqiB,wBACDriB,EAAK0b,WACR1b,EAAKyf;AAQP,GAAkB,WAFlBmF,EAAYrhB,GAAKA,EAAEsL,KAAKjJ,gBAEK0J,EAAU,WAAW/L,IAAMvD,EAAKmc,YAAYzc,OAAQ,CAUhF,IATAgJ,EAAO1I,EAAK+kB,iBACZF,EAAQ5jB,MAAM+jB,UAAUnlB,QAAQ6O,KAAK1O,EAAKmd,QAAQzD,SAAUhR,KAC5DR,EAAOjH,MAAM+jB,UAAUnlB,QAAQ6O,KAAK1O,EAAKmd,QAAQzD,SAAU/L,MAG1DmX,EAAQD,EACRA,EAAQ3c,EACRA,EAAQ4c,GAEJ/jB,EAAI8jB,EAAO9jB,GAAKmH,EAAKnH,IACzB4M,EAAO3N,EAAKmd,QAAQzD,SAAS3Y,IACW,IAApCf,EAAKmc,YAAYtc,QAAQ8N,IAC5B3N,EAAKilB,mBAAmBtX;AAG1BoB,EAAexL,EACf,KAAyB,UAAdqhB,GAAyBtV,EAAU0T,GAAuBzf,IAAuB,YAAdqhB,GAA2BtV,EAAU,WAAW/L,GAC1HoK,EAAKsE,UAAUQ,SAAS,UAC3BzS,EAAKklB,iBAAkBvX,GAEvB3N,EAAKilB,mBAAmBtX,IAGzB3N,EAAKqiB,mBACLriB,EAAKilB,mBAAmBtX;AAIzB3N,EAAKyf,aACAzf,EAAK0b,WACT1b,EAAKme,OA7C+B,CA+CtC,CAMA8G,kBAAAA,CAAoBtX,GACnB,MAAM3N,EAAOf,KACPkmB,EAAcnlB,EAAKmd,QAAQpM,cAAc;AAC3CoU,GAAcjT,EAAciT,EAA2B,eAE3DzT,EAAW/D,EAAK,sBAChB3N,EAAKF,QAAQ,cAAe6N,IACW,GAAnC3N,EAAKmc,YAAYtc,QAAQ8N,IAC5B3N,EAAKmc,YAAY7c,KAAMqO,EAEzB,CAMAuX,gBAAAA,CAAkBvX,GACjB,IAAIyX,EAAMnmB,KAAKkd,YAAYtc,QAAQ8N;AACnC1O,KAAKkd,YAAYvc,OAAOwlB,EAAK,GAC7BlT,EAAcvE,EAAK,SACpB,CAMA0U,gBAAAA,GACCnQ,EAAcjT,KAAKkd,YAAY,UAC/Bld,KAAKkd,YAAc,EACpB,CAOAkH,eAAAA,CAAiB7K,EAAwB6M,GAAe,GAEnD7M,IAAWvZ,KAAKid,eAIpBjd,KAAKqlB,oBACA9L,IAELvZ,KAAKid,aAAe1D,EACpBtF,GAAQjU,KAAKwe,WAAW,CAAC,wBAAwBjF,EAAOzI,aAAa,QACrEmD,GAAQsF,EAAO,CAAC,gBAAgB,SAChC9G,EAAW8G,EAAO,UACd6M,GAASpmB,KAAKqmB,eAAe9M,IAClC,CAMA8M,cAAAA,CAAgB9M,EAAyB+M,GAExC,IAAK/M,EAAS;AAEd,MAAM3H,EAAW5R,KAAKqe,iBAChBkI,EAAc3U,EAAQ4U,aACtBC,EAAa7U,EAAQ6U,WAAa,EAClCC,EAAcnN,EAAOoN,aACrB7gB,EAAOyT,EAAOqN,wBAAwBC,IAAMjV,EAAQgV,wBAAwBC,IAAMJ;AAEpF3gB,EAAI4gB,EAAcH,EAAcE,EACnCzmB,KAAKomB,OAAOtgB,EAAIygB,EAAcG,EAAaJ,GAEjCxgB,EAAI2gB,GACdzmB,KAAKomB,OAAOtgB,EAAGwgB,EAEjB,CAMAF,MAAAA,CAAQK,EAAkBH,GACzB,MAAM1U,EAAU5R,KAAKqe;AACjBiI,IACH1U,EAAQY,MAAMsU,eAAiBR,GAEhC1U,EAAQ6U,UAAYA,EACpB7U,EAAQY,MAAMsU,eAAiB,EAChC,CAMAzB,iBAAAA,GACKrlB,KAAKid,eACRhK,EAAcjT,KAAKid,aAAa,UAChChJ,GAAQjU,KAAKid,aAAa,CAAC,gBAAgB,QAE5Cjd,KAAKid,aAAe,KACpBhJ,GAAQjU,KAAKwe,WAAW,CAAC,wBAAwB,MAClD,CAMAwF,SAAAA,GACC,MAAMjjB,EAAOf;AAEb,GAA2B,WAAvBe,EAAKmL,SAAS6L,KAAmB;AAErC,MAAMmF,EAAcnc,EAAKgmB;AAEpB7J,EAAYzc,SAEjBM,EAAKyf,aACLzf,EAAKogB,QAELpgB,EAAKmc,YAAcA,EACnBtR,EAASsR,GAAcxO,IACtB3N,EAAKilB,mBAAmBtX,EAAK,IAG/B,CAMA8R,UAAAA,GACC,IAAIzf,EAAOf;AAENe,EAAKmd,QAAQ1K,SAASzS,EAAKwd,iBAEhCtK,GAAQlT,EAAKwd,cAAc,CAAC9F,YAAY1X,EAAKmL,SAASuM,cAElD1X,EAAKmc,YAAYzc,OAAS,IAAOM,EAAK0b,WAAa1b,EAAKmL,SAASwM,iBAAmB3X,EAAKkL,MAAMxL,OAAS,GAC3GM,EAAKukB,kBACLvkB,EAAK2b,eAAgB,IAIjB3b,EAAKmL,SAASwM,iBAAmB3X,EAAKkL,MAAMxL,OAAS,GACxDwT,GAAQlT,EAAKwd,cAAc,CAAC9F,YAAY,KAEzC1X,EAAK2b,eAAgB,GAGtB3b,EAAKwS,QAAQP,UAAUgU,OAAO,eAAgBjmB,EAAK2b,eACpD,CAKA6G,UAAAA,GACC,OAAOvjB,KAAKue,cAAc9b,MAAM8L,MACjC,CAKA2Q,KAAAA,GACC,IAAIne,EAAOf;AACPe,EAAKqb,YAAcrb,EAAKsb,aAE5Btb,EAAK6b,aAAc,EAEf7b,EAAKwd,cAAc0I,YACtBlmB,EAAKwd,cAAcW,QAEnBne,EAAKyd,WAAWU,QAGjB3P,YAAW,KACVxO,EAAK6b,aAAc,EACnB7b,EAAKof,SAAS,GACZ,GACJ,CAMAI,IAAAA,GACCvgB,KAAKwe,WAAW+B,OAChBvgB,KAAKkgB,QACN,CASApT,gBAAAA,CAAiBT,GAChB,OAAOrM,KAAK+d,OAAOjR,iBAAiBT,EAAOrM,KAAKknB,mBACjD,CASAA,gBAAAA,GACC,IAAIhb,EAAWlM,KAAKkM,SAChBnE,EAAOmE,EAAS0L;AAKpB,MAJkC,iBAAvB1L,EAAS0L,YACnB7P,EAAO,CAAC,CAAC8E,MAAOX,EAAS0L,aAGnB,CACNzK,OAAcjB,EAAS2L,YACvBpK,YAAcvB,EAAS4L,kBACvB/P,KAAcA,EACdof,QAAcjb,EAASib,QAEzB,CAOA5b,MAAAA,CAAOc,GACN,IAAItJ,EAAQqkB,EACRrmB,EAAWf,KACX+M,EAAW/M,KAAKknB;AAGpB,GAAKnmB,EAAKmL,SAASd,OAEY,mBAD9Bgc,EAAiBrmB,EAAKmL,SAASd,MAAMqE,KAAK1O,EAAKsL,IAE9C,MAAM,IAAIwP,MAAM;AAqBlB,OAhBIxP,IAAUtL,EAAKoiB,WAClBpiB,EAAKoiB,UAAc9W,EACnBtJ,EAAahC,EAAKgd,OAAOxS,OAAOc,EAAOhG,OAAOiI,OAAOvB,EAAS,CAAC3B,MAAOgc,KACtErmB,EAAKsmB,eAAkBtkB,GAEvBA,EAAasD,OAAOiI,OAAQ,CAAA,EAAIvN,EAAKsmB,gBAIlCtmB,EAAKmL,SAASyK,eACjB5T,EAAOkJ,MAAQlJ,EAAOkJ,MAAM9K,QAAQuN,IACnC,IAAI4Y,EAASxY,EAASJ,EAAKT;AAC3B,QAASqZ,IAA0C,IAAhCvmB,EAAKkL,MAAMrL,QAAQ0mB,GAAgB,KAIjDvkB,CACR,CAOA4hB,cAAAA,CAAgB4C,GAA0B,GACzC,IAAIzlB,EAAG4C,EAAGC,EAAGpE,EAAGoa,EAAU5E,EAAWyR,EAAuBC,EAAmBC,EAC3EtR;AAGJ,MAAMuR,EAAgC,CAAE,EAClCC,EAAuB;AAE7B,IAAI7mB,EAAWf,KACXqM,EAAYtL,EAAKwiB;AACrB,MAAMsE,EAAexb,IAAUtL,EAAKoiB,WAAuB,IAAT9W,GAAiC,MAAlBtL,EAAKoiB;AACtE,IAAI2E,EAAc/mB,EAAKwK,OAAOc,GAC1B0b,EAAiC,KACjCC,EAAkBjnB,EAAKmL,SAASsK,aAAc,EAC9C6H,EAAoBtd,EAAKsd;AAGzBwJ,IACHE,EAAkBhnB,EAAKkc,gBAGtByK,EAAeK,EAAcE,QAAQ,iBAKvC1nB,EAAIunB,EAAQ7b,MAAMxL,OACsB,iBAA7BM,EAAKmL,SAASuK,aACxBlW,EAAImC,KAAK8G,IAAIjJ,EAAGQ,EAAKmL,SAASuK,aAG3BlW,EAAI,IACPynB,GAAgB;AAIjB,MAAME,EAAmBA,CAACvN,EAAgBuB,KAEzC,IAAIiM,EAAgBR,EAAOhN;AAE3B,QAAsBja,IAAlBynB,EAA6B,CAChC,IAAIC,EAAcR,EAAaO;AAC/B,QAAoBznB,IAAhB0nB,EACH,MAAO,CAACD,EAAcC,EAAYC,SAEpC,CAEA,IAAIC,EAAiB7W,SAAS8W;AAI9B,OAHAJ,EAAgBP,EAAannB,OAC7BmnB,EAAavnB,KAAK,CAACgoB,SAASC,EAAepM,QAAMvB,aAE1C,CAACwN,EAAcG,EAAe;AAItC,IAAKxmB,EAAI,EAAGA,EAAIvB,EAAGuB,IAAK,CAGvB,IAAI4M,EAASoZ,EAAQ7b,MAAMnK;AAC3B,IAAK4M,EAAO;AAEZ,IAAI8Z,EAAa9Z,EAAKT,GAClBsL,EAAWxY,EAAKgM,QAAQyb;AAE5B,QAAe9nB,IAAX6Y,EAAuB;AAE3B,IAAIkP,EAAY1Z,EAASyZ,GACrBE,EAAa3nB,EAAK4nB,UAAUF,GAAS;AAWzC,IARK1nB,EAAKmL,SAASyK,cAClB+R,EAAU1V,UAAUgU,OAAO,WAAYjmB,EAAKkL,MAAM2c,SAASH,IAG5D9N,EAAcpB,EAAOxY,EAAKmL,SAASmL,gBAAkB,GAIhD3S,EAAI,EAAGC,GAHZoR,EAAc/T,MAAM2J,QAAQgP,GAAYA,EAAW,CAACA,KAGvB5E,EAAUtV,OAAQiE,EAAIC,EAAGD,IAAK,CAC1DiW,EAAW5E,EAAUrR;AAErB,IAAIwX,EAAQ3C,EAAOM,OACfgP,EAAgB9nB,EAAKgV,UAAU4E;KACbja,IAAlBmoB,EACHlO,EAAW,GAEXuB,EAAQ2M,EAAchP;AAGvB,MAAOsO,EAAcG,GAAkBJ,EAAiBvN,EAASuB;AAI7DxX,EAAI,IACPgkB,EAAYA,EAAUtT,WAAU,GAChCnB,GAAQyU,EAAU,CAACza,GAAIsL,EAAOuP,IAAI,UAAUpkB,EAAE,gBAAgB,OAC9DgkB,EAAU1V,UAAU5L,IAAI,aACxB6L,EAAcyV,EAAU,UAIpB3nB,EAAKkc,cAAgBlc,EAAKkc,aAAalD,QAAQtX,OAAS+lB,GACvDd,GAAgBA,EAAa3N,QAAQG,QAAUS,EAASoO,aAC3DhB,EAAgBW,IAKnBJ,EAAejT,YAAYqT,GACX,IAAZ/N,IACHgN,EAAOhN,GAAYwN,EAErB,CACD,CJ94C8BvX,IAC3BoY;AIg5CCjoB,EAAKmL,SAASyL,mBACjBiQ,EAAa7f,MAAK,CAACnE,EAAGO,IACdP,EAAEsY,MAAQ/X,EAAE+X,QAKrBsL,EAAO/V,SAAS8W,yBAChB3c,EAASgc,GAAeqB,IAEvB,IAAIX,EAAiBW,EAAYZ,SAC7B1N,EAAWsO,EAAYtO;AAE3B,IAAK2N,IAAmBA,EAAe7N,SAASha,OAAS;AAEzD,IAAIyoB,EAAgBnoB,EAAKgV,UAAU4E;AAEnC,QAAsBja,IAAlBwoB,EAA6B,CAEhC,IAAIC,EAAgB1X,SAAS8W,yBACzBa,EAASroB,EAAK6X,OAAO,kBAAmBsQ;AAC5CjY,EAAQkY,EAAeC,GACvBnY,EAAQkY,EAAeb;AAEvB,IAAIe,EAAatoB,EAAK6X,OAAO,WAAY,CAACsB,MAAMgP,EAAcnc,QAAQoc;AAEtElY,EAAQuW,EAAM6B,EAEf,MACCpY,EAAQuW,EAAMc,EACf,IAGDjK,EAAiB1M,UAAY,GAC7BV,EAAQoN,EAAkBmJ,GAGtBzmB,EAAKmL,SAASwI,YJr7CfsU,EIs7Ce3K,EJt7CDiL,iBAAiB,kBACnCtnB,MAAM+jB,UAAUnmB,QAAQ6P,KAAKuZ,GAAU,SAASpY,GAC/C,IAAIM,EAASN,EAAG8C;AAChBxC,EAAOuD,aAAa7D,EAAGiB,WAAoBjB,GAC3CM,EAAO3K,WACR,IIk7CMuhB,EAAQzb,MAAM5L,QAAUqnB,EAAQtb,OAAO/L,QAC1CmL,EAASkc,EAAQtb,QAAS+c,IACzB7U,GAAW2J,EAAkBkL,EAAIje,MAAM;AAM1C,IAAIke,EAAgBC,IACnB,IAAI7X,EAAU7Q,EAAK6X,OAAO6Q,EAAS,CAAC3mB,MAAMuJ;AAK1C,OAJIuF,IACHoW,GAAgB,EAChB3J,EAAiBqL,aAAa9X,EAASyM,EAAiBxM,aAElDD,CAAO;AA6Bf,GAxBI7Q,EAAKsO,QACRma,EAAa,WAGHzoB,EAAKmL,SAASyM,WAAWlJ,KAAK1O,EAAKsL,GAIX,IAAzByb,EAAQ7b,MAAMxL,QACvB+oB,EAAa,cAJbA,EAAa,gBAWd/B,EAAoB1mB,EAAK4oB,UAAUtd,MAElC+J,EAASoT,EAAa,kBAKvBzoB,EAAK+b,WAAagL,EAAQ7b,MAAMxL,OAAS,GAAKgnB,EAC1CO,EAAe,CAElB,GAAIF,EAAQ7b,MAAMxL,OAAS,GAM1B,GAJKsnB,GAAwC,WAAvBhnB,EAAKmL,SAAS6L,MAAsCrX,MAAjBK,EAAKkL,MAAM,KACnE8b,EAAgBhnB,EAAK4nB,UAAU5nB,EAAKkL,MAAM,MAGtCoS,EAAiB7K,SAASuU,GAAiB,CAE/C,IAAI6B,EAAe;AACfxT,IAAWrV,EAAKmL,SAAS2K,gBAC5B+S,EAAe,GAEhB7B,EAAgBhnB,EAAK8oB,aAAaD,EACnC,OAESxT,IACT2R,EAAgB3R;AAGbmR,IAAoBxmB,EAAKob,SAC5Bpb,EAAKkjB,OACLljB,EAAKslB,eAAe0B,EAAc,SAEnChnB,EAAKqjB,gBAAgB2D,EAEtB,MACChnB,EAAKskB,oBACDkC,GAAmBxmB,EAAKob,QAC3Bpb,EAAKogB,OAAM,EAGd,CAMA0I,UAAAA,GACC,OAAO7pB,KAAKqe,iBAAiBiL,iBAAiB,oBAC/C,CAeArP,SAAAA,CAAW1M,EAAgBuc,GAAe,GACzC,MAAM/oB,EAAOf;AAIb,GAAIgC,MAAM2J,QAAQ4B,GAEjB,OADAxM,EAAK2gB,WAAYnU,EAAMuc,IAChB;AAGR,MAAMpe,EAAMoD,EAASvB,EAAKxM,EAAKmL,SAASoL;AACxC,OAAY,OAAR5L,IAAgB3K,EAAKgM,QAAQjB,eAAeJ,KAIhD6B,EAAKsM,OAAWtM,EAAKsM,UAAY9Y,EAAKmb,MACtC3O,EAAKub,IAAQ/nB,EAAK6c,QAAU,QAAUrQ,EAAKsM,OAC3C9Y,EAAKgM,QAAQrB,GAAO6B,EACpBxM,EAAKoiB,UAAa,KAEd2G,IACH/oB,EAAKoc,YAAYzR,GAAOoe,EACxB/oB,EAAKF,QAAQ,aAAc6K,EAAK6B,IAG1B7B,EACR,CAMAgW,UAAAA,CAAYnU,EAAkBuc,GAAe,GAC5Cle,EAAS2B,GAAOwc,IACf/pB,KAAKia,UAAU8P,EAAKD,EAAa,GAEnC,CAKAE,cAAAA,CAAgBzc,GACf,OAAOvN,KAAKia,UAAU1M,EACvB,CAOAoU,mBAAAA,CAAoBpU,GACnB,IAAI7B,EAAMoD,EAASvB,EAAKvN,KAAKkM,SAASwL;AAEtC,OAAa,OAARhM,IAEL6B,EAAKsM,OAAStM,EAAKsM,UAAY7Z,KAAKkc,MACpClc,KAAK+V,UAAUrK,GAAO6B,EACf7B,EACR,CAOAue,cAAAA,CAAehc,EAAWV,GACzB,IAAI2c;AACJ3c,EAAKvN,KAAKkM,SAASwL,oBAAsBzJ,GAErCic,EAAYlqB,KAAK2hB,oBAAoBpU,KACxCvN,KAAKa,QAAQ,eAAgBqpB,EAAW3c,EAE1C,CAMA4c,iBAAAA,CAAkBlc,GACbjO,KAAK+V,UAAUjK,eAAemC,YAC1BjO,KAAK+V,UAAU9H,GACtBjO,KAAKoqB,aACLpqB,KAAKa,QAAQ,kBAAmBoN,GAElC,CAKAoc,iBAAAA,GACCrqB,KAAK+V,UAAY,CAAE,EACnB/V,KAAKoqB,aACLpqB,KAAKa,QAAQ,iBACd,CAQAypB,YAAAA,CAAa7nB,EAAc8K,GAC1B,MAAMxM,EAAOf;AACb,IAAIuqB,EACAC;AAEJ,MAAMC,EAAa3b,EAASrM,GACtBioB,EAAa5b,EAASvB,EAAKxM,EAAKmL,SAASoL;AAG/C,GAAkB,OAAdmT,EAAqB;AAEzB,MAAME,EAAY5pB,EAAKgM,QAAQ0d;AAE/B,GAAgB/pB,MAAZiqB,EAAwB;AAC5B,GAAyB,iBAAdD,EAAyB,MAAM,IAAI7O,MAAM;AAGpD,MAAMtC,EAAUxY,EAAK4nB,UAAU8B,GACzB/b,EAAS3N,EAAK6pB,QAAQH;AAa5B,GAVAld,EAAKsM,OAAStM,EAAKsM,QAAU8Q,EAAS9Q,cAC/B9Y,EAAKgM,QAAQ0d,GAIpB1pB,EAAK8pB,aAAaH,GAElB3pB,EAAKgM,QAAQ2d,GAAand,EAGtBgM,EAAQ,CACX,GAAIxY,EAAKsd,iBAAiB7K,SAAS+F,GAAS,CAE3C,MAAMuR,EAAa/pB,EAAKqd,QAAQ,SAAU7Q;AAC1C+G,GAAYiF,EAAQuR,GAEhB/pB,EAAKkc,eAAiB1D,GACzBxY,EAAKqjB,gBAAgB0G,EAEvB,CACAvR,EAAOrG,QACR,CAGIxE,KAEiB,KADpB8b,EAAazpB,EAAKkL,MAAMrL,QAAQ6pB,KAE/B1pB,EAAKkL,MAAMtL,OAAO6pB,EAAY,EAAGE,GAGlCH,EAAWxpB,EAAKqd,QAAQ,OAAQ7Q,GAE5BmB,EAAKsE,UAAUQ,SAAS,WAAYf,EAAW8X,EAAS,UAE5DjW,GAAa5F,EAAM6b,IAIpBxpB,EAAKoiB,UAAY,IAClB,CAMA4H,YAAAA,CAAatoB,EAAc+iB,GAC1B,MAAMzkB,EAAOf;AACbyC,EAAQsM,EAAStM,GAEjB1B,EAAK8pB,aAAapoB,UAEX1B,EAAKoc,YAAY1a,UACjB1B,EAAKgM,QAAQtK,GACpB1B,EAAKoiB,UAAY,KACjBpiB,EAAKF,QAAQ,gBAAiB4B,GAC9B1B,EAAKiqB,WAAWvoB,EAAO+iB,EACxB,CAKAyF,YAAAA,CAAa9pB,GAEZ,MAAM+pB,GAAe/pB,GAAUnB,KAAKmrB,aAAapd,KAAK/N;AAEtDA,KAAKwP,eAAkB,CAAE,EACzBxP,KAAKmd,YAAe,CAAE,EACtBnd,KAAKoqB;AAEL,MAAM7P,EAAsB,CAAE;AAC9B3O,EAAQ5L,KAAK+M,SAAQ,CAACwM,EAAiB7N,KAClCwf,EAAY3R,EAAO7N,KACtB6O,EAAS7O,GAAO6N,EACjB,IAGDvZ,KAAK+M,QAAU/M,KAAK+d,OAAO9R,MAAQsO,EACnCva,KAAKmjB,UAAY,KACjBnjB,KAAKa,QAAQ,eACd,CAOAsqB,WAAAA,CAAY5R,EAAiB9W,GAC5B,OAAIzC,KAAKiM,MAAMrL,QAAQ6B,IAAU,CAIlC,CAOAkmB,SAAAA,CAAUlmB,EAA4C2T,GAAe,GAEpE,MAAMkR,EAASxY,EAASrM;AACxB,GAAe,OAAX6kB,EAAkB,OAAO;AAE7B,MAAM/N,EAASvZ,KAAK+M,QAAQua;AAC5B,GAAc5mB,MAAV6Y,EAAqB,CAExB,GAAIA,EAAO6R,KACV,OAAO7R,EAAO6R;AAGf,GAAIhV,EACH,OAAOpW,KAAKoe,QAAQ,SAAU7E,EAEhC,CAEA,OAAO,IACR,CAOA4K,WAAAA,CAAa5K,EAAyBpL,EAAkByB,EAAc,UACrE,IAAiBlH;AAEjB,IAAK6Q,EACJ,OAAO;AAIP7Q,EADW,QAARkH,EANO5P,KAOG+mB,kBAPH/mB,KASGqe,iBAAiBiL,iBAAiB;AAGhD,IAAK,IAAIxnB,EAAI,EAAGA,EAAI4G,EAAIjI,OAAQqB,IAC/B,GAAI4G,EAAI5G,IAAMyX,EAId,OAAIpL,EAAY,EACRzF,EAAI5G,EAAE,GAGP4G,EAAI5G,EAAE;AAEd,OAAO,IACR,CAQA8oB,OAAAA,CAAQlc,GAEP,GAAmB,iBAARA,EACV,OAAOA;AAGR,IAAIjM,EAAQqM,EAASJ;AACrB,OAAiB,OAAVjM,EACJzC,KAAKke,QAAQpM,cAAc,gBAAgBd,EAAWvO,QACtD,IACJ,CAOAgjB,QAAAA,CAAUzK,EAAwBwK,GACjC,IAAIzkB,EAAOf,KAEPiM,EAAQjK,MAAM2J,QAAQqP,GAAUA,EAAS,CAACA;AAE9C,MAAMqQ,GADNpf,EAAQA,EAAM9K,QAAOmqB,IAAgC,IAA3BvqB,EAAKkL,MAAMrL,QAAQ0qB,MACrBrf,EAAMxL,OAAS;AACvCwL,EAAMrM,SAAQ8O,IACb3N,EAAKwqB,UAAa7c,IAAS2c,EAC3BtqB,EAAK2iB,QAAQhV,EAAM8W,EAAO,GAE5B,CAOA9B,OAAAA,CAASjhB,EAAc+iB,GAGtB9V,EAAgB1P,KAFHwlB,EAAS,GAAK,CAAC,SAAS,mBAEP,KAC7B,IAAI9W,EAAM8c;AACV,MAAMzqB,EAAOf,KACNse,EAAYvd,EAAKmL,SAAS6L,KAC3BuP,EAASxY,EAASrM;AAExB,KAAI6kB,IAA0C,IAAhCvmB,EAAKkL,MAAMrL,QAAQ0mB,KAEd,WAAdhJ,GACHvd,EAAKogB,QAGY,WAAd7C,GAA2Bvd,EAAKmL,SAAS0K,cAK/B,OAAX0Q,GAAoBvmB,EAAKgM,QAAQjB,eAAewb,KAClC,WAAdhJ,GAAwBvd,EAAKyhB,MAAMgD,GACrB,UAAdlH,IAAyBvd,EAAK0qB,UAAlC,CAYA,GAVA/c,EAAO3N,EAAKqd,QAAQ,OAAQrd,EAAKgM,QAAQua,IAErCvmB,EAAKmd,QAAQ1K,SAAS9E,KACzBA,EAAOA,EAAK0G,WAAU,IAGvBoW,EAAUzqB,EAAK0qB,SACf1qB,EAAKkL,MAAMtL,OAAOI,EAAKic,SAAU,EAAGsK,GACpCvmB,EAAK2qB,cAAchd,GAEf3N,EAAK4b,QAAS,CAGjB,IAAK5b,EAAKwqB,WAAaxqB,EAAKmL,SAASyK,aAAc,CAClD,IAAI4C,EAASxY,EAAK4nB,UAAUrB,GACxBpD,EAAOnjB,EAAKojB,YAAY5K,EAAQ;AAChC2K,GACHnjB,EAAKqjB,gBAAgBF,EAEvB,CAIKnjB,EAAKwqB,WAAcxqB,EAAKmL,SAASgZ,kBACrCnkB,EAAK4jB,eAAe5jB,EAAK0b,WAA2B,WAAd6B,GAID,GAAlCvd,EAAKmL,SAASgZ,kBAA6BnkB,EAAK0qB,SACnD1qB,EAAKogB,QACMpgB,EAAKwqB,WAChBxqB,EAAK2f,mBAGN3f,EAAKF,QAAQ,WAAYymB,EAAQ5Y,GAE5B3N,EAAKwqB,WACTxqB,EAAKkgB,oBAAoB,CAACuE,OAAQA,GAEpC,GAEKzkB,EAAKwqB,YAAeC,GAAWzqB,EAAK0qB,YACxC1qB,EAAKyf,aACLzf,EAAKigB,eA7CsC,CA8C5C,GAGF,CAOAgK,UAAAA,CAAYtc,EAAyB,KAAM8W,GAC1C,MAAMzkB,EAAQf;AAGd,KAFA0O,EAAS3N,EAAK6pB,QAAQlc,IAEV;AAEZ,IAAI5M,EAAEqkB;AACN,MAAM1jB,EAAQiM,EAAKqL,QAAQtX;AAC3BX,EAAI+R,GAAUnF,GAEdA,EAAKwE,SACDxE,EAAKsE,UAAUQ,SAAS,YAC3B2S,EAAMplB,EAAKmc,YAAYtc,QAAQ8N,GAC/B3N,EAAKmc,YAAYvc,OAAOwlB,EAAK,GAC7BlT,EAAcvE,EAAK,WAGpB3N,EAAKkL,MAAMtL,OAAOmB,EAAG,GACrBf,EAAKoiB,UAAY,MACZpiB,EAAKmL,SAASiK,SAAWpV,EAAKoc,YAAYrR,eAAerJ,IAC7D1B,EAAKgqB,aAAatoB,EAAO+iB,GAGtB1jB,EAAIf,EAAKic,UACZjc,EAAKikB,SAASjkB,EAAKic,SAAW,GAG/Bjc,EAAKkgB,oBAAoB,CAACuE,OAAQA,IAClCzkB,EAAKigB,eACLjgB,EAAK2f,mBACL3f,EAAKF,QAAQ,cAAe4B,EAAOiM,EAEpC,CAWAiV,UAAAA,CAAY7gB,EAAkB,KAAMpD,EAA6BA,QAGvC,IAArBc,UAAUC,SACbf,EAAWc,UAAU,IAEC,mBAAZd,IACVA,EAAWA;AAGZ,IAEIisB,EAFA5qB,EAAQf,KACR4rB,EAAQ7qB,EAAKic;AAIjB,GAFAla,EAAQA,GAAS/B,EAAKwiB,cAEjBxiB,EAAK4oB,UAAU7mB,GAEnB,OADApD,KACO;AAGRqB,EAAK8qB;AAEL,IAAIC,GAAU,EACV1V,EAAU7I,IAGb,GAFAxM,EAAKgrB,UAEAxe,GAAwB,iBAATA,EAAmB,OAAO7N;AAC9C,IAAI+C,EAAQqM,EAASvB,EAAKxM,EAAKmL,SAASoL;AACxC,GAAqB,iBAAV7U,EACV,OAAO/C;AAGRqB,EAAKukB,kBACLvkB,EAAKkZ,UAAU1M,GAAK,GACpBxM,EAAKikB,SAAS4G,GACd7qB,EAAK2iB,QAAQjhB,GACb/C,EAAS6N,GACTue,GAAU,CAAI;AAgBf,OAZCH,EADmC,mBAAzB5qB,EAAKmL,SAASkK,OACfrV,EAAKmL,SAASkK,OAAO3G,KAAKzP,KAAM8C,EAAOsT,GAEvC,CACR,CAACrV,EAAKmL,SAASqL,YAAazU,EAC5B,CAAC/B,EAAKmL,SAASoL,YAAaxU,GAIzBgpB,GACJ1V,EAAOuV,IAGD,CACR,CAKAzK,YAAAA,GACC,IAAIngB,EAAOf;AACXe,EAAKoiB,UAAY,KAEbpiB,EAAK4b,SACR5b,EAAK0kB,SAAS1kB,EAAKkL,OAGpBlL,EAAKkgB,sBACLlgB,EAAKigB,cACN,CAMAA,YAAAA,GACC,MAAMjgB,EAAWf;AAEjBe,EAAKirB;AAEL,MAAMP,EAAS1qB,EAAK0qB,SACdjP,EAAWzb,EAAKyb;AAEtBzb,EAAKwS,QAAQP,UAAUgU,OAAO,MAAMjmB,EAAK4c;AAGzC,MAAMsO,EAAiBlrB,EAAKwS,QAAQP;ALv8DRpI;AKy8D5BqhB,EAAejF,OAAO,QAASjmB,EAAK0b,WACpCwP,EAAejF,OAAO,WAAYjmB,EAAKqb,YACvC6P,EAAejF,OAAO,WAAYjmB,EAAKsb,YACvC4P,EAAejF,OAAO,WAAYjmB,EAAK8c,YACvCoO,EAAejF,OAAO,WAAYjmB,EAAKwb,SACvC0P,EAAejF,OAAO,SAAUxK,GAChCyP,EAAejF,OAAO,OAAQyE,GAC9BQ,EAAejF,OAAO,eAAgBjmB,EAAK0b,YAAc1b,EAAK2b,eAC9DuP,EAAejF,OAAO,kBAAmBjmB,EAAKob,QAC9C8P,EAAejF,OAAO,eLl9DMpc,EKk9DuB7J,EAAKgM,QLj9DrB,IAA5B1G,OAAOC,KAAKsE,GAAKnK,SKk9DxBwrB,EAAejF,OAAO,YAAajmB,EAAKkL,MAAMxL,OAAS,EAExD,CAWAurB,oBAAAA,GACC,IAAIjrB,EAAOf;AAENe,EAAK+B,MAAMopB,WAIhBnrB,EAAKwb,QAAUxb,EAAK+B,MAAMopB,SAASC,MACnCprB,EAAKub,WAAavb,EAAKwb,QACxB,CAQAkP,MAAAA,GACC,OAAkC,OAA3BzrB,KAAKkM,SAASwK,UAAqB1W,KAAKiM,MAAMxL,QAAUT,KAAKkM,SAASwK,QAC9E,CAOAuK,mBAAAA,CAAqBmL,EAAoB,IACxC,MAAMrrB,EAAOf;AACb,IAAIuZ,EAAQyF;AAEZ,MAAMqN,EAAetrB,EAAK+B,MAAMgP,cAAc;AAE9C,GAAI/Q,EAAK2c,cAAe,CAEvB,MAAMnD,EAAgC,GAChC+R,EAAyBvrB,EAAK+B,MAAMwmB,iBAAiB,kBAAkB7oB;AAE7E,SAAS8rB,EAAY7D,EAAkCjmB,EAAcuc,GAoBpE,OAlBK0J,IACJA,EAAYtX,EAAO,kBAAoBpC,EAAYvM,GAAS,KAAOuM,EAAYgQ,GAAS,cAKrF0J,GAAa2D,GAChBtrB,EAAK+B,MAAMmO,OAAOyX,GAGnBnO,EAASla,KAAKqoB,IAIVA,GAAa2D,GAAgBC,EAAe,KAC/C5D,EAAUnO,UAAW,GAGfmO,CACR,CAGA3nB,EAAK+B,MAAMwmB,iBAAiB,kBAAkB1pB,SAAS8oB,IAClCA,EAAWnO,UAAW,CAAK,IAKvB,GAArBxZ,EAAKkL,MAAMxL,QAAqC,UAAtBM,EAAKmL,SAAS6L,KAE3CwU,EAAYF,EAAc,GAAI,IAK9BtrB,EAAKkL,MAAMrM,SAAS6C,IAInB,GAHA8W,EAAWxY,EAAKgM,QAAQtK,GACxBuc,EAAUzF,EAAOxY,EAAKmL,SAASqL,aAAe,GAE1CgD,EAASqO,SAASrP,EAAOe,SAAU,CAEtCiS,EADkBxrB,EAAK+B,MAAMgP,cAAc,iBAAiBd,EAAWvO,sBAChDA,EAAOuc,EAC/B,MACCzF,EAAOe,QAAUiS,EAAYhT,EAAOe,QAAS7X,EAAOuc,EACrD,GAKH,MACCje,EAAK+B,MAAML,MAAQ1B,EAAKwkB;AAGrBxkB,EAAK4b,UACHyP,EAAK5G,QACTzkB,EAAKF,QAAQ,SAAUE,EAAKwkB,YAG/B,CAMAtB,IAAAA,GACC,IAAIljB,EAAOf;AAEPe,EAAKyb,UAAYzb,EAAKob,QAAkC,UAAvBpb,EAAKmL,SAAS6L,MAAoBhX,EAAK0qB,WAC5E1qB,EAAKob,QAAS,EACdlI,GAAQlT,EAAKyd,WAAW,CAAC,gBAAiB,SAC1Czd,EAAKigB,eACL1O,EAASvR,EAAKod,SAAS,CAACqO,WAAY,SAAUC,QAAS,UACvD1rB,EAAK2f,mBACLpO,EAASvR,EAAKod,SAAS,CAACqO,WAAY,UAAWC,QAAS,UACxD1rB,EAAKme,QACLne,EAAKF,QAAQ,gBAAiBE,EAAKod,UACpC,CAKAgD,KAAAA,CAAMmE,GAAgB,GACrB,IAAIvkB,EAAOf,KACPa,EAAUE,EAAKob;AAEfmJ,IAGHvkB,EAAKukB,kBAEsB,WAAvBvkB,EAAKmL,SAAS6L,MAAqBhX,EAAKkL,MAAMxL,QACjDM,EAAKyf,cAIPzf,EAAKob,QAAS,EACdlI,GAAQlT,EAAKyd,WAAW,CAAC,gBAAiB,UAC1ClM,EAASvR,EAAKod,SAAS,CAACsO,QAAS,SAC7B1rB,EAAKmL,SAASyK,cACjB5V,EAAKskB,oBAENtkB,EAAKigB,eAEDngB,GAASE,EAAKF,QAAQ,iBAAkBE,EAAKod,SAClD,CAOAuC,gBAAAA,GAEC,GAAqC,SAAjC1gB,KAAKkM,SAASoM,eAAlB,CAIA,IAAIoU,EAAY1sB,KAAKke,QACjByO,EAASD,EAAQ9F,wBACjBC,EAAS6F,EAAQ/F,aAAegG,EAAK9F,IAAOvJ,OAAOsP,QACnDC,EAASF,EAAKE,KAAOvP,OAAOwP;AAGhCxa,EAAStS,KAAKme,SAAS,CACtBkB,MAAQsN,EAAKtN,MAAQ,KACrBwH,IAAQA,EAAM,KACdgG,KAAQA,EAAO,MAXhB,CAcD,CAOArK,KAAAA,CAAMgD,GACL,IAAIzkB,EAAOf;AAEX,GAAKe,EAAKkL,MAAMxL,OAAhB,CAEA,IAAIwL,EAAQlL,EAAKgmB;AACjBnb,EAAQK,GAAOyC,IACd3N,EAAKiqB,WAAWtc,GAAK,EAAK,IAG3B3N,EAAKyf,aACAgF,GAASzkB,EAAKkgB,sBACnBlgB,EAAKF,QAAQ,QATW,CAUzB,CAOA6qB,aAAAA,CAAc9a,GACb,MAAM7P,EAAQf,KACR4rB,EAAS7qB,EAAKic,SACd7M,EAASpP,EAAKmd;AAEpB/N,EAAOuZ,aAAa9Y,EAAIT,EAAOsK,SAASmR,IAAU,MAClD7qB,EAAKikB,SAAS4G,EAAQ,EACvB,CAMAnH,eAAAA,CAAgBngB,GACf,IAAI6J,EAAW4e,EAAWnB,EAAOoB,EN9tENlqB,EM+tEvB/B,EAAOf;AAEXmO,EAAa7J,GHj1Ee,IGi1EVA,EAAEuf,SAAwC,EAAI,EAChEkJ,ENjuEM,CACN/pB,OAF2BF,EMkuEF/B,EAAKwd,eNhuEhB0O,gBAAkB,EAChCxsB,QAAUqC,EAAMoqB,cAAc,IAAMpqB,EAAMmqB,gBAAgB;AMmuE1D,MAAME,EAAqB;AAE3B,GAAIpsB,EAAKmc,YAAYzc,OAEpBusB,EAAOrZ,GAAQ5S,EAAKmc,YAAa/O,GACjCyd,EAAQ/X,GAAUmZ,GAEd7e,EAAY,GAAKyd,IAErBhgB,EAAQ7K,EAAKmc,aAAcxO,GAAiBye,EAAS9sB,KAAKqO;KAEpD,IAAK3N,EAAK0b,WAAoC,WAAvB1b,EAAKmL,SAAS6L,OAAsBhX,EAAKkL,MAAMxL,OAAQ,CACpF,MAAMwL,EAAQlL,EAAKgmB;AACnB,IAAIqG;AACAjf,EAAY,GAAyB,IAApB4e,EAAU/pB,OAAoC,IAArB+pB,EAAUtsB,OACvD2sB,EAAUnhB,EAAMlL,EAAKic,SAAW,GAEvB7O,EAAY,GAAK4e,EAAU/pB,QAAUjC,EAAKwiB,aAAa9iB,SAChE2sB,EAAUnhB,EAAMlL,EAAKic,gBAGNtc,IAAZ0sB,GACHD,EAAS9sB,KAAM+sB,EAEjB,CAEA,IAAKrsB,EAAKssB,aAAaF,EAAS7oB,GAC/B,OAAO;AAUR,IAPAwL,EAAexL,GAAE,QAGI,IAAVsnB,GACV7qB,EAAKikB,SAAS4G,GAGRuB,EAAS1sB,QACfM,EAAKiqB,WAAWmC,EAASljB;AAO1B,OAJAlJ,EAAKyf,aACLzf,EAAK2f,mBACL3f,EAAK4jB,gBAAe,IAEb,CACR,CAKA0I,YAAAA,CAAaphB,EAAgB8D,GAE5B,MAAMiL,EAAS/O,EAAM9D,KAAIuG,GAAQA,EAAKqL,QAAQtX;AAG9C,SAAKuY,EAAOva,QAA6C,mBAA3BT,KAAKkM,SAASohB,WAAkE,IAAvCttB,KAAKkM,SAASohB,SAAStS,EAAOjL,GAKtG,CASAyU,gBAAAA,CAAiBrW,EAAkB7J,GAClC,IAAI4hB,EAAaqH,EAAUxsB,EAAOf;AAE9Be,EAAK4c,MAAKxP,IAAc,GACxBpN,EAAKwiB,aAAa9iB,SAIlB4P,EAAU0T,GAAuBzf,IAAM+L,EAAU,WAAW/L,IAQ7DipB,GANFrH,EAAgBnlB,EAAK+kB,cAAc3X,IAG7B+X,EAAYlT,UAAUQ,SAAS,UAGtBzS,EAAKojB,YAAY+B,EAAY/X,EAAU,QAFvC+X,EAML/X,EAAY,EACRpN,EAAKwd,cAAciP,mBAEnBzsB,EAAKwd,cAAcvK,0BAK5BuZ,EAASva,UAAUQ,SAAS,WAC/BzS,EAAKklB,iBAAiBC,GAEvBnlB,EAAKilB,mBAAmBuH,IAKzBxsB,EAAK0sB,UAAUtf,GAEjB,CAEAsf,SAAAA,CAAUtf,GAAiB,CAM3B2X,aAAAA,CAAc3X,GAEb,IAAI+X,EAAclmB,KAAKke,QAAQpM,cAAc;AAC7C,GAAIoU,EACH,OAAOA;AAIR,IAAInjB,EAAS/C,KAAKke,QAAQoL,iBAAiB;AAC3C,OAAIvmB,EACI4Q,GAAQ5Q,EAAOoL,QADvB,CAGD,CAWA6W,QAAAA,CAAS0I,GACR1tB,KAAKgd,SAAWhd,KAAKiM,MAAMxL,MAC5B,CAMAsmB,eAAAA,GACC,OAAO/kB,MAAMC,KAAMjC,KAAKke,QAAQoL,iBAAiB,kBAClD,CAMAuC,IAAAA,GACC7rB,KAAK2tB,WAAU,EAChB,CAKA5B,MAAAA,GACC/rB,KAAK2tB,WAAU,EAChB,CAKAA,SAAAA,CAAW9B,EAAe7rB,KAAKqc,YAAcrc,KAAKoc,YACjDpc,KAAKwc,SAAWqP,EAChB7rB,KAAKghB,cACN,CAMAI,OAAAA,GACCphB,KAAK4tB,aAAY,GACjB5tB,KAAKmhB,OACN,CAMAI,MAAAA,GACCvhB,KAAK4tB,aAAY,EAClB,CAEAA,WAAAA,CAAYvT,GACXra,KAAKwe,WAAWf,SAAYpD,GAAY,EAAIra,KAAKyd,SACjDzd,KAAKoc,WAAiB/B,EACtBra,KAAK8C,MAAMuX,SAAcA,EACzBra,KAAKue,cAAclE,SAAYA,EAC/Bra,KAAK2tB,WACN,CAEArM,WAAAA,CAAYjF,GACXrc,KAAKqc,WAAiBA,EACtBrc,KAAK8C,MAAMue,SAAchF,EACzBrc,KAAKue,cAAc8C,SAAYhF,EAC/Brc,KAAK2tB,WACN,CAOAE,OAAAA,GACC,IAAI9sB,EAAOf,KACP6gB,EAAiB9f,EAAK8f;AAE1B9f,EAAKF,QAAQ,WACbE,EAAKT,MACLS,EAAKwS,QAAQL,SACbnS,EAAKod,SAASjL,SAEdnS,EAAK+B,MAAM6O,UAAYkP,EAAelP,UACtC5Q,EAAK+B,MAAM2a,SAAWoD,EAAepD,SAErCxK,EAAclS,EAAK+B,MAAM,cAAc,wBAEvC/B,EAAK4f,kBAEE5f,EAAK+B,MAAMua,SACnB,CAOAzE,MAAAA,CAAQkV,EAA+BvgB,GACtC,IAAIU,EAAIuZ;AACR,MAAMzmB,EAAOf;AAEb,GAAkD,mBAAvCA,KAAKkM,SAAS0M,OAAOkV,GAC/B,OAAO;AAMR,KAFAtG,EAAOzmB,EAAKmL,SAAS0M,OAAOkV,GAAcre,KAAKzP,KAAMuN,EAAMyB,IAG1D,OAAO;AAsBR,GAnBAwY,EAAOpW,EAAQoW,GAGM,WAAjBsG,GAA8C,kBAAjBA,EAE5BvgB,EAAKxM,EAAKmL,SAASsL,eACtBvD,GAAQuT,EAAK,CAAC,gBAAgB,SAE9BvT,GAAQuT,EAAK,CAAC,kBAAmB,KAGR,aAAjBsG,IACT7f,EAAKV,EAAK2M,MAAMnZ,EAAKmL,SAASwL,oBAC9BzD,GAAQuT,EAAK,CAAC,aAAcvZ,IACzBV,EAAK2M,MAAMnZ,EAAKmL,SAASsL,gBAC3BvD,GAAQuT,EAAK,CAAC,gBAAiB,MAIZ,WAAjBsG,GAA8C,SAAjBA,EAAyB,CACzD,MAAMrrB,EAAQsM,EAASxB,EAAKxM,EAAKmL,SAASoL;AAC1CrD,GAAQuT,EAAK,CAAC,aAAc/kB,IAIP,SAAjBqrB,GACHrb,EAAW+U,EAAKzmB,EAAKmL,SAASkM,WAC9BnE,GAAQuT,EAAK,CAAC,eAAe,OAE7B/U,EAAW+U,EAAKzmB,EAAKmL,SAASmM,aAC9BpE,GAAQuT,EAAK,CACZ3I,KAAK,SACL5Q,GAAGV,EAAKub,MAITvb,EAAK6d,KAAO5D,EACZzmB,EAAKgM,QAAQtK,GAAS8K,EAIxB,CAEA,OAAOia,CAER,CAOApJ,OAAAA,CAAS0P,EAA+BvgB,GACvC,MAAMia,EAAOxnB,KAAK4Y,OAAOkV,EAAcvgB;AAEvC,GAAY,MAARia,EACH,KAAM;AAEP,OAAOA,CACR,CASA4C,UAAAA,GAECxe,EAAQ5L,KAAK+M,SAAUwM,IAClBA,EAAO6R,OACV7R,EAAO6R,KAAKlY,gBACLqG,EAAO6R,KACf,GAGF,CAMAP,YAAAA,CAAapoB,GAEZ,MAAMimB,EAAc1oB,KAAK2oB,UAAUlmB;AAC/BimB,GAAYA,EAAUxV,QAE3B,CAOAyW,SAAAA,CAAW7mB,GACV,OAAO9C,KAAKkM,SAASkK,QAAWtT,EAAMrC,OAAS,GAAOT,KAAKkM,SAASoK,aAAkC7G,KAAKzP,KAAM8C,EAClH,CAUAirB,IAAAA,CAAMC,EAAaC,EAAeC,GACjC,IAAIntB,EAAOf,KACPmuB,EAAcptB,EAAKktB;AAGvBltB,EAAKktB,GAAU,WACd,IAAIlrB,EAAQqrB;AAQZ,MANa,UAATJ,IACHjrB,EAASorB,EAAYntB,MAAMD,EAAMP,YAGlC4tB,EAAaF,EAAOltB,MAAMD,EAAMP,WAEnB,YAATwtB,EACII,GAGK,WAATJ,IACHjrB,EAASorB,EAAYntB,MAAMD,EAAMP,YAG3BuC,EACP,CAEF;", "x_google_ignoreList": [1, 2, 3, 4, 5]}