DROP TABLE IF EXISTS public.news;

CREATE TABLE public.news (
    id serial primary key,
    type text NOT NULL DEFAULT 'news', -- news, announcement
    title text, -- устаревшее, для совместимости
    title_ru text,
    title_uz text,
    content text, -- устаревшее, для совместимости
    content_ru text,
    content_uz text,
    status text DEFAULT 'draft', -- draft, published, archived
    created_at bigint,
    published_at bigint,
    author_id integer,
    cover_image text
);

-- Тарифные планы
DROP TABLE IF EXISTS public.tariffs;

CREATE TABLE public.tariffs (
    id serial primary key,
    name text NOT NULL,
    name_uz text,
    name_ru text,
    description text,
    description_uz text,
    description_ru text,
    price_rub double precision DEFAULT 0,
    price_uzs double precision DEFAULT 0,
    price_usd double precision DEFAULT 0,
    user_limit integer DEFAULT 0,
    is_default boolean DEFAULT false,
    
    created_at bigint,
    updated_at bigint
);

-- Добавление тарифных планов
INSERT INTO public.tariffs (name, name_uz, name_ru, description, description_uz, description_ru, price_rub, price_uzs, price_usd, user_limit, is_default, created_at, updated_at)
VALUES
  ('Базовый', 'Asosiy', 'Базовый', 'Базовый бесплатный тариф', 'Bepul asosiy tarif', 'Базовый бесплатный тариф', 0, 0, 0, 100, true, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),
  ('Премиум', 'Premium', 'Премиум', 'Премиум тариф для расширенных возможностей', 'Kengaytirilgan imkoniyatlar uchun premium tarif', 'Премиум тариф для расширенных возможностей', 500, 75000, 5.5, 50, false, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW())),
  ('Корпоративный', 'Korporativ', 'Корпоративный', 'Тариф для организаций и команд', 'Tashkilot va jamoalar uchun tarif', 'Тариф для организаций и команд', 2000, 300000, 22, 500, false, EXTRACT(EPOCH FROM NOW()), EXTRACT(EPOCH FROM NOW()));

-- Добавить тариф к пользователю
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS tariff_id integer REFERENCES public.tariffs(id); 
ALTER TABLE public.users ADD COLUMN IF NOT EXISTS is_verified boolean DEFAULT false,