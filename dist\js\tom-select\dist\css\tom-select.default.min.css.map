{"version": 3, "sources": ["tom-select.default.css"], "names": [], "mappings": "AAcA,YACE,wBAAyB,CAQzB,iBAAkB,CADlB,yCAA8C,CAD9C,qBAAsB,CAGtB,YAAa,CACb,cAAe,CAPf,eAAgB,CAFhB,WAAgB,CAGhB,iBAAkB,CAFlB,UAAW,CAGX,SAMF,CACA,wCACE,mBACF,CACA,kBACE,qBACF,CACA,8CACE,wBACF,CACA,mBACE,0CACF,CACA,cAEE,oBAAqB,CADrB,sBAEF,CACA,kCAIE,kBAAmB,CAEnB,wBAAyB,CADzB,UAAW,CAJX,cAAe,CACf,kBAAmB,CACnB,eAIF,CACA,yCACE,kBAAmB,CAEnB,wBAAyB,CADzB,UAEF,CACA,6FAEE,kBAAmB,CACnB,qBAAyB,CAFzB,UAGF,CACA,kBAWE,yBAA2B,CAD3B,kBAAyB,CAIzB,yBAA2B,CAX3B,8BAAgC,CAFhC,aAAc,CAWd,6BAA+B,CAJ/B,kBAAoB,CAFpB,yBAA2B,CAC3B,wBAA0B,CAF1B,sBAAwB,CAHxB,cAAe,CAEf,mBAAqB,CAKrB,uBAAyB,CAIzB,kCAA4B,CAA5B,+BAA4B,CAA5B,8BAA4B,CAA5B,0BAEF,CACA,6BACE,YACF,CACA,wBACE,sBACF,CACA,6BACE,sBACF,CACA,gBACE,gBACF,CACA,yCACE,SAAU,CACV,UACF,CACA,kCACE,6BACF,CACA,sBAEE,wBAAyB,CADzB,UAEF,CACA,gCAGE,aAAc,CAFd,SAAU,CACV,iBAEF,CAEA,aAOE,eAAgB,CAEhB,wBAAkB,CAGlB,yBAA0B,CAH1B,YAAkB,CAElB,mCAAwC,CADxC,qBAAsB,CAPtB,MAAO,CAKP,iBAAmB,CAPnB,iBAAkB,CAClB,QAAS,CAET,UAAW,CACX,UAQF,CACA,+BACE,cAAe,CACf,eACF,CACA,0CACE,+BAAoC,CACpC,iBACF,CACA,iGAIE,eACF,CACA,wGACE,cAAe,CACf,UACF,CACA,sCAEE,cAAe,CADf,SAEF,CACA,oDACE,YACF,CACA,8BAEE,eAAgB,CADhB,aAAc,CAEd,cACF,CACA,qBACE,wBAAyB,CACzB,aACF,CACA,4BACE,aACF,CACA,qBACE,uBACF,CACA,sBACE,oBAAqB,CAErB,WAAY,CACZ,cAAe,CAFf,UAGF,CACA,4BASE,4CAA6C,CAD7C,gCAAqD,CAFrD,iBAAkB,CAElB,kBAAqD,CAArD,gBAAqD,CAPrD,WAAY,CACZ,aAAc,CAEd,WAAY,CACZ,UAAW,CAFX,UAOF,CACA,yBACE,GACE,sBACF,CACA,GACE,uBACF,CACF,CAEA,qBAEE,gBAAiB,CADjB,oBAAqB,CAErB,sBACF,CAEA,0CACE,2BACF,CACA,4CACE,2BACF,CAEA,iDACE,kBACF,CAEA,2CACE,iBACF,CAGA,qBACE,wBACF,CACA,mCAOE,gCAAkC,CAElC,cAAe,CAHf,wBAA0B,CAL1B,SAAU,CACV,iBAAkB,CAGlB,SAAsB,CAFtB,OAAQ,CACR,0BAA2B,CAI3B,sBAEF,CACA,yFACE,iCACF,CACA,qHACE,SACF,CAEA,6BAIE,sCAAyC,CADzC,+BAAgC,CAEhC,yBAA0B,CAH1B,gBAAiB,CADjB,iBAKF,CACA,mCAIE,aAAc,CAId,wBAA0B,CAD1B,gBAAiB,CADjB,gBAAiB,CADjB,UAAY,CAJZ,iBAAkB,CAClB,SAAU,CACV,OAMF,CACA,yCACE,UACF,CAEA,yDAEE,wBAAyB,CADzB,eAEF,CACA,uCAOE,sBAAuB,CALvB,oBAAqB,CAArB,oBAAqB,CAGrB,yCAA8C,CAF9C,aAAc,CACd,WAAgB,CAEhB,UAEF,CACA,0CACE,kBAAyB,CACzB,yBAA2B,CAC3B,UACF,CACA,8GACE,sBACF,CAEA,8DACE,WACF,CACA,oEACE,SAAU,CACV,aACF,CACA,2FACE,iBACF,CAFA,iFACE,iBACF,CAEA,0DACE,YACF,CACA,+CACE,8BAA+B,CAC/B,YAAkB,CAElB,YAAa,CADb,WAAY,CAEZ,WACF,CACA,0DACE,cACF,CACA,sDACE,YACF,CACA,sDACE,YACF,CAEA,uCAEE,kBAAmB,CADnB,mBAEF,CACA,+CAME,yBAA0B,CAC1B,qBAAsB,CANtB,aAAc,CAGd,oBAAqB,CACrB,aAAc,CAHd,oBAAqB,CACrB,qBAKF,CACA,qDACE,0BACF,CACA,8DACE,eACF,CACA,gDAIE,cAAe,CAHf,iBAAkB,CAClB,OAAQ,CACR,KAEF,CAEA,iDACE,yBACF,CACA,yDACE,6BAA8B,CAC9B,eACF,CACA,gEACE,yBACF,CACA,kEACE,sBACF,CAEA,2CACE,wBACF,CACA,mDACE,8BAA+B,CAC/B,gBACF,CACA,0DACE,0BACF,CACA,4DACE,uBACF,CAEA,MACE,wBAAyB,CACzB,iBAAkB,CAClB,kBACF,CAEA,oEACE,cACF,CAEA,sBACE,4FACF,CAEA,gBACE,2FACF,CAEA,YACE,iBACF,CAEA,2CAGE,aAAc,CACd,mBAAoB,CACpB,cAAe,CACf,gBACF,CAEA,wDAEE,eAAgB,CAChB,WACF,CAEA,sBACE,kBAAoB,CACpB,4BAA8B,CAC9B,sCAAgC,CAAhC,8BAAgC,CAChC,yBAA2B,CAC3B,mBAAqB,CACrB,2BAA6B,CAE7B,4BAA8B,CAD9B,mBAEF,CAEA,+BACE,kBACF,CACA,qCAUE,yCAAyD,CAAzD,kBAAyD,CAAzD,sBAAyD,CATzD,WAAY,CACZ,aAAc,CAKd,QAAS,CAFT,eAAgB,CAFhB,iBAAkB,CAClB,OAAQ,CAER,OAKF,CACA,+CACE,UACF,CACA,yCACE,SACF,CACA,qDAGE,yCAAyD,CADzD,sBAA2B,CAD3B,eAGF,CACA,8FACE,WACF,CAEA,YACE,YAAa,CACb,eACF,CACA,wCACE,gBAAiB,CACjB,sBACF,CACA,2CAGE,+CAAkD,CAClD,wDAA8D,CAC9D,0BAA2B,CAH3B,iBAAkB,CAIlB,iEAA6E,CAL7E,oCAMF,CACA,kDACE,+CAAkD,CAClD,wDAA8D,CAC9D,0BACF,CACA,oDAGE,eAAgB,CAChB,eAAgB,CAHhB,UAAW,CACX,gBAGF,CACA,gHACE,oBACF,CACA,4DACE,eACF,CACA,+BAEE,+CAAkD,CAClD,wDAA8D,CAC9D,0BAA2B,CAH3B,mEAIF,CAEA,mDACE,oBACF,CAEA,6BACE,yBACF,CAEA,8BAGE,eAAiB,CADjB,eAAiB,CADjB,eAGF,CACA,uBACE,4BACF,CACA,mCACE,YACF", "file": "tom-select.default.min.css", "sourcesContent": ["/**\n * tom-select.css (v//@@version)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n.ts-control {\n  border: 1px solid #d0d0d0;\n  padding: 8px 8px;\n  width: 100%;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n  box-sizing: border-box;\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);\n  border-radius: 3px;\n  display: flex;\n  flex-wrap: wrap;\n}\n.ts-wrapper.multi.has-items .ts-control {\n  padding: calc(8px - 2px - 1px) 8px calc(8px - 2px - 3px - 1px);\n}\n.full .ts-control {\n  background-color: #fff;\n}\n.disabled .ts-control, .disabled .ts-control * {\n  cursor: default !important;\n}\n.focus .ts-control {\n  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.15);\n}\n.ts-control > * {\n  vertical-align: baseline;\n  display: inline-block;\n}\n.ts-wrapper.multi .ts-control > div {\n  cursor: pointer;\n  margin: 0 3px 3px 0;\n  padding: 2px 6px;\n  background: #1da7ee;\n  color: #fff;\n  border: 1px solid #0073bb;\n}\n.ts-wrapper.multi .ts-control > div.active {\n  background: #92c836;\n  color: #fff;\n  border: 1px solid #00578d;\n}\n.ts-wrapper.multi.disabled .ts-control > div, .ts-wrapper.multi.disabled .ts-control > div.active {\n  color: white;\n  background: #d2d2d2;\n  border: 1px solid #aaaaaa;\n}\n.ts-control > input {\n  flex: 1 1 auto;\n  min-width: 7rem;\n  display: inline-block !important;\n  padding: 0 !important;\n  min-height: 0 !important;\n  max-height: none !important;\n  max-width: 100% !important;\n  margin: 0 !important;\n  text-indent: 0 !important;\n  border: 0 none !important;\n  background: none !important;\n  line-height: inherit !important;\n  user-select: auto !important;\n  box-shadow: none !important;\n}\n.ts-control > input::-ms-clear {\n  display: none;\n}\n.ts-control > input:focus {\n  outline: none !important;\n}\n.has-items .ts-control > input {\n  margin: 0 4px !important;\n}\n.ts-control.rtl {\n  text-align: right;\n}\n.ts-control.rtl.single .ts-control:after {\n  left: 15px;\n  right: auto;\n}\n.ts-control.rtl .ts-control > input {\n  margin: 0 4px 0 -2px !important;\n}\n.disabled .ts-control {\n  opacity: 0.5;\n  background-color: #fafafa;\n}\n.input-hidden .ts-control > input {\n  opacity: 0;\n  position: absolute;\n  left: -10000px;\n}\n\n.ts-dropdown {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  width: 100%;\n  z-index: 10;\n  border: 1px solid #d0d0d0;\n  background: #fff;\n  margin: 0.25rem 0 0;\n  border-top: 0 none;\n  box-sizing: border-box;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border-radius: 0 0 3px 3px;\n}\n.ts-dropdown [data-selectable] {\n  cursor: pointer;\n  overflow: hidden;\n}\n.ts-dropdown [data-selectable] .highlight {\n  background: rgba(125, 168, 208, 0.2);\n  border-radius: 1px;\n}\n.ts-dropdown .option,\n.ts-dropdown .optgroup-header,\n.ts-dropdown .no-results,\n.ts-dropdown .create {\n  padding: 5px 8px;\n}\n.ts-dropdown .option, .ts-dropdown [data-disabled], .ts-dropdown [data-disabled] [data-selectable].option {\n  cursor: inherit;\n  opacity: 0.5;\n}\n.ts-dropdown [data-selectable].option {\n  opacity: 1;\n  cursor: pointer;\n}\n.ts-dropdown .optgroup:first-child .optgroup-header {\n  border-top: 0 none;\n}\n.ts-dropdown .optgroup-header {\n  color: #303030;\n  background: #fff;\n  cursor: default;\n}\n.ts-dropdown .active {\n  background-color: #f5fafd;\n  color: #495c68;\n}\n.ts-dropdown .active.create {\n  color: #495c68;\n}\n.ts-dropdown .create {\n  color: rgba(48, 48, 48, 0.5);\n}\n.ts-dropdown .spinner {\n  display: inline-block;\n  width: 30px;\n  height: 30px;\n  margin: 5px 8px;\n}\n.ts-dropdown .spinner::after {\n  content: \" \";\n  display: block;\n  width: 24px;\n  height: 24px;\n  margin: 3px;\n  border-radius: 50%;\n  border: 5px solid #d0d0d0;\n  border-color: #d0d0d0 transparent #d0d0d0 transparent;\n  animation: lds-dual-ring 1.2s linear infinite;\n}\n@keyframes lds-dual-ring {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.ts-dropdown-content {\n  overflow: hidden auto;\n  max-height: 200px;\n  scroll-behavior: smooth;\n}\n\n.ts-wrapper.plugin-drag_drop .ts-dragging {\n  color: transparent !important;\n}\n.ts-wrapper.plugin-drag_drop .ts-dragging > * {\n  visibility: hidden !important;\n}\n\n.plugin-checkbox_options:not(.rtl) .option input {\n  margin-right: 0.5rem;\n}\n\n.plugin-checkbox_options.rtl .option input {\n  margin-left: 0.5rem;\n}\n\n/* stylelint-disable function-name-case */\n.plugin-clear_button {\n  --ts-pr-clear-button: 1em;\n}\n.plugin-clear_button .clear-button {\n  opacity: 0;\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  right: calc(8px - 6px);\n  margin-right: 0 !important;\n  background: transparent !important;\n  transition: opacity 0.5s;\n  cursor: pointer;\n}\n.plugin-clear_button.form-select .clear-button, .plugin-clear_button.single .clear-button {\n  right: max(var(--ts-pr-caret), 8px);\n}\n.plugin-clear_button.focus.has-items .clear-button, .plugin-clear_button:not(.disabled):hover.has-items .clear-button {\n  opacity: 1;\n}\n\n.ts-wrapper .dropdown-header {\n  position: relative;\n  padding: 10px 8px;\n  border-bottom: 1px solid #d0d0d0;\n  background: color-mix(#fff, #d0d0d0, 85%);\n  border-radius: 3px 3px 0 0;\n}\n.ts-wrapper .dropdown-header-close {\n  position: absolute;\n  right: 8px;\n  top: 50%;\n  color: #303030;\n  opacity: 0.4;\n  margin-top: -12px;\n  line-height: 20px;\n  font-size: 20px !important;\n}\n.ts-wrapper .dropdown-header-close:hover {\n  color: black;\n}\n\n.plugin-dropdown_input.focus.dropdown-active .ts-control {\n  box-shadow: none;\n  border: 1px solid #d0d0d0;\n}\n.plugin-dropdown_input .dropdown-input {\n  border: 1px solid #d0d0d0;\n  border-width: 0 0 1px;\n  display: block;\n  padding: 8px 8px;\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.1);\n  width: 100%;\n  background: transparent;\n}\n.plugin-dropdown_input .items-placeholder {\n  border: 0 none !important;\n  box-shadow: none !important;\n  width: 100%;\n}\n.plugin-dropdown_input.has-items .items-placeholder, .plugin-dropdown_input.dropdown-active .items-placeholder {\n  display: none !important;\n}\n\n.ts-wrapper.plugin-input_autogrow.has-items .ts-control > input {\n  min-width: 0;\n}\n.ts-wrapper.plugin-input_autogrow.has-items.focus .ts-control > input {\n  flex: none;\n  min-width: 4px;\n}\n.ts-wrapper.plugin-input_autogrow.has-items.focus .ts-control > input::placeholder {\n  color: transparent;\n}\n\n.ts-dropdown.plugin-optgroup_columns .ts-dropdown-content {\n  display: flex;\n}\n.ts-dropdown.plugin-optgroup_columns .optgroup {\n  border-right: 1px solid #f2f2f2;\n  border-top: 0 none;\n  flex-grow: 1;\n  flex-basis: 0;\n  min-width: 0;\n}\n.ts-dropdown.plugin-optgroup_columns .optgroup:last-child {\n  border-right: 0 none;\n}\n.ts-dropdown.plugin-optgroup_columns .optgroup::before {\n  display: none;\n}\n.ts-dropdown.plugin-optgroup_columns .optgroup-header {\n  border-top: 0 none;\n}\n\n.ts-wrapper.plugin-remove_button .item {\n  display: inline-flex;\n  align-items: center;\n}\n.ts-wrapper.plugin-remove_button .item .remove {\n  color: inherit;\n  text-decoration: none;\n  vertical-align: middle;\n  display: inline-block;\n  padding: 0 6px;\n  border-radius: 0 2px 2px 0;\n  box-sizing: border-box;\n}\n.ts-wrapper.plugin-remove_button .item .remove:hover {\n  background: rgba(0, 0, 0, 0.05);\n}\n.ts-wrapper.plugin-remove_button.disabled .item .remove:hover {\n  background: none;\n}\n.ts-wrapper.plugin-remove_button .remove-single {\n  position: absolute;\n  right: 0;\n  top: 0;\n  font-size: 23px;\n}\n\n.ts-wrapper.plugin-remove_button:not(.rtl) .item {\n  padding-right: 0 !important;\n}\n.ts-wrapper.plugin-remove_button:not(.rtl) .item .remove {\n  border-left: 1px solid #0073bb;\n  margin-left: 6px;\n}\n.ts-wrapper.plugin-remove_button:not(.rtl) .item.active .remove {\n  border-left-color: #00578d;\n}\n.ts-wrapper.plugin-remove_button:not(.rtl).disabled .item .remove {\n  border-left-color: #aaaaaa;\n}\n\n.ts-wrapper.plugin-remove_button.rtl .item {\n  padding-left: 0 !important;\n}\n.ts-wrapper.plugin-remove_button.rtl .item .remove {\n  border-right: 1px solid #0073bb;\n  margin-right: 6px;\n}\n.ts-wrapper.plugin-remove_button.rtl .item.active .remove {\n  border-right-color: #00578d;\n}\n.ts-wrapper.plugin-remove_button.rtl.disabled .item .remove {\n  border-right-color: #aaaaaa;\n}\n\n:root {\n  --ts-pr-clear-button: 0px;\n  --ts-pr-caret: 0px;\n  --ts-pr-min: .75rem;\n}\n\n.ts-wrapper.single .ts-control, .ts-wrapper.single .ts-control input {\n  cursor: pointer;\n}\n\n.ts-control:not(.rtl) {\n  padding-right: max(var(--ts-pr-min), var(--ts-pr-clear-button) + var(--ts-pr-caret)) !important;\n}\n\n.ts-control.rtl {\n  padding-left: max(var(--ts-pr-min), var(--ts-pr-clear-button) + var(--ts-pr-caret)) !important;\n}\n\n.ts-wrapper {\n  position: relative;\n}\n\n.ts-dropdown,\n.ts-control,\n.ts-control input {\n  color: #303030;\n  font-family: inherit;\n  font-size: 13px;\n  line-height: 18px;\n}\n\n.ts-control,\n.ts-wrapper.single.input-active .ts-control {\n  background: #fff;\n  cursor: text;\n}\n\n.ts-hidden-accessible {\n  border: 0 !important;\n  clip: rect(0 0 0 0) !important;\n  clip-path: inset(50%) !important;\n  overflow: hidden !important;\n  padding: 0 !important;\n  position: absolute !important;\n  width: 1px !important;\n  white-space: nowrap !important;\n}\n\n.ts-wrapper.single .ts-control {\n  --ts-pr-caret: 2rem;\n}\n.ts-wrapper.single .ts-control::after {\n  content: \" \";\n  display: block;\n  position: absolute;\n  top: 50%;\n  margin-top: -3px;\n  width: 0;\n  height: 0;\n  border-style: solid;\n  border-width: 5px 5px 0 5px;\n  border-color: #808080 transparent transparent transparent;\n}\n.ts-wrapper.single .ts-control:not(.rtl)::after {\n  right: 15px;\n}\n.ts-wrapper.single .ts-control.rtl::after {\n  left: 15px;\n}\n.ts-wrapper.single.dropdown-active .ts-control::after {\n  margin-top: -4px;\n  border-width: 0 5px 5px 5px;\n  border-color: transparent transparent #808080 transparent;\n}\n.ts-wrapper.single.input-active .ts-control, .ts-wrapper.single.input-active .ts-control input {\n  cursor: text;\n}\n\n.ts-wrapper {\n  display: flex;\n  min-height: 36px;\n}\n.ts-wrapper.multi.has-items .ts-control {\n  padding-left: 5px;\n  --ts-pr-min: $padding-x;\n}\n.ts-wrapper.multi .ts-control [data-value] {\n  text-shadow: 0 1px 0 rgba(0, 51, 83, 0.3);\n  border-radius: 3px;\n  background-color: color-mix(#1da7ee, #178ee9, 60%);\n  background-image: linear-gradient(to bottom, #1da7ee, #178ee9);\n  background-repeat: repeat-x;\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2), inset 0 1px rgba(255, 255, 255, 0.03);\n}\n.ts-wrapper.multi .ts-control [data-value].active {\n  background-color: color-mix(#008fd8, #0075cf, 60%);\n  background-image: linear-gradient(to bottom, #008fd8, #0075cf);\n  background-repeat: repeat-x;\n}\n.ts-wrapper.multi.disabled .ts-control [data-value] {\n  color: #999;\n  text-shadow: none;\n  background: none;\n  box-shadow: none;\n}\n.ts-wrapper.multi.disabled .ts-control [data-value], .ts-wrapper.multi.disabled .ts-control [data-value] .remove {\n  border-color: #e6e6e6;\n}\n.ts-wrapper.multi.disabled .ts-control [data-value] .remove {\n  background: none;\n}\n.ts-wrapper.single .ts-control {\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.8);\n  background-color: color-mix(#fefefe, #f2f2f2, 60%);\n  background-image: linear-gradient(to bottom, #fefefe, #f2f2f2);\n  background-repeat: repeat-x;\n}\n\n.ts-wrapper.single .ts-control, .ts-dropdown.single {\n  border-color: #b8b8b8;\n}\n\n.dropdown-active .ts-control {\n  border-radius: 3px 3px 0 0;\n}\n\n.ts-dropdown .optgroup-header {\n  padding-top: 7px;\n  font-weight: bold;\n  font-size: 0.85em;\n}\n.ts-dropdown .optgroup {\n  border-top: 1px solid #f0f0f0;\n}\n.ts-dropdown .optgroup:first-child {\n  border-top: 0 none;\n}"]}