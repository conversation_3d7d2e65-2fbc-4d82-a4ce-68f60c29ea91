{"version": 3, "file": "tabler.esm.js", "sources": ["../../js/src/autosize.js", "../../js/src/countup.js", "../../js/src/input-mask.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/enums.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getNodeName.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindow.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/instanceOf.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/applyStyles.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getBasePlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/math.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/userAgent.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isLayoutViewport.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getBoundingClientRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getLayoutRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/contains.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getComputedStyle.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isTableElement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getDocumentElement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getParentNode.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getOffsetParent.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getMainAxisFromPlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/within.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getFreshSideObject.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/mergePaddingObject.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/expandToHashMap.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/arrow.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getVariation.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/computeStyles.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/eventListeners.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getOppositePlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getOppositeVariationPlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindowScroll.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getWindowScrollBarX.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getViewportRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getDocumentRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/isScrollParent.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getScrollParent.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/listScrollParents.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/rectToClientRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getClippingRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/computeOffsets.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/detectOverflow.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/computeAutoPlacement.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/flip.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/hide.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/offset.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/popperOffsets.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/getAltAxis.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/modifiers/preventOverflow.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getHTMLElementScroll.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getNodeScroll.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/dom-utils/getCompositeRect.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/orderModifiers.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/debounce.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/utils/mergeByName.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/createPopper.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/popper-lite.js", "../../../node_modules/.pnpm/@popperjs+core@2.11.8/node_modules/@popperjs/core/lib/popper.js", "../../../node_modules/.pnpm/bootstrap@5.3.3_@popperjs+core@2.11.8/node_modules/bootstrap/dist/js/bootstrap.esm.js", "../../js/src/dropdown.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/switch-icon.js", "../../js/src/toast.js", "../../js/src/tabler.js"], "sourcesContent": ["// Autosize plugin\n\nimport autosize from 'autosize';\n\nconst elements = document.querySelectorAll('[data-bs-toggle=\"autosize\"]');\nif (elements.length) {\n\telements.forEach(function (element) {\n\t\tautosize && autosize(element);\n\t});\n}", "const elements = document.querySelectorAll('[data-countup]');\n\nif (elements.length) {\n\telements.forEach(function (element) {\n\t\tlet options = {};\n\t\ttry {\n\t\t\tconst dataOptions = element.getAttribute('data-countup') ? JSON.parse(element.getAttribute('data-countup')) : {};\n\t\t\toptions = Object.assign({'enableScrollSpy': true}, dataOptions);\n\t\t\t\n\t\t} catch (error) {}\n\n\t\tconst value = parseInt(element.innerHTML, 10);\n\n\t\tconst countUp = new window.countUp.CountUp(element, value, options);\n\t\tif (!countUp.error) {\n\t\t\tcountUp.start();\n\t\t}\n\t});\n}\n", "// Input mask plugin\n\nimport IMask from 'imask';\n\nvar maskElementList = [].slice.call(document.querySelectorAll('[data-mask]'));\nmaskElementList.map(function (maskEl) {\n\tIMask && new IMask(maskEl, {\n\t\tmask: maskEl.dataset.mask,\n\t\tlazy: maskEl.dataset['mask-visible'] === 'true'\n\t})\n});", "export var top = 'top';\nexport var bottom = 'bottom';\nexport var right = 'right';\nexport var left = 'left';\nexport var auto = 'auto';\nexport var basePlacements = [top, bottom, right, left];\nexport var start = 'start';\nexport var end = 'end';\nexport var clippingParents = 'clippingParents';\nexport var viewport = 'viewport';\nexport var popper = 'popper';\nexport var reference = 'reference';\nexport var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {\n  return acc.concat([placement + \"-\" + start, placement + \"-\" + end]);\n}, []);\nexport var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {\n  return acc.concat([placement, placement + \"-\" + start, placement + \"-\" + end]);\n}, []); // modifiers that need to read the DOM\n\nexport var beforeRead = 'beforeRead';\nexport var read = 'read';\nexport var afterRead = 'afterRead'; // pure-logic modifiers\n\nexport var beforeMain = 'beforeMain';\nexport var main = 'main';\nexport var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)\n\nexport var beforeWrite = 'beforeWrite';\nexport var write = 'write';\nexport var afterWrite = 'afterWrite';\nexport var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];", "export default function getNodeName(element) {\n  return element ? (element.nodeName || '').toLowerCase() : null;\n}", "export default function getWindow(node) {\n  if (node == null) {\n    return window;\n  }\n\n  if (node.toString() !== '[object Window]') {\n    var ownerDocument = node.ownerDocument;\n    return ownerDocument ? ownerDocument.defaultView || window : window;\n  }\n\n  return node;\n}", "import getWindow from \"./getWindow.js\";\n\nfunction isElement(node) {\n  var OwnElement = getWindow(node).Element;\n  return node instanceof OwnElement || node instanceof Element;\n}\n\nfunction isHTMLElement(node) {\n  var OwnElement = getWindow(node).HTMLElement;\n  return node instanceof OwnElement || node instanceof HTMLElement;\n}\n\nfunction isShadowRoot(node) {\n  // IE 11 has no ShadowRoot\n  if (typeof ShadowRoot === 'undefined') {\n    return false;\n  }\n\n  var OwnElement = getWindow(node).ShadowRoot;\n  return node instanceof OwnElement || node instanceof ShadowRoot;\n}\n\nexport { isElement, isHTMLElement, isShadowRoot };", "import getNodeName from \"../dom-utils/getNodeName.js\";\nimport { isHTMLElement } from \"../dom-utils/instanceOf.js\"; // This modifier takes the styles prepared by the `computeStyles` modifier\n// and applies them to the HTMLElements such as popper and arrow\n\nfunction applyStyles(_ref) {\n  var state = _ref.state;\n  Object.keys(state.elements).forEach(function (name) {\n    var style = state.styles[name] || {};\n    var attributes = state.attributes[name] || {};\n    var element = state.elements[name]; // arrow is optional + virtual elements\n\n    if (!isHTMLElement(element) || !getNodeName(element)) {\n      return;\n    } // Flow doesn't support to extend this property, but it's the most\n    // effective way to apply styles to an HTMLElement\n    // $FlowFixMe[cannot-write]\n\n\n    Object.assign(element.style, style);\n    Object.keys(attributes).forEach(function (name) {\n      var value = attributes[name];\n\n      if (value === false) {\n        element.removeAttribute(name);\n      } else {\n        element.setAttribute(name, value === true ? '' : value);\n      }\n    });\n  });\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state;\n  var initialStyles = {\n    popper: {\n      position: state.options.strategy,\n      left: '0',\n      top: '0',\n      margin: '0'\n    },\n    arrow: {\n      position: 'absolute'\n    },\n    reference: {}\n  };\n  Object.assign(state.elements.popper.style, initialStyles.popper);\n  state.styles = initialStyles;\n\n  if (state.elements.arrow) {\n    Object.assign(state.elements.arrow.style, initialStyles.arrow);\n  }\n\n  return function () {\n    Object.keys(state.elements).forEach(function (name) {\n      var element = state.elements[name];\n      var attributes = state.attributes[name] || {};\n      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them\n\n      var style = styleProperties.reduce(function (style, property) {\n        style[property] = '';\n        return style;\n      }, {}); // arrow is optional + virtual elements\n\n      if (!isHTMLElement(element) || !getNodeName(element)) {\n        return;\n      }\n\n      Object.assign(element.style, style);\n      Object.keys(attributes).forEach(function (attribute) {\n        element.removeAttribute(attribute);\n      });\n    });\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'applyStyles',\n  enabled: true,\n  phase: 'write',\n  fn: applyStyles,\n  effect: effect,\n  requires: ['computeStyles']\n};", "import { auto } from \"../enums.js\";\nexport default function getBasePlacement(placement) {\n  return placement.split('-')[0];\n}", "export var max = Math.max;\nexport var min = Math.min;\nexport var round = Math.round;", "export default function getUAString() {\n  var uaData = navigator.userAgentData;\n\n  if (uaData != null && uaData.brands && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(function (item) {\n      return item.brand + \"/\" + item.version;\n    }).join(' ');\n  }\n\n  return navigator.userAgent;\n}", "import getUAString from \"../utils/userAgent.js\";\nexport default function isLayoutViewport() {\n  return !/^((?!chrome|android).)*safari/i.test(getUAString());\n}", "import { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport { round } from \"../utils/math.js\";\nimport getWindow from \"./getWindow.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getBoundingClientRect(element, includeScale, isFixedStrategy) {\n  if (includeScale === void 0) {\n    includeScale = false;\n  }\n\n  if (isFixedStrategy === void 0) {\n    isFixedStrategy = false;\n  }\n\n  var clientRect = element.getBoundingClientRect();\n  var scaleX = 1;\n  var scaleY = 1;\n\n  if (includeScale && isHTMLElement(element)) {\n    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;\n    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;\n  }\n\n  var _ref = isElement(element) ? getWindow(element) : window,\n      visualViewport = _ref.visualViewport;\n\n  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;\n  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;\n  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;\n  var width = clientRect.width / scaleX;\n  var height = clientRect.height / scaleY;\n  return {\n    width: width,\n    height: height,\n    top: y,\n    right: x + width,\n    bottom: y + height,\n    left: x,\n    x: x,\n    y: y\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\"; // Returns the layout rect of an element relative to its offsetParent. Layout\n// means it doesn't take into account transforms.\n\nexport default function getLayoutRect(element) {\n  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.\n  // Fixes https://github.com/popperjs/popper-core/issues/1223\n\n  var width = element.offsetWidth;\n  var height = element.offsetHeight;\n\n  if (Math.abs(clientRect.width - width) <= 1) {\n    width = clientRect.width;\n  }\n\n  if (Math.abs(clientRect.height - height) <= 1) {\n    height = clientRect.height;\n  }\n\n  return {\n    x: element.offsetLeft,\n    y: element.offsetTop,\n    width: width,\n    height: height\n  };\n}", "import { isShadowRoot } from \"./instanceOf.js\";\nexport default function contains(parent, child) {\n  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method\n\n  if (parent.contains(child)) {\n    return true;\n  } // then fallback to custom implementation with Shadow DOM support\n  else if (rootNode && isShadowRoot(rootNode)) {\n      var next = child;\n\n      do {\n        if (next && parent.isSameNode(next)) {\n          return true;\n        } // $FlowFixMe[prop-missing]: need a better way to handle this...\n\n\n        next = next.parentNode || next.host;\n      } while (next);\n    } // Give up, the result is false\n\n\n  return false;\n}", "import getWindow from \"./getWindow.js\";\nexport default function getComputedStyle(element) {\n  return getWindow(element).getComputedStyle(element);\n}", "import getNodeName from \"./getNodeName.js\";\nexport default function isTableElement(element) {\n  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;\n}", "import { isElement } from \"./instanceOf.js\";\nexport default function getDocumentElement(element) {\n  // $FlowFixMe[incompatible-return]: assume body is always available\n  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]\n  element.document) || window.document).documentElement;\n}", "import getNodeName from \"./getNodeName.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport { isShadowRoot } from \"./instanceOf.js\";\nexport default function getParentNode(element) {\n  if (getNodeName(element) === 'html') {\n    return element;\n  }\n\n  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle\n    // $FlowFixMe[incompatible-return]\n    // $FlowFixMe[prop-missing]\n    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node\n    element.parentNode || ( // DOM Element detected\n    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected\n    // $FlowFixMe[incompatible-call]: HTMLElement is a Node\n    getDocumentElement(element) // fallback\n\n  );\n}", "import getWindow from \"./getWindow.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isHTMLElement, isShadowRoot } from \"./instanceOf.js\";\nimport isTableElement from \"./isTableElement.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getUAString from \"../utils/userAgent.js\";\n\nfunction getTrueOffsetParent(element) {\n  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837\n  getComputedStyle(element).position === 'fixed') {\n    return null;\n  }\n\n  return element.offsetParent;\n} // `.offsetParent` reports `null` for fixed elements, while absolute elements\n// return the containing block\n\n\nfunction getContainingBlock(element) {\n  var isFirefox = /firefox/i.test(getUAString());\n  var isIE = /Trident/i.test(getUAString());\n\n  if (isIE && isHTMLElement(element)) {\n    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport\n    var elementCss = getComputedStyle(element);\n\n    if (elementCss.position === 'fixed') {\n      return null;\n    }\n  }\n\n  var currentNode = getParentNode(element);\n\n  if (isShadowRoot(currentNode)) {\n    currentNode = currentNode.host;\n  }\n\n  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {\n    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that\n    // create a containing block.\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block\n\n    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {\n      return currentNode;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return null;\n} // Gets the closest ancestor positioned element. Handles some edge cases,\n// such as table ancestors and cross browser bugs.\n\n\nexport default function getOffsetParent(element) {\n  var window = getWindow(element);\n  var offsetParent = getTrueOffsetParent(element);\n\n  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {\n    offsetParent = getTrueOffsetParent(offsetParent);\n  }\n\n  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {\n    return window;\n  }\n\n  return offsetParent || getContainingBlock(element) || window;\n}", "export default function getMainAxisFromPlacement(placement) {\n  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';\n}", "import { max as mathMax, min as mathMin } from \"./math.js\";\nexport function within(min, value, max) {\n  return mathMax(min, mathMin(value, max));\n}\nexport function withinMaxClamp(min, value, max) {\n  var v = within(min, value, max);\n  return v > max ? max : v;\n}", "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "import getFreshSideObject from \"./getFreshSideObject.js\";\nexport default function mergePaddingObject(paddingObject) {\n  return Object.assign({}, getFreshSideObject(), paddingObject);\n}", "export default function expandToHashMap(value, keys) {\n  return keys.reduce(function (hashMap, key) {\n    hashMap[key] = value;\n    return hashMap;\n  }, {});\n}", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport contains from \"../dom-utils/contains.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport { within } from \"../utils/within.js\";\nimport mergePaddingObject from \"../utils/mergePaddingObject.js\";\nimport expandToHashMap from \"../utils/expandToHashMap.js\";\nimport { left, right, basePlacements, top, bottom } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar toPaddingObject = function toPaddingObject(padding, state) {\n  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : padding;\n  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n};\n\nfunction arrow(_ref) {\n  var _state$modifiersData$;\n\n  var state = _ref.state,\n      name = _ref.name,\n      options = _ref.options;\n  var arrowElement = state.elements.arrow;\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var basePlacement = getBasePlacement(state.placement);\n  var axis = getMainAxisFromPlacement(basePlacement);\n  var isVertical = [left, right].indexOf(basePlacement) >= 0;\n  var len = isVertical ? 'height' : 'width';\n\n  if (!arrowElement || !popperOffsets) {\n    return;\n  }\n\n  var paddingObject = toPaddingObject(options.padding, state);\n  var arrowRect = getLayoutRect(arrowElement);\n  var minProp = axis === 'y' ? top : left;\n  var maxProp = axis === 'y' ? bottom : right;\n  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];\n  var startDiff = popperOffsets[axis] - state.rects.reference[axis];\n  var arrowOffsetParent = getOffsetParent(arrowElement);\n  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;\n  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is\n  // outside of the popper bounds\n\n  var min = paddingObject[minProp];\n  var max = clientSize - arrowRect[len] - paddingObject[maxProp];\n  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;\n  var offset = within(min, center, max); // Prevents breaking syntax highlighting...\n\n  var axisProp = axis;\n  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);\n}\n\nfunction effect(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options;\n  var _options$element = options.element,\n      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;\n\n  if (arrowElement == null) {\n    return;\n  } // CSS selector\n\n\n  if (typeof arrowElement === 'string') {\n    arrowElement = state.elements.popper.querySelector(arrowElement);\n\n    if (!arrowElement) {\n      return;\n    }\n  }\n\n  if (!contains(state.elements.popper, arrowElement)) {\n    return;\n  }\n\n  state.elements.arrow = arrowElement;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'arrow',\n  enabled: true,\n  phase: 'main',\n  fn: arrow,\n  effect: effect,\n  requires: ['popperOffsets'],\n  requiresIfExists: ['preventOverflow']\n};", "export default function getVariation(placement) {\n  return placement.split('-')[1];\n}", "import { top, left, right, bottom, end } from \"../enums.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport getWindow from \"../dom-utils/getWindow.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getComputedStyle from \"../dom-utils/getComputedStyle.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport { round } from \"../utils/math.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar unsetSides = {\n  top: 'auto',\n  right: 'auto',\n  bottom: 'auto',\n  left: 'auto'\n}; // Round the offsets to the nearest suitable subpixel based on the DPR.\n// Zooming can change the DPR, but it seems to report a value that will\n// cleanly divide the values into the appropriate subpixels.\n\nfunction roundOffsetsByDPR(_ref, win) {\n  var x = _ref.x,\n      y = _ref.y;\n  var dpr = win.devicePixelRatio || 1;\n  return {\n    x: round(x * dpr) / dpr || 0,\n    y: round(y * dpr) / dpr || 0\n  };\n}\n\nexport function mapToStyles(_ref2) {\n  var _Object$assign2;\n\n  var popper = _ref2.popper,\n      popperRect = _ref2.popperRect,\n      placement = _ref2.placement,\n      variation = _ref2.variation,\n      offsets = _ref2.offsets,\n      position = _ref2.position,\n      gpuAcceleration = _ref2.gpuAcceleration,\n      adaptive = _ref2.adaptive,\n      roundOffsets = _ref2.roundOffsets,\n      isFixed = _ref2.isFixed;\n  var _offsets$x = offsets.x,\n      x = _offsets$x === void 0 ? 0 : _offsets$x,\n      _offsets$y = offsets.y,\n      y = _offsets$y === void 0 ? 0 : _offsets$y;\n\n  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({\n    x: x,\n    y: y\n  }) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref3.x;\n  y = _ref3.y;\n  var hasX = offsets.hasOwnProperty('x');\n  var hasY = offsets.hasOwnProperty('y');\n  var sideX = left;\n  var sideY = top;\n  var win = window;\n\n  if (adaptive) {\n    var offsetParent = getOffsetParent(popper);\n    var heightProp = 'clientHeight';\n    var widthProp = 'clientWidth';\n\n    if (offsetParent === getWindow(popper)) {\n      offsetParent = getDocumentElement(popper);\n\n      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {\n        heightProp = 'scrollHeight';\n        widthProp = 'scrollWidth';\n      }\n    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it\n\n\n    offsetParent = offsetParent;\n\n    if (placement === top || (placement === left || placement === right) && variation === end) {\n      sideY = bottom;\n      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]\n      offsetParent[heightProp];\n      y -= offsetY - popperRect.height;\n      y *= gpuAcceleration ? 1 : -1;\n    }\n\n    if (placement === left || (placement === top || placement === bottom) && variation === end) {\n      sideX = right;\n      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]\n      offsetParent[widthProp];\n      x -= offsetX - popperRect.width;\n      x *= gpuAcceleration ? 1 : -1;\n    }\n  }\n\n  var commonStyles = Object.assign({\n    position: position\n  }, adaptive && unsetSides);\n\n  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({\n    x: x,\n    y: y\n  }, getWindow(popper)) : {\n    x: x,\n    y: y\n  };\n\n  x = _ref4.x;\n  y = _ref4.y;\n\n  if (gpuAcceleration) {\n    var _Object$assign;\n\n    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? \"translate(\" + x + \"px, \" + y + \"px)\" : \"translate3d(\" + x + \"px, \" + y + \"px, 0)\", _Object$assign));\n  }\n\n  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + \"px\" : '', _Object$assign2[sideX] = hasX ? x + \"px\" : '', _Object$assign2.transform = '', _Object$assign2));\n}\n\nfunction computeStyles(_ref5) {\n  var state = _ref5.state,\n      options = _ref5.options;\n  var _options$gpuAccelerat = options.gpuAcceleration,\n      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,\n      _options$adaptive = options.adaptive,\n      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,\n      _options$roundOffsets = options.roundOffsets,\n      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;\n  var commonStyles = {\n    placement: getBasePlacement(state.placement),\n    variation: getVariation(state.placement),\n    popper: state.elements.popper,\n    popperRect: state.rects.popper,\n    gpuAcceleration: gpuAcceleration,\n    isFixed: state.options.strategy === 'fixed'\n  };\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.popperOffsets,\n      position: state.options.strategy,\n      adaptive: adaptive,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  if (state.modifiersData.arrow != null) {\n    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {\n      offsets: state.modifiersData.arrow,\n      position: 'absolute',\n      adaptive: false,\n      roundOffsets: roundOffsets\n    })));\n  }\n\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-placement': state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'computeStyles',\n  enabled: true,\n  phase: 'beforeWrite',\n  fn: computeStyles,\n  data: {}\n};", "import getWindow from \"../dom-utils/getWindow.js\"; // eslint-disable-next-line import/no-unused-modules\n\nvar passive = {\n  passive: true\n};\n\nfunction effect(_ref) {\n  var state = _ref.state,\n      instance = _ref.instance,\n      options = _ref.options;\n  var _options$scroll = options.scroll,\n      scroll = _options$scroll === void 0 ? true : _options$scroll,\n      _options$resize = options.resize,\n      resize = _options$resize === void 0 ? true : _options$resize;\n  var window = getWindow(state.elements.popper);\n  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);\n\n  if (scroll) {\n    scrollParents.forEach(function (scrollParent) {\n      scrollParent.addEventListener('scroll', instance.update, passive);\n    });\n  }\n\n  if (resize) {\n    window.addEventListener('resize', instance.update, passive);\n  }\n\n  return function () {\n    if (scroll) {\n      scrollParents.forEach(function (scrollParent) {\n        scrollParent.removeEventListener('scroll', instance.update, passive);\n      });\n    }\n\n    if (resize) {\n      window.removeEventListener('resize', instance.update, passive);\n    }\n  };\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'eventListeners',\n  enabled: true,\n  phase: 'write',\n  fn: function fn() {},\n  effect: effect,\n  data: {}\n};", "var hash = {\n  left: 'right',\n  right: 'left',\n  bottom: 'top',\n  top: 'bottom'\n};\nexport default function getOppositePlacement(placement) {\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}", "var hash = {\n  start: 'end',\n  end: 'start'\n};\nexport default function getOppositeVariationPlacement(placement) {\n  return placement.replace(/start|end/g, function (matched) {\n    return hash[matched];\n  });\n}", "import getWindow from \"./getWindow.js\";\nexport default function getWindowScroll(node) {\n  var win = getWindow(node);\n  var scrollLeft = win.pageXOffset;\n  var scrollTop = win.pageYOffset;\n  return {\n    scrollLeft: scrollLeft,\n    scrollTop: scrollTop\n  };\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nexport default function getWindowScrollBarX(element) {\n  // If <html> has a CSS width greater than the viewport, then this will be\n  // incorrect for RTL.\n  // Popper 1 is broken in this case and never had a bug report so let's assume\n  // it's not an issue. I don't think anyone ever specifies width on <html>\n  // anyway.\n  // Browsers where the left scrollbar doesn't cause an issue report `0` for\n  // this (e.g. Edge 2019, IE11, Safari)\n  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;\n}", "import getWindow from \"./getWindow.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport isLayoutViewport from \"./isLayoutViewport.js\";\nexport default function getViewportRect(element, strategy) {\n  var win = getWindow(element);\n  var html = getDocumentElement(element);\n  var visualViewport = win.visualViewport;\n  var width = html.clientWidth;\n  var height = html.clientHeight;\n  var x = 0;\n  var y = 0;\n\n  if (visualViewport) {\n    width = visualViewport.width;\n    height = visualViewport.height;\n    var layoutViewport = isLayoutViewport();\n\n    if (layoutViewport || !layoutViewport && strategy === 'fixed') {\n      x = visualViewport.offsetLeft;\n      y = visualViewport.offsetTop;\n    }\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x + getWindowScrollBarX(element),\n    y: y\n  };\n}", "import getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getWindowScroll from \"./getWindowScroll.js\";\nimport { max } from \"../utils/math.js\"; // Gets the entire size of the scrollable document area, even extending outside\n// of the `<html>` and `<body>` rect bounds if horizontally scrollable\n\nexport default function getDocumentRect(element) {\n  var _element$ownerDocumen;\n\n  var html = getDocumentElement(element);\n  var winScroll = getWindowScroll(element);\n  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;\n  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);\n  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);\n  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);\n  var y = -winScroll.scrollTop;\n\n  if (getComputedStyle(body || html).direction === 'rtl') {\n    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;\n  }\n\n  return {\n    width: width,\n    height: height,\n    x: x,\n    y: y\n  };\n}", "import getComputedStyle from \"./getComputedStyle.js\";\nexport default function isScrollParent(element) {\n  // Firefox wants us to check `-x` and `-y` variations as well\n  var _getComputedStyle = getComputedStyle(element),\n      overflow = _getComputedStyle.overflow,\n      overflowX = _getComputedStyle.overflowX,\n      overflowY = _getComputedStyle.overflowY;\n\n  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);\n}", "import getParentNode from \"./getParentNode.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nexport default function getScrollParent(node) {\n  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {\n    // $FlowFixMe[incompatible-return]: assume body is always available\n    return node.ownerDocument.body;\n  }\n\n  if (isHTMLElement(node) && isScrollParent(node)) {\n    return node;\n  }\n\n  return getScrollParent(getParentNode(node));\n}", "import getScrollParent from \"./getScrollParent.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport getWindow from \"./getWindow.js\";\nimport isScrollParent from \"./isScrollParent.js\";\n/*\ngiven a DOM element, return the list of all scroll parents, up the list of ancesors\nuntil we get to the top window object. This list is what we attach scroll listeners\nto, because if any of these parent elements scroll, we'll need to re-calculate the\nreference element's position.\n*/\n\nexport default function listScrollParents(element, list) {\n  var _element$ownerDocumen;\n\n  if (list === void 0) {\n    list = [];\n  }\n\n  var scrollParent = getScrollParent(element);\n  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);\n  var win = getWindow(scrollParent);\n  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;\n  var updatedList = list.concat(target);\n  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here\n  updatedList.concat(listScrollParents(getParentNode(target)));\n}", "export default function rectToClientRect(rect) {\n  return Object.assign({}, rect, {\n    left: rect.x,\n    top: rect.y,\n    right: rect.x + rect.width,\n    bottom: rect.y + rect.height\n  });\n}", "import { viewport } from \"../enums.js\";\nimport getViewportRect from \"./getViewportRect.js\";\nimport getDocumentRect from \"./getDocumentRect.js\";\nimport listScrollParents from \"./listScrollParents.js\";\nimport getOffsetParent from \"./getOffsetParent.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport getComputedStyle from \"./getComputedStyle.js\";\nimport { isElement, isHTMLElement } from \"./instanceOf.js\";\nimport getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getParentNode from \"./getParentNode.js\";\nimport contains from \"./contains.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport rectToClientRect from \"../utils/rectToClientRect.js\";\nimport { max, min } from \"../utils/math.js\";\n\nfunction getInnerBoundingClientRect(element, strategy) {\n  var rect = getBoundingClientRect(element, false, strategy === 'fixed');\n  rect.top = rect.top + element.clientTop;\n  rect.left = rect.left + element.clientLeft;\n  rect.bottom = rect.top + element.clientHeight;\n  rect.right = rect.left + element.clientWidth;\n  rect.width = element.clientWidth;\n  rect.height = element.clientHeight;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\n\nfunction getClientRectFromMixedType(element, clippingParent, strategy) {\n  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));\n} // A \"clipping parent\" is an overflowable container with the characteristic of\n// clipping (or hiding) overflowing elements with a position different from\n// `initial`\n\n\nfunction getClippingParents(element) {\n  var clippingParents = listScrollParents(getParentNode(element));\n  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;\n  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;\n\n  if (!isElement(clipperElement)) {\n    return [];\n  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414\n\n\n  return clippingParents.filter(function (clippingParent) {\n    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';\n  });\n} // Gets the maximum area that the element is visible in due to any number of\n// clipping parents\n\n\nexport default function getClippingRect(element, boundary, rootBoundary, strategy) {\n  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);\n  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);\n  var firstClippingParent = clippingParents[0];\n  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {\n    var rect = getClientRectFromMixedType(element, clippingParent, strategy);\n    accRect.top = max(rect.top, accRect.top);\n    accRect.right = min(rect.right, accRect.right);\n    accRect.bottom = min(rect.bottom, accRect.bottom);\n    accRect.left = max(rect.left, accRect.left);\n    return accRect;\n  }, getClientRectFromMixedType(element, firstClippingParent, strategy));\n  clippingRect.width = clippingRect.right - clippingRect.left;\n  clippingRect.height = clippingRect.bottom - clippingRect.top;\n  clippingRect.x = clippingRect.left;\n  clippingRect.y = clippingRect.top;\n  return clippingRect;\n}", "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}", "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}", "import getVariation from \"./getVariation.js\";\nimport { variationPlacements, basePlacements, placements as allPlacements } from \"../enums.js\";\nimport detectOverflow from \"./detectOverflow.js\";\nimport getBasePlacement from \"./getBasePlacement.js\";\nexport default function computeAutoPlacement(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      placement = _options.placement,\n      boundary = _options.boundary,\n      rootBoundary = _options.rootBoundary,\n      padding = _options.padding,\n      flipVariations = _options.flipVariations,\n      _options$allowedAutoP = _options.allowedAutoPlacements,\n      allowedAutoPlacements = _options$allowedAutoP === void 0 ? allPlacements : _options$allowedAutoP;\n  var variation = getVariation(placement);\n  var placements = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {\n    return getVariation(placement) === variation;\n  }) : basePlacements;\n  var allowedPlacements = placements.filter(function (placement) {\n    return allowedAutoPlacements.indexOf(placement) >= 0;\n  });\n\n  if (allowedPlacements.length === 0) {\n    allowedPlacements = placements;\n  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...\n\n\n  var overflows = allowedPlacements.reduce(function (acc, placement) {\n    acc[placement] = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding\n    })[getBasePlacement(placement)];\n    return acc;\n  }, {});\n  return Object.keys(overflows).sort(function (a, b) {\n    return overflows[a] - overflows[b];\n  });\n}", "import getOppositePlacement from \"../utils/getOppositePlacement.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getOppositeVariationPlacement from \"../utils/getOppositeVariationPlacement.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport computeAutoPlacement from \"../utils/computeAutoPlacement.js\";\nimport { bottom, top, start, right, left, auto } from \"../enums.js\";\nimport getVariation from \"../utils/getVariation.js\"; // eslint-disable-next-line import/no-unused-modules\n\nfunction getExpandedFallbackPlacements(placement) {\n  if (getBasePlacement(placement) === auto) {\n    return [];\n  }\n\n  var oppositePlacement = getOppositePlacement(placement);\n  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];\n}\n\nfunction flip(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n\n  if (state.modifiersData[name]._skip) {\n    return;\n  }\n\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,\n      specifiedFallbackPlacements = options.fallbackPlacements,\n      padding = options.padding,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      _options$flipVariatio = options.flipVariations,\n      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,\n      allowedAutoPlacements = options.allowedAutoPlacements;\n  var preferredPlacement = state.options.placement;\n  var basePlacement = getBasePlacement(preferredPlacement);\n  var isBasePlacement = basePlacement === preferredPlacement;\n  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));\n  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {\n    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      padding: padding,\n      flipVariations: flipVariations,\n      allowedAutoPlacements: allowedAutoPlacements\n    }) : placement);\n  }, []);\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var checksMap = new Map();\n  var makeFallbackChecks = true;\n  var firstFittingPlacement = placements[0];\n\n  for (var i = 0; i < placements.length; i++) {\n    var placement = placements[i];\n\n    var _basePlacement = getBasePlacement(placement);\n\n    var isStartVariation = getVariation(placement) === start;\n    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;\n    var len = isVertical ? 'width' : 'height';\n    var overflow = detectOverflow(state, {\n      placement: placement,\n      boundary: boundary,\n      rootBoundary: rootBoundary,\n      altBoundary: altBoundary,\n      padding: padding\n    });\n    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;\n\n    if (referenceRect[len] > popperRect[len]) {\n      mainVariationSide = getOppositePlacement(mainVariationSide);\n    }\n\n    var altVariationSide = getOppositePlacement(mainVariationSide);\n    var checks = [];\n\n    if (checkMainAxis) {\n      checks.push(overflow[_basePlacement] <= 0);\n    }\n\n    if (checkAltAxis) {\n      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);\n    }\n\n    if (checks.every(function (check) {\n      return check;\n    })) {\n      firstFittingPlacement = placement;\n      makeFallbackChecks = false;\n      break;\n    }\n\n    checksMap.set(placement, checks);\n  }\n\n  if (makeFallbackChecks) {\n    // `2` may be desired in some cases – research later\n    var numberOfChecks = flipVariations ? 3 : 1;\n\n    var _loop = function _loop(_i) {\n      var fittingPlacement = placements.find(function (placement) {\n        var checks = checksMap.get(placement);\n\n        if (checks) {\n          return checks.slice(0, _i).every(function (check) {\n            return check;\n          });\n        }\n      });\n\n      if (fittingPlacement) {\n        firstFittingPlacement = fittingPlacement;\n        return \"break\";\n      }\n    };\n\n    for (var _i = numberOfChecks; _i > 0; _i--) {\n      var _ret = _loop(_i);\n\n      if (_ret === \"break\") break;\n    }\n  }\n\n  if (state.placement !== firstFittingPlacement) {\n    state.modifiersData[name]._skip = true;\n    state.placement = firstFittingPlacement;\n    state.reset = true;\n  }\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'flip',\n  enabled: true,\n  phase: 'main',\n  fn: flip,\n  requiresIfExists: ['offset'],\n  data: {\n    _skip: false\n  }\n};", "import { top, bottom, left, right } from \"../enums.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\n\nfunction getSideOffsets(overflow, rect, preventedOffsets) {\n  if (preventedOffsets === void 0) {\n    preventedOffsets = {\n      x: 0,\n      y: 0\n    };\n  }\n\n  return {\n    top: overflow.top - rect.height - preventedOffsets.y,\n    right: overflow.right - rect.width + preventedOffsets.x,\n    bottom: overflow.bottom - rect.height + preventedOffsets.y,\n    left: overflow.left - rect.width - preventedOffsets.x\n  };\n}\n\nfunction isAnySideFullyClipped(overflow) {\n  return [top, right, bottom, left].some(function (side) {\n    return overflow[side] >= 0;\n  });\n}\n\nfunction hide(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var preventedOffsets = state.modifiersData.preventOverflow;\n  var referenceOverflow = detectOverflow(state, {\n    elementContext: 'reference'\n  });\n  var popperAltOverflow = detectOverflow(state, {\n    altBoundary: true\n  });\n  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);\n  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);\n  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);\n  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);\n  state.modifiersData[name] = {\n    referenceClippingOffsets: referenceClippingOffsets,\n    popperEscapeOffsets: popperEscapeOffsets,\n    isReferenceHidden: isReferenceHidden,\n    hasPopperEscaped: hasPopperEscaped\n  };\n  state.attributes.popper = Object.assign({}, state.attributes.popper, {\n    'data-popper-reference-hidden': isReferenceHidden,\n    'data-popper-escaped': hasPopperEscaped\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'hide',\n  enabled: true,\n  phase: 'main',\n  requiresIfExists: ['preventOverflow'],\n  fn: hide\n};", "import getBasePlacement from \"../utils/getBasePlacement.js\";\nimport { top, left, right, placements } from \"../enums.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport function distanceAndSkiddingToXY(placement, rects, offset) {\n  var basePlacement = getBasePlacement(placement);\n  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;\n\n  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {\n    placement: placement\n  })) : offset,\n      skidding = _ref[0],\n      distance = _ref[1];\n\n  skidding = skidding || 0;\n  distance = (distance || 0) * invertDistance;\n  return [left, right].indexOf(basePlacement) >= 0 ? {\n    x: distance,\n    y: skidding\n  } : {\n    x: skidding,\n    y: distance\n  };\n}\n\nfunction offset(_ref2) {\n  var state = _ref2.state,\n      options = _ref2.options,\n      name = _ref2.name;\n  var _options$offset = options.offset,\n      offset = _options$offset === void 0 ? [0, 0] : _options$offset;\n  var data = placements.reduce(function (acc, placement) {\n    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);\n    return acc;\n  }, {});\n  var _data$state$placement = data[state.placement],\n      x = _data$state$placement.x,\n      y = _data$state$placement.y;\n\n  if (state.modifiersData.popperOffsets != null) {\n    state.modifiersData.popperOffsets.x += x;\n    state.modifiersData.popperOffsets.y += y;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'offset',\n  enabled: true,\n  phase: 'main',\n  requires: ['popperOffsets'],\n  fn: offset\n};", "import computeOffsets from \"../utils/computeOffsets.js\";\n\nfunction popperOffsets(_ref) {\n  var state = _ref.state,\n      name = _ref.name;\n  // Offsets are the actual position the popper needs to have to be\n  // properly positioned near its reference element\n  // This is the most basic placement, and will be adjusted by\n  // the modifiers in the next step\n  state.modifiersData[name] = computeOffsets({\n    reference: state.rects.reference,\n    element: state.rects.popper,\n    strategy: 'absolute',\n    placement: state.placement\n  });\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'popperOffsets',\n  enabled: true,\n  phase: 'read',\n  fn: popperOffsets,\n  data: {}\n};", "export default function getAltAxis(axis) {\n  return axis === 'x' ? 'y' : 'x';\n}", "import { top, left, right, bottom, start } from \"../enums.js\";\nimport getBasePlacement from \"../utils/getBasePlacement.js\";\nimport getMainAxisFromPlacement from \"../utils/getMainAxisFromPlacement.js\";\nimport getAltAxis from \"../utils/getAltAxis.js\";\nimport { within, withinMaxClamp } from \"../utils/within.js\";\nimport getLayoutRect from \"../dom-utils/getLayoutRect.js\";\nimport getOffsetParent from \"../dom-utils/getOffsetParent.js\";\nimport detectOverflow from \"../utils/detectOverflow.js\";\nimport getVariation from \"../utils/getVariation.js\";\nimport getFreshSideObject from \"../utils/getFreshSideObject.js\";\nimport { min as mathMin, max as mathMax } from \"../utils/math.js\";\n\nfunction preventOverflow(_ref) {\n  var state = _ref.state,\n      options = _ref.options,\n      name = _ref.name;\n  var _options$mainAxis = options.mainAxis,\n      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,\n      _options$altAxis = options.altAxis,\n      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,\n      boundary = options.boundary,\n      rootBoundary = options.rootBoundary,\n      altBoundary = options.altBoundary,\n      padding = options.padding,\n      _options$tether = options.tether,\n      tether = _options$tether === void 0 ? true : _options$tether,\n      _options$tetherOffset = options.tetherOffset,\n      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;\n  var overflow = detectOverflow(state, {\n    boundary: boundary,\n    rootBoundary: rootBoundary,\n    padding: padding,\n    altBoundary: altBoundary\n  });\n  var basePlacement = getBasePlacement(state.placement);\n  var variation = getVariation(state.placement);\n  var isBasePlacement = !variation;\n  var mainAxis = getMainAxisFromPlacement(basePlacement);\n  var altAxis = getAltAxis(mainAxis);\n  var popperOffsets = state.modifiersData.popperOffsets;\n  var referenceRect = state.rects.reference;\n  var popperRect = state.rects.popper;\n  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {\n    placement: state.placement\n  })) : tetherOffset;\n  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {\n    mainAxis: tetherOffsetValue,\n    altAxis: tetherOffsetValue\n  } : Object.assign({\n    mainAxis: 0,\n    altAxis: 0\n  }, tetherOffsetValue);\n  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;\n  var data = {\n    x: 0,\n    y: 0\n  };\n\n  if (!popperOffsets) {\n    return;\n  }\n\n  if (checkMainAxis) {\n    var _offsetModifierState$;\n\n    var mainSide = mainAxis === 'y' ? top : left;\n    var altSide = mainAxis === 'y' ? bottom : right;\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    var offset = popperOffsets[mainAxis];\n    var min = offset + overflow[mainSide];\n    var max = offset - overflow[altSide];\n    var additive = tether ? -popperRect[len] / 2 : 0;\n    var minLen = variation === start ? referenceRect[len] : popperRect[len];\n    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go\n    // outside the reference bounds\n\n    var arrowElement = state.elements.arrow;\n    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {\n      width: 0,\n      height: 0\n    };\n    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();\n    var arrowPaddingMin = arrowPaddingObject[mainSide];\n    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want\n    // to include its full size in the calculation. If the reference is small\n    // and near the edge of a boundary, the popper can overflow even if the\n    // reference is not overflowing as well (e.g. virtual elements with no\n    // width or height)\n\n    var arrowLen = within(0, referenceRect[len], arrowRect[len]);\n    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;\n    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;\n    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);\n    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;\n    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;\n    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;\n    var tetherMax = offset + maxOffset - offsetModifierValue;\n    var preventedOffset = within(tether ? mathMin(min, tetherMin) : min, offset, tether ? mathMax(max, tetherMax) : max);\n    popperOffsets[mainAxis] = preventedOffset;\n    data[mainAxis] = preventedOffset - offset;\n  }\n\n  if (checkAltAxis) {\n    var _offsetModifierState$2;\n\n    var _mainSide = mainAxis === 'x' ? top : left;\n\n    var _altSide = mainAxis === 'x' ? bottom : right;\n\n    var _offset = popperOffsets[altAxis];\n\n    var _len = altAxis === 'y' ? 'height' : 'width';\n\n    var _min = _offset + overflow[_mainSide];\n\n    var _max = _offset - overflow[_altSide];\n\n    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;\n\n    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;\n\n    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;\n\n    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;\n\n    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);\n\n    popperOffsets[altAxis] = _preventedOffset;\n    data[altAxis] = _preventedOffset - _offset;\n  }\n\n  state.modifiersData[name] = data;\n} // eslint-disable-next-line import/no-unused-modules\n\n\nexport default {\n  name: 'preventOverflow',\n  enabled: true,\n  phase: 'main',\n  fn: preventOverflow,\n  requiresIfExists: ['offset']\n};", "export default function getHTMLElementScroll(element) {\n  return {\n    scrollLeft: element.scrollLeft,\n    scrollTop: element.scrollTop\n  };\n}", "import getWindowScroll from \"./getWindowScroll.js\";\nimport getWindow from \"./getWindow.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getHTMLElementScroll from \"./getHTMLElementScroll.js\";\nexport default function getNodeScroll(node) {\n  if (node === getWindow(node) || !isHTMLElement(node)) {\n    return getWindowScroll(node);\n  } else {\n    return getHTMLElementScroll(node);\n  }\n}", "import getBoundingClientRect from \"./getBoundingClientRect.js\";\nimport getNodeScroll from \"./getNodeScroll.js\";\nimport getNodeName from \"./getNodeName.js\";\nimport { isHTMLElement } from \"./instanceOf.js\";\nimport getWindowScrollBarX from \"./getWindowScrollBarX.js\";\nimport getDocumentElement from \"./getDocumentElement.js\";\nimport isScrollParent from \"./isScrollParent.js\";\nimport { round } from \"../utils/math.js\";\n\nfunction isElementScaled(element) {\n  var rect = element.getBoundingClientRect();\n  var scaleX = round(rect.width) / element.offsetWidth || 1;\n  var scaleY = round(rect.height) / element.offsetHeight || 1;\n  return scaleX !== 1 || scaleY !== 1;\n} // Returns the composite rect of an element relative to its offsetParent.\n// Composite means it takes into account transforms as well as layout.\n\n\nexport default function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {\n  if (isFixed === void 0) {\n    isFixed = false;\n  }\n\n  var isOffsetParentAnElement = isHTMLElement(offsetParent);\n  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);\n  var documentElement = getDocumentElement(offsetParent);\n  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);\n  var scroll = {\n    scrollLeft: 0,\n    scrollTop: 0\n  };\n  var offsets = {\n    x: 0,\n    y: 0\n  };\n\n  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {\n    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078\n    isScrollParent(documentElement)) {\n      scroll = getNodeScroll(offsetParent);\n    }\n\n    if (isHTMLElement(offsetParent)) {\n      offsets = getBoundingClientRect(offsetParent, true);\n      offsets.x += offsetParent.clientLeft;\n      offsets.y += offsetParent.clientTop;\n    } else if (documentElement) {\n      offsets.x = getWindowScrollBarX(documentElement);\n    }\n  }\n\n  return {\n    x: rect.left + scroll.scrollLeft - offsets.x,\n    y: rect.top + scroll.scrollTop - offsets.y,\n    width: rect.width,\n    height: rect.height\n  };\n}", "import { modifierPhases } from \"../enums.js\"; // source: https://stackoverflow.com/questions/49875255\n\nfunction order(modifiers) {\n  var map = new Map();\n  var visited = new Set();\n  var result = [];\n  modifiers.forEach(function (modifier) {\n    map.set(modifier.name, modifier);\n  }); // On visiting object, check for its dependencies and visit them recursively\n\n  function sort(modifier) {\n    visited.add(modifier.name);\n    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);\n    requires.forEach(function (dep) {\n      if (!visited.has(dep)) {\n        var depModifier = map.get(dep);\n\n        if (depModifier) {\n          sort(depModifier);\n        }\n      }\n    });\n    result.push(modifier);\n  }\n\n  modifiers.forEach(function (modifier) {\n    if (!visited.has(modifier.name)) {\n      // check for visited object\n      sort(modifier);\n    }\n  });\n  return result;\n}\n\nexport default function orderModifiers(modifiers) {\n  // order based on dependencies\n  var orderedModifiers = order(modifiers); // order based on phase\n\n  return modifierPhases.reduce(function (acc, phase) {\n    return acc.concat(orderedModifiers.filter(function (modifier) {\n      return modifier.phase === phase;\n    }));\n  }, []);\n}", "export default function debounce(fn) {\n  var pending;\n  return function () {\n    if (!pending) {\n      pending = new Promise(function (resolve) {\n        Promise.resolve().then(function () {\n          pending = undefined;\n          resolve(fn());\n        });\n      });\n    }\n\n    return pending;\n  };\n}", "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "import getCompositeRect from \"./dom-utils/getCompositeRect.js\";\nimport getLayoutRect from \"./dom-utils/getLayoutRect.js\";\nimport listScrollParents from \"./dom-utils/listScrollParents.js\";\nimport getOffsetParent from \"./dom-utils/getOffsetParent.js\";\nimport orderModifiers from \"./utils/orderModifiers.js\";\nimport debounce from \"./utils/debounce.js\";\nimport mergeByName from \"./utils/mergeByName.js\";\nimport detectOverflow from \"./utils/detectOverflow.js\";\nimport { isElement } from \"./dom-utils/instanceOf.js\";\nvar DEFAULT_OPTIONS = {\n  placement: 'bottom',\n  modifiers: [],\n  strategy: 'absolute'\n};\n\nfunction areValidElements() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return !args.some(function (element) {\n    return !(element && typeof element.getBoundingClientRect === 'function');\n  });\n}\n\nexport function popperGenerator(generatorOptions) {\n  if (generatorOptions === void 0) {\n    generatorOptions = {};\n  }\n\n  var _generatorOptions = generatorOptions,\n      _generatorOptions$def = _generatorOptions.defaultModifiers,\n      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,\n      _generatorOptions$def2 = _generatorOptions.defaultOptions,\n      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;\n  return function createPopper(reference, popper, options) {\n    if (options === void 0) {\n      options = defaultOptions;\n    }\n\n    var state = {\n      placement: 'bottom',\n      orderedModifiers: [],\n      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),\n      modifiersData: {},\n      elements: {\n        reference: reference,\n        popper: popper\n      },\n      attributes: {},\n      styles: {}\n    };\n    var effectCleanupFns = [];\n    var isDestroyed = false;\n    var instance = {\n      state: state,\n      setOptions: function setOptions(setOptionsAction) {\n        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;\n        cleanupModifierEffects();\n        state.options = Object.assign({}, defaultOptions, state.options, options);\n        state.scrollParents = {\n          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],\n          popper: listScrollParents(popper)\n        }; // Orders the modifiers based on their dependencies and `phase`\n        // properties\n\n        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers\n\n        state.orderedModifiers = orderedModifiers.filter(function (m) {\n          return m.enabled;\n        });\n        runModifierEffects();\n        return instance.update();\n      },\n      // Sync update – it will always be executed, even if not necessary. This\n      // is useful for low frequency updates where sync behavior simplifies the\n      // logic.\n      // For high frequency updates (e.g. `resize` and `scroll` events), always\n      // prefer the async Popper#update method\n      forceUpdate: function forceUpdate() {\n        if (isDestroyed) {\n          return;\n        }\n\n        var _state$elements = state.elements,\n            reference = _state$elements.reference,\n            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements\n        // anymore\n\n        if (!areValidElements(reference, popper)) {\n          return;\n        } // Store the reference and popper rects to be read by modifiers\n\n\n        state.rects = {\n          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),\n          popper: getLayoutRect(popper)\n        }; // Modifiers have the ability to reset the current update cycle. The\n        // most common use case for this is the `flip` modifier changing the\n        // placement, which then needs to re-run all the modifiers, because the\n        // logic was previously ran for the previous placement and is therefore\n        // stale/incorrect\n\n        state.reset = false;\n        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier\n        // is filled with the initial data specified by the modifier. This means\n        // it doesn't persist and is fresh on each update.\n        // To ensure persistent data, use `${name}#persistent`\n\n        state.orderedModifiers.forEach(function (modifier) {\n          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);\n        });\n\n        for (var index = 0; index < state.orderedModifiers.length; index++) {\n          if (state.reset === true) {\n            state.reset = false;\n            index = -1;\n            continue;\n          }\n\n          var _state$orderedModifie = state.orderedModifiers[index],\n              fn = _state$orderedModifie.fn,\n              _state$orderedModifie2 = _state$orderedModifie.options,\n              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,\n              name = _state$orderedModifie.name;\n\n          if (typeof fn === 'function') {\n            state = fn({\n              state: state,\n              options: _options,\n              name: name,\n              instance: instance\n            }) || state;\n          }\n        }\n      },\n      // Async and optimistically optimized update – it will not be executed if\n      // not necessary (debounced to run at most once-per-tick)\n      update: debounce(function () {\n        return new Promise(function (resolve) {\n          instance.forceUpdate();\n          resolve(state);\n        });\n      }),\n      destroy: function destroy() {\n        cleanupModifierEffects();\n        isDestroyed = true;\n      }\n    };\n\n    if (!areValidElements(reference, popper)) {\n      return instance;\n    }\n\n    instance.setOptions(options).then(function (state) {\n      if (!isDestroyed && options.onFirstUpdate) {\n        options.onFirstUpdate(state);\n      }\n    }); // Modifiers have the ability to execute arbitrary code before the first\n    // update cycle runs. They will be executed in the same order as the update\n    // cycle. This is useful when a modifier adds some persistent data that\n    // other modifiers need to use, but the modifier is run after the dependent\n    // one.\n\n    function runModifierEffects() {\n      state.orderedModifiers.forEach(function (_ref) {\n        var name = _ref.name,\n            _ref$options = _ref.options,\n            options = _ref$options === void 0 ? {} : _ref$options,\n            effect = _ref.effect;\n\n        if (typeof effect === 'function') {\n          var cleanupFn = effect({\n            state: state,\n            name: name,\n            instance: instance,\n            options: options\n          });\n\n          var noopFn = function noopFn() {};\n\n          effectCleanupFns.push(cleanupFn || noopFn);\n        }\n      });\n    }\n\n    function cleanupModifierEffects() {\n      effectCleanupFns.forEach(function (fn) {\n        return fn();\n      });\n      effectCleanupFns = [];\n    }\n\n    return instance;\n  };\n}\nexport var createPopper = /*#__PURE__*/popperGenerator(); // eslint-disable-next-line import/no-unused-modules\n\nexport { detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow };", "import { popperGenerator, detectOverflow } from \"./createPopper.js\";\nimport eventListeners from \"./modifiers/eventListeners.js\";\nimport popperOffsets from \"./modifiers/popperOffsets.js\";\nimport computeStyles from \"./modifiers/computeStyles.js\";\nimport applyStyles from \"./modifiers/applyStyles.js\";\nimport offset from \"./modifiers/offset.js\";\nimport flip from \"./modifiers/flip.js\";\nimport preventOverflow from \"./modifiers/preventOverflow.js\";\nimport arrow from \"./modifiers/arrow.js\";\nimport hide from \"./modifiers/hide.js\";\nvar defaultModifiers = [eventListeners, popperOffsets, computeStyles, applyStyles, offset, flip, preventOverflow, arrow, hide];\nvar createPopper = /*#__PURE__*/popperGenerator({\n  defaultModifiers: defaultModifiers\n}); // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper, popperGenerator, defaultModifiers, detectOverflow }; // eslint-disable-next-line import/no-unused-modules\n\nexport { createPopper as createPopperLite } from \"./popper-lite.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport * from \"./modifiers/index.js\";", "/*!\n  * Bootstrap v5.3.3 (https://getbootstrap.com/)\n  * Copyright 2011-2024 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)\n  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n  */\nimport * as <PERSON><PERSON> from '@popperjs/core';\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/data.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n/**\n * Constants\n */\n\nconst elementMap = new Map();\nconst Data = {\n  set(element, key, instance) {\n    if (!elementMap.has(element)) {\n      elementMap.set(element, new Map());\n    }\n    const instanceMap = elementMap.get(element);\n\n    // make it clear we only want one instance per element\n    // can be removed later when multiple key/instances are fine to be used\n    if (!instanceMap.has(key) && instanceMap.size !== 0) {\n      // eslint-disable-next-line no-console\n      console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(instanceMap.keys())[0]}.`);\n      return;\n    }\n    instanceMap.set(key, instance);\n  },\n  get(element, key) {\n    if (elementMap.has(element)) {\n      return elementMap.get(element).get(key) || null;\n    }\n    return null;\n  },\n  remove(element, key) {\n    if (!elementMap.has(element)) {\n      return;\n    }\n    const instanceMap = elementMap.get(element);\n    instanceMap.delete(key);\n\n    // free up element references if there are no instances left for an element\n    if (instanceMap.size === 0) {\n      elementMap.delete(element);\n    }\n  }\n};\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst MAX_UID = 1000000;\nconst MILLISECONDS_MULTIPLIER = 1000;\nconst TRANSITION_END = 'transitionend';\n\n/**\n * Properly escape IDs selectors to handle weird IDs\n * @param {string} selector\n * @returns {string}\n */\nconst parseSelector = selector => {\n  if (selector && window.CSS && window.CSS.escape) {\n    // document.querySelector needs escaping to handle IDs (html5+) containing for instance /\n    selector = selector.replace(/#([^\\s\"#']+)/g, (match, id) => `#${CSS.escape(id)}`);\n  }\n  return selector;\n};\n\n// Shout-out Angus Croll (https://goo.gl/pxwQGp)\nconst toType = object => {\n  if (object === null || object === undefined) {\n    return `${object}`;\n  }\n  return Object.prototype.toString.call(object).match(/\\s([a-z]+)/i)[1].toLowerCase();\n};\n\n/**\n * Public Util API\n */\n\nconst getUID = prefix => {\n  do {\n    prefix += Math.floor(Math.random() * MAX_UID);\n  } while (document.getElementById(prefix));\n  return prefix;\n};\nconst getTransitionDurationFromElement = element => {\n  if (!element) {\n    return 0;\n  }\n\n  // Get transition-duration of the element\n  let {\n    transitionDuration,\n    transitionDelay\n  } = window.getComputedStyle(element);\n  const floatTransitionDuration = Number.parseFloat(transitionDuration);\n  const floatTransitionDelay = Number.parseFloat(transitionDelay);\n\n  // Return 0 if element or transition duration is not found\n  if (!floatTransitionDuration && !floatTransitionDelay) {\n    return 0;\n  }\n\n  // If multiple durations are defined, take the first\n  transitionDuration = transitionDuration.split(',')[0];\n  transitionDelay = transitionDelay.split(',')[0];\n  return (Number.parseFloat(transitionDuration) + Number.parseFloat(transitionDelay)) * MILLISECONDS_MULTIPLIER;\n};\nconst triggerTransitionEnd = element => {\n  element.dispatchEvent(new Event(TRANSITION_END));\n};\nconst isElement = object => {\n  if (!object || typeof object !== 'object') {\n    return false;\n  }\n  if (typeof object.jquery !== 'undefined') {\n    object = object[0];\n  }\n  return typeof object.nodeType !== 'undefined';\n};\nconst getElement = object => {\n  // it's a jQuery object or a node element\n  if (isElement(object)) {\n    return object.jquery ? object[0] : object;\n  }\n  if (typeof object === 'string' && object.length > 0) {\n    return document.querySelector(parseSelector(object));\n  }\n  return null;\n};\nconst isVisible = element => {\n  if (!isElement(element) || element.getClientRects().length === 0) {\n    return false;\n  }\n  const elementIsVisible = getComputedStyle(element).getPropertyValue('visibility') === 'visible';\n  // Handle `details` element as its content may falsie appear visible when it is closed\n  const closedDetails = element.closest('details:not([open])');\n  if (!closedDetails) {\n    return elementIsVisible;\n  }\n  if (closedDetails !== element) {\n    const summary = element.closest('summary');\n    if (summary && summary.parentNode !== closedDetails) {\n      return false;\n    }\n    if (summary === null) {\n      return false;\n    }\n  }\n  return elementIsVisible;\n};\nconst isDisabled = element => {\n  if (!element || element.nodeType !== Node.ELEMENT_NODE) {\n    return true;\n  }\n  if (element.classList.contains('disabled')) {\n    return true;\n  }\n  if (typeof element.disabled !== 'undefined') {\n    return element.disabled;\n  }\n  return element.hasAttribute('disabled') && element.getAttribute('disabled') !== 'false';\n};\nconst findShadowRoot = element => {\n  if (!document.documentElement.attachShadow) {\n    return null;\n  }\n\n  // Can find the shadow root otherwise it'll return the document\n  if (typeof element.getRootNode === 'function') {\n    const root = element.getRootNode();\n    return root instanceof ShadowRoot ? root : null;\n  }\n  if (element instanceof ShadowRoot) {\n    return element;\n  }\n\n  // when we don't find a shadow root\n  if (!element.parentNode) {\n    return null;\n  }\n  return findShadowRoot(element.parentNode);\n};\nconst noop = () => {};\n\n/**\n * Trick to restart an element's animation\n *\n * @param {HTMLElement} element\n * @return void\n *\n * @see https://www.charistheo.io/blog/2021/02/restart-a-css-animation-with-javascript/#restarting-a-css-animation\n */\nconst reflow = element => {\n  element.offsetHeight; // eslint-disable-line no-unused-expressions\n};\nconst getjQuery = () => {\n  if (window.jQuery && !document.body.hasAttribute('data-bs-no-jquery')) {\n    return window.jQuery;\n  }\n  return null;\n};\nconst DOMContentLoadedCallbacks = [];\nconst onDOMContentLoaded = callback => {\n  if (document.readyState === 'loading') {\n    // add listener on the first call when the document is in loading state\n    if (!DOMContentLoadedCallbacks.length) {\n      document.addEventListener('DOMContentLoaded', () => {\n        for (const callback of DOMContentLoadedCallbacks) {\n          callback();\n        }\n      });\n    }\n    DOMContentLoadedCallbacks.push(callback);\n  } else {\n    callback();\n  }\n};\nconst isRTL = () => document.documentElement.dir === 'rtl';\nconst defineJQueryPlugin = plugin => {\n  onDOMContentLoaded(() => {\n    const $ = getjQuery();\n    /* istanbul ignore if */\n    if ($) {\n      const name = plugin.NAME;\n      const JQUERY_NO_CONFLICT = $.fn[name];\n      $.fn[name] = plugin.jQueryInterface;\n      $.fn[name].Constructor = plugin;\n      $.fn[name].noConflict = () => {\n        $.fn[name] = JQUERY_NO_CONFLICT;\n        return plugin.jQueryInterface;\n      };\n    }\n  });\n};\nconst execute = (possibleCallback, args = [], defaultValue = possibleCallback) => {\n  return typeof possibleCallback === 'function' ? possibleCallback(...args) : defaultValue;\n};\nconst executeAfterTransition = (callback, transitionElement, waitForTransition = true) => {\n  if (!waitForTransition) {\n    execute(callback);\n    return;\n  }\n  const durationPadding = 5;\n  const emulatedDuration = getTransitionDurationFromElement(transitionElement) + durationPadding;\n  let called = false;\n  const handler = ({\n    target\n  }) => {\n    if (target !== transitionElement) {\n      return;\n    }\n    called = true;\n    transitionElement.removeEventListener(TRANSITION_END, handler);\n    execute(callback);\n  };\n  transitionElement.addEventListener(TRANSITION_END, handler);\n  setTimeout(() => {\n    if (!called) {\n      triggerTransitionEnd(transitionElement);\n    }\n  }, emulatedDuration);\n};\n\n/**\n * Return the previous/next element of a list.\n *\n * @param {array} list    The list of elements\n * @param activeElement   The active element\n * @param shouldGetNext   Choose to get next or previous element\n * @param isCycleAllowed\n * @return {Element|elem} The proper element\n */\nconst getNextActiveElement = (list, activeElement, shouldGetNext, isCycleAllowed) => {\n  const listLength = list.length;\n  let index = list.indexOf(activeElement);\n\n  // if the element does not exist in the list return an element\n  // depending on the direction and if cycle is allowed\n  if (index === -1) {\n    return !shouldGetNext && isCycleAllowed ? list[listLength - 1] : list[0];\n  }\n  index += shouldGetNext ? 1 : -1;\n  if (isCycleAllowed) {\n    index = (index + listLength) % listLength;\n  }\n  return list[Math.max(0, Math.min(index, listLength - 1))];\n};\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/event-handler.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst namespaceRegex = /[^.]*(?=\\..*)\\.|.*/;\nconst stripNameRegex = /\\..*/;\nconst stripUidRegex = /::\\d+$/;\nconst eventRegistry = {}; // Events storage\nlet uidEvent = 1;\nconst customEvents = {\n  mouseenter: 'mouseover',\n  mouseleave: 'mouseout'\n};\nconst nativeEvents = new Set(['click', 'dblclick', 'mouseup', 'mousedown', 'contextmenu', 'mousewheel', 'DOMMouseScroll', 'mouseover', 'mouseout', 'mousemove', 'selectstart', 'selectend', 'keydown', 'keypress', 'keyup', 'orientationchange', 'touchstart', 'touchmove', 'touchend', 'touchcancel', 'pointerdown', 'pointermove', 'pointerup', 'pointerleave', 'pointercancel', 'gesturestart', 'gesturechange', 'gestureend', 'focus', 'blur', 'change', 'reset', 'select', 'submit', 'focusin', 'focusout', 'load', 'unload', 'beforeunload', 'resize', 'move', 'DOMContentLoaded', 'readystatechange', 'error', 'abort', 'scroll']);\n\n/**\n * Private methods\n */\n\nfunction makeEventUid(element, uid) {\n  return uid && `${uid}::${uidEvent++}` || element.uidEvent || uidEvent++;\n}\nfunction getElementEvents(element) {\n  const uid = makeEventUid(element);\n  element.uidEvent = uid;\n  eventRegistry[uid] = eventRegistry[uid] || {};\n  return eventRegistry[uid];\n}\nfunction bootstrapHandler(element, fn) {\n  return function handler(event) {\n    hydrateObj(event, {\n      delegateTarget: element\n    });\n    if (handler.oneOff) {\n      EventHandler.off(element, event.type, fn);\n    }\n    return fn.apply(element, [event]);\n  };\n}\nfunction bootstrapDelegationHandler(element, selector, fn) {\n  return function handler(event) {\n    const domElements = element.querySelectorAll(selector);\n    for (let {\n      target\n    } = event; target && target !== this; target = target.parentNode) {\n      for (const domElement of domElements) {\n        if (domElement !== target) {\n          continue;\n        }\n        hydrateObj(event, {\n          delegateTarget: target\n        });\n        if (handler.oneOff) {\n          EventHandler.off(element, event.type, selector, fn);\n        }\n        return fn.apply(target, [event]);\n      }\n    }\n  };\n}\nfunction findHandler(events, callable, delegationSelector = null) {\n  return Object.values(events).find(event => event.callable === callable && event.delegationSelector === delegationSelector);\n}\nfunction normalizeParameters(originalTypeEvent, handler, delegationFunction) {\n  const isDelegated = typeof handler === 'string';\n  // TODO: tooltip passes `false` instead of selector, so we need to check\n  const callable = isDelegated ? delegationFunction : handler || delegationFunction;\n  let typeEvent = getTypeEvent(originalTypeEvent);\n  if (!nativeEvents.has(typeEvent)) {\n    typeEvent = originalTypeEvent;\n  }\n  return [isDelegated, callable, typeEvent];\n}\nfunction addHandler(element, originalTypeEvent, handler, delegationFunction, oneOff) {\n  if (typeof originalTypeEvent !== 'string' || !element) {\n    return;\n  }\n  let [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction);\n\n  // in case of mouseenter or mouseleave wrap the handler within a function that checks for its DOM position\n  // this prevents the handler from being dispatched the same way as mouseover or mouseout does\n  if (originalTypeEvent in customEvents) {\n    const wrapFunction = fn => {\n      return function (event) {\n        if (!event.relatedTarget || event.relatedTarget !== event.delegateTarget && !event.delegateTarget.contains(event.relatedTarget)) {\n          return fn.call(this, event);\n        }\n      };\n    };\n    callable = wrapFunction(callable);\n  }\n  const events = getElementEvents(element);\n  const handlers = events[typeEvent] || (events[typeEvent] = {});\n  const previousFunction = findHandler(handlers, callable, isDelegated ? handler : null);\n  if (previousFunction) {\n    previousFunction.oneOff = previousFunction.oneOff && oneOff;\n    return;\n  }\n  const uid = makeEventUid(callable, originalTypeEvent.replace(namespaceRegex, ''));\n  const fn = isDelegated ? bootstrapDelegationHandler(element, handler, callable) : bootstrapHandler(element, callable);\n  fn.delegationSelector = isDelegated ? handler : null;\n  fn.callable = callable;\n  fn.oneOff = oneOff;\n  fn.uidEvent = uid;\n  handlers[uid] = fn;\n  element.addEventListener(typeEvent, fn, isDelegated);\n}\nfunction removeHandler(element, events, typeEvent, handler, delegationSelector) {\n  const fn = findHandler(events[typeEvent], handler, delegationSelector);\n  if (!fn) {\n    return;\n  }\n  element.removeEventListener(typeEvent, fn, Boolean(delegationSelector));\n  delete events[typeEvent][fn.uidEvent];\n}\nfunction removeNamespacedHandlers(element, events, typeEvent, namespace) {\n  const storeElementEvent = events[typeEvent] || {};\n  for (const [handlerKey, event] of Object.entries(storeElementEvent)) {\n    if (handlerKey.includes(namespace)) {\n      removeHandler(element, events, typeEvent, event.callable, event.delegationSelector);\n    }\n  }\n}\nfunction getTypeEvent(event) {\n  // allow to get the native events from namespaced events ('click.bs.button' --> 'click')\n  event = event.replace(stripNameRegex, '');\n  return customEvents[event] || event;\n}\nconst EventHandler = {\n  on(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, false);\n  },\n  one(element, event, handler, delegationFunction) {\n    addHandler(element, event, handler, delegationFunction, true);\n  },\n  off(element, originalTypeEvent, handler, delegationFunction) {\n    if (typeof originalTypeEvent !== 'string' || !element) {\n      return;\n    }\n    const [isDelegated, callable, typeEvent] = normalizeParameters(originalTypeEvent, handler, delegationFunction);\n    const inNamespace = typeEvent !== originalTypeEvent;\n    const events = getElementEvents(element);\n    const storeElementEvent = events[typeEvent] || {};\n    const isNamespace = originalTypeEvent.startsWith('.');\n    if (typeof callable !== 'undefined') {\n      // Simplest case: handler is passed, remove that listener ONLY.\n      if (!Object.keys(storeElementEvent).length) {\n        return;\n      }\n      removeHandler(element, events, typeEvent, callable, isDelegated ? handler : null);\n      return;\n    }\n    if (isNamespace) {\n      for (const elementEvent of Object.keys(events)) {\n        removeNamespacedHandlers(element, events, elementEvent, originalTypeEvent.slice(1));\n      }\n    }\n    for (const [keyHandlers, event] of Object.entries(storeElementEvent)) {\n      const handlerKey = keyHandlers.replace(stripUidRegex, '');\n      if (!inNamespace || originalTypeEvent.includes(handlerKey)) {\n        removeHandler(element, events, typeEvent, event.callable, event.delegationSelector);\n      }\n    }\n  },\n  trigger(element, event, args) {\n    if (typeof event !== 'string' || !element) {\n      return null;\n    }\n    const $ = getjQuery();\n    const typeEvent = getTypeEvent(event);\n    const inNamespace = event !== typeEvent;\n    let jQueryEvent = null;\n    let bubbles = true;\n    let nativeDispatch = true;\n    let defaultPrevented = false;\n    if (inNamespace && $) {\n      jQueryEvent = $.Event(event, args);\n      $(element).trigger(jQueryEvent);\n      bubbles = !jQueryEvent.isPropagationStopped();\n      nativeDispatch = !jQueryEvent.isImmediatePropagationStopped();\n      defaultPrevented = jQueryEvent.isDefaultPrevented();\n    }\n    const evt = hydrateObj(new Event(event, {\n      bubbles,\n      cancelable: true\n    }), args);\n    if (defaultPrevented) {\n      evt.preventDefault();\n    }\n    if (nativeDispatch) {\n      element.dispatchEvent(evt);\n    }\n    if (evt.defaultPrevented && jQueryEvent) {\n      jQueryEvent.preventDefault();\n    }\n    return evt;\n  }\n};\nfunction hydrateObj(obj, meta = {}) {\n  for (const [key, value] of Object.entries(meta)) {\n    try {\n      obj[key] = value;\n    } catch (_unused) {\n      Object.defineProperty(obj, key, {\n        configurable: true,\n        get() {\n          return value;\n        }\n      });\n    }\n  }\n  return obj;\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/manipulator.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nfunction normalizeData(value) {\n  if (value === 'true') {\n    return true;\n  }\n  if (value === 'false') {\n    return false;\n  }\n  if (value === Number(value).toString()) {\n    return Number(value);\n  }\n  if (value === '' || value === 'null') {\n    return null;\n  }\n  if (typeof value !== 'string') {\n    return value;\n  }\n  try {\n    return JSON.parse(decodeURIComponent(value));\n  } catch (_unused) {\n    return value;\n  }\n}\nfunction normalizeDataKey(key) {\n  return key.replace(/[A-Z]/g, chr => `-${chr.toLowerCase()}`);\n}\nconst Manipulator = {\n  setDataAttribute(element, key, value) {\n    element.setAttribute(`data-bs-${normalizeDataKey(key)}`, value);\n  },\n  removeDataAttribute(element, key) {\n    element.removeAttribute(`data-bs-${normalizeDataKey(key)}`);\n  },\n  getDataAttributes(element) {\n    if (!element) {\n      return {};\n    }\n    const attributes = {};\n    const bsKeys = Object.keys(element.dataset).filter(key => key.startsWith('bs') && !key.startsWith('bsConfig'));\n    for (const key of bsKeys) {\n      let pureKey = key.replace(/^bs/, '');\n      pureKey = pureKey.charAt(0).toLowerCase() + pureKey.slice(1, pureKey.length);\n      attributes[pureKey] = normalizeData(element.dataset[key]);\n    }\n    return attributes;\n  },\n  getDataAttribute(element, key) {\n    return normalizeData(element.getAttribute(`data-bs-${normalizeDataKey(key)}`));\n  }\n};\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/config.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Class definition\n */\n\nclass Config {\n  // Getters\n  static get Default() {\n    return {};\n  }\n  static get DefaultType() {\n    return {};\n  }\n  static get NAME() {\n    throw new Error('You have to implement the static method \"NAME\", for each component!');\n  }\n  _getConfig(config) {\n    config = this._mergeConfigObj(config);\n    config = this._configAfterMerge(config);\n    this._typeCheckConfig(config);\n    return config;\n  }\n  _configAfterMerge(config) {\n    return config;\n  }\n  _mergeConfigObj(config, element) {\n    const jsonConfig = isElement(element) ? Manipulator.getDataAttribute(element, 'config') : {}; // try to parse\n\n    return {\n      ...this.constructor.Default,\n      ...(typeof jsonConfig === 'object' ? jsonConfig : {}),\n      ...(isElement(element) ? Manipulator.getDataAttributes(element) : {}),\n      ...(typeof config === 'object' ? config : {})\n    };\n  }\n  _typeCheckConfig(config, configTypes = this.constructor.DefaultType) {\n    for (const [property, expectedTypes] of Object.entries(configTypes)) {\n      const value = config[property];\n      const valueType = isElement(value) ? 'element' : toType(value);\n      if (!new RegExp(expectedTypes).test(valueType)) {\n        throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option \"${property}\" provided type \"${valueType}\" but expected type \"${expectedTypes}\".`);\n      }\n    }\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap base-component.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst VERSION = '5.3.3';\n\n/**\n * Class definition\n */\n\nclass BaseComponent extends Config {\n  constructor(element, config) {\n    super();\n    element = getElement(element);\n    if (!element) {\n      return;\n    }\n    this._element = element;\n    this._config = this._getConfig(config);\n    Data.set(this._element, this.constructor.DATA_KEY, this);\n  }\n\n  // Public\n  dispose() {\n    Data.remove(this._element, this.constructor.DATA_KEY);\n    EventHandler.off(this._element, this.constructor.EVENT_KEY);\n    for (const propertyName of Object.getOwnPropertyNames(this)) {\n      this[propertyName] = null;\n    }\n  }\n  _queueCallback(callback, element, isAnimated = true) {\n    executeAfterTransition(callback, element, isAnimated);\n  }\n  _getConfig(config) {\n    config = this._mergeConfigObj(config, this._element);\n    config = this._configAfterMerge(config);\n    this._typeCheckConfig(config);\n    return config;\n  }\n\n  // Static\n  static getInstance(element) {\n    return Data.get(getElement(element), this.DATA_KEY);\n  }\n  static getOrCreateInstance(element, config = {}) {\n    return this.getInstance(element) || new this(element, typeof config === 'object' ? config : null);\n  }\n  static get VERSION() {\n    return VERSION;\n  }\n  static get DATA_KEY() {\n    return `bs.${this.NAME}`;\n  }\n  static get EVENT_KEY() {\n    return `.${this.DATA_KEY}`;\n  }\n  static eventName(name) {\n    return `${name}${this.EVENT_KEY}`;\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap dom/selector-engine.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst getSelector = element => {\n  let selector = element.getAttribute('data-bs-target');\n  if (!selector || selector === '#') {\n    let hrefAttribute = element.getAttribute('href');\n\n    // The only valid content that could double as a selector are IDs or classes,\n    // so everything starting with `#` or `.`. If a \"real\" URL is used as the selector,\n    // `document.querySelector` will rightfully complain it is invalid.\n    // See https://github.com/twbs/bootstrap/issues/32273\n    if (!hrefAttribute || !hrefAttribute.includes('#') && !hrefAttribute.startsWith('.')) {\n      return null;\n    }\n\n    // Just in case some CMS puts out a full URL with the anchor appended\n    if (hrefAttribute.includes('#') && !hrefAttribute.startsWith('#')) {\n      hrefAttribute = `#${hrefAttribute.split('#')[1]}`;\n    }\n    selector = hrefAttribute && hrefAttribute !== '#' ? hrefAttribute.trim() : null;\n  }\n  return selector ? selector.split(',').map(sel => parseSelector(sel)).join(',') : null;\n};\nconst SelectorEngine = {\n  find(selector, element = document.documentElement) {\n    return [].concat(...Element.prototype.querySelectorAll.call(element, selector));\n  },\n  findOne(selector, element = document.documentElement) {\n    return Element.prototype.querySelector.call(element, selector);\n  },\n  children(element, selector) {\n    return [].concat(...element.children).filter(child => child.matches(selector));\n  },\n  parents(element, selector) {\n    const parents = [];\n    let ancestor = element.parentNode.closest(selector);\n    while (ancestor) {\n      parents.push(ancestor);\n      ancestor = ancestor.parentNode.closest(selector);\n    }\n    return parents;\n  },\n  prev(element, selector) {\n    let previous = element.previousElementSibling;\n    while (previous) {\n      if (previous.matches(selector)) {\n        return [previous];\n      }\n      previous = previous.previousElementSibling;\n    }\n    return [];\n  },\n  // TODO: this is now unused; remove later along with prev()\n  next(element, selector) {\n    let next = element.nextElementSibling;\n    while (next) {\n      if (next.matches(selector)) {\n        return [next];\n      }\n      next = next.nextElementSibling;\n    }\n    return [];\n  },\n  focusableChildren(element) {\n    const focusables = ['a', 'button', 'input', 'textarea', 'select', 'details', '[tabindex]', '[contenteditable=\"true\"]'].map(selector => `${selector}:not([tabindex^=\"-\"])`).join(',');\n    return this.find(focusables, element).filter(el => !isDisabled(el) && isVisible(el));\n  },\n  getSelectorFromElement(element) {\n    const selector = getSelector(element);\n    if (selector) {\n      return SelectorEngine.findOne(selector) ? selector : null;\n    }\n    return null;\n  },\n  getElementFromSelector(element) {\n    const selector = getSelector(element);\n    return selector ? SelectorEngine.findOne(selector) : null;\n  },\n  getMultipleElementsFromSelector(element) {\n    const selector = getSelector(element);\n    return selector ? SelectorEngine.find(selector) : [];\n  }\n};\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/component-functions.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst enableDismissTrigger = (component, method = 'hide') => {\n  const clickEvent = `click.dismiss${component.EVENT_KEY}`;\n  const name = component.NAME;\n  EventHandler.on(document, clickEvent, `[data-bs-dismiss=\"${name}\"]`, function (event) {\n    if (['A', 'AREA'].includes(this.tagName)) {\n      event.preventDefault();\n    }\n    if (isDisabled(this)) {\n      return;\n    }\n    const target = SelectorEngine.getElementFromSelector(this) || this.closest(`.${name}`);\n    const instance = component.getOrCreateInstance(target);\n\n    // Method argument is left, for Alert and only, as it doesn't implement the 'hide' method\n    instance[method]();\n  });\n};\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$f = 'alert';\nconst DATA_KEY$a = 'bs.alert';\nconst EVENT_KEY$b = `.${DATA_KEY$a}`;\nconst EVENT_CLOSE = `close${EVENT_KEY$b}`;\nconst EVENT_CLOSED = `closed${EVENT_KEY$b}`;\nconst CLASS_NAME_FADE$5 = 'fade';\nconst CLASS_NAME_SHOW$8 = 'show';\n\n/**\n * Class definition\n */\n\nclass Alert extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME$f;\n  }\n\n  // Public\n  close() {\n    const closeEvent = EventHandler.trigger(this._element, EVENT_CLOSE);\n    if (closeEvent.defaultPrevented) {\n      return;\n    }\n    this._element.classList.remove(CLASS_NAME_SHOW$8);\n    const isAnimated = this._element.classList.contains(CLASS_NAME_FADE$5);\n    this._queueCallback(() => this._destroyElement(), this._element, isAnimated);\n  }\n\n  // Private\n  _destroyElement() {\n    this._element.remove();\n    EventHandler.trigger(this._element, EVENT_CLOSED);\n    this.dispose();\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Alert.getOrCreateInstance(this);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config](this);\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Alert, 'close');\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Alert);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$e = 'button';\nconst DATA_KEY$9 = 'bs.button';\nconst EVENT_KEY$a = `.${DATA_KEY$9}`;\nconst DATA_API_KEY$6 = '.data-api';\nconst CLASS_NAME_ACTIVE$3 = 'active';\nconst SELECTOR_DATA_TOGGLE$5 = '[data-bs-toggle=\"button\"]';\nconst EVENT_CLICK_DATA_API$6 = `click${EVENT_KEY$a}${DATA_API_KEY$6}`;\n\n/**\n * Class definition\n */\n\nclass Button extends BaseComponent {\n  // Getters\n  static get NAME() {\n    return NAME$e;\n  }\n\n  // Public\n  toggle() {\n    // Toggle class and sync the `aria-pressed` attribute with the return value of the `.toggle()` method\n    this._element.setAttribute('aria-pressed', this._element.classList.toggle(CLASS_NAME_ACTIVE$3));\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Button.getOrCreateInstance(this);\n      if (config === 'toggle') {\n        data[config]();\n      }\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API$6, SELECTOR_DATA_TOGGLE$5, event => {\n  event.preventDefault();\n  const button = event.target.closest(SELECTOR_DATA_TOGGLE$5);\n  const data = Button.getOrCreateInstance(button);\n  data.toggle();\n});\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Button);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/swipe.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$d = 'swipe';\nconst EVENT_KEY$9 = '.bs.swipe';\nconst EVENT_TOUCHSTART = `touchstart${EVENT_KEY$9}`;\nconst EVENT_TOUCHMOVE = `touchmove${EVENT_KEY$9}`;\nconst EVENT_TOUCHEND = `touchend${EVENT_KEY$9}`;\nconst EVENT_POINTERDOWN = `pointerdown${EVENT_KEY$9}`;\nconst EVENT_POINTERUP = `pointerup${EVENT_KEY$9}`;\nconst POINTER_TYPE_TOUCH = 'touch';\nconst POINTER_TYPE_PEN = 'pen';\nconst CLASS_NAME_POINTER_EVENT = 'pointer-event';\nconst SWIPE_THRESHOLD = 40;\nconst Default$c = {\n  endCallback: null,\n  leftCallback: null,\n  rightCallback: null\n};\nconst DefaultType$c = {\n  endCallback: '(function|null)',\n  leftCallback: '(function|null)',\n  rightCallback: '(function|null)'\n};\n\n/**\n * Class definition\n */\n\nclass Swipe extends Config {\n  constructor(element, config) {\n    super();\n    this._element = element;\n    if (!element || !Swipe.isSupported()) {\n      return;\n    }\n    this._config = this._getConfig(config);\n    this._deltaX = 0;\n    this._supportPointerEvents = Boolean(window.PointerEvent);\n    this._initEvents();\n  }\n\n  // Getters\n  static get Default() {\n    return Default$c;\n  }\n  static get DefaultType() {\n    return DefaultType$c;\n  }\n  static get NAME() {\n    return NAME$d;\n  }\n\n  // Public\n  dispose() {\n    EventHandler.off(this._element, EVENT_KEY$9);\n  }\n\n  // Private\n  _start(event) {\n    if (!this._supportPointerEvents) {\n      this._deltaX = event.touches[0].clientX;\n      return;\n    }\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX;\n    }\n  }\n  _end(event) {\n    if (this._eventIsPointerPenTouch(event)) {\n      this._deltaX = event.clientX - this._deltaX;\n    }\n    this._handleSwipe();\n    execute(this._config.endCallback);\n  }\n  _move(event) {\n    this._deltaX = event.touches && event.touches.length > 1 ? 0 : event.touches[0].clientX - this._deltaX;\n  }\n  _handleSwipe() {\n    const absDeltaX = Math.abs(this._deltaX);\n    if (absDeltaX <= SWIPE_THRESHOLD) {\n      return;\n    }\n    const direction = absDeltaX / this._deltaX;\n    this._deltaX = 0;\n    if (!direction) {\n      return;\n    }\n    execute(direction > 0 ? this._config.rightCallback : this._config.leftCallback);\n  }\n  _initEvents() {\n    if (this._supportPointerEvents) {\n      EventHandler.on(this._element, EVENT_POINTERDOWN, event => this._start(event));\n      EventHandler.on(this._element, EVENT_POINTERUP, event => this._end(event));\n      this._element.classList.add(CLASS_NAME_POINTER_EVENT);\n    } else {\n      EventHandler.on(this._element, EVENT_TOUCHSTART, event => this._start(event));\n      EventHandler.on(this._element, EVENT_TOUCHMOVE, event => this._move(event));\n      EventHandler.on(this._element, EVENT_TOUCHEND, event => this._end(event));\n    }\n  }\n  _eventIsPointerPenTouch(event) {\n    return this._supportPointerEvents && (event.pointerType === POINTER_TYPE_PEN || event.pointerType === POINTER_TYPE_TOUCH);\n  }\n\n  // Static\n  static isSupported() {\n    return 'ontouchstart' in document.documentElement || navigator.maxTouchPoints > 0;\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$c = 'carousel';\nconst DATA_KEY$8 = 'bs.carousel';\nconst EVENT_KEY$8 = `.${DATA_KEY$8}`;\nconst DATA_API_KEY$5 = '.data-api';\nconst ARROW_LEFT_KEY$1 = 'ArrowLeft';\nconst ARROW_RIGHT_KEY$1 = 'ArrowRight';\nconst TOUCHEVENT_COMPAT_WAIT = 500; // Time for mouse compat events to fire after touch\n\nconst ORDER_NEXT = 'next';\nconst ORDER_PREV = 'prev';\nconst DIRECTION_LEFT = 'left';\nconst DIRECTION_RIGHT = 'right';\nconst EVENT_SLIDE = `slide${EVENT_KEY$8}`;\nconst EVENT_SLID = `slid${EVENT_KEY$8}`;\nconst EVENT_KEYDOWN$1 = `keydown${EVENT_KEY$8}`;\nconst EVENT_MOUSEENTER$1 = `mouseenter${EVENT_KEY$8}`;\nconst EVENT_MOUSELEAVE$1 = `mouseleave${EVENT_KEY$8}`;\nconst EVENT_DRAG_START = `dragstart${EVENT_KEY$8}`;\nconst EVENT_LOAD_DATA_API$3 = `load${EVENT_KEY$8}${DATA_API_KEY$5}`;\nconst EVENT_CLICK_DATA_API$5 = `click${EVENT_KEY$8}${DATA_API_KEY$5}`;\nconst CLASS_NAME_CAROUSEL = 'carousel';\nconst CLASS_NAME_ACTIVE$2 = 'active';\nconst CLASS_NAME_SLIDE = 'slide';\nconst CLASS_NAME_END = 'carousel-item-end';\nconst CLASS_NAME_START = 'carousel-item-start';\nconst CLASS_NAME_NEXT = 'carousel-item-next';\nconst CLASS_NAME_PREV = 'carousel-item-prev';\nconst SELECTOR_ACTIVE = '.active';\nconst SELECTOR_ITEM = '.carousel-item';\nconst SELECTOR_ACTIVE_ITEM = SELECTOR_ACTIVE + SELECTOR_ITEM;\nconst SELECTOR_ITEM_IMG = '.carousel-item img';\nconst SELECTOR_INDICATORS = '.carousel-indicators';\nconst SELECTOR_DATA_SLIDE = '[data-bs-slide], [data-bs-slide-to]';\nconst SELECTOR_DATA_RIDE = '[data-bs-ride=\"carousel\"]';\nconst KEY_TO_DIRECTION = {\n  [ARROW_LEFT_KEY$1]: DIRECTION_RIGHT,\n  [ARROW_RIGHT_KEY$1]: DIRECTION_LEFT\n};\nconst Default$b = {\n  interval: 5000,\n  keyboard: true,\n  pause: 'hover',\n  ride: false,\n  touch: true,\n  wrap: true\n};\nconst DefaultType$b = {\n  interval: '(number|boolean)',\n  // TODO:v6 remove boolean support\n  keyboard: 'boolean',\n  pause: '(string|boolean)',\n  ride: '(boolean|string)',\n  touch: 'boolean',\n  wrap: 'boolean'\n};\n\n/**\n * Class definition\n */\n\nclass Carousel extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n    this._interval = null;\n    this._activeElement = null;\n    this._isSliding = false;\n    this.touchTimeout = null;\n    this._swipeHelper = null;\n    this._indicatorsElement = SelectorEngine.findOne(SELECTOR_INDICATORS, this._element);\n    this._addEventListeners();\n    if (this._config.ride === CLASS_NAME_CAROUSEL) {\n      this.cycle();\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default$b;\n  }\n  static get DefaultType() {\n    return DefaultType$b;\n  }\n  static get NAME() {\n    return NAME$c;\n  }\n\n  // Public\n  next() {\n    this._slide(ORDER_NEXT);\n  }\n  nextWhenVisible() {\n    // FIXME TODO use `document.visibilityState`\n    // Don't call next when the page isn't visible\n    // or the carousel or its parent isn't visible\n    if (!document.hidden && isVisible(this._element)) {\n      this.next();\n    }\n  }\n  prev() {\n    this._slide(ORDER_PREV);\n  }\n  pause() {\n    if (this._isSliding) {\n      triggerTransitionEnd(this._element);\n    }\n    this._clearInterval();\n  }\n  cycle() {\n    this._clearInterval();\n    this._updateInterval();\n    this._interval = setInterval(() => this.nextWhenVisible(), this._config.interval);\n  }\n  _maybeEnableCycle() {\n    if (!this._config.ride) {\n      return;\n    }\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.cycle());\n      return;\n    }\n    this.cycle();\n  }\n  to(index) {\n    const items = this._getItems();\n    if (index > items.length - 1 || index < 0) {\n      return;\n    }\n    if (this._isSliding) {\n      EventHandler.one(this._element, EVENT_SLID, () => this.to(index));\n      return;\n    }\n    const activeIndex = this._getItemIndex(this._getActive());\n    if (activeIndex === index) {\n      return;\n    }\n    const order = index > activeIndex ? ORDER_NEXT : ORDER_PREV;\n    this._slide(order, items[index]);\n  }\n  dispose() {\n    if (this._swipeHelper) {\n      this._swipeHelper.dispose();\n    }\n    super.dispose();\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.defaultInterval = config.interval;\n    return config;\n  }\n  _addEventListeners() {\n    if (this._config.keyboard) {\n      EventHandler.on(this._element, EVENT_KEYDOWN$1, event => this._keydown(event));\n    }\n    if (this._config.pause === 'hover') {\n      EventHandler.on(this._element, EVENT_MOUSEENTER$1, () => this.pause());\n      EventHandler.on(this._element, EVENT_MOUSELEAVE$1, () => this._maybeEnableCycle());\n    }\n    if (this._config.touch && Swipe.isSupported()) {\n      this._addTouchEventListeners();\n    }\n  }\n  _addTouchEventListeners() {\n    for (const img of SelectorEngine.find(SELECTOR_ITEM_IMG, this._element)) {\n      EventHandler.on(img, EVENT_DRAG_START, event => event.preventDefault());\n    }\n    const endCallBack = () => {\n      if (this._config.pause !== 'hover') {\n        return;\n      }\n\n      // If it's a touch-enabled device, mouseenter/leave are fired as\n      // part of the mouse compatibility events on first tap - the carousel\n      // would stop cycling until user tapped out of it;\n      // here, we listen for touchend, explicitly pause the carousel\n      // (as if it's the second time we tap on it, mouseenter compat event\n      // is NOT fired) and after a timeout (to allow for mouse compatibility\n      // events to fire) we explicitly restart cycling\n\n      this.pause();\n      if (this.touchTimeout) {\n        clearTimeout(this.touchTimeout);\n      }\n      this.touchTimeout = setTimeout(() => this._maybeEnableCycle(), TOUCHEVENT_COMPAT_WAIT + this._config.interval);\n    };\n    const swipeConfig = {\n      leftCallback: () => this._slide(this._directionToOrder(DIRECTION_LEFT)),\n      rightCallback: () => this._slide(this._directionToOrder(DIRECTION_RIGHT)),\n      endCallback: endCallBack\n    };\n    this._swipeHelper = new Swipe(this._element, swipeConfig);\n  }\n  _keydown(event) {\n    if (/input|textarea/i.test(event.target.tagName)) {\n      return;\n    }\n    const direction = KEY_TO_DIRECTION[event.key];\n    if (direction) {\n      event.preventDefault();\n      this._slide(this._directionToOrder(direction));\n    }\n  }\n  _getItemIndex(element) {\n    return this._getItems().indexOf(element);\n  }\n  _setActiveIndicatorElement(index) {\n    if (!this._indicatorsElement) {\n      return;\n    }\n    const activeIndicator = SelectorEngine.findOne(SELECTOR_ACTIVE, this._indicatorsElement);\n    activeIndicator.classList.remove(CLASS_NAME_ACTIVE$2);\n    activeIndicator.removeAttribute('aria-current');\n    const newActiveIndicator = SelectorEngine.findOne(`[data-bs-slide-to=\"${index}\"]`, this._indicatorsElement);\n    if (newActiveIndicator) {\n      newActiveIndicator.classList.add(CLASS_NAME_ACTIVE$2);\n      newActiveIndicator.setAttribute('aria-current', 'true');\n    }\n  }\n  _updateInterval() {\n    const element = this._activeElement || this._getActive();\n    if (!element) {\n      return;\n    }\n    const elementInterval = Number.parseInt(element.getAttribute('data-bs-interval'), 10);\n    this._config.interval = elementInterval || this._config.defaultInterval;\n  }\n  _slide(order, element = null) {\n    if (this._isSliding) {\n      return;\n    }\n    const activeElement = this._getActive();\n    const isNext = order === ORDER_NEXT;\n    const nextElement = element || getNextActiveElement(this._getItems(), activeElement, isNext, this._config.wrap);\n    if (nextElement === activeElement) {\n      return;\n    }\n    const nextElementIndex = this._getItemIndex(nextElement);\n    const triggerEvent = eventName => {\n      return EventHandler.trigger(this._element, eventName, {\n        relatedTarget: nextElement,\n        direction: this._orderToDirection(order),\n        from: this._getItemIndex(activeElement),\n        to: nextElementIndex\n      });\n    };\n    const slideEvent = triggerEvent(EVENT_SLIDE);\n    if (slideEvent.defaultPrevented) {\n      return;\n    }\n    if (!activeElement || !nextElement) {\n      // Some weirdness is happening, so we bail\n      // TODO: change tests that use empty divs to avoid this check\n      return;\n    }\n    const isCycling = Boolean(this._interval);\n    this.pause();\n    this._isSliding = true;\n    this._setActiveIndicatorElement(nextElementIndex);\n    this._activeElement = nextElement;\n    const directionalClassName = isNext ? CLASS_NAME_START : CLASS_NAME_END;\n    const orderClassName = isNext ? CLASS_NAME_NEXT : CLASS_NAME_PREV;\n    nextElement.classList.add(orderClassName);\n    reflow(nextElement);\n    activeElement.classList.add(directionalClassName);\n    nextElement.classList.add(directionalClassName);\n    const completeCallBack = () => {\n      nextElement.classList.remove(directionalClassName, orderClassName);\n      nextElement.classList.add(CLASS_NAME_ACTIVE$2);\n      activeElement.classList.remove(CLASS_NAME_ACTIVE$2, orderClassName, directionalClassName);\n      this._isSliding = false;\n      triggerEvent(EVENT_SLID);\n    };\n    this._queueCallback(completeCallBack, activeElement, this._isAnimated());\n    if (isCycling) {\n      this.cycle();\n    }\n  }\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_SLIDE);\n  }\n  _getActive() {\n    return SelectorEngine.findOne(SELECTOR_ACTIVE_ITEM, this._element);\n  }\n  _getItems() {\n    return SelectorEngine.find(SELECTOR_ITEM, this._element);\n  }\n  _clearInterval() {\n    if (this._interval) {\n      clearInterval(this._interval);\n      this._interval = null;\n    }\n  }\n  _directionToOrder(direction) {\n    if (isRTL()) {\n      return direction === DIRECTION_LEFT ? ORDER_PREV : ORDER_NEXT;\n    }\n    return direction === DIRECTION_LEFT ? ORDER_NEXT : ORDER_PREV;\n  }\n  _orderToDirection(order) {\n    if (isRTL()) {\n      return order === ORDER_PREV ? DIRECTION_LEFT : DIRECTION_RIGHT;\n    }\n    return order === ORDER_PREV ? DIRECTION_RIGHT : DIRECTION_LEFT;\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Carousel.getOrCreateInstance(this, config);\n      if (typeof config === 'number') {\n        data.to(config);\n        return;\n      }\n      if (typeof config === 'string') {\n        if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n          throw new TypeError(`No method named \"${config}\"`);\n        }\n        data[config]();\n      }\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API$5, SELECTOR_DATA_SLIDE, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this);\n  if (!target || !target.classList.contains(CLASS_NAME_CAROUSEL)) {\n    return;\n  }\n  event.preventDefault();\n  const carousel = Carousel.getOrCreateInstance(target);\n  const slideIndex = this.getAttribute('data-bs-slide-to');\n  if (slideIndex) {\n    carousel.to(slideIndex);\n    carousel._maybeEnableCycle();\n    return;\n  }\n  if (Manipulator.getDataAttribute(this, 'slide') === 'next') {\n    carousel.next();\n    carousel._maybeEnableCycle();\n    return;\n  }\n  carousel.prev();\n  carousel._maybeEnableCycle();\n});\nEventHandler.on(window, EVENT_LOAD_DATA_API$3, () => {\n  const carousels = SelectorEngine.find(SELECTOR_DATA_RIDE);\n  for (const carousel of carousels) {\n    Carousel.getOrCreateInstance(carousel);\n  }\n});\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Carousel);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$b = 'collapse';\nconst DATA_KEY$7 = 'bs.collapse';\nconst EVENT_KEY$7 = `.${DATA_KEY$7}`;\nconst DATA_API_KEY$4 = '.data-api';\nconst EVENT_SHOW$6 = `show${EVENT_KEY$7}`;\nconst EVENT_SHOWN$6 = `shown${EVENT_KEY$7}`;\nconst EVENT_HIDE$6 = `hide${EVENT_KEY$7}`;\nconst EVENT_HIDDEN$6 = `hidden${EVENT_KEY$7}`;\nconst EVENT_CLICK_DATA_API$4 = `click${EVENT_KEY$7}${DATA_API_KEY$4}`;\nconst CLASS_NAME_SHOW$7 = 'show';\nconst CLASS_NAME_COLLAPSE = 'collapse';\nconst CLASS_NAME_COLLAPSING = 'collapsing';\nconst CLASS_NAME_COLLAPSED = 'collapsed';\nconst CLASS_NAME_DEEPER_CHILDREN = `:scope .${CLASS_NAME_COLLAPSE} .${CLASS_NAME_COLLAPSE}`;\nconst CLASS_NAME_HORIZONTAL = 'collapse-horizontal';\nconst WIDTH = 'width';\nconst HEIGHT = 'height';\nconst SELECTOR_ACTIVES = '.collapse.show, .collapse.collapsing';\nconst SELECTOR_DATA_TOGGLE$4 = '[data-bs-toggle=\"collapse\"]';\nconst Default$a = {\n  parent: null,\n  toggle: true\n};\nconst DefaultType$a = {\n  parent: '(null|element)',\n  toggle: 'boolean'\n};\n\n/**\n * Class definition\n */\n\nclass Collapse extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n    this._isTransitioning = false;\n    this._triggerArray = [];\n    const toggleList = SelectorEngine.find(SELECTOR_DATA_TOGGLE$4);\n    for (const elem of toggleList) {\n      const selector = SelectorEngine.getSelectorFromElement(elem);\n      const filterElement = SelectorEngine.find(selector).filter(foundElement => foundElement === this._element);\n      if (selector !== null && filterElement.length) {\n        this._triggerArray.push(elem);\n      }\n    }\n    this._initializeChildren();\n    if (!this._config.parent) {\n      this._addAriaAndCollapsedClass(this._triggerArray, this._isShown());\n    }\n    if (this._config.toggle) {\n      this.toggle();\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default$a;\n  }\n  static get DefaultType() {\n    return DefaultType$a;\n  }\n  static get NAME() {\n    return NAME$b;\n  }\n\n  // Public\n  toggle() {\n    if (this._isShown()) {\n      this.hide();\n    } else {\n      this.show();\n    }\n  }\n  show() {\n    if (this._isTransitioning || this._isShown()) {\n      return;\n    }\n    let activeChildren = [];\n\n    // find active children\n    if (this._config.parent) {\n      activeChildren = this._getFirstLevelChildren(SELECTOR_ACTIVES).filter(element => element !== this._element).map(element => Collapse.getOrCreateInstance(element, {\n        toggle: false\n      }));\n    }\n    if (activeChildren.length && activeChildren[0]._isTransitioning) {\n      return;\n    }\n    const startEvent = EventHandler.trigger(this._element, EVENT_SHOW$6);\n    if (startEvent.defaultPrevented) {\n      return;\n    }\n    for (const activeInstance of activeChildren) {\n      activeInstance.hide();\n    }\n    const dimension = this._getDimension();\n    this._element.classList.remove(CLASS_NAME_COLLAPSE);\n    this._element.classList.add(CLASS_NAME_COLLAPSING);\n    this._element.style[dimension] = 0;\n    this._addAriaAndCollapsedClass(this._triggerArray, true);\n    this._isTransitioning = true;\n    const complete = () => {\n      this._isTransitioning = false;\n      this._element.classList.remove(CLASS_NAME_COLLAPSING);\n      this._element.classList.add(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW$7);\n      this._element.style[dimension] = '';\n      EventHandler.trigger(this._element, EVENT_SHOWN$6);\n    };\n    const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1);\n    const scrollSize = `scroll${capitalizedDimension}`;\n    this._queueCallback(complete, this._element, true);\n    this._element.style[dimension] = `${this._element[scrollSize]}px`;\n  }\n  hide() {\n    if (this._isTransitioning || !this._isShown()) {\n      return;\n    }\n    const startEvent = EventHandler.trigger(this._element, EVENT_HIDE$6);\n    if (startEvent.defaultPrevented) {\n      return;\n    }\n    const dimension = this._getDimension();\n    this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`;\n    reflow(this._element);\n    this._element.classList.add(CLASS_NAME_COLLAPSING);\n    this._element.classList.remove(CLASS_NAME_COLLAPSE, CLASS_NAME_SHOW$7);\n    for (const trigger of this._triggerArray) {\n      const element = SelectorEngine.getElementFromSelector(trigger);\n      if (element && !this._isShown(element)) {\n        this._addAriaAndCollapsedClass([trigger], false);\n      }\n    }\n    this._isTransitioning = true;\n    const complete = () => {\n      this._isTransitioning = false;\n      this._element.classList.remove(CLASS_NAME_COLLAPSING);\n      this._element.classList.add(CLASS_NAME_COLLAPSE);\n      EventHandler.trigger(this._element, EVENT_HIDDEN$6);\n    };\n    this._element.style[dimension] = '';\n    this._queueCallback(complete, this._element, true);\n  }\n  _isShown(element = this._element) {\n    return element.classList.contains(CLASS_NAME_SHOW$7);\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    config.toggle = Boolean(config.toggle); // Coerce string values\n    config.parent = getElement(config.parent);\n    return config;\n  }\n  _getDimension() {\n    return this._element.classList.contains(CLASS_NAME_HORIZONTAL) ? WIDTH : HEIGHT;\n  }\n  _initializeChildren() {\n    if (!this._config.parent) {\n      return;\n    }\n    const children = this._getFirstLevelChildren(SELECTOR_DATA_TOGGLE$4);\n    for (const element of children) {\n      const selected = SelectorEngine.getElementFromSelector(element);\n      if (selected) {\n        this._addAriaAndCollapsedClass([element], this._isShown(selected));\n      }\n    }\n  }\n  _getFirstLevelChildren(selector) {\n    const children = SelectorEngine.find(CLASS_NAME_DEEPER_CHILDREN, this._config.parent);\n    // remove children if greater depth\n    return SelectorEngine.find(selector, this._config.parent).filter(element => !children.includes(element));\n  }\n  _addAriaAndCollapsedClass(triggerArray, isOpen) {\n    if (!triggerArray.length) {\n      return;\n    }\n    for (const element of triggerArray) {\n      element.classList.toggle(CLASS_NAME_COLLAPSED, !isOpen);\n      element.setAttribute('aria-expanded', isOpen);\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    const _config = {};\n    if (typeof config === 'string' && /show|hide/.test(config)) {\n      _config.toggle = false;\n    }\n    return this.each(function () {\n      const data = Collapse.getOrCreateInstance(this, _config);\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`);\n        }\n        data[config]();\n      }\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API$4, SELECTOR_DATA_TOGGLE$4, function (event) {\n  // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n  if (event.target.tagName === 'A' || event.delegateTarget && event.delegateTarget.tagName === 'A') {\n    event.preventDefault();\n  }\n  for (const element of SelectorEngine.getMultipleElementsFromSelector(this)) {\n    Collapse.getOrCreateInstance(element, {\n      toggle: false\n    }).toggle();\n  }\n});\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Collapse);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$a = 'dropdown';\nconst DATA_KEY$6 = 'bs.dropdown';\nconst EVENT_KEY$6 = `.${DATA_KEY$6}`;\nconst DATA_API_KEY$3 = '.data-api';\nconst ESCAPE_KEY$2 = 'Escape';\nconst TAB_KEY$1 = 'Tab';\nconst ARROW_UP_KEY$1 = 'ArrowUp';\nconst ARROW_DOWN_KEY$1 = 'ArrowDown';\nconst RIGHT_MOUSE_BUTTON = 2; // MouseEvent.button value for the secondary button, usually the right button\n\nconst EVENT_HIDE$5 = `hide${EVENT_KEY$6}`;\nconst EVENT_HIDDEN$5 = `hidden${EVENT_KEY$6}`;\nconst EVENT_SHOW$5 = `show${EVENT_KEY$6}`;\nconst EVENT_SHOWN$5 = `shown${EVENT_KEY$6}`;\nconst EVENT_CLICK_DATA_API$3 = `click${EVENT_KEY$6}${DATA_API_KEY$3}`;\nconst EVENT_KEYDOWN_DATA_API = `keydown${EVENT_KEY$6}${DATA_API_KEY$3}`;\nconst EVENT_KEYUP_DATA_API = `keyup${EVENT_KEY$6}${DATA_API_KEY$3}`;\nconst CLASS_NAME_SHOW$6 = 'show';\nconst CLASS_NAME_DROPUP = 'dropup';\nconst CLASS_NAME_DROPEND = 'dropend';\nconst CLASS_NAME_DROPSTART = 'dropstart';\nconst CLASS_NAME_DROPUP_CENTER = 'dropup-center';\nconst CLASS_NAME_DROPDOWN_CENTER = 'dropdown-center';\nconst SELECTOR_DATA_TOGGLE$3 = '[data-bs-toggle=\"dropdown\"]:not(.disabled):not(:disabled)';\nconst SELECTOR_DATA_TOGGLE_SHOWN = `${SELECTOR_DATA_TOGGLE$3}.${CLASS_NAME_SHOW$6}`;\nconst SELECTOR_MENU = '.dropdown-menu';\nconst SELECTOR_NAVBAR = '.navbar';\nconst SELECTOR_NAVBAR_NAV = '.navbar-nav';\nconst SELECTOR_VISIBLE_ITEMS = '.dropdown-menu .dropdown-item:not(.disabled):not(:disabled)';\nconst PLACEMENT_TOP = isRTL() ? 'top-end' : 'top-start';\nconst PLACEMENT_TOPEND = isRTL() ? 'top-start' : 'top-end';\nconst PLACEMENT_BOTTOM = isRTL() ? 'bottom-end' : 'bottom-start';\nconst PLACEMENT_BOTTOMEND = isRTL() ? 'bottom-start' : 'bottom-end';\nconst PLACEMENT_RIGHT = isRTL() ? 'left-start' : 'right-start';\nconst PLACEMENT_LEFT = isRTL() ? 'right-start' : 'left-start';\nconst PLACEMENT_TOPCENTER = 'top';\nconst PLACEMENT_BOTTOMCENTER = 'bottom';\nconst Default$9 = {\n  autoClose: true,\n  boundary: 'clippingParents',\n  display: 'dynamic',\n  offset: [0, 2],\n  popperConfig: null,\n  reference: 'toggle'\n};\nconst DefaultType$9 = {\n  autoClose: '(boolean|string)',\n  boundary: '(string|element)',\n  display: 'string',\n  offset: '(array|string|function)',\n  popperConfig: '(null|object|function)',\n  reference: '(string|element|object)'\n};\n\n/**\n * Class definition\n */\n\nclass Dropdown extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n    this._popper = null;\n    this._parent = this._element.parentNode; // dropdown wrapper\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    this._menu = SelectorEngine.next(this._element, SELECTOR_MENU)[0] || SelectorEngine.prev(this._element, SELECTOR_MENU)[0] || SelectorEngine.findOne(SELECTOR_MENU, this._parent);\n    this._inNavbar = this._detectNavbar();\n  }\n\n  // Getters\n  static get Default() {\n    return Default$9;\n  }\n  static get DefaultType() {\n    return DefaultType$9;\n  }\n  static get NAME() {\n    return NAME$a;\n  }\n\n  // Public\n  toggle() {\n    return this._isShown() ? this.hide() : this.show();\n  }\n  show() {\n    if (isDisabled(this._element) || this._isShown()) {\n      return;\n    }\n    const relatedTarget = {\n      relatedTarget: this._element\n    };\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW$5, relatedTarget);\n    if (showEvent.defaultPrevented) {\n      return;\n    }\n    this._createPopper();\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement && !this._parent.closest(SELECTOR_NAVBAR_NAV)) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop);\n      }\n    }\n    this._element.focus();\n    this._element.setAttribute('aria-expanded', true);\n    this._menu.classList.add(CLASS_NAME_SHOW$6);\n    this._element.classList.add(CLASS_NAME_SHOW$6);\n    EventHandler.trigger(this._element, EVENT_SHOWN$5, relatedTarget);\n  }\n  hide() {\n    if (isDisabled(this._element) || !this._isShown()) {\n      return;\n    }\n    const relatedTarget = {\n      relatedTarget: this._element\n    };\n    this._completeHide(relatedTarget);\n  }\n  dispose() {\n    if (this._popper) {\n      this._popper.destroy();\n    }\n    super.dispose();\n  }\n  update() {\n    this._inNavbar = this._detectNavbar();\n    if (this._popper) {\n      this._popper.update();\n    }\n  }\n\n  // Private\n  _completeHide(relatedTarget) {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE$5, relatedTarget);\n    if (hideEvent.defaultPrevented) {\n      return;\n    }\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop);\n      }\n    }\n    if (this._popper) {\n      this._popper.destroy();\n    }\n    this._menu.classList.remove(CLASS_NAME_SHOW$6);\n    this._element.classList.remove(CLASS_NAME_SHOW$6);\n    this._element.setAttribute('aria-expanded', 'false');\n    Manipulator.removeDataAttribute(this._menu, 'popper');\n    EventHandler.trigger(this._element, EVENT_HIDDEN$5, relatedTarget);\n  }\n  _getConfig(config) {\n    config = super._getConfig(config);\n    if (typeof config.reference === 'object' && !isElement(config.reference) && typeof config.reference.getBoundingClientRect !== 'function') {\n      // Popper virtual elements require a getBoundingClientRect method\n      throw new TypeError(`${NAME$a.toUpperCase()}: Option \"reference\" provided type \"object\" without a required \"getBoundingClientRect\" method.`);\n    }\n    return config;\n  }\n  _createPopper() {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s dropdowns require Popper (https://popper.js.org)');\n    }\n    let referenceElement = this._element;\n    if (this._config.reference === 'parent') {\n      referenceElement = this._parent;\n    } else if (isElement(this._config.reference)) {\n      referenceElement = getElement(this._config.reference);\n    } else if (typeof this._config.reference === 'object') {\n      referenceElement = this._config.reference;\n    }\n    const popperConfig = this._getPopperConfig();\n    this._popper = Popper.createPopper(referenceElement, this._menu, popperConfig);\n  }\n  _isShown() {\n    return this._menu.classList.contains(CLASS_NAME_SHOW$6);\n  }\n  _getPlacement() {\n    const parentDropdown = this._parent;\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPEND)) {\n      return PLACEMENT_RIGHT;\n    }\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPSTART)) {\n      return PLACEMENT_LEFT;\n    }\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP_CENTER)) {\n      return PLACEMENT_TOPCENTER;\n    }\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPDOWN_CENTER)) {\n      return PLACEMENT_BOTTOMCENTER;\n    }\n\n    // We need to trim the value because custom properties can also include spaces\n    const isEnd = getComputedStyle(this._menu).getPropertyValue('--bs-position').trim() === 'end';\n    if (parentDropdown.classList.contains(CLASS_NAME_DROPUP)) {\n      return isEnd ? PLACEMENT_TOPEND : PLACEMENT_TOP;\n    }\n    return isEnd ? PLACEMENT_BOTTOMEND : PLACEMENT_BOTTOM;\n  }\n  _detectNavbar() {\n    return this._element.closest(SELECTOR_NAVBAR) !== null;\n  }\n  _getOffset() {\n    const {\n      offset\n    } = this._config;\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10));\n    }\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element);\n    }\n    return offset;\n  }\n  _getPopperConfig() {\n    const defaultBsPopperConfig = {\n      placement: this._getPlacement(),\n      modifiers: [{\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      }, {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }]\n    };\n\n    // Disable Popper if we have a static display or Dropdown is in Navbar\n    if (this._inNavbar || this._config.display === 'static') {\n      Manipulator.setDataAttribute(this._menu, 'popper', 'static'); // TODO: v6 remove\n      defaultBsPopperConfig.modifiers = [{\n        name: 'applyStyles',\n        enabled: false\n      }];\n    }\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    };\n  }\n  _selectMenuItem({\n    key,\n    target\n  }) {\n    const items = SelectorEngine.find(SELECTOR_VISIBLE_ITEMS, this._menu).filter(element => isVisible(element));\n    if (!items.length) {\n      return;\n    }\n\n    // if target isn't included in items (e.g. when expanding the dropdown)\n    // allow cycling to get the last item in case key equals ARROW_UP_KEY\n    getNextActiveElement(items, target, key === ARROW_DOWN_KEY$1, !items.includes(target)).focus();\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Dropdown.getOrCreateInstance(this, config);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config]();\n    });\n  }\n  static clearMenus(event) {\n    if (event.button === RIGHT_MOUSE_BUTTON || event.type === 'keyup' && event.key !== TAB_KEY$1) {\n      return;\n    }\n    const openToggles = SelectorEngine.find(SELECTOR_DATA_TOGGLE_SHOWN);\n    for (const toggle of openToggles) {\n      const context = Dropdown.getInstance(toggle);\n      if (!context || context._config.autoClose === false) {\n        continue;\n      }\n      const composedPath = event.composedPath();\n      const isMenuTarget = composedPath.includes(context._menu);\n      if (composedPath.includes(context._element) || context._config.autoClose === 'inside' && !isMenuTarget || context._config.autoClose === 'outside' && isMenuTarget) {\n        continue;\n      }\n\n      // Tab navigation through the dropdown menu or events from contained inputs shouldn't close the menu\n      if (context._menu.contains(event.target) && (event.type === 'keyup' && event.key === TAB_KEY$1 || /input|select|option|textarea|form/i.test(event.target.tagName))) {\n        continue;\n      }\n      const relatedTarget = {\n        relatedTarget: context._element\n      };\n      if (event.type === 'click') {\n        relatedTarget.clickEvent = event;\n      }\n      context._completeHide(relatedTarget);\n    }\n  }\n  static dataApiKeydownHandler(event) {\n    // If not an UP | DOWN | ESCAPE key => not a dropdown command\n    // If input/textarea && if key is other than ESCAPE => not a dropdown command\n\n    const isInput = /input|textarea/i.test(event.target.tagName);\n    const isEscapeEvent = event.key === ESCAPE_KEY$2;\n    const isUpOrDownEvent = [ARROW_UP_KEY$1, ARROW_DOWN_KEY$1].includes(event.key);\n    if (!isUpOrDownEvent && !isEscapeEvent) {\n      return;\n    }\n    if (isInput && !isEscapeEvent) {\n      return;\n    }\n    event.preventDefault();\n\n    // TODO: v6 revert #37011 & change markup https://getbootstrap.com/docs/5.3/forms/input-group/\n    const getToggleButton = this.matches(SELECTOR_DATA_TOGGLE$3) ? this : SelectorEngine.prev(this, SELECTOR_DATA_TOGGLE$3)[0] || SelectorEngine.next(this, SELECTOR_DATA_TOGGLE$3)[0] || SelectorEngine.findOne(SELECTOR_DATA_TOGGLE$3, event.delegateTarget.parentNode);\n    const instance = Dropdown.getOrCreateInstance(getToggleButton);\n    if (isUpOrDownEvent) {\n      event.stopPropagation();\n      instance.show();\n      instance._selectMenuItem(event);\n      return;\n    }\n    if (instance._isShown()) {\n      // else is escape and we check if it is shown\n      event.stopPropagation();\n      instance.hide();\n      getToggleButton.focus();\n    }\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_DATA_TOGGLE$3, Dropdown.dataApiKeydownHandler);\nEventHandler.on(document, EVENT_KEYDOWN_DATA_API, SELECTOR_MENU, Dropdown.dataApiKeydownHandler);\nEventHandler.on(document, EVENT_CLICK_DATA_API$3, Dropdown.clearMenus);\nEventHandler.on(document, EVENT_KEYUP_DATA_API, Dropdown.clearMenus);\nEventHandler.on(document, EVENT_CLICK_DATA_API$3, SELECTOR_DATA_TOGGLE$3, function (event) {\n  event.preventDefault();\n  Dropdown.getOrCreateInstance(this).toggle();\n});\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Dropdown);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/backdrop.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$9 = 'backdrop';\nconst CLASS_NAME_FADE$4 = 'fade';\nconst CLASS_NAME_SHOW$5 = 'show';\nconst EVENT_MOUSEDOWN = `mousedown.bs.${NAME$9}`;\nconst Default$8 = {\n  className: 'modal-backdrop',\n  clickCallback: null,\n  isAnimated: false,\n  isVisible: true,\n  // if false, we use the backdrop helper without adding any element to the dom\n  rootElement: 'body' // give the choice to place backdrop under different elements\n};\nconst DefaultType$8 = {\n  className: 'string',\n  clickCallback: '(function|null)',\n  isAnimated: 'boolean',\n  isVisible: 'boolean',\n  rootElement: '(element|string)'\n};\n\n/**\n * Class definition\n */\n\nclass Backdrop extends Config {\n  constructor(config) {\n    super();\n    this._config = this._getConfig(config);\n    this._isAppended = false;\n    this._element = null;\n  }\n\n  // Getters\n  static get Default() {\n    return Default$8;\n  }\n  static get DefaultType() {\n    return DefaultType$8;\n  }\n  static get NAME() {\n    return NAME$9;\n  }\n\n  // Public\n  show(callback) {\n    if (!this._config.isVisible) {\n      execute(callback);\n      return;\n    }\n    this._append();\n    const element = this._getElement();\n    if (this._config.isAnimated) {\n      reflow(element);\n    }\n    element.classList.add(CLASS_NAME_SHOW$5);\n    this._emulateAnimation(() => {\n      execute(callback);\n    });\n  }\n  hide(callback) {\n    if (!this._config.isVisible) {\n      execute(callback);\n      return;\n    }\n    this._getElement().classList.remove(CLASS_NAME_SHOW$5);\n    this._emulateAnimation(() => {\n      this.dispose();\n      execute(callback);\n    });\n  }\n  dispose() {\n    if (!this._isAppended) {\n      return;\n    }\n    EventHandler.off(this._element, EVENT_MOUSEDOWN);\n    this._element.remove();\n    this._isAppended = false;\n  }\n\n  // Private\n  _getElement() {\n    if (!this._element) {\n      const backdrop = document.createElement('div');\n      backdrop.className = this._config.className;\n      if (this._config.isAnimated) {\n        backdrop.classList.add(CLASS_NAME_FADE$4);\n      }\n      this._element = backdrop;\n    }\n    return this._element;\n  }\n  _configAfterMerge(config) {\n    // use getElement() with the default \"body\" to get a fresh Element on each instantiation\n    config.rootElement = getElement(config.rootElement);\n    return config;\n  }\n  _append() {\n    if (this._isAppended) {\n      return;\n    }\n    const element = this._getElement();\n    this._config.rootElement.append(element);\n    EventHandler.on(element, EVENT_MOUSEDOWN, () => {\n      execute(this._config.clickCallback);\n    });\n    this._isAppended = true;\n  }\n  _emulateAnimation(callback) {\n    executeAfterTransition(callback, this._getElement(), this._config.isAnimated);\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/focustrap.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$8 = 'focustrap';\nconst DATA_KEY$5 = 'bs.focustrap';\nconst EVENT_KEY$5 = `.${DATA_KEY$5}`;\nconst EVENT_FOCUSIN$2 = `focusin${EVENT_KEY$5}`;\nconst EVENT_KEYDOWN_TAB = `keydown.tab${EVENT_KEY$5}`;\nconst TAB_KEY = 'Tab';\nconst TAB_NAV_FORWARD = 'forward';\nconst TAB_NAV_BACKWARD = 'backward';\nconst Default$7 = {\n  autofocus: true,\n  trapElement: null // The element to trap focus inside of\n};\nconst DefaultType$7 = {\n  autofocus: 'boolean',\n  trapElement: 'element'\n};\n\n/**\n * Class definition\n */\n\nclass FocusTrap extends Config {\n  constructor(config) {\n    super();\n    this._config = this._getConfig(config);\n    this._isActive = false;\n    this._lastTabNavDirection = null;\n  }\n\n  // Getters\n  static get Default() {\n    return Default$7;\n  }\n  static get DefaultType() {\n    return DefaultType$7;\n  }\n  static get NAME() {\n    return NAME$8;\n  }\n\n  // Public\n  activate() {\n    if (this._isActive) {\n      return;\n    }\n    if (this._config.autofocus) {\n      this._config.trapElement.focus();\n    }\n    EventHandler.off(document, EVENT_KEY$5); // guard against infinite focus loop\n    EventHandler.on(document, EVENT_FOCUSIN$2, event => this._handleFocusin(event));\n    EventHandler.on(document, EVENT_KEYDOWN_TAB, event => this._handleKeydown(event));\n    this._isActive = true;\n  }\n  deactivate() {\n    if (!this._isActive) {\n      return;\n    }\n    this._isActive = false;\n    EventHandler.off(document, EVENT_KEY$5);\n  }\n\n  // Private\n  _handleFocusin(event) {\n    const {\n      trapElement\n    } = this._config;\n    if (event.target === document || event.target === trapElement || trapElement.contains(event.target)) {\n      return;\n    }\n    const elements = SelectorEngine.focusableChildren(trapElement);\n    if (elements.length === 0) {\n      trapElement.focus();\n    } else if (this._lastTabNavDirection === TAB_NAV_BACKWARD) {\n      elements[elements.length - 1].focus();\n    } else {\n      elements[0].focus();\n    }\n  }\n  _handleKeydown(event) {\n    if (event.key !== TAB_KEY) {\n      return;\n    }\n    this._lastTabNavDirection = event.shiftKey ? TAB_NAV_BACKWARD : TAB_NAV_FORWARD;\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/scrollBar.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst SELECTOR_FIXED_CONTENT = '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top';\nconst SELECTOR_STICKY_CONTENT = '.sticky-top';\nconst PROPERTY_PADDING = 'padding-right';\nconst PROPERTY_MARGIN = 'margin-right';\n\n/**\n * Class definition\n */\n\nclass ScrollBarHelper {\n  constructor() {\n    this._element = document.body;\n  }\n\n  // Public\n  getWidth() {\n    // https://developer.mozilla.org/en-US/docs/Web/API/Window/innerWidth#usage_notes\n    const documentWidth = document.documentElement.clientWidth;\n    return Math.abs(window.innerWidth - documentWidth);\n  }\n  hide() {\n    const width = this.getWidth();\n    this._disableOverFlow();\n    // give padding to element to balance the hidden scrollbar width\n    this._setElementAttributes(this._element, PROPERTY_PADDING, calculatedValue => calculatedValue + width);\n    // trick: We adjust positive paddingRight and negative marginRight to sticky-top elements to keep showing fullwidth\n    this._setElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING, calculatedValue => calculatedValue + width);\n    this._setElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN, calculatedValue => calculatedValue - width);\n  }\n  reset() {\n    this._resetElementAttributes(this._element, 'overflow');\n    this._resetElementAttributes(this._element, PROPERTY_PADDING);\n    this._resetElementAttributes(SELECTOR_FIXED_CONTENT, PROPERTY_PADDING);\n    this._resetElementAttributes(SELECTOR_STICKY_CONTENT, PROPERTY_MARGIN);\n  }\n  isOverflowing() {\n    return this.getWidth() > 0;\n  }\n\n  // Private\n  _disableOverFlow() {\n    this._saveInitialAttribute(this._element, 'overflow');\n    this._element.style.overflow = 'hidden';\n  }\n  _setElementAttributes(selector, styleProperty, callback) {\n    const scrollbarWidth = this.getWidth();\n    const manipulationCallBack = element => {\n      if (element !== this._element && window.innerWidth > element.clientWidth + scrollbarWidth) {\n        return;\n      }\n      this._saveInitialAttribute(element, styleProperty);\n      const calculatedValue = window.getComputedStyle(element).getPropertyValue(styleProperty);\n      element.style.setProperty(styleProperty, `${callback(Number.parseFloat(calculatedValue))}px`);\n    };\n    this._applyManipulationCallback(selector, manipulationCallBack);\n  }\n  _saveInitialAttribute(element, styleProperty) {\n    const actualValue = element.style.getPropertyValue(styleProperty);\n    if (actualValue) {\n      Manipulator.setDataAttribute(element, styleProperty, actualValue);\n    }\n  }\n  _resetElementAttributes(selector, styleProperty) {\n    const manipulationCallBack = element => {\n      const value = Manipulator.getDataAttribute(element, styleProperty);\n      // We only want to remove the property if the value is `null`; the value can also be zero\n      if (value === null) {\n        element.style.removeProperty(styleProperty);\n        return;\n      }\n      Manipulator.removeDataAttribute(element, styleProperty);\n      element.style.setProperty(styleProperty, value);\n    };\n    this._applyManipulationCallback(selector, manipulationCallBack);\n  }\n  _applyManipulationCallback(selector, callBack) {\n    if (isElement(selector)) {\n      callBack(selector);\n      return;\n    }\n    for (const sel of SelectorEngine.find(selector, this._element)) {\n      callBack(sel);\n    }\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$7 = 'modal';\nconst DATA_KEY$4 = 'bs.modal';\nconst EVENT_KEY$4 = `.${DATA_KEY$4}`;\nconst DATA_API_KEY$2 = '.data-api';\nconst ESCAPE_KEY$1 = 'Escape';\nconst EVENT_HIDE$4 = `hide${EVENT_KEY$4}`;\nconst EVENT_HIDE_PREVENTED$1 = `hidePrevented${EVENT_KEY$4}`;\nconst EVENT_HIDDEN$4 = `hidden${EVENT_KEY$4}`;\nconst EVENT_SHOW$4 = `show${EVENT_KEY$4}`;\nconst EVENT_SHOWN$4 = `shown${EVENT_KEY$4}`;\nconst EVENT_RESIZE$1 = `resize${EVENT_KEY$4}`;\nconst EVENT_CLICK_DISMISS = `click.dismiss${EVENT_KEY$4}`;\nconst EVENT_MOUSEDOWN_DISMISS = `mousedown.dismiss${EVENT_KEY$4}`;\nconst EVENT_KEYDOWN_DISMISS$1 = `keydown.dismiss${EVENT_KEY$4}`;\nconst EVENT_CLICK_DATA_API$2 = `click${EVENT_KEY$4}${DATA_API_KEY$2}`;\nconst CLASS_NAME_OPEN = 'modal-open';\nconst CLASS_NAME_FADE$3 = 'fade';\nconst CLASS_NAME_SHOW$4 = 'show';\nconst CLASS_NAME_STATIC = 'modal-static';\nconst OPEN_SELECTOR$1 = '.modal.show';\nconst SELECTOR_DIALOG = '.modal-dialog';\nconst SELECTOR_MODAL_BODY = '.modal-body';\nconst SELECTOR_DATA_TOGGLE$2 = '[data-bs-toggle=\"modal\"]';\nconst Default$6 = {\n  backdrop: true,\n  focus: true,\n  keyboard: true\n};\nconst DefaultType$6 = {\n  backdrop: '(boolean|string)',\n  focus: 'boolean',\n  keyboard: 'boolean'\n};\n\n/**\n * Class definition\n */\n\nclass Modal extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n    this._dialog = SelectorEngine.findOne(SELECTOR_DIALOG, this._element);\n    this._backdrop = this._initializeBackDrop();\n    this._focustrap = this._initializeFocusTrap();\n    this._isShown = false;\n    this._isTransitioning = false;\n    this._scrollBar = new ScrollBarHelper();\n    this._addEventListeners();\n  }\n\n  // Getters\n  static get Default() {\n    return Default$6;\n  }\n  static get DefaultType() {\n    return DefaultType$6;\n  }\n  static get NAME() {\n    return NAME$7;\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget);\n  }\n  show(relatedTarget) {\n    if (this._isShown || this._isTransitioning) {\n      return;\n    }\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW$4, {\n      relatedTarget\n    });\n    if (showEvent.defaultPrevented) {\n      return;\n    }\n    this._isShown = true;\n    this._isTransitioning = true;\n    this._scrollBar.hide();\n    document.body.classList.add(CLASS_NAME_OPEN);\n    this._adjustDialog();\n    this._backdrop.show(() => this._showElement(relatedTarget));\n  }\n  hide() {\n    if (!this._isShown || this._isTransitioning) {\n      return;\n    }\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE$4);\n    if (hideEvent.defaultPrevented) {\n      return;\n    }\n    this._isShown = false;\n    this._isTransitioning = true;\n    this._focustrap.deactivate();\n    this._element.classList.remove(CLASS_NAME_SHOW$4);\n    this._queueCallback(() => this._hideModal(), this._element, this._isAnimated());\n  }\n  dispose() {\n    EventHandler.off(window, EVENT_KEY$4);\n    EventHandler.off(this._dialog, EVENT_KEY$4);\n    this._backdrop.dispose();\n    this._focustrap.deactivate();\n    super.dispose();\n  }\n  handleUpdate() {\n    this._adjustDialog();\n  }\n\n  // Private\n  _initializeBackDrop() {\n    return new Backdrop({\n      isVisible: Boolean(this._config.backdrop),\n      // 'static' option will be translated to true, and booleans will keep their value,\n      isAnimated: this._isAnimated()\n    });\n  }\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    });\n  }\n  _showElement(relatedTarget) {\n    // try to append dynamic modal\n    if (!document.body.contains(this._element)) {\n      document.body.append(this._element);\n    }\n    this._element.style.display = 'block';\n    this._element.removeAttribute('aria-hidden');\n    this._element.setAttribute('aria-modal', true);\n    this._element.setAttribute('role', 'dialog');\n    this._element.scrollTop = 0;\n    const modalBody = SelectorEngine.findOne(SELECTOR_MODAL_BODY, this._dialog);\n    if (modalBody) {\n      modalBody.scrollTop = 0;\n    }\n    reflow(this._element);\n    this._element.classList.add(CLASS_NAME_SHOW$4);\n    const transitionComplete = () => {\n      if (this._config.focus) {\n        this._focustrap.activate();\n      }\n      this._isTransitioning = false;\n      EventHandler.trigger(this._element, EVENT_SHOWN$4, {\n        relatedTarget\n      });\n    };\n    this._queueCallback(transitionComplete, this._dialog, this._isAnimated());\n  }\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS$1, event => {\n      if (event.key !== ESCAPE_KEY$1) {\n        return;\n      }\n      if (this._config.keyboard) {\n        this.hide();\n        return;\n      }\n      this._triggerBackdropTransition();\n    });\n    EventHandler.on(window, EVENT_RESIZE$1, () => {\n      if (this._isShown && !this._isTransitioning) {\n        this._adjustDialog();\n      }\n    });\n    EventHandler.on(this._element, EVENT_MOUSEDOWN_DISMISS, event => {\n      // a bad trick to segregate clicks that may start inside dialog but end outside, and avoid listen to scrollbar clicks\n      EventHandler.one(this._element, EVENT_CLICK_DISMISS, event2 => {\n        if (this._element !== event.target || this._element !== event2.target) {\n          return;\n        }\n        if (this._config.backdrop === 'static') {\n          this._triggerBackdropTransition();\n          return;\n        }\n        if (this._config.backdrop) {\n          this.hide();\n        }\n      });\n    });\n  }\n  _hideModal() {\n    this._element.style.display = 'none';\n    this._element.setAttribute('aria-hidden', true);\n    this._element.removeAttribute('aria-modal');\n    this._element.removeAttribute('role');\n    this._isTransitioning = false;\n    this._backdrop.hide(() => {\n      document.body.classList.remove(CLASS_NAME_OPEN);\n      this._resetAdjustments();\n      this._scrollBar.reset();\n      EventHandler.trigger(this._element, EVENT_HIDDEN$4);\n    });\n  }\n  _isAnimated() {\n    return this._element.classList.contains(CLASS_NAME_FADE$3);\n  }\n  _triggerBackdropTransition() {\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED$1);\n    if (hideEvent.defaultPrevented) {\n      return;\n    }\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight;\n    const initialOverflowY = this._element.style.overflowY;\n    // return if the following background transition hasn't yet completed\n    if (initialOverflowY === 'hidden' || this._element.classList.contains(CLASS_NAME_STATIC)) {\n      return;\n    }\n    if (!isModalOverflowing) {\n      this._element.style.overflowY = 'hidden';\n    }\n    this._element.classList.add(CLASS_NAME_STATIC);\n    this._queueCallback(() => {\n      this._element.classList.remove(CLASS_NAME_STATIC);\n      this._queueCallback(() => {\n        this._element.style.overflowY = initialOverflowY;\n      }, this._dialog);\n    }, this._dialog);\n    this._element.focus();\n  }\n\n  /**\n   * The following methods are used to handle overflowing modals\n   */\n\n  _adjustDialog() {\n    const isModalOverflowing = this._element.scrollHeight > document.documentElement.clientHeight;\n    const scrollbarWidth = this._scrollBar.getWidth();\n    const isBodyOverflowing = scrollbarWidth > 0;\n    if (isBodyOverflowing && !isModalOverflowing) {\n      const property = isRTL() ? 'paddingLeft' : 'paddingRight';\n      this._element.style[property] = `${scrollbarWidth}px`;\n    }\n    if (!isBodyOverflowing && isModalOverflowing) {\n      const property = isRTL() ? 'paddingRight' : 'paddingLeft';\n      this._element.style[property] = `${scrollbarWidth}px`;\n    }\n  }\n  _resetAdjustments() {\n    this._element.style.paddingLeft = '';\n    this._element.style.paddingRight = '';\n  }\n\n  // Static\n  static jQueryInterface(config, relatedTarget) {\n    return this.each(function () {\n      const data = Modal.getOrCreateInstance(this, config);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config](relatedTarget);\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API$2, SELECTOR_DATA_TOGGLE$2, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this);\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault();\n  }\n  EventHandler.one(target, EVENT_SHOW$4, showEvent => {\n    if (showEvent.defaultPrevented) {\n      // only register focus restorer if modal will actually get shown\n      return;\n    }\n    EventHandler.one(target, EVENT_HIDDEN$4, () => {\n      if (isVisible(this)) {\n        this.focus();\n      }\n    });\n  });\n\n  // avoid conflict when clicking modal toggler while another one is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR$1);\n  if (alreadyOpen) {\n    Modal.getInstance(alreadyOpen).hide();\n  }\n  const data = Modal.getOrCreateInstance(target);\n  data.toggle(this);\n});\nenableDismissTrigger(Modal);\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Modal);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap offcanvas.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$6 = 'offcanvas';\nconst DATA_KEY$3 = 'bs.offcanvas';\nconst EVENT_KEY$3 = `.${DATA_KEY$3}`;\nconst DATA_API_KEY$1 = '.data-api';\nconst EVENT_LOAD_DATA_API$2 = `load${EVENT_KEY$3}${DATA_API_KEY$1}`;\nconst ESCAPE_KEY = 'Escape';\nconst CLASS_NAME_SHOW$3 = 'show';\nconst CLASS_NAME_SHOWING$1 = 'showing';\nconst CLASS_NAME_HIDING = 'hiding';\nconst CLASS_NAME_BACKDROP = 'offcanvas-backdrop';\nconst OPEN_SELECTOR = '.offcanvas.show';\nconst EVENT_SHOW$3 = `show${EVENT_KEY$3}`;\nconst EVENT_SHOWN$3 = `shown${EVENT_KEY$3}`;\nconst EVENT_HIDE$3 = `hide${EVENT_KEY$3}`;\nconst EVENT_HIDE_PREVENTED = `hidePrevented${EVENT_KEY$3}`;\nconst EVENT_HIDDEN$3 = `hidden${EVENT_KEY$3}`;\nconst EVENT_RESIZE = `resize${EVENT_KEY$3}`;\nconst EVENT_CLICK_DATA_API$1 = `click${EVENT_KEY$3}${DATA_API_KEY$1}`;\nconst EVENT_KEYDOWN_DISMISS = `keydown.dismiss${EVENT_KEY$3}`;\nconst SELECTOR_DATA_TOGGLE$1 = '[data-bs-toggle=\"offcanvas\"]';\nconst Default$5 = {\n  backdrop: true,\n  keyboard: true,\n  scroll: false\n};\nconst DefaultType$5 = {\n  backdrop: '(boolean|string)',\n  keyboard: 'boolean',\n  scroll: 'boolean'\n};\n\n/**\n * Class definition\n */\n\nclass Offcanvas extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n    this._isShown = false;\n    this._backdrop = this._initializeBackDrop();\n    this._focustrap = this._initializeFocusTrap();\n    this._addEventListeners();\n  }\n\n  // Getters\n  static get Default() {\n    return Default$5;\n  }\n  static get DefaultType() {\n    return DefaultType$5;\n  }\n  static get NAME() {\n    return NAME$6;\n  }\n\n  // Public\n  toggle(relatedTarget) {\n    return this._isShown ? this.hide() : this.show(relatedTarget);\n  }\n  show(relatedTarget) {\n    if (this._isShown) {\n      return;\n    }\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW$3, {\n      relatedTarget\n    });\n    if (showEvent.defaultPrevented) {\n      return;\n    }\n    this._isShown = true;\n    this._backdrop.show();\n    if (!this._config.scroll) {\n      new ScrollBarHelper().hide();\n    }\n    this._element.setAttribute('aria-modal', true);\n    this._element.setAttribute('role', 'dialog');\n    this._element.classList.add(CLASS_NAME_SHOWING$1);\n    const completeCallBack = () => {\n      if (!this._config.scroll || this._config.backdrop) {\n        this._focustrap.activate();\n      }\n      this._element.classList.add(CLASS_NAME_SHOW$3);\n      this._element.classList.remove(CLASS_NAME_SHOWING$1);\n      EventHandler.trigger(this._element, EVENT_SHOWN$3, {\n        relatedTarget\n      });\n    };\n    this._queueCallback(completeCallBack, this._element, true);\n  }\n  hide() {\n    if (!this._isShown) {\n      return;\n    }\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE$3);\n    if (hideEvent.defaultPrevented) {\n      return;\n    }\n    this._focustrap.deactivate();\n    this._element.blur();\n    this._isShown = false;\n    this._element.classList.add(CLASS_NAME_HIDING);\n    this._backdrop.hide();\n    const completeCallback = () => {\n      this._element.classList.remove(CLASS_NAME_SHOW$3, CLASS_NAME_HIDING);\n      this._element.removeAttribute('aria-modal');\n      this._element.removeAttribute('role');\n      if (!this._config.scroll) {\n        new ScrollBarHelper().reset();\n      }\n      EventHandler.trigger(this._element, EVENT_HIDDEN$3);\n    };\n    this._queueCallback(completeCallback, this._element, true);\n  }\n  dispose() {\n    this._backdrop.dispose();\n    this._focustrap.deactivate();\n    super.dispose();\n  }\n\n  // Private\n  _initializeBackDrop() {\n    const clickCallback = () => {\n      if (this._config.backdrop === 'static') {\n        EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED);\n        return;\n      }\n      this.hide();\n    };\n\n    // 'static' option will be translated to true, and booleans will keep their value\n    const isVisible = Boolean(this._config.backdrop);\n    return new Backdrop({\n      className: CLASS_NAME_BACKDROP,\n      isVisible,\n      isAnimated: true,\n      rootElement: this._element.parentNode,\n      clickCallback: isVisible ? clickCallback : null\n    });\n  }\n  _initializeFocusTrap() {\n    return new FocusTrap({\n      trapElement: this._element\n    });\n  }\n  _addEventListeners() {\n    EventHandler.on(this._element, EVENT_KEYDOWN_DISMISS, event => {\n      if (event.key !== ESCAPE_KEY) {\n        return;\n      }\n      if (this._config.keyboard) {\n        this.hide();\n        return;\n      }\n      EventHandler.trigger(this._element, EVENT_HIDE_PREVENTED);\n    });\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Offcanvas.getOrCreateInstance(this, config);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config](this);\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API$1, SELECTOR_DATA_TOGGLE$1, function (event) {\n  const target = SelectorEngine.getElementFromSelector(this);\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault();\n  }\n  if (isDisabled(this)) {\n    return;\n  }\n  EventHandler.one(target, EVENT_HIDDEN$3, () => {\n    // focus on trigger when it is closed\n    if (isVisible(this)) {\n      this.focus();\n    }\n  });\n\n  // avoid conflict when clicking a toggler of an offcanvas, while another is open\n  const alreadyOpen = SelectorEngine.findOne(OPEN_SELECTOR);\n  if (alreadyOpen && alreadyOpen !== target) {\n    Offcanvas.getInstance(alreadyOpen).hide();\n  }\n  const data = Offcanvas.getOrCreateInstance(target);\n  data.toggle(this);\n});\nEventHandler.on(window, EVENT_LOAD_DATA_API$2, () => {\n  for (const selector of SelectorEngine.find(OPEN_SELECTOR)) {\n    Offcanvas.getOrCreateInstance(selector).show();\n  }\n});\nEventHandler.on(window, EVENT_RESIZE, () => {\n  for (const element of SelectorEngine.find('[aria-modal][class*=show][class*=offcanvas-]')) {\n    if (getComputedStyle(element).position !== 'fixed') {\n      Offcanvas.getOrCreateInstance(element).hide();\n    }\n  }\n});\nenableDismissTrigger(Offcanvas);\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Offcanvas);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/sanitizer.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n// js-docs-start allow-list\nconst ARIA_ATTRIBUTE_PATTERN = /^aria-[\\w-]*$/i;\nconst DefaultAllowlist = {\n  // Global attributes allowed on any supplied element below.\n  '*': ['class', 'dir', 'id', 'lang', 'role', ARIA_ATTRIBUTE_PATTERN],\n  a: ['target', 'href', 'title', 'rel'],\n  area: [],\n  b: [],\n  br: [],\n  col: [],\n  code: [],\n  dd: [],\n  div: [],\n  dl: [],\n  dt: [],\n  em: [],\n  hr: [],\n  h1: [],\n  h2: [],\n  h3: [],\n  h4: [],\n  h5: [],\n  h6: [],\n  i: [],\n  img: ['src', 'srcset', 'alt', 'title', 'width', 'height'],\n  li: [],\n  ol: [],\n  p: [],\n  pre: [],\n  s: [],\n  small: [],\n  span: [],\n  sub: [],\n  sup: [],\n  strong: [],\n  u: [],\n  ul: []\n};\n// js-docs-end allow-list\n\nconst uriAttributes = new Set(['background', 'cite', 'href', 'itemtype', 'longdesc', 'poster', 'src', 'xlink:href']);\n\n/**\n * A pattern that recognizes URLs that are safe wrt. XSS in URL navigation\n * contexts.\n *\n * Shout-out to Angular https://github.com/angular/angular/blob/15.2.8/packages/core/src/sanitization/url_sanitizer.ts#L38\n */\n// eslint-disable-next-line unicorn/better-regex\nconst SAFE_URL_PATTERN = /^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i;\nconst allowedAttribute = (attribute, allowedAttributeList) => {\n  const attributeName = attribute.nodeName.toLowerCase();\n  if (allowedAttributeList.includes(attributeName)) {\n    if (uriAttributes.has(attributeName)) {\n      return Boolean(SAFE_URL_PATTERN.test(attribute.nodeValue));\n    }\n    return true;\n  }\n\n  // Check if a regular expression validates the attribute.\n  return allowedAttributeList.filter(attributeRegex => attributeRegex instanceof RegExp).some(regex => regex.test(attributeName));\n};\nfunction sanitizeHtml(unsafeHtml, allowList, sanitizeFunction) {\n  if (!unsafeHtml.length) {\n    return unsafeHtml;\n  }\n  if (sanitizeFunction && typeof sanitizeFunction === 'function') {\n    return sanitizeFunction(unsafeHtml);\n  }\n  const domParser = new window.DOMParser();\n  const createdDocument = domParser.parseFromString(unsafeHtml, 'text/html');\n  const elements = [].concat(...createdDocument.body.querySelectorAll('*'));\n  for (const element of elements) {\n    const elementName = element.nodeName.toLowerCase();\n    if (!Object.keys(allowList).includes(elementName)) {\n      element.remove();\n      continue;\n    }\n    const attributeList = [].concat(...element.attributes);\n    const allowedAttributes = [].concat(allowList['*'] || [], allowList[elementName] || []);\n    for (const attribute of attributeList) {\n      if (!allowedAttribute(attribute, allowedAttributes)) {\n        element.removeAttribute(attribute.nodeName);\n      }\n    }\n  }\n  return createdDocument.body.innerHTML;\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap util/template-factory.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$5 = 'TemplateFactory';\nconst Default$4 = {\n  allowList: DefaultAllowlist,\n  content: {},\n  // { selector : text ,  selector2 : text2 , }\n  extraClass: '',\n  html: false,\n  sanitize: true,\n  sanitizeFn: null,\n  template: '<div></div>'\n};\nconst DefaultType$4 = {\n  allowList: 'object',\n  content: 'object',\n  extraClass: '(string|function)',\n  html: 'boolean',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  template: 'string'\n};\nconst DefaultContentType = {\n  entry: '(string|element|function|null)',\n  selector: '(string|element)'\n};\n\n/**\n * Class definition\n */\n\nclass TemplateFactory extends Config {\n  constructor(config) {\n    super();\n    this._config = this._getConfig(config);\n  }\n\n  // Getters\n  static get Default() {\n    return Default$4;\n  }\n  static get DefaultType() {\n    return DefaultType$4;\n  }\n  static get NAME() {\n    return NAME$5;\n  }\n\n  // Public\n  getContent() {\n    return Object.values(this._config.content).map(config => this._resolvePossibleFunction(config)).filter(Boolean);\n  }\n  hasContent() {\n    return this.getContent().length > 0;\n  }\n  changeContent(content) {\n    this._checkContent(content);\n    this._config.content = {\n      ...this._config.content,\n      ...content\n    };\n    return this;\n  }\n  toHtml() {\n    const templateWrapper = document.createElement('div');\n    templateWrapper.innerHTML = this._maybeSanitize(this._config.template);\n    for (const [selector, text] of Object.entries(this._config.content)) {\n      this._setContent(templateWrapper, text, selector);\n    }\n    const template = templateWrapper.children[0];\n    const extraClass = this._resolvePossibleFunction(this._config.extraClass);\n    if (extraClass) {\n      template.classList.add(...extraClass.split(' '));\n    }\n    return template;\n  }\n\n  // Private\n  _typeCheckConfig(config) {\n    super._typeCheckConfig(config);\n    this._checkContent(config.content);\n  }\n  _checkContent(arg) {\n    for (const [selector, content] of Object.entries(arg)) {\n      super._typeCheckConfig({\n        selector,\n        entry: content\n      }, DefaultContentType);\n    }\n  }\n  _setContent(template, content, selector) {\n    const templateElement = SelectorEngine.findOne(selector, template);\n    if (!templateElement) {\n      return;\n    }\n    content = this._resolvePossibleFunction(content);\n    if (!content) {\n      templateElement.remove();\n      return;\n    }\n    if (isElement(content)) {\n      this._putElementInTemplate(getElement(content), templateElement);\n      return;\n    }\n    if (this._config.html) {\n      templateElement.innerHTML = this._maybeSanitize(content);\n      return;\n    }\n    templateElement.textContent = content;\n  }\n  _maybeSanitize(arg) {\n    return this._config.sanitize ? sanitizeHtml(arg, this._config.allowList, this._config.sanitizeFn) : arg;\n  }\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this]);\n  }\n  _putElementInTemplate(element, templateElement) {\n    if (this._config.html) {\n      templateElement.innerHTML = '';\n      templateElement.append(element);\n      return;\n    }\n    templateElement.textContent = element.textContent;\n  }\n}\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$4 = 'tooltip';\nconst DISALLOWED_ATTRIBUTES = new Set(['sanitize', 'allowList', 'sanitizeFn']);\nconst CLASS_NAME_FADE$2 = 'fade';\nconst CLASS_NAME_MODAL = 'modal';\nconst CLASS_NAME_SHOW$2 = 'show';\nconst SELECTOR_TOOLTIP_INNER = '.tooltip-inner';\nconst SELECTOR_MODAL = `.${CLASS_NAME_MODAL}`;\nconst EVENT_MODAL_HIDE = 'hide.bs.modal';\nconst TRIGGER_HOVER = 'hover';\nconst TRIGGER_FOCUS = 'focus';\nconst TRIGGER_CLICK = 'click';\nconst TRIGGER_MANUAL = 'manual';\nconst EVENT_HIDE$2 = 'hide';\nconst EVENT_HIDDEN$2 = 'hidden';\nconst EVENT_SHOW$2 = 'show';\nconst EVENT_SHOWN$2 = 'shown';\nconst EVENT_INSERTED = 'inserted';\nconst EVENT_CLICK$1 = 'click';\nconst EVENT_FOCUSIN$1 = 'focusin';\nconst EVENT_FOCUSOUT$1 = 'focusout';\nconst EVENT_MOUSEENTER = 'mouseenter';\nconst EVENT_MOUSELEAVE = 'mouseleave';\nconst AttachmentMap = {\n  AUTO: 'auto',\n  TOP: 'top',\n  RIGHT: isRTL() ? 'left' : 'right',\n  BOTTOM: 'bottom',\n  LEFT: isRTL() ? 'right' : 'left'\n};\nconst Default$3 = {\n  allowList: DefaultAllowlist,\n  animation: true,\n  boundary: 'clippingParents',\n  container: false,\n  customClass: '',\n  delay: 0,\n  fallbackPlacements: ['top', 'right', 'bottom', 'left'],\n  html: false,\n  offset: [0, 6],\n  placement: 'top',\n  popperConfig: null,\n  sanitize: true,\n  sanitizeFn: null,\n  selector: false,\n  template: '<div class=\"tooltip\" role=\"tooltip\">' + '<div class=\"tooltip-arrow\"></div>' + '<div class=\"tooltip-inner\"></div>' + '</div>',\n  title: '',\n  trigger: 'hover focus'\n};\nconst DefaultType$3 = {\n  allowList: 'object',\n  animation: 'boolean',\n  boundary: '(string|element)',\n  container: '(string|element|boolean)',\n  customClass: '(string|function)',\n  delay: '(number|object)',\n  fallbackPlacements: 'array',\n  html: 'boolean',\n  offset: '(array|string|function)',\n  placement: '(string|function)',\n  popperConfig: '(null|object|function)',\n  sanitize: 'boolean',\n  sanitizeFn: '(null|function)',\n  selector: '(string|boolean)',\n  template: 'string',\n  title: '(string|element|function)',\n  trigger: 'string'\n};\n\n/**\n * Class definition\n */\n\nclass Tooltip extends BaseComponent {\n  constructor(element, config) {\n    if (typeof Popper === 'undefined') {\n      throw new TypeError('Bootstrap\\'s tooltips require Popper (https://popper.js.org)');\n    }\n    super(element, config);\n\n    // Private\n    this._isEnabled = true;\n    this._timeout = 0;\n    this._isHovered = null;\n    this._activeTrigger = {};\n    this._popper = null;\n    this._templateFactory = null;\n    this._newContent = null;\n\n    // Protected\n    this.tip = null;\n    this._setListeners();\n    if (!this._config.selector) {\n      this._fixTitle();\n    }\n  }\n\n  // Getters\n  static get Default() {\n    return Default$3;\n  }\n  static get DefaultType() {\n    return DefaultType$3;\n  }\n  static get NAME() {\n    return NAME$4;\n  }\n\n  // Public\n  enable() {\n    this._isEnabled = true;\n  }\n  disable() {\n    this._isEnabled = false;\n  }\n  toggleEnabled() {\n    this._isEnabled = !this._isEnabled;\n  }\n  toggle() {\n    if (!this._isEnabled) {\n      return;\n    }\n    this._activeTrigger.click = !this._activeTrigger.click;\n    if (this._isShown()) {\n      this._leave();\n      return;\n    }\n    this._enter();\n  }\n  dispose() {\n    clearTimeout(this._timeout);\n    EventHandler.off(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler);\n    if (this._element.getAttribute('data-bs-original-title')) {\n      this._element.setAttribute('title', this._element.getAttribute('data-bs-original-title'));\n    }\n    this._disposePopper();\n    super.dispose();\n  }\n  show() {\n    if (this._element.style.display === 'none') {\n      throw new Error('Please use show on visible elements');\n    }\n    if (!(this._isWithContent() && this._isEnabled)) {\n      return;\n    }\n    const showEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOW$2));\n    const shadowRoot = findShadowRoot(this._element);\n    const isInTheDom = (shadowRoot || this._element.ownerDocument.documentElement).contains(this._element);\n    if (showEvent.defaultPrevented || !isInTheDom) {\n      return;\n    }\n\n    // TODO: v6 remove this or make it optional\n    this._disposePopper();\n    const tip = this._getTipElement();\n    this._element.setAttribute('aria-describedby', tip.getAttribute('id'));\n    const {\n      container\n    } = this._config;\n    if (!this._element.ownerDocument.documentElement.contains(this.tip)) {\n      container.append(tip);\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_INSERTED));\n    }\n    this._popper = this._createPopper(tip);\n    tip.classList.add(CLASS_NAME_SHOW$2);\n\n    // If this is a touch-enabled device we add extra\n    // empty mouseover listeners to the body's immediate children;\n    // only needed because of broken event delegation on iOS\n    // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.on(element, 'mouseover', noop);\n      }\n    }\n    const complete = () => {\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_SHOWN$2));\n      if (this._isHovered === false) {\n        this._leave();\n      }\n      this._isHovered = false;\n    };\n    this._queueCallback(complete, this.tip, this._isAnimated());\n  }\n  hide() {\n    if (!this._isShown()) {\n      return;\n    }\n    const hideEvent = EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDE$2));\n    if (hideEvent.defaultPrevented) {\n      return;\n    }\n    const tip = this._getTipElement();\n    tip.classList.remove(CLASS_NAME_SHOW$2);\n\n    // If this is a touch-enabled device we remove the extra\n    // empty mouseover listeners we added for iOS support\n    if ('ontouchstart' in document.documentElement) {\n      for (const element of [].concat(...document.body.children)) {\n        EventHandler.off(element, 'mouseover', noop);\n      }\n    }\n    this._activeTrigger[TRIGGER_CLICK] = false;\n    this._activeTrigger[TRIGGER_FOCUS] = false;\n    this._activeTrigger[TRIGGER_HOVER] = false;\n    this._isHovered = null; // it is a trick to support manual triggering\n\n    const complete = () => {\n      if (this._isWithActiveTrigger()) {\n        return;\n      }\n      if (!this._isHovered) {\n        this._disposePopper();\n      }\n      this._element.removeAttribute('aria-describedby');\n      EventHandler.trigger(this._element, this.constructor.eventName(EVENT_HIDDEN$2));\n    };\n    this._queueCallback(complete, this.tip, this._isAnimated());\n  }\n  update() {\n    if (this._popper) {\n      this._popper.update();\n    }\n  }\n\n  // Protected\n  _isWithContent() {\n    return Boolean(this._getTitle());\n  }\n  _getTipElement() {\n    if (!this.tip) {\n      this.tip = this._createTipElement(this._newContent || this._getContentForTemplate());\n    }\n    return this.tip;\n  }\n  _createTipElement(content) {\n    const tip = this._getTemplateFactory(content).toHtml();\n\n    // TODO: remove this check in v6\n    if (!tip) {\n      return null;\n    }\n    tip.classList.remove(CLASS_NAME_FADE$2, CLASS_NAME_SHOW$2);\n    // TODO: v6 the following can be achieved with CSS only\n    tip.classList.add(`bs-${this.constructor.NAME}-auto`);\n    const tipId = getUID(this.constructor.NAME).toString();\n    tip.setAttribute('id', tipId);\n    if (this._isAnimated()) {\n      tip.classList.add(CLASS_NAME_FADE$2);\n    }\n    return tip;\n  }\n  setContent(content) {\n    this._newContent = content;\n    if (this._isShown()) {\n      this._disposePopper();\n      this.show();\n    }\n  }\n  _getTemplateFactory(content) {\n    if (this._templateFactory) {\n      this._templateFactory.changeContent(content);\n    } else {\n      this._templateFactory = new TemplateFactory({\n        ...this._config,\n        // the `content` var has to be after `this._config`\n        // to override config.content in case of popover\n        content,\n        extraClass: this._resolvePossibleFunction(this._config.customClass)\n      });\n    }\n    return this._templateFactory;\n  }\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TOOLTIP_INNER]: this._getTitle()\n    };\n  }\n  _getTitle() {\n    return this._resolvePossibleFunction(this._config.title) || this._element.getAttribute('data-bs-original-title');\n  }\n\n  // Private\n  _initializeOnDelegatedTarget(event) {\n    return this.constructor.getOrCreateInstance(event.delegateTarget, this._getDelegateConfig());\n  }\n  _isAnimated() {\n    return this._config.animation || this.tip && this.tip.classList.contains(CLASS_NAME_FADE$2);\n  }\n  _isShown() {\n    return this.tip && this.tip.classList.contains(CLASS_NAME_SHOW$2);\n  }\n  _createPopper(tip) {\n    const placement = execute(this._config.placement, [this, tip, this._element]);\n    const attachment = AttachmentMap[placement.toUpperCase()];\n    return Popper.createPopper(this._element, tip, this._getPopperConfig(attachment));\n  }\n  _getOffset() {\n    const {\n      offset\n    } = this._config;\n    if (typeof offset === 'string') {\n      return offset.split(',').map(value => Number.parseInt(value, 10));\n    }\n    if (typeof offset === 'function') {\n      return popperData => offset(popperData, this._element);\n    }\n    return offset;\n  }\n  _resolvePossibleFunction(arg) {\n    return execute(arg, [this._element]);\n  }\n  _getPopperConfig(attachment) {\n    const defaultBsPopperConfig = {\n      placement: attachment,\n      modifiers: [{\n        name: 'flip',\n        options: {\n          fallbackPlacements: this._config.fallbackPlacements\n        }\n      }, {\n        name: 'offset',\n        options: {\n          offset: this._getOffset()\n        }\n      }, {\n        name: 'preventOverflow',\n        options: {\n          boundary: this._config.boundary\n        }\n      }, {\n        name: 'arrow',\n        options: {\n          element: `.${this.constructor.NAME}-arrow`\n        }\n      }, {\n        name: 'preSetPlacement',\n        enabled: true,\n        phase: 'beforeMain',\n        fn: data => {\n          // Pre-set Popper's placement attribute in order to read the arrow sizes properly.\n          // Otherwise, Popper mixes up the width and height dimensions since the initial arrow style is for top placement\n          this._getTipElement().setAttribute('data-popper-placement', data.state.placement);\n        }\n      }]\n    };\n    return {\n      ...defaultBsPopperConfig,\n      ...execute(this._config.popperConfig, [defaultBsPopperConfig])\n    };\n  }\n  _setListeners() {\n    const triggers = this._config.trigger.split(' ');\n    for (const trigger of triggers) {\n      if (trigger === 'click') {\n        EventHandler.on(this._element, this.constructor.eventName(EVENT_CLICK$1), this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event);\n          context.toggle();\n        });\n      } else if (trigger !== TRIGGER_MANUAL) {\n        const eventIn = trigger === TRIGGER_HOVER ? this.constructor.eventName(EVENT_MOUSEENTER) : this.constructor.eventName(EVENT_FOCUSIN$1);\n        const eventOut = trigger === TRIGGER_HOVER ? this.constructor.eventName(EVENT_MOUSELEAVE) : this.constructor.eventName(EVENT_FOCUSOUT$1);\n        EventHandler.on(this._element, eventIn, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event);\n          context._activeTrigger[event.type === 'focusin' ? TRIGGER_FOCUS : TRIGGER_HOVER] = true;\n          context._enter();\n        });\n        EventHandler.on(this._element, eventOut, this._config.selector, event => {\n          const context = this._initializeOnDelegatedTarget(event);\n          context._activeTrigger[event.type === 'focusout' ? TRIGGER_FOCUS : TRIGGER_HOVER] = context._element.contains(event.relatedTarget);\n          context._leave();\n        });\n      }\n    }\n    this._hideModalHandler = () => {\n      if (this._element) {\n        this.hide();\n      }\n    };\n    EventHandler.on(this._element.closest(SELECTOR_MODAL), EVENT_MODAL_HIDE, this._hideModalHandler);\n  }\n  _fixTitle() {\n    const title = this._element.getAttribute('title');\n    if (!title) {\n      return;\n    }\n    if (!this._element.getAttribute('aria-label') && !this._element.textContent.trim()) {\n      this._element.setAttribute('aria-label', title);\n    }\n    this._element.setAttribute('data-bs-original-title', title); // DO NOT USE IT. Is only for backwards compatibility\n    this._element.removeAttribute('title');\n  }\n  _enter() {\n    if (this._isShown() || this._isHovered) {\n      this._isHovered = true;\n      return;\n    }\n    this._isHovered = true;\n    this._setTimeout(() => {\n      if (this._isHovered) {\n        this.show();\n      }\n    }, this._config.delay.show);\n  }\n  _leave() {\n    if (this._isWithActiveTrigger()) {\n      return;\n    }\n    this._isHovered = false;\n    this._setTimeout(() => {\n      if (!this._isHovered) {\n        this.hide();\n      }\n    }, this._config.delay.hide);\n  }\n  _setTimeout(handler, timeout) {\n    clearTimeout(this._timeout);\n    this._timeout = setTimeout(handler, timeout);\n  }\n  _isWithActiveTrigger() {\n    return Object.values(this._activeTrigger).includes(true);\n  }\n  _getConfig(config) {\n    const dataAttributes = Manipulator.getDataAttributes(this._element);\n    for (const dataAttribute of Object.keys(dataAttributes)) {\n      if (DISALLOWED_ATTRIBUTES.has(dataAttribute)) {\n        delete dataAttributes[dataAttribute];\n      }\n    }\n    config = {\n      ...dataAttributes,\n      ...(typeof config === 'object' && config ? config : {})\n    };\n    config = this._mergeConfigObj(config);\n    config = this._configAfterMerge(config);\n    this._typeCheckConfig(config);\n    return config;\n  }\n  _configAfterMerge(config) {\n    config.container = config.container === false ? document.body : getElement(config.container);\n    if (typeof config.delay === 'number') {\n      config.delay = {\n        show: config.delay,\n        hide: config.delay\n      };\n    }\n    if (typeof config.title === 'number') {\n      config.title = config.title.toString();\n    }\n    if (typeof config.content === 'number') {\n      config.content = config.content.toString();\n    }\n    return config;\n  }\n  _getDelegateConfig() {\n    const config = {};\n    for (const [key, value] of Object.entries(this._config)) {\n      if (this.constructor.Default[key] !== value) {\n        config[key] = value;\n      }\n    }\n    config.selector = false;\n    config.trigger = 'manual';\n\n    // In the future can be replaced with:\n    // const keysWithDifferentValues = Object.entries(this._config).filter(entry => this.constructor.Default[entry[0]] !== this._config[entry[0]])\n    // `Object.fromEntries(keysWithDifferentValues)`\n    return config;\n  }\n  _disposePopper() {\n    if (this._popper) {\n      this._popper.destroy();\n      this._popper = null;\n    }\n    if (this.tip) {\n      this.tip.remove();\n      this.tip = null;\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tooltip.getOrCreateInstance(this, config);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config]();\n    });\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tooltip);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$3 = 'popover';\nconst SELECTOR_TITLE = '.popover-header';\nconst SELECTOR_CONTENT = '.popover-body';\nconst Default$2 = {\n  ...Tooltip.Default,\n  content: '',\n  offset: [0, 8],\n  placement: 'right',\n  template: '<div class=\"popover\" role=\"tooltip\">' + '<div class=\"popover-arrow\"></div>' + '<h3 class=\"popover-header\"></h3>' + '<div class=\"popover-body\"></div>' + '</div>',\n  trigger: 'click'\n};\nconst DefaultType$2 = {\n  ...Tooltip.DefaultType,\n  content: '(null|string|element|function)'\n};\n\n/**\n * Class definition\n */\n\nclass Popover extends Tooltip {\n  // Getters\n  static get Default() {\n    return Default$2;\n  }\n  static get DefaultType() {\n    return DefaultType$2;\n  }\n  static get NAME() {\n    return NAME$3;\n  }\n\n  // Overrides\n  _isWithContent() {\n    return this._getTitle() || this._getContent();\n  }\n\n  // Private\n  _getContentForTemplate() {\n    return {\n      [SELECTOR_TITLE]: this._getTitle(),\n      [SELECTOR_CONTENT]: this._getContent()\n    };\n  }\n  _getContent() {\n    return this._resolvePossibleFunction(this._config.content);\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Popover.getOrCreateInstance(this, config);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (typeof data[config] === 'undefined') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config]();\n    });\n  }\n}\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Popover);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$2 = 'scrollspy';\nconst DATA_KEY$2 = 'bs.scrollspy';\nconst EVENT_KEY$2 = `.${DATA_KEY$2}`;\nconst DATA_API_KEY = '.data-api';\nconst EVENT_ACTIVATE = `activate${EVENT_KEY$2}`;\nconst EVENT_CLICK = `click${EVENT_KEY$2}`;\nconst EVENT_LOAD_DATA_API$1 = `load${EVENT_KEY$2}${DATA_API_KEY}`;\nconst CLASS_NAME_DROPDOWN_ITEM = 'dropdown-item';\nconst CLASS_NAME_ACTIVE$1 = 'active';\nconst SELECTOR_DATA_SPY = '[data-bs-spy=\"scroll\"]';\nconst SELECTOR_TARGET_LINKS = '[href]';\nconst SELECTOR_NAV_LIST_GROUP = '.nav, .list-group';\nconst SELECTOR_NAV_LINKS = '.nav-link';\nconst SELECTOR_NAV_ITEMS = '.nav-item';\nconst SELECTOR_LIST_ITEMS = '.list-group-item';\nconst SELECTOR_LINK_ITEMS = `${SELECTOR_NAV_LINKS}, ${SELECTOR_NAV_ITEMS} > ${SELECTOR_NAV_LINKS}, ${SELECTOR_LIST_ITEMS}`;\nconst SELECTOR_DROPDOWN = '.dropdown';\nconst SELECTOR_DROPDOWN_TOGGLE$1 = '.dropdown-toggle';\nconst Default$1 = {\n  offset: null,\n  // TODO: v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: '0px 0px -25%',\n  smoothScroll: false,\n  target: null,\n  threshold: [0.1, 0.5, 1]\n};\nconst DefaultType$1 = {\n  offset: '(number|null)',\n  // TODO v6 @deprecated, keep it for backwards compatibility reasons\n  rootMargin: 'string',\n  smoothScroll: 'boolean',\n  target: 'element',\n  threshold: 'array'\n};\n\n/**\n * Class definition\n */\n\nclass ScrollSpy extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n\n    // this._element is the observablesContainer and config.target the menu links wrapper\n    this._targetLinks = new Map();\n    this._observableSections = new Map();\n    this._rootElement = getComputedStyle(this._element).overflowY === 'visible' ? null : this._element;\n    this._activeTarget = null;\n    this._observer = null;\n    this._previousScrollData = {\n      visibleEntryTop: 0,\n      parentScrollTop: 0\n    };\n    this.refresh(); // initialize\n  }\n\n  // Getters\n  static get Default() {\n    return Default$1;\n  }\n  static get DefaultType() {\n    return DefaultType$1;\n  }\n  static get NAME() {\n    return NAME$2;\n  }\n\n  // Public\n  refresh() {\n    this._initializeTargetsAndObservables();\n    this._maybeEnableSmoothScroll();\n    if (this._observer) {\n      this._observer.disconnect();\n    } else {\n      this._observer = this._getNewObserver();\n    }\n    for (const section of this._observableSections.values()) {\n      this._observer.observe(section);\n    }\n  }\n  dispose() {\n    this._observer.disconnect();\n    super.dispose();\n  }\n\n  // Private\n  _configAfterMerge(config) {\n    // TODO: on v6 target should be given explicitly & remove the {target: 'ss-target'} case\n    config.target = getElement(config.target) || document.body;\n\n    // TODO: v6 Only for backwards compatibility reasons. Use rootMargin only\n    config.rootMargin = config.offset ? `${config.offset}px 0px -30%` : config.rootMargin;\n    if (typeof config.threshold === 'string') {\n      config.threshold = config.threshold.split(',').map(value => Number.parseFloat(value));\n    }\n    return config;\n  }\n  _maybeEnableSmoothScroll() {\n    if (!this._config.smoothScroll) {\n      return;\n    }\n\n    // unregister any previous listeners\n    EventHandler.off(this._config.target, EVENT_CLICK);\n    EventHandler.on(this._config.target, EVENT_CLICK, SELECTOR_TARGET_LINKS, event => {\n      const observableSection = this._observableSections.get(event.target.hash);\n      if (observableSection) {\n        event.preventDefault();\n        const root = this._rootElement || window;\n        const height = observableSection.offsetTop - this._element.offsetTop;\n        if (root.scrollTo) {\n          root.scrollTo({\n            top: height,\n            behavior: 'smooth'\n          });\n          return;\n        }\n\n        // Chrome 60 doesn't support `scrollTo`\n        root.scrollTop = height;\n      }\n    });\n  }\n  _getNewObserver() {\n    const options = {\n      root: this._rootElement,\n      threshold: this._config.threshold,\n      rootMargin: this._config.rootMargin\n    };\n    return new IntersectionObserver(entries => this._observerCallback(entries), options);\n  }\n\n  // The logic of selection\n  _observerCallback(entries) {\n    const targetElement = entry => this._targetLinks.get(`#${entry.target.id}`);\n    const activate = entry => {\n      this._previousScrollData.visibleEntryTop = entry.target.offsetTop;\n      this._process(targetElement(entry));\n    };\n    const parentScrollTop = (this._rootElement || document.documentElement).scrollTop;\n    const userScrollsDown = parentScrollTop >= this._previousScrollData.parentScrollTop;\n    this._previousScrollData.parentScrollTop = parentScrollTop;\n    for (const entry of entries) {\n      if (!entry.isIntersecting) {\n        this._activeTarget = null;\n        this._clearActiveClass(targetElement(entry));\n        continue;\n      }\n      const entryIsLowerThanPrevious = entry.target.offsetTop >= this._previousScrollData.visibleEntryTop;\n      // if we are scrolling down, pick the bigger offsetTop\n      if (userScrollsDown && entryIsLowerThanPrevious) {\n        activate(entry);\n        // if parent isn't scrolled, let's keep the first visible item, breaking the iteration\n        if (!parentScrollTop) {\n          return;\n        }\n        continue;\n      }\n\n      // if we are scrolling up, pick the smallest offsetTop\n      if (!userScrollsDown && !entryIsLowerThanPrevious) {\n        activate(entry);\n      }\n    }\n  }\n  _initializeTargetsAndObservables() {\n    this._targetLinks = new Map();\n    this._observableSections = new Map();\n    const targetLinks = SelectorEngine.find(SELECTOR_TARGET_LINKS, this._config.target);\n    for (const anchor of targetLinks) {\n      // ensure that the anchor has an id and is not disabled\n      if (!anchor.hash || isDisabled(anchor)) {\n        continue;\n      }\n      const observableSection = SelectorEngine.findOne(decodeURI(anchor.hash), this._element);\n\n      // ensure that the observableSection exists & is visible\n      if (isVisible(observableSection)) {\n        this._targetLinks.set(decodeURI(anchor.hash), anchor);\n        this._observableSections.set(anchor.hash, observableSection);\n      }\n    }\n  }\n  _process(target) {\n    if (this._activeTarget === target) {\n      return;\n    }\n    this._clearActiveClass(this._config.target);\n    this._activeTarget = target;\n    target.classList.add(CLASS_NAME_ACTIVE$1);\n    this._activateParents(target);\n    EventHandler.trigger(this._element, EVENT_ACTIVATE, {\n      relatedTarget: target\n    });\n  }\n  _activateParents(target) {\n    // Activate dropdown parents\n    if (target.classList.contains(CLASS_NAME_DROPDOWN_ITEM)) {\n      SelectorEngine.findOne(SELECTOR_DROPDOWN_TOGGLE$1, target.closest(SELECTOR_DROPDOWN)).classList.add(CLASS_NAME_ACTIVE$1);\n      return;\n    }\n    for (const listGroup of SelectorEngine.parents(target, SELECTOR_NAV_LIST_GROUP)) {\n      // Set triggered links parents as active\n      // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n      for (const item of SelectorEngine.prev(listGroup, SELECTOR_LINK_ITEMS)) {\n        item.classList.add(CLASS_NAME_ACTIVE$1);\n      }\n    }\n  }\n  _clearActiveClass(parent) {\n    parent.classList.remove(CLASS_NAME_ACTIVE$1);\n    const activeNodes = SelectorEngine.find(`${SELECTOR_TARGET_LINKS}.${CLASS_NAME_ACTIVE$1}`, parent);\n    for (const node of activeNodes) {\n      node.classList.remove(CLASS_NAME_ACTIVE$1);\n    }\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = ScrollSpy.getOrCreateInstance(this, config);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config]();\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(window, EVENT_LOAD_DATA_API$1, () => {\n  for (const spy of SelectorEngine.find(SELECTOR_DATA_SPY)) {\n    ScrollSpy.getOrCreateInstance(spy);\n  }\n});\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(ScrollSpy);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME$1 = 'tab';\nconst DATA_KEY$1 = 'bs.tab';\nconst EVENT_KEY$1 = `.${DATA_KEY$1}`;\nconst EVENT_HIDE$1 = `hide${EVENT_KEY$1}`;\nconst EVENT_HIDDEN$1 = `hidden${EVENT_KEY$1}`;\nconst EVENT_SHOW$1 = `show${EVENT_KEY$1}`;\nconst EVENT_SHOWN$1 = `shown${EVENT_KEY$1}`;\nconst EVENT_CLICK_DATA_API = `click${EVENT_KEY$1}`;\nconst EVENT_KEYDOWN = `keydown${EVENT_KEY$1}`;\nconst EVENT_LOAD_DATA_API = `load${EVENT_KEY$1}`;\nconst ARROW_LEFT_KEY = 'ArrowLeft';\nconst ARROW_RIGHT_KEY = 'ArrowRight';\nconst ARROW_UP_KEY = 'ArrowUp';\nconst ARROW_DOWN_KEY = 'ArrowDown';\nconst HOME_KEY = 'Home';\nconst END_KEY = 'End';\nconst CLASS_NAME_ACTIVE = 'active';\nconst CLASS_NAME_FADE$1 = 'fade';\nconst CLASS_NAME_SHOW$1 = 'show';\nconst CLASS_DROPDOWN = 'dropdown';\nconst SELECTOR_DROPDOWN_TOGGLE = '.dropdown-toggle';\nconst SELECTOR_DROPDOWN_MENU = '.dropdown-menu';\nconst NOT_SELECTOR_DROPDOWN_TOGGLE = `:not(${SELECTOR_DROPDOWN_TOGGLE})`;\nconst SELECTOR_TAB_PANEL = '.list-group, .nav, [role=\"tablist\"]';\nconst SELECTOR_OUTER = '.nav-item, .list-group-item';\nconst SELECTOR_INNER = `.nav-link${NOT_SELECTOR_DROPDOWN_TOGGLE}, .list-group-item${NOT_SELECTOR_DROPDOWN_TOGGLE}, [role=\"tab\"]${NOT_SELECTOR_DROPDOWN_TOGGLE}`;\nconst SELECTOR_DATA_TOGGLE = '[data-bs-toggle=\"tab\"], [data-bs-toggle=\"pill\"], [data-bs-toggle=\"list\"]'; // TODO: could only be `tab` in v6\nconst SELECTOR_INNER_ELEM = `${SELECTOR_INNER}, ${SELECTOR_DATA_TOGGLE}`;\nconst SELECTOR_DATA_TOGGLE_ACTIVE = `.${CLASS_NAME_ACTIVE}[data-bs-toggle=\"tab\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"pill\"], .${CLASS_NAME_ACTIVE}[data-bs-toggle=\"list\"]`;\n\n/**\n * Class definition\n */\n\nclass Tab extends BaseComponent {\n  constructor(element) {\n    super(element);\n    this._parent = this._element.closest(SELECTOR_TAB_PANEL);\n    if (!this._parent) {\n      return;\n      // TODO: should throw exception in v6\n      // throw new TypeError(`${element.outerHTML} has not a valid parent ${SELECTOR_INNER_ELEM}`)\n    }\n\n    // Set up initial aria attributes\n    this._setInitialAttributes(this._parent, this._getChildren());\n    EventHandler.on(this._element, EVENT_KEYDOWN, event => this._keydown(event));\n  }\n\n  // Getters\n  static get NAME() {\n    return NAME$1;\n  }\n\n  // Public\n  show() {\n    // Shows this elem and deactivate the active sibling if exists\n    const innerElem = this._element;\n    if (this._elemIsActive(innerElem)) {\n      return;\n    }\n\n    // Search for active tab on same parent to deactivate it\n    const active = this._getActiveElem();\n    const hideEvent = active ? EventHandler.trigger(active, EVENT_HIDE$1, {\n      relatedTarget: innerElem\n    }) : null;\n    const showEvent = EventHandler.trigger(innerElem, EVENT_SHOW$1, {\n      relatedTarget: active\n    });\n    if (showEvent.defaultPrevented || hideEvent && hideEvent.defaultPrevented) {\n      return;\n    }\n    this._deactivate(active, innerElem);\n    this._activate(innerElem, active);\n  }\n\n  // Private\n  _activate(element, relatedElem) {\n    if (!element) {\n      return;\n    }\n    element.classList.add(CLASS_NAME_ACTIVE);\n    this._activate(SelectorEngine.getElementFromSelector(element)); // Search and activate/show the proper section\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.add(CLASS_NAME_SHOW$1);\n        return;\n      }\n      element.removeAttribute('tabindex');\n      element.setAttribute('aria-selected', true);\n      this._toggleDropDown(element, true);\n      EventHandler.trigger(element, EVENT_SHOWN$1, {\n        relatedTarget: relatedElem\n      });\n    };\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE$1));\n  }\n  _deactivate(element, relatedElem) {\n    if (!element) {\n      return;\n    }\n    element.classList.remove(CLASS_NAME_ACTIVE);\n    element.blur();\n    this._deactivate(SelectorEngine.getElementFromSelector(element)); // Search and deactivate the shown section too\n\n    const complete = () => {\n      if (element.getAttribute('role') !== 'tab') {\n        element.classList.remove(CLASS_NAME_SHOW$1);\n        return;\n      }\n      element.setAttribute('aria-selected', false);\n      element.setAttribute('tabindex', '-1');\n      this._toggleDropDown(element, false);\n      EventHandler.trigger(element, EVENT_HIDDEN$1, {\n        relatedTarget: relatedElem\n      });\n    };\n    this._queueCallback(complete, element, element.classList.contains(CLASS_NAME_FADE$1));\n  }\n  _keydown(event) {\n    if (![ARROW_LEFT_KEY, ARROW_RIGHT_KEY, ARROW_UP_KEY, ARROW_DOWN_KEY, HOME_KEY, END_KEY].includes(event.key)) {\n      return;\n    }\n    event.stopPropagation(); // stopPropagation/preventDefault both added to support up/down keys without scrolling the page\n    event.preventDefault();\n    const children = this._getChildren().filter(element => !isDisabled(element));\n    let nextActiveElement;\n    if ([HOME_KEY, END_KEY].includes(event.key)) {\n      nextActiveElement = children[event.key === HOME_KEY ? 0 : children.length - 1];\n    } else {\n      const isNext = [ARROW_RIGHT_KEY, ARROW_DOWN_KEY].includes(event.key);\n      nextActiveElement = getNextActiveElement(children, event.target, isNext, true);\n    }\n    if (nextActiveElement) {\n      nextActiveElement.focus({\n        preventScroll: true\n      });\n      Tab.getOrCreateInstance(nextActiveElement).show();\n    }\n  }\n  _getChildren() {\n    // collection of inner elements\n    return SelectorEngine.find(SELECTOR_INNER_ELEM, this._parent);\n  }\n  _getActiveElem() {\n    return this._getChildren().find(child => this._elemIsActive(child)) || null;\n  }\n  _setInitialAttributes(parent, children) {\n    this._setAttributeIfNotExists(parent, 'role', 'tablist');\n    for (const child of children) {\n      this._setInitialAttributesOnChild(child);\n    }\n  }\n  _setInitialAttributesOnChild(child) {\n    child = this._getInnerElement(child);\n    const isActive = this._elemIsActive(child);\n    const outerElem = this._getOuterElement(child);\n    child.setAttribute('aria-selected', isActive);\n    if (outerElem !== child) {\n      this._setAttributeIfNotExists(outerElem, 'role', 'presentation');\n    }\n    if (!isActive) {\n      child.setAttribute('tabindex', '-1');\n    }\n    this._setAttributeIfNotExists(child, 'role', 'tab');\n\n    // set attributes to the related panel too\n    this._setInitialAttributesOnTargetPanel(child);\n  }\n  _setInitialAttributesOnTargetPanel(child) {\n    const target = SelectorEngine.getElementFromSelector(child);\n    if (!target) {\n      return;\n    }\n    this._setAttributeIfNotExists(target, 'role', 'tabpanel');\n    if (child.id) {\n      this._setAttributeIfNotExists(target, 'aria-labelledby', `${child.id}`);\n    }\n  }\n  _toggleDropDown(element, open) {\n    const outerElem = this._getOuterElement(element);\n    if (!outerElem.classList.contains(CLASS_DROPDOWN)) {\n      return;\n    }\n    const toggle = (selector, className) => {\n      const element = SelectorEngine.findOne(selector, outerElem);\n      if (element) {\n        element.classList.toggle(className, open);\n      }\n    };\n    toggle(SELECTOR_DROPDOWN_TOGGLE, CLASS_NAME_ACTIVE);\n    toggle(SELECTOR_DROPDOWN_MENU, CLASS_NAME_SHOW$1);\n    outerElem.setAttribute('aria-expanded', open);\n  }\n  _setAttributeIfNotExists(element, attribute, value) {\n    if (!element.hasAttribute(attribute)) {\n      element.setAttribute(attribute, value);\n    }\n  }\n  _elemIsActive(elem) {\n    return elem.classList.contains(CLASS_NAME_ACTIVE);\n  }\n\n  // Try to get the inner element (usually the .nav-link)\n  _getInnerElement(elem) {\n    return elem.matches(SELECTOR_INNER_ELEM) ? elem : SelectorEngine.findOne(SELECTOR_INNER_ELEM, elem);\n  }\n\n  // Try to get the outer element (usually the .nav-item)\n  _getOuterElement(elem) {\n    return elem.closest(SELECTOR_OUTER) || elem;\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Tab.getOrCreateInstance(this);\n      if (typeof config !== 'string') {\n        return;\n      }\n      if (data[config] === undefined || config.startsWith('_') || config === 'constructor') {\n        throw new TypeError(`No method named \"${config}\"`);\n      }\n      data[config]();\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nEventHandler.on(document, EVENT_CLICK_DATA_API, SELECTOR_DATA_TOGGLE, function (event) {\n  if (['A', 'AREA'].includes(this.tagName)) {\n    event.preventDefault();\n  }\n  if (isDisabled(this)) {\n    return;\n  }\n  Tab.getOrCreateInstance(this).show();\n});\n\n/**\n * Initialize on focus\n */\nEventHandler.on(window, EVENT_LOAD_DATA_API, () => {\n  for (const element of SelectorEngine.find(SELECTOR_DATA_TOGGLE_ACTIVE)) {\n    Tab.getOrCreateInstance(element);\n  }\n});\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Tab);\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap toast.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n\n/**\n * Constants\n */\n\nconst NAME = 'toast';\nconst DATA_KEY = 'bs.toast';\nconst EVENT_KEY = `.${DATA_KEY}`;\nconst EVENT_MOUSEOVER = `mouseover${EVENT_KEY}`;\nconst EVENT_MOUSEOUT = `mouseout${EVENT_KEY}`;\nconst EVENT_FOCUSIN = `focusin${EVENT_KEY}`;\nconst EVENT_FOCUSOUT = `focusout${EVENT_KEY}`;\nconst EVENT_HIDE = `hide${EVENT_KEY}`;\nconst EVENT_HIDDEN = `hidden${EVENT_KEY}`;\nconst EVENT_SHOW = `show${EVENT_KEY}`;\nconst EVENT_SHOWN = `shown${EVENT_KEY}`;\nconst CLASS_NAME_FADE = 'fade';\nconst CLASS_NAME_HIDE = 'hide'; // @deprecated - kept here only for backwards compatibility\nconst CLASS_NAME_SHOW = 'show';\nconst CLASS_NAME_SHOWING = 'showing';\nconst DefaultType = {\n  animation: 'boolean',\n  autohide: 'boolean',\n  delay: 'number'\n};\nconst Default = {\n  animation: true,\n  autohide: true,\n  delay: 5000\n};\n\n/**\n * Class definition\n */\n\nclass Toast extends BaseComponent {\n  constructor(element, config) {\n    super(element, config);\n    this._timeout = null;\n    this._hasMouseInteraction = false;\n    this._hasKeyboardInteraction = false;\n    this._setListeners();\n  }\n\n  // Getters\n  static get Default() {\n    return Default;\n  }\n  static get DefaultType() {\n    return DefaultType;\n  }\n  static get NAME() {\n    return NAME;\n  }\n\n  // Public\n  show() {\n    const showEvent = EventHandler.trigger(this._element, EVENT_SHOW);\n    if (showEvent.defaultPrevented) {\n      return;\n    }\n    this._clearTimeout();\n    if (this._config.animation) {\n      this._element.classList.add(CLASS_NAME_FADE);\n    }\n    const complete = () => {\n      this._element.classList.remove(CLASS_NAME_SHOWING);\n      EventHandler.trigger(this._element, EVENT_SHOWN);\n      this._maybeScheduleHide();\n    };\n    this._element.classList.remove(CLASS_NAME_HIDE); // @deprecated\n    reflow(this._element);\n    this._element.classList.add(CLASS_NAME_SHOW, CLASS_NAME_SHOWING);\n    this._queueCallback(complete, this._element, this._config.animation);\n  }\n  hide() {\n    if (!this.isShown()) {\n      return;\n    }\n    const hideEvent = EventHandler.trigger(this._element, EVENT_HIDE);\n    if (hideEvent.defaultPrevented) {\n      return;\n    }\n    const complete = () => {\n      this._element.classList.add(CLASS_NAME_HIDE); // @deprecated\n      this._element.classList.remove(CLASS_NAME_SHOWING, CLASS_NAME_SHOW);\n      EventHandler.trigger(this._element, EVENT_HIDDEN);\n    };\n    this._element.classList.add(CLASS_NAME_SHOWING);\n    this._queueCallback(complete, this._element, this._config.animation);\n  }\n  dispose() {\n    this._clearTimeout();\n    if (this.isShown()) {\n      this._element.classList.remove(CLASS_NAME_SHOW);\n    }\n    super.dispose();\n  }\n  isShown() {\n    return this._element.classList.contains(CLASS_NAME_SHOW);\n  }\n\n  // Private\n\n  _maybeScheduleHide() {\n    if (!this._config.autohide) {\n      return;\n    }\n    if (this._hasMouseInteraction || this._hasKeyboardInteraction) {\n      return;\n    }\n    this._timeout = setTimeout(() => {\n      this.hide();\n    }, this._config.delay);\n  }\n  _onInteraction(event, isInteracting) {\n    switch (event.type) {\n      case 'mouseover':\n      case 'mouseout':\n        {\n          this._hasMouseInteraction = isInteracting;\n          break;\n        }\n      case 'focusin':\n      case 'focusout':\n        {\n          this._hasKeyboardInteraction = isInteracting;\n          break;\n        }\n    }\n    if (isInteracting) {\n      this._clearTimeout();\n      return;\n    }\n    const nextElement = event.relatedTarget;\n    if (this._element === nextElement || this._element.contains(nextElement)) {\n      return;\n    }\n    this._maybeScheduleHide();\n  }\n  _setListeners() {\n    EventHandler.on(this._element, EVENT_MOUSEOVER, event => this._onInteraction(event, true));\n    EventHandler.on(this._element, EVENT_MOUSEOUT, event => this._onInteraction(event, false));\n    EventHandler.on(this._element, EVENT_FOCUSIN, event => this._onInteraction(event, true));\n    EventHandler.on(this._element, EVENT_FOCUSOUT, event => this._onInteraction(event, false));\n  }\n  _clearTimeout() {\n    clearTimeout(this._timeout);\n    this._timeout = null;\n  }\n\n  // Static\n  static jQueryInterface(config) {\n    return this.each(function () {\n      const data = Toast.getOrCreateInstance(this, config);\n      if (typeof config === 'string') {\n        if (typeof data[config] === 'undefined') {\n          throw new TypeError(`No method named \"${config}\"`);\n        }\n        data[config](this);\n      }\n    });\n  }\n}\n\n/**\n * Data API implementation\n */\n\nenableDismissTrigger(Toast);\n\n/**\n * jQuery\n */\n\ndefineJQueryPlugin(Toast);\n\nexport { Alert, Button, Carousel, Collapse, Dropdown, Modal, Offcanvas, Popover, ScrollSpy, Tab, Toast, Tooltip };\n//# sourceMappingURL=bootstrap.esm.js.map\n", "import { Dropdown } from './bootstrap';\n\n/*\nCore dropdowns\n */\nlet dropdownTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"dropdown\"]'));\ndropdownTriggerList.map(function (dropdownTriggerEl) {\n\tlet options = {\n\t\tboundary: dropdownTriggerEl.getAttribute('data-bs-boundary') === 'viewport' ? document.querySelector('.btn') : 'clippingParents',\n\t}\n\treturn new Dropdown(dropdownTriggerEl, options);\n});\n", "import { Tooltip } from './bootstrap';\n\nlet tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"tooltip\"]'));\ntooltipTriggerList.map(function (tooltipTriggerEl) {\n\tlet options = {\n\t\tdelay: {show: 50, hide: 50},\n\t\thtml: tooltipTriggerEl.getAttribute(\"data-bs-html\") === \"true\" ?? false,\n\t\tplacement: tooltipTriggerEl.getAttribute('data-bs-placement') ?? 'auto'\n\t};\n\treturn new Tooltip(tooltipTriggerEl, options);\n});", "import { Popover } from './bootstrap';\n\n/*\nCore popovers\n */\nlet popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"popover\"]'));\npopoverTriggerList.map(function (popoverTriggerEl) {\n\tlet options = {\n\t\tdelay: {show: 50, hide: 50},\n\t\thtml: popoverTriggerEl.getAttribute('data-bs-html') === \"true\" ?? false,\n\t\tplacement: popoverTriggerEl.getAttribute('data-bs-placement') ?? 'auto'\n\t};\n\treturn new Popover(popoverTriggerEl, options);\n});", "/*\nSwitch icons\n */\nlet switchesTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"switch-icon\"]'));\nswitchesTriggerList.map(function (switchTriggerEl) {\n\tswitchTriggerEl.addEventListener('click', (e) => {\n\t\te.stopPropagation();\n\n\t\tswitchTriggerEl.classList.toggle('active');\n\t});\n});", "import { Toast } from './bootstrap';\n\n/*\nToasts\n */\nlet toastsTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle=\"toast\"]'));\ntoastsTriggerList.map(function (toastTriggerEl) {\n\tif (!toastTriggerEl.hasAttribute('data-bs-target')) {\n\t\treturn;\n\t}\n\n\tconst toastEl = new Toast(toastTriggerEl.getAttribute('data-bs-target'));\n\n\ttoastTriggerEl.addEventListener('click', () => {\n\t\ttoastEl.show()\n\t});\n});", "export const prefix = 'tblr-'\n\nexport const hexToRgba = (hex, opacity) => {\n  const result = /^#?([a-f\\d]{2})([a-f\\d]{2})([a-f\\d]{2})$/i.exec(hex)\n\n  return result ? `rgba(${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}, ${opacity})` : null\n}\n\nexport const getColor = (color, opacity = 1) => {\n  const c = getComputedStyle(document.body).getPropertyValue(`--${prefix}${color}`).trim()\n\n  if (opacity !== 1) {\n\t return hexToRgba(c, opacity)\n  }\n\n  return c\n}"], "names": ["elements", "document", "querySelectorAll", "length", "for<PERSON>ach", "element", "autosize", "options", "dataOptions", "getAttribute", "JSON", "parse", "Object", "assign", "error", "value", "parseInt", "innerHTML", "countUp", "window", "CountUp", "start", "maskElementList", "slice", "call", "map", "maskEl", "IMask", "mask", "dataset", "lazy", "top", "bottom", "right", "left", "auto", "basePlacements", "end", "clippingParents", "viewport", "popper", "reference", "variationPlacements", "reduce", "acc", "placement", "concat", "placements", "beforeRead", "read", "afterRead", "<PERSON><PERSON><PERSON>", "main", "<PERSON><PERSON><PERSON>", "beforeWrite", "write", "afterWrite", "modifierPhases", "getNodeName", "nodeName", "toLowerCase", "getWindow", "node", "toString", "ownerDocument", "defaultView", "isElement", "OwnElement", "Element", "isHTMLElement", "HTMLElement", "isShadowRoot", "ShadowRoot", "applyStyles", "_ref", "state", "keys", "name", "style", "styles", "attributes", "removeAttribute", "setAttribute", "effect", "_ref2", "initialStyles", "position", "strategy", "margin", "arrow", "styleProperties", "hasOwnProperty", "property", "attribute", "enabled", "phase", "fn", "requires", "getBasePlacement", "split", "max", "Math", "min", "round", "getUAString", "uaData", "navigator", "userAgentData", "brands", "Array", "isArray", "item", "brand", "version", "join", "userAgent", "isLayoutViewport", "test", "getBoundingClientRect", "includeScale", "isFixedStrategy", "clientRect", "scaleX", "scaleY", "offsetWidth", "width", "offsetHeight", "height", "visualViewport", "addVisualOffsets", "x", "offsetLeft", "y", "offsetTop", "getLayoutRect", "abs", "contains", "parent", "child", "rootNode", "getRootNode", "next", "isSameNode", "parentNode", "host", "getComputedStyle", "isTableElement", "indexOf", "getDocumentElement", "documentElement", "getParentNode", "assignedSlot", "getTrueOffsetParent", "offsetParent", "getContainingBlock", "isFirefox", "isIE", "elementCss", "currentNode", "css", "transform", "perspective", "contain", "<PERSON><PERSON><PERSON><PERSON>", "filter", "getOffsetParent", "getMainAxisFromPlacement", "within", "mathMax", "mathMin", "withinMaxClamp", "v", "getFreshSideObject", "mergePaddingObject", "paddingObject", "expandToHashMap", "hashMap", "key", "toPaddingObject", "padding", "rects", "_state$modifiersData$", "arrowElement", "popperOffsets", "modifiersData", "basePlacement", "axis", "isVertical", "len", "arrowRect", "minProp", "maxProp", "endDiff", "startDiff", "arrowOffsetParent", "clientSize", "clientHeight", "clientWidth", "centerToReference", "center", "offset", "axisProp", "centerOffset", "_options$element", "querySelector", "requiresIfExists", "getVariation", "unsetSides", "roundOffsetsByDPR", "win", "dpr", "devicePixelRatio", "mapToStyles", "_Object$assign2", "popperRect", "variation", "offsets", "gpuAcceleration", "adaptive", "roundOffsets", "isFixed", "_offsets$x", "_offsets$y", "_ref3", "hasX", "hasY", "sideX", "sideY", "heightProp", "widthProp", "offsetY", "offsetX", "commonStyles", "_ref4", "_Object$assign", "computeStyles", "_ref5", "_options$gpuAccelerat", "_options$adaptive", "_options$roundOffsets", "data", "passive", "instance", "_options$scroll", "scroll", "_options$resize", "resize", "scrollParents", "scrollParent", "addEventListener", "update", "removeEventListener", "hash", "getOppositePlacement", "replace", "matched", "getOppositeVariationPlacement", "getWindowScroll", "scrollLeft", "pageXOffset", "scrollTop", "pageYOffset", "getWindowScrollBarX", "getViewportRect", "html", "layoutViewport", "getDocumentRect", "_element$ownerDocumen", "winScroll", "body", "scrollWidth", "scrollHeight", "direction", "isScrollParent", "_getComputedStyle", "overflow", "overflowX", "overflowY", "getScrollParent", "listScrollParents", "list", "isBody", "target", "updatedList", "rectToClientRect", "rect", "getInnerBoundingClientRect", "clientTop", "clientLeft", "getClientRectFromMixedType", "clippingParent", "getClippingParents", "canEscapeClipping", "clipperElement", "getClippingRect", "boundary", "rootBoundary", "mainClippingParents", "firstClippingParent", "clippingRect", "accRect", "computeOffsets", "commonX", "commonY", "mainAxis", "detectOverflow", "_options", "_options$placement", "_options$strategy", "_options$boundary", "_options$rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "altContext", "clippingClientRect", "contextElement", "referenceClientRect", "popperClientRect", "elementClientRect", "overflowOffsets", "offsetData", "multiply", "computeAutoPlacement", "flipVariations", "_options$allowedAutoP", "allowedAutoPlacements", "allPlacements", "allowedPlacements", "overflows", "sort", "a", "b", "getExpandedFallbackPlacements", "oppositePlacement", "flip", "_skip", "_options$mainAxis", "checkMainAxis", "_options$altAxis", "altAxis", "checkAltAxis", "specifiedFallbackPlacements", "fallbackPlacements", "_options$flipVariatio", "preferredPlacement", "isBasePlacement", "referenceRect", "checksMap", "Map", "makeFallbackChecks", "firstFittingPlacement", "i", "_basePlacement", "isStartVariation", "mainVariationSide", "altVariationSide", "checks", "push", "every", "check", "set", "numberOfChecks", "_loop", "_i", "fittingPlacement", "find", "get", "_ret", "reset", "getSideOffsets", "preventedOffsets", "isAnySideFullyClipped", "some", "side", "hide", "preventOverflow", "referenceOverflow", "popperAltOverflow", "referenceClippingOffsets", "popperEscapeOffsets", "isReferenceHidden", "hasPopperEscaped", "distanceAndSkiddingToXY", "invertDistance", "skidding", "distance", "_options$offset", "_data$state$placement", "getAltAxis", "_options$tether", "tether", "_options$tetherOffset", "tetherOffset", "tetherOffsetValue", "normalizedTetherOffsetValue", "offsetModifierState", "_offsetModifierState$", "mainSide", "altSide", "additive", "minLen", "maxLen", "arrowPaddingObject", "arrowPaddingMin", "arrowPaddingMax", "arrowLen", "minOffset", "maxOffset", "clientOffset", "offsetModifierValue", "tetherMin", "tetherMax", "preventedOffset", "_offsetModifierState$2", "_mainSide", "_altSide", "_offset", "_len", "_min", "_max", "isOriginSide", "_offsetModifierValue", "_tetherMin", "_tetherMax", "_preventedOffset", "getHTMLElementScroll", "getNodeScroll", "isElementScaled", "getCompositeRect", "elementOrVirtualElement", "isOffsetParentAnElement", "offsetParentIsScaled", "order", "modifiers", "visited", "Set", "result", "modifier", "add", "dep", "has", "depModifier", "orderModifiers", "orderedModifiers", "debounce", "pending", "Promise", "resolve", "then", "undefined", "mergeByName", "merged", "current", "existing", "DEFAULT_OPTIONS", "areValidElements", "arguments", "args", "_key", "popperGenerator", "generatorOptions", "_generatorOptions", "_generatorOptions$def", "defaultModifiers", "_generatorOptions$def2", "defaultOptions", "createPopper", "effectCleanupFns", "isDestroyed", "setOptions", "setOptionsAction", "cleanupModifierEffects", "m", "runModifierEffects", "forceUpdate", "_state$elements", "index", "_state$orderedModifie", "_state$orderedModifie2", "destroy", "onFirstUpdate", "_ref$options", "cleanupFn", "noopFn", "eventListeners", "elementMap", "Data", "instanceMap", "size", "console", "from", "remove", "delete", "dropdownTriggerList", "dropdownTriggerEl", "Dropdown", "tooltipTriggerList", "tooltipTriggerEl", "delay", "show", "<PERSON><PERSON><PERSON>", "popoverTriggerList", "popoverTriggerEl", "Popover", "switchesTriggerList", "switchTriggerEl", "e", "stopPropagation", "classList", "toggle", "toastsTriggerList", "toastTriggerEl", "hasAttribute", "toastEl", "Toast", "prefix", "hexToRgba", "hex", "opacity", "exec", "getColor", "color", "c", "getPropertyValue", "trim"], "mappings": ";;;;;;;;;AAAA;;AAIA,MAAMA,UAAQ,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,6BAA6B,CAAC;AACzE,IAAIF,UAAQ,CAACG,MAAM,EAAE;AACpBH,EAAAA,UAAQ,CAACI,OAAO,CAAC,UAAUC,OAAO,EAAE;AACnCC,IAAAA,QAAQ,IAAIA,QAAQ,CAACD,OAAO,CAAC;AAC9B,GAAC,CAAC;AACH;;ACTA,MAAML,QAAQ,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,gBAAgB,CAAC;AAE5D,IAAIF,QAAQ,CAACG,MAAM,EAAE;AACpBH,EAAAA,QAAQ,CAACI,OAAO,CAAC,UAAUC,OAAO,EAAE;IACnC,IAAIE,OAAO,GAAG,EAAE;IAChB,IAAI;MACH,MAAMC,WAAW,GAAGH,OAAO,CAACI,YAAY,CAAC,cAAc,CAAC,GAAGC,IAAI,CAACC,KAAK,CAACN,OAAO,CAACI,YAAY,CAAC,cAAc,CAAC,CAAC,GAAG,EAAE;AAChHF,MAAAA,OAAO,GAAGK,MAAM,CAACC,MAAM,CAAC;AAAC,QAAA,iBAAiB,EAAE;OAAK,EAAEL,WAAW,CAAC;AAEhE,KAAC,CAAC,OAAOM,KAAK,EAAE;IAEhB,MAAMC,KAAK,GAAGC,QAAQ,CAACX,OAAO,CAACY,SAAS,EAAE,EAAE,CAAC;AAE7C,IAAA,MAAMC,OAAO,GAAG,IAAIC,MAAM,CAACD,OAAO,CAACE,OAAO,CAACf,OAAO,EAAEU,KAAK,EAAER,OAAO,CAAC;AACnE,IAAA,IAAI,CAACW,OAAO,CAACJ,KAAK,EAAE;MACnBI,OAAO,CAACG,KAAK,EAAE;AAChB;AACD,GAAC,CAAC;AACH;;AClBA;;AAIA,IAAIC,eAAe,GAAG,EAAE,CAACC,KAAK,CAACC,IAAI,CAACvB,QAAQ,CAACC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AAC7EoB,eAAe,CAACG,GAAG,CAAC,UAAUC,MAAM,EAAE;AACrCC,EAAAA,KAAK,IAAI,IAAIA,KAAK,CAACD,MAAM,EAAE;AAC1BE,IAAAA,IAAI,EAAEF,MAAM,CAACG,OAAO,CAACD,IAAI;AACzBE,IAAAA,IAAI,EAAEJ,MAAM,CAACG,OAAO,CAAC,cAAc,CAAC,KAAK;AAC1C,GAAC,CAAC;AACH,CAAC,CAAC;;ACVK,IAAIE,GAAG,GAAG,KAAK;AACf,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,cAAc,GAAG,CAACL,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,IAAI,CAAC;AAC/C,IAAIb,KAAK,GAAG,OAAO;AACnB,IAAIgB,GAAG,GAAG,KAAK;AACf,IAAIC,eAAe,GAAG,iBAAiB;AACvC,IAAIC,QAAQ,GAAG,UAAU;AACzB,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,SAAS,GAAG,WAAW;AAC3B,IAAIC,mBAAmB,gBAAgBN,cAAc,CAACO,MAAM,CAAC,UAAUC,GAAG,EAAEC,SAAS,EAAE;AAC5F,EAAA,OAAOD,GAAG,CAACE,MAAM,CAAC,CAACD,SAAS,GAAG,GAAG,GAAGxB,KAAK,EAAEwB,SAAS,GAAG,GAAG,GAAGR,GAAG,CAAC,CAAC;AACrE,CAAC,EAAE,EAAE,CAAC;AACC,IAAIU,UAAU,gBAAgB,EAAE,CAACD,MAAM,CAACV,cAAc,EAAE,CAACD,IAAI,CAAC,CAAC,CAACQ,MAAM,CAAC,UAAUC,GAAG,EAAEC,SAAS,EAAE;AACtG,EAAA,OAAOD,GAAG,CAACE,MAAM,CAAC,CAACD,SAAS,EAAEA,SAAS,GAAG,GAAG,GAAGxB,KAAK,EAAEwB,SAAS,GAAG,GAAG,GAAGR,GAAG,CAAC,CAAC;AAChF,CAAC,EAAE,EAAE,CAAC,CAAC;;AAEA,IAAIW,UAAU,GAAG,YAAY;AAC7B,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,SAAS,GAAG,WAAW,CAAC;;AAE5B,IAAIC,UAAU,GAAG,YAAY;AAC7B,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,SAAS,GAAG,WAAW,CAAC;;AAE5B,IAAIC,WAAW,GAAG,aAAa;AAC/B,IAAIC,KAAK,GAAG,OAAO;AACnB,IAAIC,UAAU,GAAG,YAAY;AAC7B,IAAIC,cAAc,GAAG,CAACT,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,UAAU,EAAEC,IAAI,EAAEC,SAAS,EAAEC,WAAW,EAAEC,KAAK,EAAEC,UAAU,CAAC;;AC9BvG,SAASE,WAAWA,CAACrD,OAAO,EAAE;AAC3C,EAAA,OAAOA,OAAO,GAAG,CAACA,OAAO,CAACsD,QAAQ,IAAI,EAAE,EAAEC,WAAW,EAAE,GAAG,IAAI;AAChE;;ACFe,SAASC,SAASA,CAACC,IAAI,EAAE;EACtC,IAAIA,IAAI,IAAI,IAAI,EAAE;AAChB,IAAA,OAAO3C,MAAM;AACf;AAEA,EAAA,IAAI2C,IAAI,CAACC,QAAQ,EAAE,KAAK,iBAAiB,EAAE;AACzC,IAAA,IAAIC,aAAa,GAAGF,IAAI,CAACE,aAAa;IACtC,OAAOA,aAAa,GAAGA,aAAa,CAACC,WAAW,IAAI9C,MAAM,GAAGA,MAAM;AACrE;AAEA,EAAA,OAAO2C,IAAI;AACb;;ACTA,SAASI,WAASA,CAACJ,IAAI,EAAE;AACvB,EAAA,IAAIK,UAAU,GAAGN,SAAS,CAACC,IAAI,CAAC,CAACM,OAAO;AACxC,EAAA,OAAON,IAAI,YAAYK,UAAU,IAAIL,IAAI,YAAYM,OAAO;AAC9D;AAEA,SAASC,aAAaA,CAACP,IAAI,EAAE;AAC3B,EAAA,IAAIK,UAAU,GAAGN,SAAS,CAACC,IAAI,CAAC,CAACQ,WAAW;AAC5C,EAAA,OAAOR,IAAI,YAAYK,UAAU,IAAIL,IAAI,YAAYQ,WAAW;AAClE;AAEA,SAASC,YAAYA,CAACT,IAAI,EAAE;AAC1B;AACA,EAAA,IAAI,OAAOU,UAAU,KAAK,WAAW,EAAE;AACrC,IAAA,OAAO,KAAK;AACd;AAEA,EAAA,IAAIL,UAAU,GAAGN,SAAS,CAACC,IAAI,CAAC,CAACU,UAAU;AAC3C,EAAA,OAAOV,IAAI,YAAYK,UAAU,IAAIL,IAAI,YAAYU,UAAU;AACjE;;AClBA;;AAEA,SAASC,WAAWA,CAACC,IAAI,EAAE;AACzB,EAAA,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;AACtB/D,EAAAA,MAAM,CAACgE,IAAI,CAACD,KAAK,CAAC3E,QAAQ,CAAC,CAACI,OAAO,CAAC,UAAUyE,IAAI,EAAE;IAClD,IAAIC,KAAK,GAAGH,KAAK,CAACI,MAAM,CAACF,IAAI,CAAC,IAAI,EAAE;IACpC,IAAIG,UAAU,GAAGL,KAAK,CAACK,UAAU,CAACH,IAAI,CAAC,IAAI,EAAE;IAC7C,IAAIxE,OAAO,GAAGsE,KAAK,CAAC3E,QAAQ,CAAC6E,IAAI,CAAC,CAAC;;IAEnC,IAAI,CAACR,aAAa,CAAChE,OAAO,CAAC,IAAI,CAACqD,WAAW,CAACrD,OAAO,CAAC,EAAE;AACpD,MAAA;AACF,KAAC;AACD;AACA;;IAGAO,MAAM,CAACC,MAAM,CAACR,OAAO,CAACyE,KAAK,EAAEA,KAAK,CAAC;IACnClE,MAAM,CAACgE,IAAI,CAACI,UAAU,CAAC,CAAC5E,OAAO,CAAC,UAAUyE,IAAI,EAAE;AAC9C,MAAA,IAAI9D,KAAK,GAAGiE,UAAU,CAACH,IAAI,CAAC;MAE5B,IAAI9D,KAAK,KAAK,KAAK,EAAE;AACnBV,QAAAA,OAAO,CAAC4E,eAAe,CAACJ,IAAI,CAAC;AAC/B,OAAC,MAAM;AACLxE,QAAAA,OAAO,CAAC6E,YAAY,CAACL,IAAI,EAAE9D,KAAK,KAAK,IAAI,GAAG,EAAE,GAAGA,KAAK,CAAC;AACzD;AACF,KAAC,CAAC;AACJ,GAAC,CAAC;AACJ;AAEA,SAASoE,QAAMA,CAACC,KAAK,EAAE;AACrB,EAAA,IAAIT,KAAK,GAAGS,KAAK,CAACT,KAAK;AACvB,EAAA,IAAIU,aAAa,GAAG;AAClB7C,IAAAA,MAAM,EAAE;AACN8C,MAAAA,QAAQ,EAAEX,KAAK,CAACpE,OAAO,CAACgF,QAAQ;AAChCrD,MAAAA,IAAI,EAAE,GAAG;AACTH,MAAAA,GAAG,EAAE,GAAG;AACRyD,MAAAA,MAAM,EAAE;KACT;AACDC,IAAAA,KAAK,EAAE;AACLH,MAAAA,QAAQ,EAAE;KACX;AACD7C,IAAAA,SAAS,EAAE;GACZ;AACD7B,EAAAA,MAAM,CAACC,MAAM,CAAC8D,KAAK,CAAC3E,QAAQ,CAACwC,MAAM,CAACsC,KAAK,EAAEO,aAAa,CAAC7C,MAAM,CAAC;EAChEmC,KAAK,CAACI,MAAM,GAAGM,aAAa;AAE5B,EAAA,IAAIV,KAAK,CAAC3E,QAAQ,CAACyF,KAAK,EAAE;AACxB7E,IAAAA,MAAM,CAACC,MAAM,CAAC8D,KAAK,CAAC3E,QAAQ,CAACyF,KAAK,CAACX,KAAK,EAAEO,aAAa,CAACI,KAAK,CAAC;AAChE;AAEA,EAAA,OAAO,YAAY;AACjB7E,IAAAA,MAAM,CAACgE,IAAI,CAACD,KAAK,CAAC3E,QAAQ,CAAC,CAACI,OAAO,CAAC,UAAUyE,IAAI,EAAE;AAClD,MAAA,IAAIxE,OAAO,GAAGsE,KAAK,CAAC3E,QAAQ,CAAC6E,IAAI,CAAC;MAClC,IAAIG,UAAU,GAAGL,KAAK,CAACK,UAAU,CAACH,IAAI,CAAC,IAAI,EAAE;AAC7C,MAAA,IAAIa,eAAe,GAAG9E,MAAM,CAACgE,IAAI,CAACD,KAAK,CAACI,MAAM,CAACY,cAAc,CAACd,IAAI,CAAC,GAAGF,KAAK,CAACI,MAAM,CAACF,IAAI,CAAC,GAAGQ,aAAa,CAACR,IAAI,CAAC,CAAC,CAAC;;MAEhH,IAAIC,KAAK,GAAGY,eAAe,CAAC/C,MAAM,CAAC,UAAUmC,KAAK,EAAEc,QAAQ,EAAE;AAC5Dd,QAAAA,KAAK,CAACc,QAAQ,CAAC,GAAG,EAAE;AACpB,QAAA,OAAOd,KAAK;AACd,OAAC,EAAE,EAAE,CAAC,CAAC;;MAEP,IAAI,CAACT,aAAa,CAAChE,OAAO,CAAC,IAAI,CAACqD,WAAW,CAACrD,OAAO,CAAC,EAAE;AACpD,QAAA;AACF;MAEAO,MAAM,CAACC,MAAM,CAACR,OAAO,CAACyE,KAAK,EAAEA,KAAK,CAAC;MACnClE,MAAM,CAACgE,IAAI,CAACI,UAAU,CAAC,CAAC5E,OAAO,CAAC,UAAUyF,SAAS,EAAE;AACnDxF,QAAAA,OAAO,CAAC4E,eAAe,CAACY,SAAS,CAAC;AACpC,OAAC,CAAC;AACJ,KAAC,CAAC;GACH;AACH,CAAC;;AAGD,sBAAe;AACbhB,EAAAA,IAAI,EAAE,aAAa;AACnBiB,EAAAA,OAAO,EAAE,IAAI;AACbC,EAAAA,KAAK,EAAE,OAAO;AACdC,EAAAA,EAAE,EAAEvB,WAAW;AACfU,EAAAA,MAAM,EAAEA,QAAM;EACdc,QAAQ,EAAE,CAAC,eAAe;AAC5B,CAAC;;AClFc,SAASC,gBAAgBA,CAACrD,SAAS,EAAE;EAClD,OAAOA,SAAS,CAACsD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC;;ACHO,IAAIC,GAAG,GAAGC,IAAI,CAACD,GAAG;AAClB,IAAIE,GAAG,GAAGD,IAAI,CAACC,GAAG;AAClB,IAAIC,KAAK,GAAGF,IAAI,CAACE,KAAK;;ACFd,SAASC,WAAWA,GAAG;AACpC,EAAA,IAAIC,MAAM,GAAGC,SAAS,CAACC,aAAa;AAEpC,EAAA,IAAIF,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACG,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACL,MAAM,CAACG,MAAM,CAAC,EAAE;IACnE,OAAOH,MAAM,CAACG,MAAM,CAACnF,GAAG,CAAC,UAAUsF,IAAI,EAAE;MACvC,OAAOA,IAAI,CAACC,KAAK,GAAG,GAAG,GAAGD,IAAI,CAACE,OAAO;AACxC,KAAC,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;AACd;EAEA,OAAOR,SAAS,CAACS,SAAS;AAC5B;;ACTe,SAASC,gBAAgBA,GAAG;EACzC,OAAO,CAAC,gCAAgC,CAACC,IAAI,CAACb,WAAW,EAAE,CAAC;AAC9D;;ACCe,SAASc,qBAAqBA,CAACjH,OAAO,EAAEkH,YAAY,EAAEC,eAAe,EAAE;AACpF,EAAA,IAAID,YAAY,KAAK,MAAM,EAAE;AAC3BA,IAAAA,YAAY,GAAG,KAAK;AACtB;AAEA,EAAA,IAAIC,eAAe,KAAK,MAAM,EAAE;AAC9BA,IAAAA,eAAe,GAAG,KAAK;AACzB;AAEA,EAAA,IAAIC,UAAU,GAAGpH,OAAO,CAACiH,qBAAqB,EAAE;EAChD,IAAII,MAAM,GAAG,CAAC;EACd,IAAIC,MAAM,GAAG,CAAC;AAEd,EAAA,IAAIJ,YAAY,IAAIlD,aAAa,CAAChE,OAAO,CAAC,EAAE;IAC1CqH,MAAM,GAAGrH,OAAO,CAACuH,WAAW,GAAG,CAAC,GAAGrB,KAAK,CAACkB,UAAU,CAACI,KAAK,CAAC,GAAGxH,OAAO,CAACuH,WAAW,IAAI,CAAC,GAAG,CAAC;IACzFD,MAAM,GAAGtH,OAAO,CAACyH,YAAY,GAAG,CAAC,GAAGvB,KAAK,CAACkB,UAAU,CAACM,MAAM,CAAC,GAAG1H,OAAO,CAACyH,YAAY,IAAI,CAAC,GAAG,CAAC;AAC9F;AAEA,EAAA,IAAIpD,IAAI,GAAGR,WAAS,CAAC7D,OAAO,CAAC,GAAGwD,SAAS,CAACxD,OAAO,CAAC,GAAGc,MAAM;IACvD6G,cAAc,GAAGtD,IAAI,CAACsD,cAAc;AAExC,EAAA,IAAIC,gBAAgB,GAAG,CAACb,gBAAgB,EAAE,IAAII,eAAe;AAC7D,EAAA,IAAIU,CAAC,GAAG,CAACT,UAAU,CAACvF,IAAI,IAAI+F,gBAAgB,IAAID,cAAc,GAAGA,cAAc,CAACG,UAAU,GAAG,CAAC,CAAC,IAAIT,MAAM;AACzG,EAAA,IAAIU,CAAC,GAAG,CAACX,UAAU,CAAC1F,GAAG,IAAIkG,gBAAgB,IAAID,cAAc,GAAGA,cAAc,CAACK,SAAS,GAAG,CAAC,CAAC,IAAIV,MAAM;AACvG,EAAA,IAAIE,KAAK,GAAGJ,UAAU,CAACI,KAAK,GAAGH,MAAM;AACrC,EAAA,IAAIK,MAAM,GAAGN,UAAU,CAACM,MAAM,GAAGJ,MAAM;EACvC,OAAO;AACLE,IAAAA,KAAK,EAAEA,KAAK;AACZE,IAAAA,MAAM,EAAEA,MAAM;AACdhG,IAAAA,GAAG,EAAEqG,CAAC;IACNnG,KAAK,EAAEiG,CAAC,GAAGL,KAAK;IAChB7F,MAAM,EAAEoG,CAAC,GAAGL,MAAM;AAClB7F,IAAAA,IAAI,EAAEgG,CAAC;AACPA,IAAAA,CAAC,EAAEA,CAAC;AACJE,IAAAA,CAAC,EAAEA;GACJ;AACH;;ACvCA;;AAEe,SAASE,aAAaA,CAACjI,OAAO,EAAE;AAC7C,EAAA,IAAIoH,UAAU,GAAGH,qBAAqB,CAACjH,OAAO,CAAC,CAAC;AAChD;;AAEA,EAAA,IAAIwH,KAAK,GAAGxH,OAAO,CAACuH,WAAW;AAC/B,EAAA,IAAIG,MAAM,GAAG1H,OAAO,CAACyH,YAAY;AAEjC,EAAA,IAAIzB,IAAI,CAACkC,GAAG,CAACd,UAAU,CAACI,KAAK,GAAGA,KAAK,CAAC,IAAI,CAAC,EAAE;IAC3CA,KAAK,GAAGJ,UAAU,CAACI,KAAK;AAC1B;AAEA,EAAA,IAAIxB,IAAI,CAACkC,GAAG,CAACd,UAAU,CAACM,MAAM,GAAGA,MAAM,CAAC,IAAI,CAAC,EAAE;IAC7CA,MAAM,GAAGN,UAAU,CAACM,MAAM;AAC5B;EAEA,OAAO;IACLG,CAAC,EAAE7H,OAAO,CAAC8H,UAAU;IACrBC,CAAC,EAAE/H,OAAO,CAACgI,SAAS;AACpBR,IAAAA,KAAK,EAAEA,KAAK;AACZE,IAAAA,MAAM,EAAEA;GACT;AACH;;ACvBe,SAASS,QAAQA,CAACC,MAAM,EAAEC,KAAK,EAAE;AAC9C,EAAA,IAAIC,QAAQ,GAAGD,KAAK,CAACE,WAAW,IAAIF,KAAK,CAACE,WAAW,EAAE,CAAC;;AAExD,EAAA,IAAIH,MAAM,CAACD,QAAQ,CAACE,KAAK,CAAC,EAAE;AAC1B,IAAA,OAAO,IAAI;AACb,GAAC;AAAC,OACG,IAAIC,QAAQ,IAAIpE,YAAY,CAACoE,QAAQ,CAAC,EAAE;IACzC,IAAIE,IAAI,GAAGH,KAAK;IAEhB,GAAG;MACD,IAAIG,IAAI,IAAIJ,MAAM,CAACK,UAAU,CAACD,IAAI,CAAC,EAAE;AACnC,QAAA,OAAO,IAAI;AACb,OAAC;;AAGDA,MAAAA,IAAI,GAAGA,IAAI,CAACE,UAAU,IAAIF,IAAI,CAACG,IAAI;AACrC,KAAC,QAAQH,IAAI;AACf,GAAC;;AAGH,EAAA,OAAO,KAAK;AACd;;ACrBe,SAASI,kBAAgBA,CAAC5I,OAAO,EAAE;EAChD,OAAOwD,SAAS,CAACxD,OAAO,CAAC,CAAC4I,gBAAgB,CAAC5I,OAAO,CAAC;AACrD;;ACFe,SAAS6I,cAAcA,CAAC7I,OAAO,EAAE;AAC9C,EAAA,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC8I,OAAO,CAACzF,WAAW,CAACrD,OAAO,CAAC,CAAC,IAAI,CAAC;AACjE;;ACFe,SAAS+I,kBAAkBA,CAAC/I,OAAO,EAAE;AAClD;EACA,OAAO,CAAC,CAAC6D,WAAS,CAAC7D,OAAO,CAAC,GAAGA,OAAO,CAAC2D,aAAa;AAAG;AACtD3D,EAAAA,OAAO,CAACJ,QAAQ,KAAKkB,MAAM,CAAClB,QAAQ,EAAEoJ,eAAe;AACvD;;ACFe,SAASC,aAAaA,CAACjJ,OAAO,EAAE;AAC7C,EAAA,IAAIqD,WAAW,CAACrD,OAAO,CAAC,KAAK,MAAM,EAAE;AACnC,IAAA,OAAOA,OAAO;AAChB;AAEA,EAAA;AAAQ;AACN;AACA;AACAA,IAAAA,OAAO,CAACkJ,YAAY;AAAI;AACxBlJ,IAAAA,OAAO,CAAC0I,UAAU;AAAM;IACxBxE,YAAY,CAAClE,OAAO,CAAC,GAAGA,OAAO,CAAC2I,IAAI,GAAG,IAAI,CAAC;AAAI;AAChD;IACAI,kBAAkB,CAAC/I,OAAO,CAAC;AAAC;AAGhC;;ACVA,SAASmJ,mBAAmBA,CAACnJ,OAAO,EAAE;AACpC,EAAA,IAAI,CAACgE,aAAa,CAAChE,OAAO,CAAC;AAAI;AAC/B4I,EAAAA,kBAAgB,CAAC5I,OAAO,CAAC,CAACiF,QAAQ,KAAK,OAAO,EAAE;AAC9C,IAAA,OAAO,IAAI;AACb;EAEA,OAAOjF,OAAO,CAACoJ,YAAY;AAC7B,CAAC;AACD;;AAGA,SAASC,kBAAkBA,CAACrJ,OAAO,EAAE;EACnC,IAAIsJ,SAAS,GAAG,UAAU,CAACtC,IAAI,CAACb,WAAW,EAAE,CAAC;EAC9C,IAAIoD,IAAI,GAAG,UAAU,CAACvC,IAAI,CAACb,WAAW,EAAE,CAAC;AAEzC,EAAA,IAAIoD,IAAI,IAAIvF,aAAa,CAAChE,OAAO,CAAC,EAAE;AAClC;AACA,IAAA,IAAIwJ,UAAU,GAAGZ,kBAAgB,CAAC5I,OAAO,CAAC;AAE1C,IAAA,IAAIwJ,UAAU,CAACvE,QAAQ,KAAK,OAAO,EAAE;AACnC,MAAA,OAAO,IAAI;AACb;AACF;AAEA,EAAA,IAAIwE,WAAW,GAAGR,aAAa,CAACjJ,OAAO,CAAC;AAExC,EAAA,IAAIkE,YAAY,CAACuF,WAAW,CAAC,EAAE;IAC7BA,WAAW,GAAGA,WAAW,CAACd,IAAI;AAChC;EAEA,OAAO3E,aAAa,CAACyF,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAACX,OAAO,CAACzF,WAAW,CAACoG,WAAW,CAAC,CAAC,GAAG,CAAC,EAAE;AAC3F,IAAA,IAAIC,GAAG,GAAGd,kBAAgB,CAACa,WAAW,CAAC,CAAC;AACxC;AACA;;IAEA,IAAIC,GAAG,CAACC,SAAS,KAAK,MAAM,IAAID,GAAG,CAACE,WAAW,KAAK,MAAM,IAAIF,GAAG,CAACG,OAAO,KAAK,OAAO,IAAI,CAAC,WAAW,EAAE,aAAa,CAAC,CAACf,OAAO,CAACY,GAAG,CAACI,UAAU,CAAC,KAAK,EAAE,IAAIR,SAAS,IAAII,GAAG,CAACI,UAAU,KAAK,QAAQ,IAAIR,SAAS,IAAII,GAAG,CAACK,MAAM,IAAIL,GAAG,CAACK,MAAM,KAAK,MAAM,EAAE;AACpP,MAAA,OAAON,WAAW;AACpB,KAAC,MAAM;MACLA,WAAW,GAAGA,WAAW,CAACf,UAAU;AACtC;AACF;AAEA,EAAA,OAAO,IAAI;AACb,CAAC;AACD;;AAGe,SAASsB,eAAeA,CAAChK,OAAO,EAAE;AAC/C,EAAA,IAAIc,MAAM,GAAG0C,SAAS,CAACxD,OAAO,CAAC;AAC/B,EAAA,IAAIoJ,YAAY,GAAGD,mBAAmB,CAACnJ,OAAO,CAAC;AAE/C,EAAA,OAAOoJ,YAAY,IAAIP,cAAc,CAACO,YAAY,CAAC,IAAIR,kBAAgB,CAACQ,YAAY,CAAC,CAACnE,QAAQ,KAAK,QAAQ,EAAE;AAC3GmE,IAAAA,YAAY,GAAGD,mBAAmB,CAACC,YAAY,CAAC;AAClD;EAEA,IAAIA,YAAY,KAAK/F,WAAW,CAAC+F,YAAY,CAAC,KAAK,MAAM,IAAI/F,WAAW,CAAC+F,YAAY,CAAC,KAAK,MAAM,IAAIR,kBAAgB,CAACQ,YAAY,CAAC,CAACnE,QAAQ,KAAK,QAAQ,CAAC,EAAE;AAC1J,IAAA,OAAOnE,MAAM;AACf;AAEA,EAAA,OAAOsI,YAAY,IAAIC,kBAAkB,CAACrJ,OAAO,CAAC,IAAIc,MAAM;AAC9D;;ACpEe,SAASmJ,wBAAwBA,CAACzH,SAAS,EAAE;AAC1D,EAAA,OAAO,CAAC,KAAK,EAAE,QAAQ,CAAC,CAACsG,OAAO,CAACtG,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;AAC9D;;ACDO,SAAS0H,MAAMA,CAACjE,KAAG,EAAEvF,KAAK,EAAEqF,KAAG,EAAE;EACtC,OAAOoE,GAAO,CAAClE,KAAG,EAAEmE,GAAO,CAAC1J,KAAK,EAAEqF,KAAG,CAAC,CAAC;AAC1C;AACO,SAASsE,cAAcA,CAACpE,GAAG,EAAEvF,KAAK,EAAEqF,GAAG,EAAE;EAC9C,IAAIuE,CAAC,GAAGJ,MAAM,CAACjE,GAAG,EAAEvF,KAAK,EAAEqF,GAAG,CAAC;AAC/B,EAAA,OAAOuE,CAAC,GAAGvE,GAAG,GAAGA,GAAG,GAAGuE,CAAC;AAC1B;;ACPe,SAASC,kBAAkBA,GAAG;EAC3C,OAAO;AACL7I,IAAAA,GAAG,EAAE,CAAC;AACNE,IAAAA,KAAK,EAAE,CAAC;AACRD,IAAAA,MAAM,EAAE,CAAC;AACTE,IAAAA,IAAI,EAAE;GACP;AACH;;ACNe,SAAS2I,kBAAkBA,CAACC,aAAa,EAAE;AACxD,EAAA,OAAOlK,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE+J,kBAAkB,EAAE,EAAEE,aAAa,CAAC;AAC/D;;ACHe,SAASC,eAAeA,CAAChK,KAAK,EAAE6D,IAAI,EAAE;EACnD,OAAOA,IAAI,CAACjC,MAAM,CAAC,UAAUqI,OAAO,EAAEC,GAAG,EAAE;AACzCD,IAAAA,OAAO,CAACC,GAAG,CAAC,GAAGlK,KAAK;AACpB,IAAA,OAAOiK,OAAO;GACf,EAAE,EAAE,CAAC;AACR;;ACKA,IAAIE,eAAe,GAAG,SAASA,eAAeA,CAACC,OAAO,EAAExG,KAAK,EAAE;AAC7DwG,EAAAA,OAAO,GAAG,OAAOA,OAAO,KAAK,UAAU,GAAGA,OAAO,CAACvK,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE8D,KAAK,CAACyG,KAAK,EAAE;IAC/EvI,SAAS,EAAE8B,KAAK,CAAC9B;GAClB,CAAC,CAAC,GAAGsI,OAAO;AACb,EAAA,OAAON,kBAAkB,CAAC,OAAOM,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGJ,eAAe,CAACI,OAAO,EAAE/I,cAAc,CAAC,CAAC;AAC7G,CAAC;AAED,SAASqD,KAAKA,CAACf,IAAI,EAAE;AACnB,EAAA,IAAI2G,qBAAqB;AAEzB,EAAA,IAAI1G,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBE,IAAI,GAAGH,IAAI,CAACG,IAAI;IAChBtE,OAAO,GAAGmE,IAAI,CAACnE,OAAO;AAC1B,EAAA,IAAI+K,YAAY,GAAG3G,KAAK,CAAC3E,QAAQ,CAACyF,KAAK;AACvC,EAAA,IAAI8F,aAAa,GAAG5G,KAAK,CAAC6G,aAAa,CAACD,aAAa;AACrD,EAAA,IAAIE,aAAa,GAAGvF,gBAAgB,CAACvB,KAAK,CAAC9B,SAAS,CAAC;AACrD,EAAA,IAAI6I,IAAI,GAAGpB,wBAAwB,CAACmB,aAAa,CAAC;AAClD,EAAA,IAAIE,UAAU,GAAG,CAACzJ,IAAI,EAAED,KAAK,CAAC,CAACkH,OAAO,CAACsC,aAAa,CAAC,IAAI,CAAC;AAC1D,EAAA,IAAIG,GAAG,GAAGD,UAAU,GAAG,QAAQ,GAAG,OAAO;AAEzC,EAAA,IAAI,CAACL,YAAY,IAAI,CAACC,aAAa,EAAE;AACnC,IAAA;AACF;EAEA,IAAIT,aAAa,GAAGI,eAAe,CAAC3K,OAAO,CAAC4K,OAAO,EAAExG,KAAK,CAAC;AAC3D,EAAA,IAAIkH,SAAS,GAAGvD,aAAa,CAACgD,YAAY,CAAC;EAC3C,IAAIQ,OAAO,GAAGJ,IAAI,KAAK,GAAG,GAAG3J,GAAG,GAAGG,IAAI;EACvC,IAAI6J,OAAO,GAAGL,IAAI,KAAK,GAAG,GAAG1J,MAAM,GAAGC,KAAK;AAC3C,EAAA,IAAI+J,OAAO,GAAGrH,KAAK,CAACyG,KAAK,CAAC3I,SAAS,CAACmJ,GAAG,CAAC,GAAGjH,KAAK,CAACyG,KAAK,CAAC3I,SAAS,CAACiJ,IAAI,CAAC,GAAGH,aAAa,CAACG,IAAI,CAAC,GAAG/G,KAAK,CAACyG,KAAK,CAAC5I,MAAM,CAACoJ,GAAG,CAAC;AACtH,EAAA,IAAIK,SAAS,GAAGV,aAAa,CAACG,IAAI,CAAC,GAAG/G,KAAK,CAACyG,KAAK,CAAC3I,SAAS,CAACiJ,IAAI,CAAC;AACjE,EAAA,IAAIQ,iBAAiB,GAAG7B,eAAe,CAACiB,YAAY,CAAC;EACrD,IAAIa,UAAU,GAAGD,iBAAiB,GAAGR,IAAI,KAAK,GAAG,GAAGQ,iBAAiB,CAACE,YAAY,IAAI,CAAC,GAAGF,iBAAiB,CAACG,WAAW,IAAI,CAAC,GAAG,CAAC;EAChI,IAAIC,iBAAiB,GAAGN,OAAO,GAAG,CAAC,GAAGC,SAAS,GAAG,CAAC,CAAC;AACpD;;AAEA,EAAA,IAAI3F,GAAG,GAAGwE,aAAa,CAACgB,OAAO,CAAC;AAChC,EAAA,IAAI1F,GAAG,GAAG+F,UAAU,GAAGN,SAAS,CAACD,GAAG,CAAC,GAAGd,aAAa,CAACiB,OAAO,CAAC;AAC9D,EAAA,IAAIQ,MAAM,GAAGJ,UAAU,GAAG,CAAC,GAAGN,SAAS,CAACD,GAAG,CAAC,GAAG,CAAC,GAAGU,iBAAiB;EACpE,IAAIE,MAAM,GAAGjC,MAAM,CAACjE,GAAG,EAAEiG,MAAM,EAAEnG,GAAG,CAAC,CAAC;;EAEtC,IAAIqG,QAAQ,GAAGf,IAAI;EACnB/G,KAAK,CAAC6G,aAAa,CAAC3G,IAAI,CAAC,IAAIwG,qBAAqB,GAAG,EAAE,EAAEA,qBAAqB,CAACoB,QAAQ,CAAC,GAAGD,MAAM,EAAEnB,qBAAqB,CAACqB,YAAY,GAAGF,MAAM,GAAGD,MAAM,EAAElB,qBAAqB,CAAC;AACjL;AAEA,SAASlG,QAAMA,CAACC,KAAK,EAAE;AACrB,EAAA,IAAIT,KAAK,GAAGS,KAAK,CAACT,KAAK;IACnBpE,OAAO,GAAG6E,KAAK,CAAC7E,OAAO;AAC3B,EAAA,IAAIoM,gBAAgB,GAAGpM,OAAO,CAACF,OAAO;IAClCiL,YAAY,GAAGqB,gBAAgB,KAAK,MAAM,GAAG,qBAAqB,GAAGA,gBAAgB;EAEzF,IAAIrB,YAAY,IAAI,IAAI,EAAE;AACxB,IAAA;AACF,GAAC;;AAGD,EAAA,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;IACpCA,YAAY,GAAG3G,KAAK,CAAC3E,QAAQ,CAACwC,MAAM,CAACoK,aAAa,CAACtB,YAAY,CAAC;IAEhE,IAAI,CAACA,YAAY,EAAE;AACjB,MAAA;AACF;AACF;EAEA,IAAI,CAAC9C,QAAQ,CAAC7D,KAAK,CAAC3E,QAAQ,CAACwC,MAAM,EAAE8I,YAAY,CAAC,EAAE;AAClD,IAAA;AACF;AAEA3G,EAAAA,KAAK,CAAC3E,QAAQ,CAACyF,KAAK,GAAG6F,YAAY;AACrC,CAAC;;AAGD,gBAAe;AACbzG,EAAAA,IAAI,EAAE,OAAO;AACbiB,EAAAA,OAAO,EAAE,IAAI;AACbC,EAAAA,KAAK,EAAE,MAAM;AACbC,EAAAA,EAAE,EAAEP,KAAK;AACTN,EAAAA,MAAM,EAAEA,QAAM;EACdc,QAAQ,EAAE,CAAC,eAAe,CAAC;EAC3B4G,gBAAgB,EAAE,CAAC,iBAAiB;AACtC,CAAC;;ACzFc,SAASC,YAAYA,CAACjK,SAAS,EAAE;EAC9C,OAAOA,SAAS,CAACsD,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChC;;ACOA,IAAI4G,UAAU,GAAG;AACfhL,EAAAA,GAAG,EAAE,MAAM;AACXE,EAAAA,KAAK,EAAE,MAAM;AACbD,EAAAA,MAAM,EAAE,MAAM;AACdE,EAAAA,IAAI,EAAE;AACR,CAAC,CAAC;AACF;AACA;;AAEA,SAAS8K,iBAAiBA,CAACtI,IAAI,EAAEuI,GAAG,EAAE;AACpC,EAAA,IAAI/E,CAAC,GAAGxD,IAAI,CAACwD,CAAC;IACVE,CAAC,GAAG1D,IAAI,CAAC0D,CAAC;AACd,EAAA,IAAI8E,GAAG,GAAGD,GAAG,CAACE,gBAAgB,IAAI,CAAC;EACnC,OAAO;IACLjF,CAAC,EAAE3B,KAAK,CAAC2B,CAAC,GAAGgF,GAAG,CAAC,GAAGA,GAAG,IAAI,CAAC;IAC5B9E,CAAC,EAAE7B,KAAK,CAAC6B,CAAC,GAAG8E,GAAG,CAAC,GAAGA,GAAG,IAAI;GAC5B;AACH;AAEO,SAASE,WAAWA,CAAChI,KAAK,EAAE;AACjC,EAAA,IAAIiI,eAAe;AAEnB,EAAA,IAAI7K,MAAM,GAAG4C,KAAK,CAAC5C,MAAM;IACrB8K,UAAU,GAAGlI,KAAK,CAACkI,UAAU;IAC7BzK,SAAS,GAAGuC,KAAK,CAACvC,SAAS;IAC3B0K,SAAS,GAAGnI,KAAK,CAACmI,SAAS;IAC3BC,OAAO,GAAGpI,KAAK,CAACoI,OAAO;IACvBlI,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IACzBmI,eAAe,GAAGrI,KAAK,CAACqI,eAAe;IACvCC,QAAQ,GAAGtI,KAAK,CAACsI,QAAQ;IACzBC,YAAY,GAAGvI,KAAK,CAACuI,YAAY;IACjCC,OAAO,GAAGxI,KAAK,CAACwI,OAAO;AAC3B,EAAA,IAAIC,UAAU,GAAGL,OAAO,CAACtF,CAAC;IACtBA,CAAC,GAAG2F,UAAU,KAAK,MAAM,GAAG,CAAC,GAAGA,UAAU;IAC1CC,UAAU,GAAGN,OAAO,CAACpF,CAAC;IACtBA,CAAC,GAAG0F,UAAU,KAAK,MAAM,GAAG,CAAC,GAAGA,UAAU;EAE9C,IAAIC,KAAK,GAAG,OAAOJ,YAAY,KAAK,UAAU,GAAGA,YAAY,CAAC;AAC5DzF,IAAAA,CAAC,EAAEA,CAAC;AACJE,IAAAA,CAAC,EAAEA;AACL,GAAC,CAAC,GAAG;AACHF,IAAAA,CAAC,EAAEA,CAAC;AACJE,IAAAA,CAAC,EAAEA;GACJ;EAEDF,CAAC,GAAG6F,KAAK,CAAC7F,CAAC;EACXE,CAAC,GAAG2F,KAAK,CAAC3F,CAAC;AACX,EAAA,IAAI4F,IAAI,GAAGR,OAAO,CAAC7H,cAAc,CAAC,GAAG,CAAC;AACtC,EAAA,IAAIsI,IAAI,GAAGT,OAAO,CAAC7H,cAAc,CAAC,GAAG,CAAC;EACtC,IAAIuI,KAAK,GAAGhM,IAAI;EAChB,IAAIiM,KAAK,GAAGpM,GAAG;EACf,IAAIkL,GAAG,GAAG9L,MAAM;AAEhB,EAAA,IAAIuM,QAAQ,EAAE;AACZ,IAAA,IAAIjE,YAAY,GAAGY,eAAe,CAAC7H,MAAM,CAAC;IAC1C,IAAI4L,UAAU,GAAG,cAAc;IAC/B,IAAIC,SAAS,GAAG,aAAa;AAE7B,IAAA,IAAI5E,YAAY,KAAK5F,SAAS,CAACrB,MAAM,CAAC,EAAE;AACtCiH,MAAAA,YAAY,GAAGL,kBAAkB,CAAC5G,MAAM,CAAC;AAEzC,MAAA,IAAIyG,kBAAgB,CAACQ,YAAY,CAAC,CAACnE,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,UAAU,EAAE;AACnF8I,QAAAA,UAAU,GAAG,cAAc;AAC3BC,QAAAA,SAAS,GAAG,aAAa;AAC3B;AACF,KAAC;;AAGD5E,IAAAA,YAAY,GAAGA,YAAY;AAE3B,IAAA,IAAI5G,SAAS,KAAKd,GAAG,IAAI,CAACc,SAAS,KAAKX,IAAI,IAAIW,SAAS,KAAKZ,KAAK,KAAKsL,SAAS,KAAKlL,GAAG,EAAE;AACzF8L,MAAAA,KAAK,GAAGnM,MAAM;AACd,MAAA,IAAIsM,OAAO,GAAGV,OAAO,IAAInE,YAAY,KAAKwD,GAAG,IAAIA,GAAG,CAACjF,cAAc,GAAGiF,GAAG,CAACjF,cAAc,CAACD,MAAM;AAAG;MAClG0B,YAAY,CAAC2E,UAAU,CAAC;AACxBhG,MAAAA,CAAC,IAAIkG,OAAO,GAAGhB,UAAU,CAACvF,MAAM;AAChCK,MAAAA,CAAC,IAAIqF,eAAe,GAAG,CAAC,GAAG,EAAE;AAC/B;AAEA,IAAA,IAAI5K,SAAS,KAAKX,IAAI,IAAI,CAACW,SAAS,KAAKd,GAAG,IAAIc,SAAS,KAAKb,MAAM,KAAKuL,SAAS,KAAKlL,GAAG,EAAE;AAC1F6L,MAAAA,KAAK,GAAGjM,KAAK;AACb,MAAA,IAAIsM,OAAO,GAAGX,OAAO,IAAInE,YAAY,KAAKwD,GAAG,IAAIA,GAAG,CAACjF,cAAc,GAAGiF,GAAG,CAACjF,cAAc,CAACH,KAAK;AAAG;MACjG4B,YAAY,CAAC4E,SAAS,CAAC;AACvBnG,MAAAA,CAAC,IAAIqG,OAAO,GAAGjB,UAAU,CAACzF,KAAK;AAC/BK,MAAAA,CAAC,IAAIuF,eAAe,GAAG,CAAC,GAAG,EAAE;AAC/B;AACF;AAEA,EAAA,IAAIe,YAAY,GAAG5N,MAAM,CAACC,MAAM,CAAC;AAC/ByE,IAAAA,QAAQ,EAAEA;AACZ,GAAC,EAAEoI,QAAQ,IAAIX,UAAU,CAAC;AAE1B,EAAA,IAAI0B,KAAK,GAAGd,YAAY,KAAK,IAAI,GAAGX,iBAAiB,CAAC;AACpD9E,IAAAA,CAAC,EAAEA,CAAC;AACJE,IAAAA,CAAC,EAAEA;AACL,GAAC,EAAEvE,SAAS,CAACrB,MAAM,CAAC,CAAC,GAAG;AACtB0F,IAAAA,CAAC,EAAEA,CAAC;AACJE,IAAAA,CAAC,EAAEA;GACJ;EAEDF,CAAC,GAAGuG,KAAK,CAACvG,CAAC;EACXE,CAAC,GAAGqG,KAAK,CAACrG,CAAC;AAEX,EAAA,IAAIqF,eAAe,EAAE;AACnB,IAAA,IAAIiB,cAAc;AAElB,IAAA,OAAO9N,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE2N,YAAY,GAAGE,cAAc,GAAG,EAAE,EAAEA,cAAc,CAACP,KAAK,CAAC,GAAGF,IAAI,GAAG,GAAG,GAAG,EAAE,EAAES,cAAc,CAACR,KAAK,CAAC,GAAGF,IAAI,GAAG,GAAG,GAAG,EAAE,EAAEU,cAAc,CAAC1E,SAAS,GAAG,CAACiD,GAAG,CAACE,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG,YAAY,GAAGjF,CAAC,GAAG,MAAM,GAAGE,CAAC,GAAG,KAAK,GAAG,cAAc,GAAGF,CAAC,GAAG,MAAM,GAAGE,CAAC,GAAG,QAAQ,EAAEsG,cAAc,EAAE;AACnT;EAEA,OAAO9N,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE2N,YAAY,GAAGnB,eAAe,GAAG,EAAE,EAAEA,eAAe,CAACc,KAAK,CAAC,GAAGF,IAAI,GAAG7F,CAAC,GAAG,IAAI,GAAG,EAAE,EAAEiF,eAAe,CAACa,KAAK,CAAC,GAAGF,IAAI,GAAG9F,CAAC,GAAG,IAAI,GAAG,EAAE,EAAEmF,eAAe,CAACrD,SAAS,GAAG,EAAE,EAAEqD,eAAe,EAAE;AAC/M;AAEA,SAASsB,aAAaA,CAACC,KAAK,EAAE;AAC5B,EAAA,IAAIjK,KAAK,GAAGiK,KAAK,CAACjK,KAAK;IACnBpE,OAAO,GAAGqO,KAAK,CAACrO,OAAO;AAC3B,EAAA,IAAIsO,qBAAqB,GAAGtO,OAAO,CAACkN,eAAe;IAC/CA,eAAe,GAAGoB,qBAAqB,KAAK,MAAM,GAAG,IAAI,GAAGA,qBAAqB;IACjFC,iBAAiB,GAAGvO,OAAO,CAACmN,QAAQ;IACpCA,QAAQ,GAAGoB,iBAAiB,KAAK,MAAM,GAAG,IAAI,GAAGA,iBAAiB;IAClEC,qBAAqB,GAAGxO,OAAO,CAACoN,YAAY;IAC5CA,YAAY,GAAGoB,qBAAqB,KAAK,MAAM,GAAG,IAAI,GAAGA,qBAAqB;AAClF,EAAA,IAAIP,YAAY,GAAG;AACjB3L,IAAAA,SAAS,EAAEqD,gBAAgB,CAACvB,KAAK,CAAC9B,SAAS,CAAC;AAC5C0K,IAAAA,SAAS,EAAET,YAAY,CAACnI,KAAK,CAAC9B,SAAS,CAAC;AACxCL,IAAAA,MAAM,EAAEmC,KAAK,CAAC3E,QAAQ,CAACwC,MAAM;AAC7B8K,IAAAA,UAAU,EAAE3I,KAAK,CAACyG,KAAK,CAAC5I,MAAM;AAC9BiL,IAAAA,eAAe,EAAEA,eAAe;AAChCG,IAAAA,OAAO,EAAEjJ,KAAK,CAACpE,OAAO,CAACgF,QAAQ,KAAK;GACrC;AAED,EAAA,IAAIZ,KAAK,CAAC6G,aAAa,CAACD,aAAa,IAAI,IAAI,EAAE;AAC7C5G,IAAAA,KAAK,CAACI,MAAM,CAACvC,MAAM,GAAG5B,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE8D,KAAK,CAACI,MAAM,CAACvC,MAAM,EAAE4K,WAAW,CAACxM,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE2N,YAAY,EAAE;AACvGhB,MAAAA,OAAO,EAAE7I,KAAK,CAAC6G,aAAa,CAACD,aAAa;AAC1CjG,MAAAA,QAAQ,EAAEX,KAAK,CAACpE,OAAO,CAACgF,QAAQ;AAChCmI,MAAAA,QAAQ,EAAEA,QAAQ;AAClBC,MAAAA,YAAY,EAAEA;KACf,CAAC,CAAC,CAAC;AACN;AAEA,EAAA,IAAIhJ,KAAK,CAAC6G,aAAa,CAAC/F,KAAK,IAAI,IAAI,EAAE;AACrCd,IAAAA,KAAK,CAACI,MAAM,CAACU,KAAK,GAAG7E,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE8D,KAAK,CAACI,MAAM,CAACU,KAAK,EAAE2H,WAAW,CAACxM,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE2N,YAAY,EAAE;AACrGhB,MAAAA,OAAO,EAAE7I,KAAK,CAAC6G,aAAa,CAAC/F,KAAK;AAClCH,MAAAA,QAAQ,EAAE,UAAU;AACpBoI,MAAAA,QAAQ,EAAE,KAAK;AACfC,MAAAA,YAAY,EAAEA;KACf,CAAC,CAAC,CAAC;AACN;AAEAhJ,EAAAA,KAAK,CAACK,UAAU,CAACxC,MAAM,GAAG5B,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE8D,KAAK,CAACK,UAAU,CAACxC,MAAM,EAAE;IACnE,uBAAuB,EAAEmC,KAAK,CAAC9B;AACjC,GAAC,CAAC;AACJ,CAAC;;AAGD,wBAAe;AACbgC,EAAAA,IAAI,EAAE,eAAe;AACrBiB,EAAAA,OAAO,EAAE,IAAI;AACbC,EAAAA,KAAK,EAAE,aAAa;AACpBC,EAAAA,EAAE,EAAE2I,aAAa;AACjBK,EAAAA,IAAI,EAAE;AACR,CAAC;;ACtKD,IAAIC,OAAO,GAAG;AACZA,EAAAA,OAAO,EAAE;AACX,CAAC;AAED,SAAS9J,MAAMA,CAACT,IAAI,EAAE;AACpB,EAAA,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBuK,QAAQ,GAAGxK,IAAI,CAACwK,QAAQ;IACxB3O,OAAO,GAAGmE,IAAI,CAACnE,OAAO;AAC1B,EAAA,IAAI4O,eAAe,GAAG5O,OAAO,CAAC6O,MAAM;IAChCA,MAAM,GAAGD,eAAe,KAAK,MAAM,GAAG,IAAI,GAAGA,eAAe;IAC5DE,eAAe,GAAG9O,OAAO,CAAC+O,MAAM;IAChCA,MAAM,GAAGD,eAAe,KAAK,MAAM,GAAG,IAAI,GAAGA,eAAe;EAChE,IAAIlO,MAAM,GAAG0C,SAAS,CAACc,KAAK,CAAC3E,QAAQ,CAACwC,MAAM,CAAC;AAC7C,EAAA,IAAI+M,aAAa,GAAG,EAAE,CAACzM,MAAM,CAAC6B,KAAK,CAAC4K,aAAa,CAAC9M,SAAS,EAAEkC,KAAK,CAAC4K,aAAa,CAAC/M,MAAM,CAAC;AAExF,EAAA,IAAI4M,MAAM,EAAE;AACVG,IAAAA,aAAa,CAACnP,OAAO,CAAC,UAAUoP,YAAY,EAAE;MAC5CA,YAAY,CAACC,gBAAgB,CAAC,QAAQ,EAAEP,QAAQ,CAACQ,MAAM,EAAET,OAAO,CAAC;AACnE,KAAC,CAAC;AACJ;AAEA,EAAA,IAAIK,MAAM,EAAE;IACVnO,MAAM,CAACsO,gBAAgB,CAAC,QAAQ,EAAEP,QAAQ,CAACQ,MAAM,EAAET,OAAO,CAAC;AAC7D;AAEA,EAAA,OAAO,YAAY;AACjB,IAAA,IAAIG,MAAM,EAAE;AACVG,MAAAA,aAAa,CAACnP,OAAO,CAAC,UAAUoP,YAAY,EAAE;QAC5CA,YAAY,CAACG,mBAAmB,CAAC,QAAQ,EAAET,QAAQ,CAACQ,MAAM,EAAET,OAAO,CAAC;AACtE,OAAC,CAAC;AACJ;AAEA,IAAA,IAAIK,MAAM,EAAE;MACVnO,MAAM,CAACwO,mBAAmB,CAAC,QAAQ,EAAET,QAAQ,CAACQ,MAAM,EAAET,OAAO,CAAC;AAChE;GACD;AACH,CAAC;;AAGD,uBAAe;AACbpK,EAAAA,IAAI,EAAE,gBAAgB;AACtBiB,EAAAA,OAAO,EAAE,IAAI;AACbC,EAAAA,KAAK,EAAE,OAAO;AACdC,EAAAA,EAAE,EAAE,SAASA,EAAEA,GAAG,EAAE;AACpBb,EAAAA,MAAM,EAAEA,MAAM;AACd6J,EAAAA,IAAI,EAAE;AACR,CAAC;;AChDD,IAAIY,MAAI,GAAG;AACT1N,EAAAA,IAAI,EAAE,OAAO;AACbD,EAAAA,KAAK,EAAE,MAAM;AACbD,EAAAA,MAAM,EAAE,KAAK;AACbD,EAAAA,GAAG,EAAE;AACP,CAAC;AACc,SAAS8N,oBAAoBA,CAAChN,SAAS,EAAE;EACtD,OAAOA,SAAS,CAACiN,OAAO,CAAC,wBAAwB,EAAE,UAAUC,OAAO,EAAE;IACpE,OAAOH,MAAI,CAACG,OAAO,CAAC;AACtB,GAAC,CAAC;AACJ;;ACVA,IAAIH,IAAI,GAAG;AACTvO,EAAAA,KAAK,EAAE,KAAK;AACZgB,EAAAA,GAAG,EAAE;AACP,CAAC;AACc,SAAS2N,6BAA6BA,CAACnN,SAAS,EAAE;EAC/D,OAAOA,SAAS,CAACiN,OAAO,CAAC,YAAY,EAAE,UAAUC,OAAO,EAAE;IACxD,OAAOH,IAAI,CAACG,OAAO,CAAC;AACtB,GAAC,CAAC;AACJ;;ACPe,SAASE,eAAeA,CAACnM,IAAI,EAAE;AAC5C,EAAA,IAAImJ,GAAG,GAAGpJ,SAAS,CAACC,IAAI,CAAC;AACzB,EAAA,IAAIoM,UAAU,GAAGjD,GAAG,CAACkD,WAAW;AAChC,EAAA,IAAIC,SAAS,GAAGnD,GAAG,CAACoD,WAAW;EAC/B,OAAO;AACLH,IAAAA,UAAU,EAAEA,UAAU;AACtBE,IAAAA,SAAS,EAAEA;GACZ;AACH;;ACNe,SAASE,mBAAmBA,CAACjQ,OAAO,EAAE;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAA,OAAOiH,qBAAqB,CAAC8B,kBAAkB,CAAC/I,OAAO,CAAC,CAAC,CAAC6B,IAAI,GAAG+N,eAAe,CAAC5P,OAAO,CAAC,CAAC6P,UAAU;AACtG;;ACRe,SAASK,eAAeA,CAAClQ,OAAO,EAAEkF,QAAQ,EAAE;AACzD,EAAA,IAAI0H,GAAG,GAAGpJ,SAAS,CAACxD,OAAO,CAAC;AAC5B,EAAA,IAAImQ,IAAI,GAAGpH,kBAAkB,CAAC/I,OAAO,CAAC;AACtC,EAAA,IAAI2H,cAAc,GAAGiF,GAAG,CAACjF,cAAc;AACvC,EAAA,IAAIH,KAAK,GAAG2I,IAAI,CAACnE,WAAW;AAC5B,EAAA,IAAItE,MAAM,GAAGyI,IAAI,CAACpE,YAAY;EAC9B,IAAIlE,CAAC,GAAG,CAAC;EACT,IAAIE,CAAC,GAAG,CAAC;AAET,EAAA,IAAIJ,cAAc,EAAE;IAClBH,KAAK,GAAGG,cAAc,CAACH,KAAK;IAC5BE,MAAM,GAAGC,cAAc,CAACD,MAAM;AAC9B,IAAA,IAAI0I,cAAc,GAAGrJ,gBAAgB,EAAE;IAEvC,IAAIqJ,cAAc,IAAI,CAACA,cAAc,IAAIlL,QAAQ,KAAK,OAAO,EAAE;MAC7D2C,CAAC,GAAGF,cAAc,CAACG,UAAU;MAC7BC,CAAC,GAAGJ,cAAc,CAACK,SAAS;AAC9B;AACF;EAEA,OAAO;AACLR,IAAAA,KAAK,EAAEA,KAAK;AACZE,IAAAA,MAAM,EAAEA,MAAM;AACdG,IAAAA,CAAC,EAAEA,CAAC,GAAGoI,mBAAmB,CAACjQ,OAAO,CAAC;AACnC+H,IAAAA,CAAC,EAAEA;GACJ;AACH;;ACzBA;;AAEe,SAASsI,eAAeA,CAACrQ,OAAO,EAAE;AAC/C,EAAA,IAAIsQ,qBAAqB;AAEzB,EAAA,IAAIH,IAAI,GAAGpH,kBAAkB,CAAC/I,OAAO,CAAC;AACtC,EAAA,IAAIuQ,SAAS,GAAGX,eAAe,CAAC5P,OAAO,CAAC;AACxC,EAAA,IAAIwQ,IAAI,GAAG,CAACF,qBAAqB,GAAGtQ,OAAO,CAAC2D,aAAa,KAAK,IAAI,GAAG,MAAM,GAAG2M,qBAAqB,CAACE,IAAI;EACxG,IAAIhJ,KAAK,GAAGzB,GAAG,CAACoK,IAAI,CAACM,WAAW,EAAEN,IAAI,CAACnE,WAAW,EAAEwE,IAAI,GAAGA,IAAI,CAACC,WAAW,GAAG,CAAC,EAAED,IAAI,GAAGA,IAAI,CAACxE,WAAW,GAAG,CAAC,CAAC;EAC7G,IAAItE,MAAM,GAAG3B,GAAG,CAACoK,IAAI,CAACO,YAAY,EAAEP,IAAI,CAACpE,YAAY,EAAEyE,IAAI,GAAGA,IAAI,CAACE,YAAY,GAAG,CAAC,EAAEF,IAAI,GAAGA,IAAI,CAACzE,YAAY,GAAG,CAAC,CAAC;EAClH,IAAIlE,CAAC,GAAG,CAAC0I,SAAS,CAACV,UAAU,GAAGI,mBAAmB,CAACjQ,OAAO,CAAC;AAC5D,EAAA,IAAI+H,CAAC,GAAG,CAACwI,SAAS,CAACR,SAAS;EAE5B,IAAInH,kBAAgB,CAAC4H,IAAI,IAAIL,IAAI,CAAC,CAACQ,SAAS,KAAK,KAAK,EAAE;AACtD9I,IAAAA,CAAC,IAAI9B,GAAG,CAACoK,IAAI,CAACnE,WAAW,EAAEwE,IAAI,GAAGA,IAAI,CAACxE,WAAW,GAAG,CAAC,CAAC,GAAGxE,KAAK;AACjE;EAEA,OAAO;AACLA,IAAAA,KAAK,EAAEA,KAAK;AACZE,IAAAA,MAAM,EAAEA,MAAM;AACdG,IAAAA,CAAC,EAAEA,CAAC;AACJE,IAAAA,CAAC,EAAEA;GACJ;AACH;;AC3Be,SAAS6I,cAAcA,CAAC5Q,OAAO,EAAE;AAC9C;AACA,EAAA,IAAI6Q,iBAAiB,GAAGjI,kBAAgB,CAAC5I,OAAO,CAAC;IAC7C8Q,QAAQ,GAAGD,iBAAiB,CAACC,QAAQ;IACrCC,SAAS,GAAGF,iBAAiB,CAACE,SAAS;IACvCC,SAAS,GAAGH,iBAAiB,CAACG,SAAS;EAE3C,OAAO,4BAA4B,CAAChK,IAAI,CAAC8J,QAAQ,GAAGE,SAAS,GAAGD,SAAS,CAAC;AAC5E;;ACLe,SAASE,eAAeA,CAACxN,IAAI,EAAE;AAC5C,EAAA,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,CAACqF,OAAO,CAACzF,WAAW,CAACI,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE;AACjE;AACA,IAAA,OAAOA,IAAI,CAACE,aAAa,CAAC6M,IAAI;AAChC;EAEA,IAAIxM,aAAa,CAACP,IAAI,CAAC,IAAImN,cAAc,CAACnN,IAAI,CAAC,EAAE;AAC/C,IAAA,OAAOA,IAAI;AACb;AAEA,EAAA,OAAOwN,eAAe,CAAChI,aAAa,CAACxF,IAAI,CAAC,CAAC;AAC7C;;ACXA;AACA;AACA;AACA;AACA;AACA;;AAEe,SAASyN,iBAAiBA,CAAClR,OAAO,EAAEmR,IAAI,EAAE;AACvD,EAAA,IAAIb,qBAAqB;AAEzB,EAAA,IAAIa,IAAI,KAAK,MAAM,EAAE;AACnBA,IAAAA,IAAI,GAAG,EAAE;AACX;AAEA,EAAA,IAAIhC,YAAY,GAAG8B,eAAe,CAACjR,OAAO,CAAC;AAC3C,EAAA,IAAIoR,MAAM,GAAGjC,YAAY,MAAM,CAACmB,qBAAqB,GAAGtQ,OAAO,CAAC2D,aAAa,KAAK,IAAI,GAAG,MAAM,GAAG2M,qBAAqB,CAACE,IAAI,CAAC;AAC7H,EAAA,IAAI5D,GAAG,GAAGpJ,SAAS,CAAC2L,YAAY,CAAC;EACjC,IAAIkC,MAAM,GAAGD,MAAM,GAAG,CAACxE,GAAG,CAAC,CAACnK,MAAM,CAACmK,GAAG,CAACjF,cAAc,IAAI,EAAE,EAAEiJ,cAAc,CAACzB,YAAY,CAAC,GAAGA,YAAY,GAAG,EAAE,CAAC,GAAGA,YAAY;AAC7H,EAAA,IAAImC,WAAW,GAAGH,IAAI,CAAC1O,MAAM,CAAC4O,MAAM,CAAC;EACrC,OAAOD,MAAM,GAAGE,WAAW;AAAG;EAC9BA,WAAW,CAAC7O,MAAM,CAACyO,iBAAiB,CAACjI,aAAa,CAACoI,MAAM,CAAC,CAAC,CAAC;AAC9D;;ACzBe,SAASE,gBAAgBA,CAACC,IAAI,EAAE;EAC7C,OAAOjR,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEgR,IAAI,EAAE;IAC7B3P,IAAI,EAAE2P,IAAI,CAAC3J,CAAC;IACZnG,GAAG,EAAE8P,IAAI,CAACzJ,CAAC;AACXnG,IAAAA,KAAK,EAAE4P,IAAI,CAAC3J,CAAC,GAAG2J,IAAI,CAAChK,KAAK;AAC1B7F,IAAAA,MAAM,EAAE6P,IAAI,CAACzJ,CAAC,GAAGyJ,IAAI,CAAC9J;AACxB,GAAC,CAAC;AACJ;;ACQA,SAAS+J,0BAA0BA,CAACzR,OAAO,EAAEkF,QAAQ,EAAE;EACrD,IAAIsM,IAAI,GAAGvK,qBAAqB,CAACjH,OAAO,EAAE,KAAK,EAAEkF,QAAQ,KAAK,OAAO,CAAC;EACtEsM,IAAI,CAAC9P,GAAG,GAAG8P,IAAI,CAAC9P,GAAG,GAAG1B,OAAO,CAAC0R,SAAS;EACvCF,IAAI,CAAC3P,IAAI,GAAG2P,IAAI,CAAC3P,IAAI,GAAG7B,OAAO,CAAC2R,UAAU;EAC1CH,IAAI,CAAC7P,MAAM,GAAG6P,IAAI,CAAC9P,GAAG,GAAG1B,OAAO,CAAC+L,YAAY;EAC7CyF,IAAI,CAAC5P,KAAK,GAAG4P,IAAI,CAAC3P,IAAI,GAAG7B,OAAO,CAACgM,WAAW;AAC5CwF,EAAAA,IAAI,CAAChK,KAAK,GAAGxH,OAAO,CAACgM,WAAW;AAChCwF,EAAAA,IAAI,CAAC9J,MAAM,GAAG1H,OAAO,CAAC+L,YAAY;AAClCyF,EAAAA,IAAI,CAAC3J,CAAC,GAAG2J,IAAI,CAAC3P,IAAI;AAClB2P,EAAAA,IAAI,CAACzJ,CAAC,GAAGyJ,IAAI,CAAC9P,GAAG;AACjB,EAAA,OAAO8P,IAAI;AACb;AAEA,SAASI,0BAA0BA,CAAC5R,OAAO,EAAE6R,cAAc,EAAE3M,QAAQ,EAAE;AACrE,EAAA,OAAO2M,cAAc,KAAK3P,QAAQ,GAAGqP,gBAAgB,CAACrB,eAAe,CAAClQ,OAAO,EAAEkF,QAAQ,CAAC,CAAC,GAAGrB,WAAS,CAACgO,cAAc,CAAC,GAAGJ,0BAA0B,CAACI,cAAc,EAAE3M,QAAQ,CAAC,GAAGqM,gBAAgB,CAAClB,eAAe,CAACtH,kBAAkB,CAAC/I,OAAO,CAAC,CAAC,CAAC;AAC/O,CAAC;AACD;AACA;;AAGA,SAAS8R,kBAAkBA,CAAC9R,OAAO,EAAE;EACnC,IAAIiC,eAAe,GAAGiP,iBAAiB,CAACjI,aAAa,CAACjJ,OAAO,CAAC,CAAC;AAC/D,EAAA,IAAI+R,iBAAiB,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAACjJ,OAAO,CAACF,kBAAgB,CAAC5I,OAAO,CAAC,CAACiF,QAAQ,CAAC,IAAI,CAAC;AAC9F,EAAA,IAAI+M,cAAc,GAAGD,iBAAiB,IAAI/N,aAAa,CAAChE,OAAO,CAAC,GAAGgK,eAAe,CAAChK,OAAO,CAAC,GAAGA,OAAO;AAErG,EAAA,IAAI,CAAC6D,WAAS,CAACmO,cAAc,CAAC,EAAE;AAC9B,IAAA,OAAO,EAAE;AACX,GAAC;;AAGD,EAAA,OAAO/P,eAAe,CAAC8H,MAAM,CAAC,UAAU8H,cAAc,EAAE;AACtD,IAAA,OAAOhO,WAAS,CAACgO,cAAc,CAAC,IAAI1J,QAAQ,CAAC0J,cAAc,EAAEG,cAAc,CAAC,IAAI3O,WAAW,CAACwO,cAAc,CAAC,KAAK,MAAM;AACxH,GAAC,CAAC;AACJ,CAAC;AACD;;AAGe,SAASI,eAAeA,CAACjS,OAAO,EAAEkS,QAAQ,EAAEC,YAAY,EAAEjN,QAAQ,EAAE;AACjF,EAAA,IAAIkN,mBAAmB,GAAGF,QAAQ,KAAK,iBAAiB,GAAGJ,kBAAkB,CAAC9R,OAAO,CAAC,GAAG,EAAE,CAACyC,MAAM,CAACyP,QAAQ,CAAC;EAC5G,IAAIjQ,eAAe,GAAG,EAAE,CAACQ,MAAM,CAAC2P,mBAAmB,EAAE,CAACD,YAAY,CAAC,CAAC;AACpE,EAAA,IAAIE,mBAAmB,GAAGpQ,eAAe,CAAC,CAAC,CAAC;EAC5C,IAAIqQ,YAAY,GAAGrQ,eAAe,CAACK,MAAM,CAAC,UAAUiQ,OAAO,EAAEV,cAAc,EAAE;IAC3E,IAAIL,IAAI,GAAGI,0BAA0B,CAAC5R,OAAO,EAAE6R,cAAc,EAAE3M,QAAQ,CAAC;AACxEqN,IAAAA,OAAO,CAAC7Q,GAAG,GAAGqE,GAAG,CAACyL,IAAI,CAAC9P,GAAG,EAAE6Q,OAAO,CAAC7Q,GAAG,CAAC;AACxC6Q,IAAAA,OAAO,CAAC3Q,KAAK,GAAGqE,GAAG,CAACuL,IAAI,CAAC5P,KAAK,EAAE2Q,OAAO,CAAC3Q,KAAK,CAAC;AAC9C2Q,IAAAA,OAAO,CAAC5Q,MAAM,GAAGsE,GAAG,CAACuL,IAAI,CAAC7P,MAAM,EAAE4Q,OAAO,CAAC5Q,MAAM,CAAC;AACjD4Q,IAAAA,OAAO,CAAC1Q,IAAI,GAAGkE,GAAG,CAACyL,IAAI,CAAC3P,IAAI,EAAE0Q,OAAO,CAAC1Q,IAAI,CAAC;AAC3C,IAAA,OAAO0Q,OAAO;GACf,EAAEX,0BAA0B,CAAC5R,OAAO,EAAEqS,mBAAmB,EAAEnN,QAAQ,CAAC,CAAC;EACtEoN,YAAY,CAAC9K,KAAK,GAAG8K,YAAY,CAAC1Q,KAAK,GAAG0Q,YAAY,CAACzQ,IAAI;EAC3DyQ,YAAY,CAAC5K,MAAM,GAAG4K,YAAY,CAAC3Q,MAAM,GAAG2Q,YAAY,CAAC5Q,GAAG;AAC5D4Q,EAAAA,YAAY,CAACzK,CAAC,GAAGyK,YAAY,CAACzQ,IAAI;AAClCyQ,EAAAA,YAAY,CAACvK,CAAC,GAAGuK,YAAY,CAAC5Q,GAAG;AACjC,EAAA,OAAO4Q,YAAY;AACrB;;ACjEe,SAASE,cAAcA,CAACnO,IAAI,EAAE;AAC3C,EAAA,IAAIjC,SAAS,GAAGiC,IAAI,CAACjC,SAAS;IAC1BpC,OAAO,GAAGqE,IAAI,CAACrE,OAAO;IACtBwC,SAAS,GAAG6B,IAAI,CAAC7B,SAAS;EAC9B,IAAI4I,aAAa,GAAG5I,SAAS,GAAGqD,gBAAgB,CAACrD,SAAS,CAAC,GAAG,IAAI;EAClE,IAAI0K,SAAS,GAAG1K,SAAS,GAAGiK,YAAY,CAACjK,SAAS,CAAC,GAAG,IAAI;AAC1D,EAAA,IAAIiQ,OAAO,GAAGrQ,SAAS,CAACyF,CAAC,GAAGzF,SAAS,CAACoF,KAAK,GAAG,CAAC,GAAGxH,OAAO,CAACwH,KAAK,GAAG,CAAC;AACnE,EAAA,IAAIkL,OAAO,GAAGtQ,SAAS,CAAC2F,CAAC,GAAG3F,SAAS,CAACsF,MAAM,GAAG,CAAC,GAAG1H,OAAO,CAAC0H,MAAM,GAAG,CAAC;AACrE,EAAA,IAAIyF,OAAO;AAEX,EAAA,QAAQ/B,aAAa;AACnB,IAAA,KAAK1J,GAAG;AACNyL,MAAAA,OAAO,GAAG;AACRtF,QAAAA,CAAC,EAAE4K,OAAO;AACV1K,QAAAA,CAAC,EAAE3F,SAAS,CAAC2F,CAAC,GAAG/H,OAAO,CAAC0H;OAC1B;AACD,MAAA;AAEF,IAAA,KAAK/F,MAAM;AACTwL,MAAAA,OAAO,GAAG;AACRtF,QAAAA,CAAC,EAAE4K,OAAO;AACV1K,QAAAA,CAAC,EAAE3F,SAAS,CAAC2F,CAAC,GAAG3F,SAAS,CAACsF;OAC5B;AACD,MAAA;AAEF,IAAA,KAAK9F,KAAK;AACRuL,MAAAA,OAAO,GAAG;AACRtF,QAAAA,CAAC,EAAEzF,SAAS,CAACyF,CAAC,GAAGzF,SAAS,CAACoF,KAAK;AAChCO,QAAAA,CAAC,EAAE2K;OACJ;AACD,MAAA;AAEF,IAAA,KAAK7Q,IAAI;AACPsL,MAAAA,OAAO,GAAG;AACRtF,QAAAA,CAAC,EAAEzF,SAAS,CAACyF,CAAC,GAAG7H,OAAO,CAACwH,KAAK;AAC9BO,QAAAA,CAAC,EAAE2K;OACJ;AACD,MAAA;AAEF,IAAA;AACEvF,MAAAA,OAAO,GAAG;QACRtF,CAAC,EAAEzF,SAAS,CAACyF,CAAC;QACdE,CAAC,EAAE3F,SAAS,CAAC2F;OACd;AACL;EAEA,IAAI4K,QAAQ,GAAGvH,aAAa,GAAGnB,wBAAwB,CAACmB,aAAa,CAAC,GAAG,IAAI;EAE7E,IAAIuH,QAAQ,IAAI,IAAI,EAAE;IACpB,IAAIpH,GAAG,GAAGoH,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;AAE/C,IAAA,QAAQzF,SAAS;AACf,MAAA,KAAKlM,KAAK;QACRmM,OAAO,CAACwF,QAAQ,CAAC,GAAGxF,OAAO,CAACwF,QAAQ,CAAC,IAAIvQ,SAAS,CAACmJ,GAAG,CAAC,GAAG,CAAC,GAAGvL,OAAO,CAACuL,GAAG,CAAC,GAAG,CAAC,CAAC;AAC/E,QAAA;AAEF,MAAA,KAAKvJ,GAAG;QACNmL,OAAO,CAACwF,QAAQ,CAAC,GAAGxF,OAAO,CAACwF,QAAQ,CAAC,IAAIvQ,SAAS,CAACmJ,GAAG,CAAC,GAAG,CAAC,GAAGvL,OAAO,CAACuL,GAAG,CAAC,GAAG,CAAC,CAAC;AAC/E,QAAA;AAGJ;AACF;AAEA,EAAA,OAAO4B,OAAO;AAChB;;AC3De,SAASyF,cAAcA,CAACtO,KAAK,EAAEpE,OAAO,EAAE;AACrD,EAAA,IAAIA,OAAO,KAAK,MAAM,EAAE;IACtBA,OAAO,GAAG,EAAE;AACd;EAEA,IAAI2S,QAAQ,GAAG3S,OAAO;IAClB4S,kBAAkB,GAAGD,QAAQ,CAACrQ,SAAS;IACvCA,SAAS,GAAGsQ,kBAAkB,KAAK,MAAM,GAAGxO,KAAK,CAAC9B,SAAS,GAAGsQ,kBAAkB;IAChFC,iBAAiB,GAAGF,QAAQ,CAAC3N,QAAQ;IACrCA,QAAQ,GAAG6N,iBAAiB,KAAK,MAAM,GAAGzO,KAAK,CAACY,QAAQ,GAAG6N,iBAAiB;IAC5EC,iBAAiB,GAAGH,QAAQ,CAACX,QAAQ;IACrCA,QAAQ,GAAGc,iBAAiB,KAAK,MAAM,GAAG/Q,eAAe,GAAG+Q,iBAAiB;IAC7EC,qBAAqB,GAAGJ,QAAQ,CAACV,YAAY;IAC7CA,YAAY,GAAGc,qBAAqB,KAAK,MAAM,GAAG/Q,QAAQ,GAAG+Q,qBAAqB;IAClFC,qBAAqB,GAAGL,QAAQ,CAACM,cAAc;IAC/CA,cAAc,GAAGD,qBAAqB,KAAK,MAAM,GAAG/Q,MAAM,GAAG+Q,qBAAqB;IAClFE,oBAAoB,GAAGP,QAAQ,CAACQ,WAAW;IAC3CA,WAAW,GAAGD,oBAAoB,KAAK,MAAM,GAAG,KAAK,GAAGA,oBAAoB;IAC5EE,gBAAgB,GAAGT,QAAQ,CAAC/H,OAAO;IACnCA,OAAO,GAAGwI,gBAAgB,KAAK,MAAM,GAAG,CAAC,GAAGA,gBAAgB;AAChE,EAAA,IAAI7I,aAAa,GAAGD,kBAAkB,CAAC,OAAOM,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGJ,eAAe,CAACI,OAAO,EAAE/I,cAAc,CAAC,CAAC;EACxH,IAAIwR,UAAU,GAAGJ,cAAc,KAAKhR,MAAM,GAAGC,SAAS,GAAGD,MAAM;AAC/D,EAAA,IAAI8K,UAAU,GAAG3I,KAAK,CAACyG,KAAK,CAAC5I,MAAM;EACnC,IAAInC,OAAO,GAAGsE,KAAK,CAAC3E,QAAQ,CAAC0T,WAAW,GAAGE,UAAU,GAAGJ,cAAc,CAAC;AACvE,EAAA,IAAIK,kBAAkB,GAAGvB,eAAe,CAACpO,WAAS,CAAC7D,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAACyT,cAAc,IAAI1K,kBAAkB,CAACzE,KAAK,CAAC3E,QAAQ,CAACwC,MAAM,CAAC,EAAE+P,QAAQ,EAAEC,YAAY,EAAEjN,QAAQ,CAAC;EAC9K,IAAIwO,mBAAmB,GAAGzM,qBAAqB,CAAC3C,KAAK,CAAC3E,QAAQ,CAACyC,SAAS,CAAC;EACzE,IAAI8I,aAAa,GAAGsH,cAAc,CAAC;AACjCpQ,IAAAA,SAAS,EAAEsR,mBAAmB;AAC9B1T,IAAAA,OAAO,EAAEiN,UAAU;AACnB/H,IACA1C,SAAS,EAAEA;AACb,GAAC,CAAC;AACF,EAAA,IAAImR,gBAAgB,GAAGpC,gBAAgB,CAAChR,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEyM,UAAU,EAAE/B,aAAa,CAAC,CAAC;EACrF,IAAI0I,iBAAiB,GAAGT,cAAc,KAAKhR,MAAM,GAAGwR,gBAAgB,GAAGD,mBAAmB,CAAC;AAC3F;;AAEA,EAAA,IAAIG,eAAe,GAAG;IACpBnS,GAAG,EAAE8R,kBAAkB,CAAC9R,GAAG,GAAGkS,iBAAiB,CAAClS,GAAG,GAAG+I,aAAa,CAAC/I,GAAG;IACvEC,MAAM,EAAEiS,iBAAiB,CAACjS,MAAM,GAAG6R,kBAAkB,CAAC7R,MAAM,GAAG8I,aAAa,CAAC9I,MAAM;IACnFE,IAAI,EAAE2R,kBAAkB,CAAC3R,IAAI,GAAG+R,iBAAiB,CAAC/R,IAAI,GAAG4I,aAAa,CAAC5I,IAAI;IAC3ED,KAAK,EAAEgS,iBAAiB,CAAChS,KAAK,GAAG4R,kBAAkB,CAAC5R,KAAK,GAAG6I,aAAa,CAAC7I;GAC3E;EACD,IAAIkS,UAAU,GAAGxP,KAAK,CAAC6G,aAAa,CAACgB,MAAM,CAAC;;AAE5C,EAAA,IAAIgH,cAAc,KAAKhR,MAAM,IAAI2R,UAAU,EAAE;AAC3C,IAAA,IAAI3H,MAAM,GAAG2H,UAAU,CAACtR,SAAS,CAAC;IAClCjC,MAAM,CAACgE,IAAI,CAACsP,eAAe,CAAC,CAAC9T,OAAO,CAAC,UAAU6K,GAAG,EAAE;AAClD,MAAA,IAAImJ,QAAQ,GAAG,CAACnS,KAAK,EAAED,MAAM,CAAC,CAACmH,OAAO,CAAC8B,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE;AACzD,MAAA,IAAIS,IAAI,GAAG,CAAC3J,GAAG,EAAEC,MAAM,CAAC,CAACmH,OAAO,CAAC8B,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;MACtDiJ,eAAe,CAACjJ,GAAG,CAAC,IAAIuB,MAAM,CAACd,IAAI,CAAC,GAAG0I,QAAQ;AACjD,KAAC,CAAC;AACJ;AAEA,EAAA,OAAOF,eAAe;AACxB;;AC5De,SAASG,oBAAoBA,CAAC1P,KAAK,EAAEpE,OAAO,EAAE;AAC3D,EAAA,IAAIA,OAAO,KAAK,MAAM,EAAE;IACtBA,OAAO,GAAG,EAAE;AACd;EAEA,IAAI2S,QAAQ,GAAG3S,OAAO;IAClBsC,SAAS,GAAGqQ,QAAQ,CAACrQ,SAAS;IAC9B0P,QAAQ,GAAGW,QAAQ,CAACX,QAAQ;IAC5BC,YAAY,GAAGU,QAAQ,CAACV,YAAY;IACpCrH,OAAO,GAAG+H,QAAQ,CAAC/H,OAAO;IAC1BmJ,cAAc,GAAGpB,QAAQ,CAACoB,cAAc;IACxCC,qBAAqB,GAAGrB,QAAQ,CAACsB,qBAAqB;IACtDA,qBAAqB,GAAGD,qBAAqB,KAAK,MAAM,GAAGE,UAAa,GAAGF,qBAAqB;AACpG,EAAA,IAAIhH,SAAS,GAAGT,YAAY,CAACjK,SAAS,CAAC;AACvC,EAAA,IAAIE,YAAU,GAAGwK,SAAS,GAAG+G,cAAc,GAAG5R,mBAAmB,GAAGA,mBAAmB,CAAC0H,MAAM,CAAC,UAAUvH,SAAS,EAAE;AAClH,IAAA,OAAOiK,YAAY,CAACjK,SAAS,CAAC,KAAK0K,SAAS;GAC7C,CAAC,GAAGnL,cAAc;EACnB,IAAIsS,iBAAiB,GAAG3R,YAAU,CAACqH,MAAM,CAAC,UAAUvH,SAAS,EAAE;AAC7D,IAAA,OAAO2R,qBAAqB,CAACrL,OAAO,CAACtG,SAAS,CAAC,IAAI,CAAC;AACtD,GAAC,CAAC;AAEF,EAAA,IAAI6R,iBAAiB,CAACvU,MAAM,KAAK,CAAC,EAAE;AAClCuU,IAAAA,iBAAiB,GAAG3R,YAAU;AAChC,GAAC;;EAGD,IAAI4R,SAAS,GAAGD,iBAAiB,CAAC/R,MAAM,CAAC,UAAUC,GAAG,EAAEC,SAAS,EAAE;AACjED,IAAAA,GAAG,CAACC,SAAS,CAAC,GAAGoQ,cAAc,CAACtO,KAAK,EAAE;AACrC9B,MAAAA,SAAS,EAAEA,SAAS;AACpB0P,MAAAA,QAAQ,EAAEA,QAAQ;AAClBC,MAAAA,YAAY,EAAEA,YAAY;AAC1BrH,MAAAA,OAAO,EAAEA;AACX,KAAC,CAAC,CAACjF,gBAAgB,CAACrD,SAAS,CAAC,CAAC;AAC/B,IAAA,OAAOD,GAAG;GACX,EAAE,EAAE,CAAC;AACN,EAAA,OAAOhC,MAAM,CAACgE,IAAI,CAAC+P,SAAS,CAAC,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;IACjD,OAAOH,SAAS,CAACE,CAAC,CAAC,GAAGF,SAAS,CAACG,CAAC,CAAC;AACpC,GAAC,CAAC;AACJ;;AClCA,SAASC,6BAA6BA,CAAClS,SAAS,EAAE;AAChD,EAAA,IAAIqD,gBAAgB,CAACrD,SAAS,CAAC,KAAKV,IAAI,EAAE;AACxC,IAAA,OAAO,EAAE;AACX;AAEA,EAAA,IAAI6S,iBAAiB,GAAGnF,oBAAoB,CAAChN,SAAS,CAAC;AACvD,EAAA,OAAO,CAACmN,6BAA6B,CAACnN,SAAS,CAAC,EAAEmS,iBAAiB,EAAEhF,6BAA6B,CAACgF,iBAAiB,CAAC,CAAC;AACxH;AAEA,SAASC,IAAIA,CAACvQ,IAAI,EAAE;AAClB,EAAA,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBpE,OAAO,GAAGmE,IAAI,CAACnE,OAAO;IACtBsE,IAAI,GAAGH,IAAI,CAACG,IAAI;EAEpB,IAAIF,KAAK,CAAC6G,aAAa,CAAC3G,IAAI,CAAC,CAACqQ,KAAK,EAAE;AACnC,IAAA;AACF;AAEA,EAAA,IAAIC,iBAAiB,GAAG5U,OAAO,CAACyS,QAAQ;IACpCoC,aAAa,GAAGD,iBAAiB,KAAK,MAAM,GAAG,IAAI,GAAGA,iBAAiB;IACvEE,gBAAgB,GAAG9U,OAAO,CAAC+U,OAAO;IAClCC,YAAY,GAAGF,gBAAgB,KAAK,MAAM,GAAG,IAAI,GAAGA,gBAAgB;IACpEG,2BAA2B,GAAGjV,OAAO,CAACkV,kBAAkB;IACxDtK,OAAO,GAAG5K,OAAO,CAAC4K,OAAO;IACzBoH,QAAQ,GAAGhS,OAAO,CAACgS,QAAQ;IAC3BC,YAAY,GAAGjS,OAAO,CAACiS,YAAY;IACnCkB,WAAW,GAAGnT,OAAO,CAACmT,WAAW;IACjCgC,qBAAqB,GAAGnV,OAAO,CAAC+T,cAAc;IAC9CA,cAAc,GAAGoB,qBAAqB,KAAK,MAAM,GAAG,IAAI,GAAGA,qBAAqB;IAChFlB,qBAAqB,GAAGjU,OAAO,CAACiU,qBAAqB;AACzD,EAAA,IAAImB,kBAAkB,GAAGhR,KAAK,CAACpE,OAAO,CAACsC,SAAS;AAChD,EAAA,IAAI4I,aAAa,GAAGvF,gBAAgB,CAACyP,kBAAkB,CAAC;AACxD,EAAA,IAAIC,eAAe,GAAGnK,aAAa,KAAKkK,kBAAkB;EAC1D,IAAIF,kBAAkB,GAAGD,2BAA2B,KAAKI,eAAe,IAAI,CAACtB,cAAc,GAAG,CAACzE,oBAAoB,CAAC8F,kBAAkB,CAAC,CAAC,GAAGZ,6BAA6B,CAACY,kBAAkB,CAAC,CAAC;AAC7L,EAAA,IAAI5S,UAAU,GAAG,CAAC4S,kBAAkB,CAAC,CAAC7S,MAAM,CAAC2S,kBAAkB,CAAC,CAAC9S,MAAM,CAAC,UAAUC,GAAG,EAAEC,SAAS,EAAE;AAChG,IAAA,OAAOD,GAAG,CAACE,MAAM,CAACoD,gBAAgB,CAACrD,SAAS,CAAC,KAAKV,IAAI,GAAGkS,oBAAoB,CAAC1P,KAAK,EAAE;AACnF9B,MAAAA,SAAS,EAAEA,SAAS;AACpB0P,MAAAA,QAAQ,EAAEA,QAAQ;AAClBC,MAAAA,YAAY,EAAEA,YAAY;AAC1BrH,MAAAA,OAAO,EAAEA,OAAO;AAChBmJ,MAAAA,cAAc,EAAEA,cAAc;AAC9BE,MAAAA,qBAAqB,EAAEA;KACxB,CAAC,GAAG3R,SAAS,CAAC;GAChB,EAAE,EAAE,CAAC;AACN,EAAA,IAAIgT,aAAa,GAAGlR,KAAK,CAACyG,KAAK,CAAC3I,SAAS;AACzC,EAAA,IAAI6K,UAAU,GAAG3I,KAAK,CAACyG,KAAK,CAAC5I,MAAM;AACnC,EAAA,IAAIsT,SAAS,GAAG,IAAIC,GAAG,EAAE;EACzB,IAAIC,kBAAkB,GAAG,IAAI;AAC7B,EAAA,IAAIC,qBAAqB,GAAGlT,UAAU,CAAC,CAAC,CAAC;AAEzC,EAAA,KAAK,IAAImT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnT,UAAU,CAAC5C,MAAM,EAAE+V,CAAC,EAAE,EAAE;AAC1C,IAAA,IAAIrT,SAAS,GAAGE,UAAU,CAACmT,CAAC,CAAC;AAE7B,IAAA,IAAIC,cAAc,GAAGjQ,gBAAgB,CAACrD,SAAS,CAAC;AAEhD,IAAA,IAAIuT,gBAAgB,GAAGtJ,YAAY,CAACjK,SAAS,CAAC,KAAKxB,KAAK;AACxD,IAAA,IAAIsK,UAAU,GAAG,CAAC5J,GAAG,EAAEC,MAAM,CAAC,CAACmH,OAAO,CAACgN,cAAc,CAAC,IAAI,CAAC;AAC3D,IAAA,IAAIvK,GAAG,GAAGD,UAAU,GAAG,OAAO,GAAG,QAAQ;AACzC,IAAA,IAAIwF,QAAQ,GAAG8B,cAAc,CAACtO,KAAK,EAAE;AACnC9B,MAAAA,SAAS,EAAEA,SAAS;AACpB0P,MAAAA,QAAQ,EAAEA,QAAQ;AAClBC,MAAAA,YAAY,EAAEA,YAAY;AAC1BkB,MAAAA,WAAW,EAAEA,WAAW;AACxBvI,MAAAA,OAAO,EAAEA;AACX,KAAC,CAAC;AACF,IAAA,IAAIkL,iBAAiB,GAAG1K,UAAU,GAAGyK,gBAAgB,GAAGnU,KAAK,GAAGC,IAAI,GAAGkU,gBAAgB,GAAGpU,MAAM,GAAGD,GAAG;IAEtG,IAAI8T,aAAa,CAACjK,GAAG,CAAC,GAAG0B,UAAU,CAAC1B,GAAG,CAAC,EAAE;AACxCyK,MAAAA,iBAAiB,GAAGxG,oBAAoB,CAACwG,iBAAiB,CAAC;AAC7D;AAEA,IAAA,IAAIC,gBAAgB,GAAGzG,oBAAoB,CAACwG,iBAAiB,CAAC;IAC9D,IAAIE,MAAM,GAAG,EAAE;AAEf,IAAA,IAAInB,aAAa,EAAE;MACjBmB,MAAM,CAACC,IAAI,CAACrF,QAAQ,CAACgF,cAAc,CAAC,IAAI,CAAC,CAAC;AAC5C;AAEA,IAAA,IAAIZ,YAAY,EAAE;AAChBgB,MAAAA,MAAM,CAACC,IAAI,CAACrF,QAAQ,CAACkF,iBAAiB,CAAC,IAAI,CAAC,EAAElF,QAAQ,CAACmF,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAChF;AAEA,IAAA,IAAIC,MAAM,CAACE,KAAK,CAAC,UAAUC,KAAK,EAAE;AAChC,MAAA,OAAOA,KAAK;AACd,KAAC,CAAC,EAAE;AACFT,MAAAA,qBAAqB,GAAGpT,SAAS;AACjCmT,MAAAA,kBAAkB,GAAG,KAAK;AAC1B,MAAA;AACF;AAEAF,IAAAA,SAAS,CAACa,GAAG,CAAC9T,SAAS,EAAE0T,MAAM,CAAC;AAClC;AAEA,EAAA,IAAIP,kBAAkB,EAAE;AACtB;AACA,IAAA,IAAIY,cAAc,GAAGtC,cAAc,GAAG,CAAC,GAAG,CAAC;AAE3C,IAAA,IAAIuC,KAAK,GAAG,SAASA,KAAKA,CAACC,EAAE,EAAE;MAC7B,IAAIC,gBAAgB,GAAGhU,UAAU,CAACiU,IAAI,CAAC,UAAUnU,SAAS,EAAE;AAC1D,QAAA,IAAI0T,MAAM,GAAGT,SAAS,CAACmB,GAAG,CAACpU,SAAS,CAAC;AAErC,QAAA,IAAI0T,MAAM,EAAE;AACV,UAAA,OAAOA,MAAM,CAAChV,KAAK,CAAC,CAAC,EAAEuV,EAAE,CAAC,CAACL,KAAK,CAAC,UAAUC,KAAK,EAAE;AAChD,YAAA,OAAOA,KAAK;AACd,WAAC,CAAC;AACJ;AACF,OAAC,CAAC;AAEF,MAAA,IAAIK,gBAAgB,EAAE;AACpBd,QAAAA,qBAAqB,GAAGc,gBAAgB;AACxC,QAAA,OAAO,OAAO;AAChB;KACD;IAED,KAAK,IAAID,EAAE,GAAGF,cAAc,EAAEE,EAAE,GAAG,CAAC,EAAEA,EAAE,EAAE,EAAE;AAC1C,MAAA,IAAII,IAAI,GAAGL,KAAK,CAACC,EAAE,CAAC;MAEpB,IAAII,IAAI,KAAK,OAAO,EAAE;AACxB;AACF;AAEA,EAAA,IAAIvS,KAAK,CAAC9B,SAAS,KAAKoT,qBAAqB,EAAE;IAC7CtR,KAAK,CAAC6G,aAAa,CAAC3G,IAAI,CAAC,CAACqQ,KAAK,GAAG,IAAI;IACtCvQ,KAAK,CAAC9B,SAAS,GAAGoT,qBAAqB;IACvCtR,KAAK,CAACwS,KAAK,GAAG,IAAI;AACpB;AACF,CAAC;;AAGD,eAAe;AACbtS,EAAAA,IAAI,EAAE,MAAM;AACZiB,EAAAA,OAAO,EAAE,IAAI;AACbC,EAAAA,KAAK,EAAE,MAAM;AACbC,EAAAA,EAAE,EAAEiP,IAAI;EACRpI,gBAAgB,EAAE,CAAC,QAAQ,CAAC;AAC5BmC,EAAAA,IAAI,EAAE;AACJkG,IAAAA,KAAK,EAAE;AACT;AACF,CAAC;;AC/ID,SAASkC,cAAcA,CAACjG,QAAQ,EAAEU,IAAI,EAAEwF,gBAAgB,EAAE;AACxD,EAAA,IAAIA,gBAAgB,KAAK,MAAM,EAAE;AAC/BA,IAAAA,gBAAgB,GAAG;AACjBnP,MAAAA,CAAC,EAAE,CAAC;AACJE,MAAAA,CAAC,EAAE;KACJ;AACH;EAEA,OAAO;IACLrG,GAAG,EAAEoP,QAAQ,CAACpP,GAAG,GAAG8P,IAAI,CAAC9J,MAAM,GAAGsP,gBAAgB,CAACjP,CAAC;IACpDnG,KAAK,EAAEkP,QAAQ,CAAClP,KAAK,GAAG4P,IAAI,CAAChK,KAAK,GAAGwP,gBAAgB,CAACnP,CAAC;IACvDlG,MAAM,EAAEmP,QAAQ,CAACnP,MAAM,GAAG6P,IAAI,CAAC9J,MAAM,GAAGsP,gBAAgB,CAACjP,CAAC;IAC1DlG,IAAI,EAAEiP,QAAQ,CAACjP,IAAI,GAAG2P,IAAI,CAAChK,KAAK,GAAGwP,gBAAgB,CAACnP;GACrD;AACH;AAEA,SAASoP,qBAAqBA,CAACnG,QAAQ,EAAE;AACvC,EAAA,OAAO,CAACpP,GAAG,EAAEE,KAAK,EAAED,MAAM,EAAEE,IAAI,CAAC,CAACqV,IAAI,CAAC,UAAUC,IAAI,EAAE;AACrD,IAAA,OAAOrG,QAAQ,CAACqG,IAAI,CAAC,IAAI,CAAC;AAC5B,GAAC,CAAC;AACJ;AAEA,SAASC,IAAIA,CAAC/S,IAAI,EAAE;AAClB,EAAA,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBE,IAAI,GAAGH,IAAI,CAACG,IAAI;AACpB,EAAA,IAAIgR,aAAa,GAAGlR,KAAK,CAACyG,KAAK,CAAC3I,SAAS;AACzC,EAAA,IAAI6K,UAAU,GAAG3I,KAAK,CAACyG,KAAK,CAAC5I,MAAM;AACnC,EAAA,IAAI6U,gBAAgB,GAAG1S,KAAK,CAAC6G,aAAa,CAACkM,eAAe;AAC1D,EAAA,IAAIC,iBAAiB,GAAG1E,cAAc,CAACtO,KAAK,EAAE;AAC5C6O,IAAAA,cAAc,EAAE;AAClB,GAAC,CAAC;AACF,EAAA,IAAIoE,iBAAiB,GAAG3E,cAAc,CAACtO,KAAK,EAAE;AAC5C+O,IAAAA,WAAW,EAAE;AACf,GAAC,CAAC;AACF,EAAA,IAAImE,wBAAwB,GAAGT,cAAc,CAACO,iBAAiB,EAAE9B,aAAa,CAAC;EAC/E,IAAIiC,mBAAmB,GAAGV,cAAc,CAACQ,iBAAiB,EAAEtK,UAAU,EAAE+J,gBAAgB,CAAC;AACzF,EAAA,IAAIU,iBAAiB,GAAGT,qBAAqB,CAACO,wBAAwB,CAAC;AACvE,EAAA,IAAIG,gBAAgB,GAAGV,qBAAqB,CAACQ,mBAAmB,CAAC;AACjEnT,EAAAA,KAAK,CAAC6G,aAAa,CAAC3G,IAAI,CAAC,GAAG;AAC1BgT,IAAAA,wBAAwB,EAAEA,wBAAwB;AAClDC,IAAAA,mBAAmB,EAAEA,mBAAmB;AACxCC,IAAAA,iBAAiB,EAAEA,iBAAiB;AACpCC,IAAAA,gBAAgB,EAAEA;GACnB;AACDrT,EAAAA,KAAK,CAACK,UAAU,CAACxC,MAAM,GAAG5B,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE8D,KAAK,CAACK,UAAU,CAACxC,MAAM,EAAE;AACnE,IAAA,8BAA8B,EAAEuV,iBAAiB;AACjD,IAAA,qBAAqB,EAAEC;AACzB,GAAC,CAAC;AACJ,CAAC;;AAGD,eAAe;AACbnT,EAAAA,IAAI,EAAE,MAAM;AACZiB,EAAAA,OAAO,EAAE,IAAI;AACbC,EAAAA,KAAK,EAAE,MAAM;EACb8G,gBAAgB,EAAE,CAAC,iBAAiB,CAAC;AACrC7G,EAAAA,EAAE,EAAEyR;AACN,CAAC;;ACzDM,SAASQ,uBAAuBA,CAACpV,SAAS,EAAEuI,KAAK,EAAEoB,MAAM,EAAE;AAChE,EAAA,IAAIf,aAAa,GAAGvF,gBAAgB,CAACrD,SAAS,CAAC;AAC/C,EAAA,IAAIqV,cAAc,GAAG,CAAChW,IAAI,EAAEH,GAAG,CAAC,CAACoH,OAAO,CAACsC,aAAa,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC;AAErE,EAAA,IAAI/G,IAAI,GAAG,OAAO8H,MAAM,KAAK,UAAU,GAAGA,MAAM,CAAC5L,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEuK,KAAK,EAAE;AACxEvI,MAAAA,SAAS,EAAEA;KACZ,CAAC,CAAC,GAAG2J,MAAM;AACR2L,IAAAA,QAAQ,GAAGzT,IAAI,CAAC,CAAC,CAAC;AAClB0T,IAAAA,QAAQ,GAAG1T,IAAI,CAAC,CAAC,CAAC;EAEtByT,QAAQ,GAAGA,QAAQ,IAAI,CAAC;AACxBC,EAAAA,QAAQ,GAAG,CAACA,QAAQ,IAAI,CAAC,IAAIF,cAAc;AAC3C,EAAA,OAAO,CAAChW,IAAI,EAAED,KAAK,CAAC,CAACkH,OAAO,CAACsC,aAAa,CAAC,IAAI,CAAC,GAAG;AACjDvD,IAAAA,CAAC,EAAEkQ,QAAQ;AACXhQ,IAAAA,CAAC,EAAE+P;AACL,GAAC,GAAG;AACFjQ,IAAAA,CAAC,EAAEiQ,QAAQ;AACX/P,IAAAA,CAAC,EAAEgQ;GACJ;AACH;AAEA,SAAS5L,MAAMA,CAACpH,KAAK,EAAE;AACrB,EAAA,IAAIT,KAAK,GAAGS,KAAK,CAACT,KAAK;IACnBpE,OAAO,GAAG6E,KAAK,CAAC7E,OAAO;IACvBsE,IAAI,GAAGO,KAAK,CAACP,IAAI;AACrB,EAAA,IAAIwT,eAAe,GAAG9X,OAAO,CAACiM,MAAM;AAChCA,IAAAA,MAAM,GAAG6L,eAAe,KAAK,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAGA,eAAe;EAClE,IAAIrJ,IAAI,GAAGjM,UAAU,CAACJ,MAAM,CAAC,UAAUC,GAAG,EAAEC,SAAS,EAAE;AACrDD,IAAAA,GAAG,CAACC,SAAS,CAAC,GAAGoV,uBAAuB,CAACpV,SAAS,EAAE8B,KAAK,CAACyG,KAAK,EAAEoB,MAAM,CAAC;AACxE,IAAA,OAAO5J,GAAG;GACX,EAAE,EAAE,CAAC;AACN,EAAA,IAAI0V,qBAAqB,GAAGtJ,IAAI,CAACrK,KAAK,CAAC9B,SAAS,CAAC;IAC7CqF,CAAC,GAAGoQ,qBAAqB,CAACpQ,CAAC;IAC3BE,CAAC,GAAGkQ,qBAAqB,CAAClQ,CAAC;AAE/B,EAAA,IAAIzD,KAAK,CAAC6G,aAAa,CAACD,aAAa,IAAI,IAAI,EAAE;AAC7C5G,IAAAA,KAAK,CAAC6G,aAAa,CAACD,aAAa,CAACrD,CAAC,IAAIA,CAAC;AACxCvD,IAAAA,KAAK,CAAC6G,aAAa,CAACD,aAAa,CAACnD,CAAC,IAAIA,CAAC;AAC1C;AAEAzD,EAAAA,KAAK,CAAC6G,aAAa,CAAC3G,IAAI,CAAC,GAAGmK,IAAI;AAClC,CAAC;;AAGD,iBAAe;AACbnK,EAAAA,IAAI,EAAE,QAAQ;AACdiB,EAAAA,OAAO,EAAE,IAAI;AACbC,EAAAA,KAAK,EAAE,MAAM;EACbE,QAAQ,EAAE,CAAC,eAAe,CAAC;AAC3BD,EAAAA,EAAE,EAAEwG;AACN,CAAC;;ACnDD,SAASjB,aAAaA,CAAC7G,IAAI,EAAE;AAC3B,EAAA,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBE,IAAI,GAAGH,IAAI,CAACG,IAAI;AACpB;AACA;AACA;AACA;AACAF,EAAAA,KAAK,CAAC6G,aAAa,CAAC3G,IAAI,CAAC,GAAGgO,cAAc,CAAC;AACzCpQ,IAAAA,SAAS,EAAEkC,KAAK,CAACyG,KAAK,CAAC3I,SAAS;AAChCpC,IAAAA,OAAO,EAAEsE,KAAK,CAACyG,KAAK,CAAC5I,MAAM;AAC3B+C,IACA1C,SAAS,EAAE8B,KAAK,CAAC9B;AACnB,GAAC,CAAC;AACJ,CAAC;;AAGD,wBAAe;AACbgC,EAAAA,IAAI,EAAE,eAAe;AACrBiB,EAAAA,OAAO,EAAE,IAAI;AACbC,EAAAA,KAAK,EAAE,MAAM;AACbC,EAAAA,EAAE,EAAEuF,aAAa;AACjByD,EAAAA,IAAI,EAAE;AACR,CAAC;;ACxBc,SAASuJ,UAAUA,CAAC7M,IAAI,EAAE;AACvC,EAAA,OAAOA,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;AACjC;;ACUA,SAASgM,eAAeA,CAAChT,IAAI,EAAE;AAC7B,EAAA,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBpE,OAAO,GAAGmE,IAAI,CAACnE,OAAO;IACtBsE,IAAI,GAAGH,IAAI,CAACG,IAAI;AACpB,EAAA,IAAIsQ,iBAAiB,GAAG5U,OAAO,CAACyS,QAAQ;IACpCoC,aAAa,GAAGD,iBAAiB,KAAK,MAAM,GAAG,IAAI,GAAGA,iBAAiB;IACvEE,gBAAgB,GAAG9U,OAAO,CAAC+U,OAAO;IAClCC,YAAY,GAAGF,gBAAgB,KAAK,MAAM,GAAG,KAAK,GAAGA,gBAAgB;IACrE9C,QAAQ,GAAGhS,OAAO,CAACgS,QAAQ;IAC3BC,YAAY,GAAGjS,OAAO,CAACiS,YAAY;IACnCkB,WAAW,GAAGnT,OAAO,CAACmT,WAAW;IACjCvI,OAAO,GAAG5K,OAAO,CAAC4K,OAAO;IACzBqN,eAAe,GAAGjY,OAAO,CAACkY,MAAM;IAChCA,MAAM,GAAGD,eAAe,KAAK,MAAM,GAAG,IAAI,GAAGA,eAAe;IAC5DE,qBAAqB,GAAGnY,OAAO,CAACoY,YAAY;IAC5CA,YAAY,GAAGD,qBAAqB,KAAK,MAAM,GAAG,CAAC,GAAGA,qBAAqB;AAC/E,EAAA,IAAIvH,QAAQ,GAAG8B,cAAc,CAACtO,KAAK,EAAE;AACnC4N,IAAAA,QAAQ,EAAEA,QAAQ;AAClBC,IAAAA,YAAY,EAAEA,YAAY;AAC1BrH,IAAAA,OAAO,EAAEA,OAAO;AAChBuI,IAAAA,WAAW,EAAEA;AACf,GAAC,CAAC;AACF,EAAA,IAAIjI,aAAa,GAAGvF,gBAAgB,CAACvB,KAAK,CAAC9B,SAAS,CAAC;AACrD,EAAA,IAAI0K,SAAS,GAAGT,YAAY,CAACnI,KAAK,CAAC9B,SAAS,CAAC;EAC7C,IAAI+S,eAAe,GAAG,CAACrI,SAAS;AAChC,EAAA,IAAIyF,QAAQ,GAAG1I,wBAAwB,CAACmB,aAAa,CAAC;AACtD,EAAA,IAAI6J,OAAO,GAAGiD,UAAU,CAACvF,QAAQ,CAAC;AAClC,EAAA,IAAIzH,aAAa,GAAG5G,KAAK,CAAC6G,aAAa,CAACD,aAAa;AACrD,EAAA,IAAIsK,aAAa,GAAGlR,KAAK,CAACyG,KAAK,CAAC3I,SAAS;AACzC,EAAA,IAAI6K,UAAU,GAAG3I,KAAK,CAACyG,KAAK,CAAC5I,MAAM;AACnC,EAAA,IAAIoW,iBAAiB,GAAG,OAAOD,YAAY,KAAK,UAAU,GAAGA,YAAY,CAAC/X,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE8D,KAAK,CAACyG,KAAK,EAAE;IACvGvI,SAAS,EAAE8B,KAAK,CAAC9B;GAClB,CAAC,CAAC,GAAG8V,YAAY;AAClB,EAAA,IAAIE,2BAA2B,GAAG,OAAOD,iBAAiB,KAAK,QAAQ,GAAG;AACxE5F,IAAAA,QAAQ,EAAE4F,iBAAiB;AAC3BtD,IAAAA,OAAO,EAAEsD;AACX,GAAC,GAAGhY,MAAM,CAACC,MAAM,CAAC;AAChBmS,IAAAA,QAAQ,EAAE,CAAC;AACXsC,IAAAA,OAAO,EAAE;GACV,EAAEsD,iBAAiB,CAAC;AACrB,EAAA,IAAIE,mBAAmB,GAAGnU,KAAK,CAAC6G,aAAa,CAACgB,MAAM,GAAG7H,KAAK,CAAC6G,aAAa,CAACgB,MAAM,CAAC7H,KAAK,CAAC9B,SAAS,CAAC,GAAG,IAAI;AACzG,EAAA,IAAImM,IAAI,GAAG;AACT9G,IAAAA,CAAC,EAAE,CAAC;AACJE,IAAAA,CAAC,EAAE;GACJ;EAED,IAAI,CAACmD,aAAa,EAAE;AAClB,IAAA;AACF;AAEA,EAAA,IAAI6J,aAAa,EAAE;AACjB,IAAA,IAAI2D,qBAAqB;IAEzB,IAAIC,QAAQ,GAAGhG,QAAQ,KAAK,GAAG,GAAGjR,GAAG,GAAGG,IAAI;IAC5C,IAAI+W,OAAO,GAAGjG,QAAQ,KAAK,GAAG,GAAGhR,MAAM,GAAGC,KAAK;IAC/C,IAAI2J,GAAG,GAAGoH,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;AAC/C,IAAA,IAAIxG,MAAM,GAAGjB,aAAa,CAACyH,QAAQ,CAAC;AACpC,IAAA,IAAI1M,KAAG,GAAGkG,MAAM,GAAG2E,QAAQ,CAAC6H,QAAQ,CAAC;AACrC,IAAA,IAAI5S,KAAG,GAAGoG,MAAM,GAAG2E,QAAQ,CAAC8H,OAAO,CAAC;AACpC,IAAA,IAAIC,QAAQ,GAAGT,MAAM,GAAG,CAACnL,UAAU,CAAC1B,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AAChD,IAAA,IAAIuN,MAAM,GAAG5L,SAAS,KAAKlM,KAAK,GAAGwU,aAAa,CAACjK,GAAG,CAAC,GAAG0B,UAAU,CAAC1B,GAAG,CAAC;AACvE,IAAA,IAAIwN,MAAM,GAAG7L,SAAS,KAAKlM,KAAK,GAAG,CAACiM,UAAU,CAAC1B,GAAG,CAAC,GAAG,CAACiK,aAAa,CAACjK,GAAG,CAAC,CAAC;AAC1E;;AAEA,IAAA,IAAIN,YAAY,GAAG3G,KAAK,CAAC3E,QAAQ,CAACyF,KAAK;IACvC,IAAIoG,SAAS,GAAG4M,MAAM,IAAInN,YAAY,GAAGhD,aAAa,CAACgD,YAAY,CAAC,GAAG;AACrEzD,MAAAA,KAAK,EAAE,CAAC;AACRE,MAAAA,MAAM,EAAE;KACT;IACD,IAAIsR,kBAAkB,GAAG1U,KAAK,CAAC6G,aAAa,CAAC,kBAAkB,CAAC,GAAG7G,KAAK,CAAC6G,aAAa,CAAC,kBAAkB,CAAC,CAACL,OAAO,GAAGP,kBAAkB,EAAE;AACzI,IAAA,IAAI0O,eAAe,GAAGD,kBAAkB,CAACL,QAAQ,CAAC;AAClD,IAAA,IAAIO,eAAe,GAAGF,kBAAkB,CAACJ,OAAO,CAAC,CAAC;AAClD;AACA;AACA;AACA;;AAEA,IAAA,IAAIO,QAAQ,GAAGjP,MAAM,CAAC,CAAC,EAAEsL,aAAa,CAACjK,GAAG,CAAC,EAAEC,SAAS,CAACD,GAAG,CAAC,CAAC;AAC5D,IAAA,IAAI6N,SAAS,GAAG7D,eAAe,GAAGC,aAAa,CAACjK,GAAG,CAAC,GAAG,CAAC,GAAGsN,QAAQ,GAAGM,QAAQ,GAAGF,eAAe,GAAGT,2BAA2B,CAAC7F,QAAQ,GAAGmG,MAAM,GAAGK,QAAQ,GAAGF,eAAe,GAAGT,2BAA2B,CAAC7F,QAAQ;AACpN,IAAA,IAAI0G,SAAS,GAAG9D,eAAe,GAAG,CAACC,aAAa,CAACjK,GAAG,CAAC,GAAG,CAAC,GAAGsN,QAAQ,GAAGM,QAAQ,GAAGD,eAAe,GAAGV,2BAA2B,CAAC7F,QAAQ,GAAGoG,MAAM,GAAGI,QAAQ,GAAGD,eAAe,GAAGV,2BAA2B,CAAC7F,QAAQ;AACrN,IAAA,IAAI9G,iBAAiB,GAAGvH,KAAK,CAAC3E,QAAQ,CAACyF,KAAK,IAAI4E,eAAe,CAAC1F,KAAK,CAAC3E,QAAQ,CAACyF,KAAK,CAAC;IACrF,IAAIkU,YAAY,GAAGzN,iBAAiB,GAAG8G,QAAQ,KAAK,GAAG,GAAG9G,iBAAiB,CAAC6F,SAAS,IAAI,CAAC,GAAG7F,iBAAiB,CAAC8F,UAAU,IAAI,CAAC,GAAG,CAAC;IAClI,IAAI4H,mBAAmB,GAAG,CAACb,qBAAqB,GAAGD,mBAAmB,IAAI,IAAI,GAAG,MAAM,GAAGA,mBAAmB,CAAC9F,QAAQ,CAAC,KAAK,IAAI,GAAG+F,qBAAqB,GAAG,CAAC;IAC5J,IAAIc,SAAS,GAAGrN,MAAM,GAAGiN,SAAS,GAAGG,mBAAmB,GAAGD,YAAY;AACvE,IAAA,IAAIG,SAAS,GAAGtN,MAAM,GAAGkN,SAAS,GAAGE,mBAAmB;IACxD,IAAIG,eAAe,GAAGxP,MAAM,CAACkO,MAAM,GAAGhO,GAAO,CAACnE,KAAG,EAAEuT,SAAS,CAAC,GAAGvT,KAAG,EAAEkG,MAAM,EAAEiM,MAAM,GAAGjO,GAAO,CAACpE,KAAG,EAAE0T,SAAS,CAAC,GAAG1T,KAAG,CAAC;AACpHmF,IAAAA,aAAa,CAACyH,QAAQ,CAAC,GAAG+G,eAAe;AACzC/K,IAAAA,IAAI,CAACgE,QAAQ,CAAC,GAAG+G,eAAe,GAAGvN,MAAM;AAC3C;AAEA,EAAA,IAAI+I,YAAY,EAAE;AAChB,IAAA,IAAIyE,sBAAsB;IAE1B,IAAIC,SAAS,GAAGjH,QAAQ,KAAK,GAAG,GAAGjR,GAAG,GAAGG,IAAI;IAE7C,IAAIgY,QAAQ,GAAGlH,QAAQ,KAAK,GAAG,GAAGhR,MAAM,GAAGC,KAAK;AAEhD,IAAA,IAAIkY,OAAO,GAAG5O,aAAa,CAAC+J,OAAO,CAAC;IAEpC,IAAI8E,IAAI,GAAG9E,OAAO,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;AAE/C,IAAA,IAAI+E,IAAI,GAAGF,OAAO,GAAGhJ,QAAQ,CAAC8I,SAAS,CAAC;AAExC,IAAA,IAAIK,IAAI,GAAGH,OAAO,GAAGhJ,QAAQ,CAAC+I,QAAQ,CAAC;AAEvC,IAAA,IAAIK,YAAY,GAAG,CAACxY,GAAG,EAAEG,IAAI,CAAC,CAACiH,OAAO,CAACsC,aAAa,CAAC,KAAK,EAAE;IAE5D,IAAI+O,oBAAoB,GAAG,CAACR,sBAAsB,GAAGlB,mBAAmB,IAAI,IAAI,GAAG,MAAM,GAAGA,mBAAmB,CAACxD,OAAO,CAAC,KAAK,IAAI,GAAG0E,sBAAsB,GAAG,CAAC;IAE9J,IAAIS,UAAU,GAAGF,YAAY,GAAGF,IAAI,GAAGF,OAAO,GAAGtE,aAAa,CAACuE,IAAI,CAAC,GAAG9M,UAAU,CAAC8M,IAAI,CAAC,GAAGI,oBAAoB,GAAG3B,2BAA2B,CAACvD,OAAO;IAEpJ,IAAIoF,UAAU,GAAGH,YAAY,GAAGJ,OAAO,GAAGtE,aAAa,CAACuE,IAAI,CAAC,GAAG9M,UAAU,CAAC8M,IAAI,CAAC,GAAGI,oBAAoB,GAAG3B,2BAA2B,CAACvD,OAAO,GAAGgF,IAAI;AAEpJ,IAAA,IAAIK,gBAAgB,GAAGlC,MAAM,IAAI8B,YAAY,GAAG7P,cAAc,CAAC+P,UAAU,EAAEN,OAAO,EAAEO,UAAU,CAAC,GAAGnQ,MAAM,CAACkO,MAAM,GAAGgC,UAAU,GAAGJ,IAAI,EAAEF,OAAO,EAAE1B,MAAM,GAAGiC,UAAU,GAAGJ,IAAI,CAAC;AAEzK/O,IAAAA,aAAa,CAAC+J,OAAO,CAAC,GAAGqF,gBAAgB;AACzC3L,IAAAA,IAAI,CAACsG,OAAO,CAAC,GAAGqF,gBAAgB,GAAGR,OAAO;AAC5C;AAEAxV,EAAAA,KAAK,CAAC6G,aAAa,CAAC3G,IAAI,CAAC,GAAGmK,IAAI;AAClC,CAAC;;AAGD,0BAAe;AACbnK,EAAAA,IAAI,EAAE,iBAAiB;AACvBiB,EAAAA,OAAO,EAAE,IAAI;AACbC,EAAAA,KAAK,EAAE,MAAM;AACbC,EAAAA,EAAE,EAAE0R,eAAe;EACnB7K,gBAAgB,EAAE,CAAC,QAAQ;AAC7B,CAAC;;AC7Ic,SAAS+N,oBAAoBA,CAACva,OAAO,EAAE;EACpD,OAAO;IACL6P,UAAU,EAAE7P,OAAO,CAAC6P,UAAU;IAC9BE,SAAS,EAAE/P,OAAO,CAAC+P;GACpB;AACH;;ACDe,SAASyK,aAAaA,CAAC/W,IAAI,EAAE;AAC1C,EAAA,IAAIA,IAAI,KAAKD,SAAS,CAACC,IAAI,CAAC,IAAI,CAACO,aAAa,CAACP,IAAI,CAAC,EAAE;IACpD,OAAOmM,eAAe,CAACnM,IAAI,CAAC;AAC9B,GAAC,MAAM;IACL,OAAO8W,oBAAoB,CAAC9W,IAAI,CAAC;AACnC;AACF;;ACDA,SAASgX,eAAeA,CAACza,OAAO,EAAE;AAChC,EAAA,IAAIwR,IAAI,GAAGxR,OAAO,CAACiH,qBAAqB,EAAE;AAC1C,EAAA,IAAII,MAAM,GAAGnB,KAAK,CAACsL,IAAI,CAAChK,KAAK,CAAC,GAAGxH,OAAO,CAACuH,WAAW,IAAI,CAAC;AACzD,EAAA,IAAID,MAAM,GAAGpB,KAAK,CAACsL,IAAI,CAAC9J,MAAM,CAAC,GAAG1H,OAAO,CAACyH,YAAY,IAAI,CAAC;AAC3D,EAAA,OAAOJ,MAAM,KAAK,CAAC,IAAIC,MAAM,KAAK,CAAC;AACrC,CAAC;AACD;;AAGe,SAASoT,gBAAgBA,CAACC,uBAAuB,EAAEvR,YAAY,EAAEmE,OAAO,EAAE;AACvF,EAAA,IAAIA,OAAO,KAAK,MAAM,EAAE;AACtBA,IAAAA,OAAO,GAAG,KAAK;AACjB;AAEA,EAAA,IAAIqN,uBAAuB,GAAG5W,aAAa,CAACoF,YAAY,CAAC;EACzD,IAAIyR,oBAAoB,GAAG7W,aAAa,CAACoF,YAAY,CAAC,IAAIqR,eAAe,CAACrR,YAAY,CAAC;AACvF,EAAA,IAAIJ,eAAe,GAAGD,kBAAkB,CAACK,YAAY,CAAC;EACtD,IAAIoI,IAAI,GAAGvK,qBAAqB,CAAC0T,uBAAuB,EAAEE,oBAAoB,EAAEtN,OAAO,CAAC;AACxF,EAAA,IAAIwB,MAAM,GAAG;AACXc,IAAAA,UAAU,EAAE,CAAC;AACbE,IAAAA,SAAS,EAAE;GACZ;AACD,EAAA,IAAI5C,OAAO,GAAG;AACZtF,IAAAA,CAAC,EAAE,CAAC;AACJE,IAAAA,CAAC,EAAE;GACJ;AAED,EAAA,IAAI6S,uBAAuB,IAAI,CAACA,uBAAuB,IAAI,CAACrN,OAAO,EAAE;AACnE,IAAA,IAAIlK,WAAW,CAAC+F,YAAY,CAAC,KAAK,MAAM;AAAI;IAC5CwH,cAAc,CAAC5H,eAAe,CAAC,EAAE;AAC/B+F,MAAAA,MAAM,GAAGyL,aAAa,CAACpR,YAAY,CAAC;AACtC;AAEA,IAAA,IAAIpF,aAAa,CAACoF,YAAY,CAAC,EAAE;AAC/B+D,MAAAA,OAAO,GAAGlG,qBAAqB,CAACmC,YAAY,EAAE,IAAI,CAAC;AACnD+D,MAAAA,OAAO,CAACtF,CAAC,IAAIuB,YAAY,CAACuI,UAAU;AACpCxE,MAAAA,OAAO,CAACpF,CAAC,IAAIqB,YAAY,CAACsI,SAAS;KACpC,MAAM,IAAI1I,eAAe,EAAE;AAC1BmE,MAAAA,OAAO,CAACtF,CAAC,GAAGoI,mBAAmB,CAACjH,eAAe,CAAC;AAClD;AACF;EAEA,OAAO;IACLnB,CAAC,EAAE2J,IAAI,CAAC3P,IAAI,GAAGkN,MAAM,CAACc,UAAU,GAAG1C,OAAO,CAACtF,CAAC;IAC5CE,CAAC,EAAEyJ,IAAI,CAAC9P,GAAG,GAAGqN,MAAM,CAACgB,SAAS,GAAG5C,OAAO,CAACpF,CAAC;IAC1CP,KAAK,EAAEgK,IAAI,CAAChK,KAAK;IACjBE,MAAM,EAAE8J,IAAI,CAAC9J;GACd;AACH;;ACvDA,SAASoT,KAAKA,CAACC,SAAS,EAAE;AACxB,EAAA,IAAI3Z,GAAG,GAAG,IAAIsU,GAAG,EAAE;AACnB,EAAA,IAAIsF,OAAO,GAAG,IAAIC,GAAG,EAAE;EACvB,IAAIC,MAAM,GAAG,EAAE;AACfH,EAAAA,SAAS,CAAChb,OAAO,CAAC,UAAUob,QAAQ,EAAE;IACpC/Z,GAAG,CAACkV,GAAG,CAAC6E,QAAQ,CAAC3W,IAAI,EAAE2W,QAAQ,CAAC;GACjC,CAAC,CAAC;;EAEH,SAAS5G,IAAIA,CAAC4G,QAAQ,EAAE;AACtBH,IAAAA,OAAO,CAACI,GAAG,CAACD,QAAQ,CAAC3W,IAAI,CAAC;AAC1B,IAAA,IAAIoB,QAAQ,GAAG,EAAE,CAACnD,MAAM,CAAC0Y,QAAQ,CAACvV,QAAQ,IAAI,EAAE,EAAEuV,QAAQ,CAAC3O,gBAAgB,IAAI,EAAE,CAAC;AAClF5G,IAAAA,QAAQ,CAAC7F,OAAO,CAAC,UAAUsb,GAAG,EAAE;AAC9B,MAAA,IAAI,CAACL,OAAO,CAACM,GAAG,CAACD,GAAG,CAAC,EAAE;AACrB,QAAA,IAAIE,WAAW,GAAGna,GAAG,CAACwV,GAAG,CAACyE,GAAG,CAAC;AAE9B,QAAA,IAAIE,WAAW,EAAE;UACfhH,IAAI,CAACgH,WAAW,CAAC;AACnB;AACF;AACF,KAAC,CAAC;AACFL,IAAAA,MAAM,CAAC/E,IAAI,CAACgF,QAAQ,CAAC;AACvB;AAEAJ,EAAAA,SAAS,CAAChb,OAAO,CAAC,UAAUob,QAAQ,EAAE;IACpC,IAAI,CAACH,OAAO,CAACM,GAAG,CAACH,QAAQ,CAAC3W,IAAI,CAAC,EAAE;AAC/B;MACA+P,IAAI,CAAC4G,QAAQ,CAAC;AAChB;AACF,GAAC,CAAC;AACF,EAAA,OAAOD,MAAM;AACf;AAEe,SAASM,cAAcA,CAACT,SAAS,EAAE;AAChD;AACA,EAAA,IAAIU,gBAAgB,GAAGX,KAAK,CAACC,SAAS,CAAC,CAAC;;EAExC,OAAO3X,cAAc,CAACd,MAAM,CAAC,UAAUC,GAAG,EAAEmD,KAAK,EAAE;IACjD,OAAOnD,GAAG,CAACE,MAAM,CAACgZ,gBAAgB,CAAC1R,MAAM,CAAC,UAAUoR,QAAQ,EAAE;AAC5D,MAAA,OAAOA,QAAQ,CAACzV,KAAK,KAAKA,KAAK;AACjC,KAAC,CAAC,CAAC;GACJ,EAAE,EAAE,CAAC;AACR;;AC3Ce,SAASgW,QAAQA,CAAC/V,EAAE,EAAE;AACnC,EAAA,IAAIgW,OAAO;AACX,EAAA,OAAO,YAAY;IACjB,IAAI,CAACA,OAAO,EAAE;AACZA,MAAAA,OAAO,GAAG,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;AACvCD,QAAAA,OAAO,CAACC,OAAO,EAAE,CAACC,IAAI,CAAC,YAAY;AACjCH,UAAAA,OAAO,GAAGI,SAAS;AACnBF,UAAAA,OAAO,CAAClW,EAAE,EAAE,CAAC;AACf,SAAC,CAAC;AACJ,OAAC,CAAC;AACJ;AAEA,IAAA,OAAOgW,OAAO;GACf;AACH;;ACde,SAASK,WAAWA,CAACjB,SAAS,EAAE;EAC7C,IAAIkB,MAAM,GAAGlB,SAAS,CAACzY,MAAM,CAAC,UAAU2Z,MAAM,EAAEC,OAAO,EAAE;AACvD,IAAA,IAAIC,QAAQ,GAAGF,MAAM,CAACC,OAAO,CAAC1X,IAAI,CAAC;AACnCyX,IAAAA,MAAM,CAACC,OAAO,CAAC1X,IAAI,CAAC,GAAG2X,QAAQ,GAAG5b,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE2b,QAAQ,EAAED,OAAO,EAAE;AACrEhc,MAAAA,OAAO,EAAEK,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE2b,QAAQ,CAACjc,OAAO,EAAEgc,OAAO,CAAChc,OAAO,CAAC;AAC7DyO,MAAAA,IAAI,EAAEpO,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE2b,QAAQ,CAACxN,IAAI,EAAEuN,OAAO,CAACvN,IAAI;KACpD,CAAC,GAAGuN,OAAO;AACZ,IAAA,OAAOD,MAAM;AACf,GAAC,EAAE,EAAE,CAAC,CAAC;;EAEP,OAAO1b,MAAM,CAACgE,IAAI,CAAC0X,MAAM,CAAC,CAAC7a,GAAG,CAAC,UAAUwJ,GAAG,EAAE;IAC5C,OAAOqR,MAAM,CAACrR,GAAG,CAAC;AACpB,GAAC,CAAC;AACJ;;ACJA,IAAIwR,eAAe,GAAG;AACpB5Z,EAAAA,SAAS,EAAE,QAAQ;AACnBuY,EAAAA,SAAS,EAAE,EAAE;AACb7V,EAAAA,QAAQ,EAAE;AACZ,CAAC;AAED,SAASmX,gBAAgBA,GAAG;EAC1B,KAAK,IAAItC,IAAI,GAAGuC,SAAS,CAACxc,MAAM,EAAEyc,IAAI,GAAG,IAAI/V,KAAK,CAACuT,IAAI,CAAC,EAAEyC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGzC,IAAI,EAAEyC,IAAI,EAAE,EAAE;AACvFD,IAAAA,IAAI,CAACC,IAAI,CAAC,GAAGF,SAAS,CAACE,IAAI,CAAC;AAC9B;AAEA,EAAA,OAAO,CAACD,IAAI,CAACrF,IAAI,CAAC,UAAUlX,OAAO,EAAE;IACnC,OAAO,EAAEA,OAAO,IAAI,OAAOA,OAAO,CAACiH,qBAAqB,KAAK,UAAU,CAAC;AAC1E,GAAC,CAAC;AACJ;AAEO,SAASwV,eAAeA,CAACC,gBAAgB,EAAE;AAChD,EAAA,IAAIA,gBAAgB,KAAK,MAAM,EAAE;IAC/BA,gBAAgB,GAAG,EAAE;AACvB;EAEA,IAAIC,iBAAiB,GAAGD,gBAAgB;IACpCE,qBAAqB,GAAGD,iBAAiB,CAACE,gBAAgB;IAC1DA,gBAAgB,GAAGD,qBAAqB,KAAK,MAAM,GAAG,EAAE,GAAGA,qBAAqB;IAChFE,sBAAsB,GAAGH,iBAAiB,CAACI,cAAc;IACzDA,cAAc,GAAGD,sBAAsB,KAAK,MAAM,GAAGV,eAAe,GAAGU,sBAAsB;EACjG,OAAO,SAASE,YAAYA,CAAC5a,SAAS,EAAED,MAAM,EAAEjC,OAAO,EAAE;AACvD,IAAA,IAAIA,OAAO,KAAK,MAAM,EAAE;AACtBA,MAAAA,OAAO,GAAG6c,cAAc;AAC1B;AAEA,IAAA,IAAIzY,KAAK,GAAG;AACV9B,MAAAA,SAAS,EAAE,QAAQ;AACnBiZ,MAAAA,gBAAgB,EAAE,EAAE;MACpBvb,OAAO,EAAEK,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE4b,eAAe,EAAEW,cAAc,CAAC;MAC3D5R,aAAa,EAAE,EAAE;AACjBxL,MAAAA,QAAQ,EAAE;AACRyC,QAAAA,SAAS,EAAEA,SAAS;AACpBD,QAAAA,MAAM,EAAEA;OACT;MACDwC,UAAU,EAAE,EAAE;AACdD,MAAAA,MAAM,EAAE;KACT;IACD,IAAIuY,gBAAgB,GAAG,EAAE;IACzB,IAAIC,WAAW,GAAG,KAAK;AACvB,IAAA,IAAIrO,QAAQ,GAAG;AACbvK,MAAAA,KAAK,EAAEA,KAAK;AACZ6Y,MAAAA,UAAU,EAAE,SAASA,UAAUA,CAACC,gBAAgB,EAAE;AAChD,QAAA,IAAIld,OAAO,GAAG,OAAOkd,gBAAgB,KAAK,UAAU,GAAGA,gBAAgB,CAAC9Y,KAAK,CAACpE,OAAO,CAAC,GAAGkd,gBAAgB;AACzGC,QAAAA,sBAAsB,EAAE;AACxB/Y,QAAAA,KAAK,CAACpE,OAAO,GAAGK,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEuc,cAAc,EAAEzY,KAAK,CAACpE,OAAO,EAAEA,OAAO,CAAC;QACzEoE,KAAK,CAAC4K,aAAa,GAAG;UACpB9M,SAAS,EAAEyB,WAAS,CAACzB,SAAS,CAAC,GAAG8O,iBAAiB,CAAC9O,SAAS,CAAC,GAAGA,SAAS,CAACqR,cAAc,GAAGvC,iBAAiB,CAAC9O,SAAS,CAACqR,cAAc,CAAC,GAAG,EAAE;UAC5ItR,MAAM,EAAE+O,iBAAiB,CAAC/O,MAAM;AAClC,SAAC,CAAC;AACF;;QAEA,IAAIsZ,gBAAgB,GAAGD,cAAc,CAACQ,WAAW,CAAC,EAAE,CAACvZ,MAAM,CAACoa,gBAAgB,EAAEvY,KAAK,CAACpE,OAAO,CAAC6a,SAAS,CAAC,CAAC,CAAC,CAAC;;QAEzGzW,KAAK,CAACmX,gBAAgB,GAAGA,gBAAgB,CAAC1R,MAAM,CAAC,UAAUuT,CAAC,EAAE;UAC5D,OAAOA,CAAC,CAAC7X,OAAO;AAClB,SAAC,CAAC;AACF8X,QAAAA,kBAAkB,EAAE;AACpB,QAAA,OAAO1O,QAAQ,CAACQ,MAAM,EAAE;OACzB;AACD;AACA;AACA;AACA;AACA;AACAmO,MAAAA,WAAW,EAAE,SAASA,WAAWA,GAAG;AAClC,QAAA,IAAIN,WAAW,EAAE;AACf,UAAA;AACF;AAEA,QAAA,IAAIO,eAAe,GAAGnZ,KAAK,CAAC3E,QAAQ;UAChCyC,SAAS,GAAGqb,eAAe,CAACrb,SAAS;AACrCD,UAAAA,MAAM,GAAGsb,eAAe,CAACtb,MAAM,CAAC;AACpC;;AAEA,QAAA,IAAI,CAACka,gBAAgB,CAACja,SAAS,EAAED,MAAM,CAAC,EAAE;AACxC,UAAA;AACF,SAAC;;QAGDmC,KAAK,CAACyG,KAAK,GAAG;AACZ3I,UAAAA,SAAS,EAAEsY,gBAAgB,CAACtY,SAAS,EAAE4H,eAAe,CAAC7H,MAAM,CAAC,EAAEmC,KAAK,CAACpE,OAAO,CAACgF,QAAQ,KAAK,OAAO,CAAC;UACnG/C,MAAM,EAAE8F,aAAa,CAAC9F,MAAM;AAC9B,SAAC,CAAC;AACF;AACA;AACA;AACA;;QAEAmC,KAAK,CAACwS,KAAK,GAAG,KAAK;QACnBxS,KAAK,CAAC9B,SAAS,GAAG8B,KAAK,CAACpE,OAAO,CAACsC,SAAS,CAAC;AAC1C;AACA;AACA;;AAEA8B,QAAAA,KAAK,CAACmX,gBAAgB,CAAC1b,OAAO,CAAC,UAAUob,QAAQ,EAAE;AACjD,UAAA,OAAO7W,KAAK,CAAC6G,aAAa,CAACgQ,QAAQ,CAAC3W,IAAI,CAAC,GAAGjE,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE2a,QAAQ,CAACxM,IAAI,CAAC;AAC9E,SAAC,CAAC;AAEF,QAAA,KAAK,IAAI+O,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGpZ,KAAK,CAACmX,gBAAgB,CAAC3b,MAAM,EAAE4d,KAAK,EAAE,EAAE;AAClE,UAAA,IAAIpZ,KAAK,CAACwS,KAAK,KAAK,IAAI,EAAE;YACxBxS,KAAK,CAACwS,KAAK,GAAG,KAAK;YACnB4G,KAAK,GAAG,EAAE;AACV,YAAA;AACF;AAEA,UAAA,IAAIC,qBAAqB,GAAGrZ,KAAK,CAACmX,gBAAgB,CAACiC,KAAK,CAAC;YACrD/X,EAAE,GAAGgY,qBAAqB,CAAChY,EAAE;YAC7BiY,sBAAsB,GAAGD,qBAAqB,CAACzd,OAAO;YACtD2S,QAAQ,GAAG+K,sBAAsB,KAAK,MAAM,GAAG,EAAE,GAAGA,sBAAsB;YAC1EpZ,IAAI,GAAGmZ,qBAAqB,CAACnZ,IAAI;AAErC,UAAA,IAAI,OAAOmB,EAAE,KAAK,UAAU,EAAE;YAC5BrB,KAAK,GAAGqB,EAAE,CAAC;AACTrB,cAAAA,KAAK,EAAEA,KAAK;AACZpE,cAAAA,OAAO,EAAE2S,QAAQ;AACjBrO,cAAAA,IAAI,EAAEA,IAAI;AACVqK,cAAAA,QAAQ,EAAEA;aACX,CAAC,IAAIvK,KAAK;AACb;AACF;OACD;AACD;AACA;MACA+K,MAAM,EAAEqM,QAAQ,CAAC,YAAY;AAC3B,QAAA,OAAO,IAAIE,OAAO,CAAC,UAAUC,OAAO,EAAE;UACpChN,QAAQ,CAAC2O,WAAW,EAAE;UACtB3B,OAAO,CAACvX,KAAK,CAAC;AAChB,SAAC,CAAC;AACJ,OAAC,CAAC;AACFuZ,MAAAA,OAAO,EAAE,SAASA,OAAOA,GAAG;AAC1BR,QAAAA,sBAAsB,EAAE;AACxBH,QAAAA,WAAW,GAAG,IAAI;AACpB;KACD;AAED,IAAA,IAAI,CAACb,gBAAgB,CAACja,SAAS,EAAED,MAAM,CAAC,EAAE;AACxC,MAAA,OAAO0M,QAAQ;AACjB;IAEAA,QAAQ,CAACsO,UAAU,CAACjd,OAAO,CAAC,CAAC4b,IAAI,CAAC,UAAUxX,KAAK,EAAE;AACjD,MAAA,IAAI,CAAC4Y,WAAW,IAAIhd,OAAO,CAAC4d,aAAa,EAAE;AACzC5d,QAAAA,OAAO,CAAC4d,aAAa,CAACxZ,KAAK,CAAC;AAC9B;KACD,CAAC,CAAC;AACH;AACA;AACA;AACA;;IAEA,SAASiZ,kBAAkBA,GAAG;AAC5BjZ,MAAAA,KAAK,CAACmX,gBAAgB,CAAC1b,OAAO,CAAC,UAAUsE,IAAI,EAAE;AAC7C,QAAA,IAAIG,IAAI,GAAGH,IAAI,CAACG,IAAI;UAChBuZ,YAAY,GAAG1Z,IAAI,CAACnE,OAAO;UAC3BA,OAAO,GAAG6d,YAAY,KAAK,MAAM,GAAG,EAAE,GAAGA,YAAY;UACrDjZ,MAAM,GAAGT,IAAI,CAACS,MAAM;AAExB,QAAA,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;UAChC,IAAIkZ,SAAS,GAAGlZ,MAAM,CAAC;AACrBR,YAAAA,KAAK,EAAEA,KAAK;AACZE,YAAAA,IAAI,EAAEA,IAAI;AACVqK,YAAAA,QAAQ,EAAEA,QAAQ;AAClB3O,YAAAA,OAAO,EAAEA;AACX,WAAC,CAAC;AAEF,UAAA,IAAI+d,MAAM,GAAG,SAASA,MAAMA,GAAG,EAAE;AAEjChB,UAAAA,gBAAgB,CAAC9G,IAAI,CAAC6H,SAAS,IAAIC,MAAM,CAAC;AAC5C;AACF,OAAC,CAAC;AACJ;IAEA,SAASZ,sBAAsBA,GAAG;AAChCJ,MAAAA,gBAAgB,CAACld,OAAO,CAAC,UAAU4F,EAAE,EAAE;QACrC,OAAOA,EAAE,EAAE;AACb,OAAC,CAAC;AACFsX,MAAAA,gBAAgB,GAAG,EAAE;AACvB;AAEA,IAAA,OAAOpO,QAAQ;GAChB;AACH;AACO,IAAImO,cAAY,gBAAgBP,eAAe,EAAE,CAAC;;AC/LzD,IAAII,kBAAgB,GAAG,CAACqB,cAAc,EAAEhT,eAAa,EAAEoD,eAAa,EAAElK,aAAW,CAAC;AAClF,IAAI4Y,cAAY,gBAAgBP,eAAe,CAAC;AAC9CI,EAAAA,gBAAgB,EAAEA;AACpB,CAAC,CAAC,CAAC;;ACEH,IAAIA,gBAAgB,GAAG,CAACqB,cAAc,EAAEhT,eAAa,EAAEoD,eAAa,EAAElK,aAAW,EAAE+H,QAAM,EAAEyI,MAAI,EAAEyC,iBAAe,EAAEjS,OAAK,EAAEgS,MAAI,CAAC;AAC9H,IAAI4F,YAAY,gBAAgBP,eAAe,CAAC;AAC9CI,EAAAA,gBAAgB,EAAEA;AACpB,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACbH;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,MAAMsB,UAAU,GAAG,IAAIzI,GAAG,EAAE;AAE5B,MAAA0I,IAAA,GAAe;AACb9H,EAAAA,GAAGA,CAACtW,OAAO,EAAE4K,GAAG,EAAEiE,QAAQ,EAAE;AAC1B,IAAA,IAAI,CAACsP,UAAU,CAAC7C,GAAG,CAACtb,OAAO,CAAC,EAAE;MAC5Bme,UAAU,CAAC7H,GAAG,CAACtW,OAAO,EAAE,IAAI0V,GAAG,EAAE,CAAC;AACpC;AAEA,IAAA,MAAM2I,WAAW,GAAGF,UAAU,CAACvH,GAAG,CAAC5W,OAAO,CAAC;;AAE3C;AACA;AACA,IAAA,IAAI,CAACqe,WAAW,CAAC/C,GAAG,CAAC1Q,GAAG,CAAC,IAAIyT,WAAW,CAACC,IAAI,KAAK,CAAC,EAAE;AACnD;AACAC,MAAAA,OAAO,CAAC9d,KAAK,CAAE,CAA8E+F,4EAAAA,EAAAA,KAAK,CAACgY,IAAI,CAACH,WAAW,CAAC9Z,IAAI,EAAE,CAAC,CAAC,CAAC,CAAE,GAAE,CAAC;AAClI,MAAA;AACF;AAEA8Z,IAAAA,WAAW,CAAC/H,GAAG,CAAC1L,GAAG,EAAEiE,QAAQ,CAAC;AAC/B,GAAA;AAED+H,EAAAA,GAAGA,CAAC5W,OAAO,EAAE4K,GAAG,EAAE;AAChB,IAAA,IAAIuT,UAAU,CAAC7C,GAAG,CAACtb,OAAO,CAAC,EAAE;AAC3B,MAAA,OAAOme,UAAU,CAACvH,GAAG,CAAC5W,OAAO,CAAC,CAAC4W,GAAG,CAAChM,GAAG,CAAC,IAAI,IAAI;AACjD;AAEA,IAAA,OAAO,IAAI;AACZ,GAAA;AAED6T,EAAAA,MAAMA,CAACze,OAAO,EAAE4K,GAAG,EAAE;AACnB,IAAA,IAAI,CAACuT,UAAU,CAAC7C,GAAG,CAACtb,OAAO,CAAC,EAAE;AAC5B,MAAA;AACF;AAEA,IAAA,MAAMqe,WAAW,GAAGF,UAAU,CAACvH,GAAG,CAAC5W,OAAO,CAAC;AAE3Cqe,IAAAA,WAAW,CAACK,MAAM,CAAC9T,GAAG,CAAC;;AAEvB;AACA,IAAA,IAAIyT,WAAW,CAACC,IAAI,KAAK,CAAC,EAAE;AAC1BH,MAAAA,UAAU,CAACO,MAAM,CAAC1e,OAAO,CAAC;AAC5B;AACF;AACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpDD;AACA;AACA;AACA,IAAI2e,mBAAmB,GAAG,EAAE,CAACzd,KAAK,CAACC,IAAI,CAACvB,QAAQ,CAACC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;AACjG8e,mBAAmB,CAACvd,GAAG,CAAC,UAAUwd,iBAAiB,EAAE;AACpD,EAAA,IAAI1e,OAAO,GAAG;AACbgS,IAAAA,QAAQ,EAAE0M,iBAAiB,CAACxe,YAAY,CAAC,kBAAkB,CAAC,KAAK,UAAU,GAAGR,QAAQ,CAAC2M,aAAa,CAAC,MAAM,CAAC,GAAG;GAC/G;AACD,EAAA,OAAO,IAAIsS,QAAQ,CAACD,iBAAiB,EAAE1e,OAAO,CAAC;AAChD,CAAC,CAAC;;ACTF,IAAI4e,kBAAkB,GAAG,EAAE,CAAC5d,KAAK,CAACC,IAAI,CAACvB,QAAQ,CAACC,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;AAC/Fif,kBAAkB,CAAC1d,GAAG,CAAC,UAAU2d,gBAAgB,EAAE;AAClD,EAAA,IAAI7e,OAAO,GAAG;AACb8e,IAAAA,KAAK,EAAE;AAACC,MAAAA,IAAI,EAAE,EAAE;AAAE7H,MAAAA,IAAI,EAAE;KAAG;IAC3BjH,IAAI,EAAE4O,gBAAgB,CAAC3e,YAAY,CAAC,cAAc,CAAC,KAAK,MAAM,IAAI,KAAK;AACvEoC,IAAAA,SAAS,EAAEuc,gBAAgB,CAAC3e,YAAY,CAAC,mBAAmB,CAAC,IAAI;GACjE;AACD,EAAA,OAAO,IAAI8e,OAAO,CAACH,gBAAgB,EAAE7e,OAAO,CAAC;AAC9C,CAAC,CAAC;;ACRF;AACA;AACA;AACA,IAAIif,kBAAkB,GAAG,EAAE,CAACje,KAAK,CAACC,IAAI,CAACvB,QAAQ,CAACC,gBAAgB,CAAC,4BAA4B,CAAC,CAAC;AAC/Fsf,kBAAkB,CAAC/d,GAAG,CAAC,UAAUge,gBAAgB,EAAE;AAClD,EAAA,IAAIlf,OAAO,GAAG;AACb8e,IAAAA,KAAK,EAAE;AAACC,MAAAA,IAAI,EAAE,EAAE;AAAE7H,MAAAA,IAAI,EAAE;KAAG;IAC3BjH,IAAI,EAAEiP,gBAAgB,CAAChf,YAAY,CAAC,cAAc,CAAC,KAAK,MAAM,IAAI,KAAK;AACvEoC,IAAAA,SAAS,EAAE4c,gBAAgB,CAAChf,YAAY,CAAC,mBAAmB,CAAC,IAAI;GACjE;AACD,EAAA,OAAO,IAAIif,OAAO,CAACD,gBAAgB,EAAElf,OAAO,CAAC;AAC9C,CAAC,CAAC;;ACbF;AACA;AACA;AACA,IAAIof,mBAAmB,GAAG,EAAE,CAACpe,KAAK,CAACC,IAAI,CAACvB,QAAQ,CAACC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC;AACpGyf,mBAAmB,CAACle,GAAG,CAAC,UAAUme,eAAe,EAAE;AAClDA,EAAAA,eAAe,CAACnQ,gBAAgB,CAAC,OAAO,EAAGoQ,CAAC,IAAK;IAChDA,CAAC,CAACC,eAAe,EAAE;AAEnBF,IAAAA,eAAe,CAACG,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;AAC3C,GAAC,CAAC;AACH,CAAC,CAAC;;ACRF;AACA;AACA;AACA,IAAIC,iBAAiB,GAAG,EAAE,CAAC1e,KAAK,CAACC,IAAI,CAACvB,QAAQ,CAACC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC;AAC5F+f,iBAAiB,CAACxe,GAAG,CAAC,UAAUye,cAAc,EAAE;AAC/C,EAAA,IAAI,CAACA,cAAc,CAACC,YAAY,CAAC,gBAAgB,CAAC,EAAE;AACnD,IAAA;AACD;EAEA,MAAMC,OAAO,GAAG,IAAIC,KAAK,CAACH,cAAc,CAACzf,YAAY,CAAC,gBAAgB,CAAC,CAAC;AAExEyf,EAAAA,cAAc,CAACzQ,gBAAgB,CAAC,OAAO,EAAE,MAAM;IAC9C2Q,OAAO,CAACd,IAAI,EAAE;AACf,GAAC,CAAC;AACH,CAAC,CAAC;;AChBK,MAAMgB,MAAM,GAAG,OAAO;AAEtB,MAAMC,SAAS,GAAGA,CAACC,GAAG,EAAEC,OAAO,KAAK;AACzC,EAAA,MAAMlF,MAAM,GAAG,2CAA2C,CAACmF,IAAI,CAACF,GAAG,CAAC;AAEpE,EAAA,OAAOjF,MAAM,GAAG,CAAA,KAAA,EAAQva,QAAQ,CAACua,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA,EAAA,EAAKva,QAAQ,CAACua,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA,EAAA,EAAKva,QAAQ,CAACua,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAA,EAAA,EAAKkF,OAAO,CAAA,CAAA,CAAG,GAAG,IAAI;AAC/H,CAAC;AAEM,MAAME,QAAQ,GAAGA,CAACC,KAAK,EAAEH,OAAO,GAAG,CAAC,KAAK;EAC9C,MAAMI,CAAC,GAAG5X,gBAAgB,CAAChJ,QAAQ,CAAC4Q,IAAI,CAAC,CAACiQ,gBAAgB,CAAC,CAAKR,EAAAA,EAAAA,MAAM,GAAGM,KAAK,CAAA,CAAE,CAAC,CAACG,IAAI,EAAE;EAExF,IAAIN,OAAO,KAAK,CAAC,EAAE;AACnB,IAAA,OAAOF,SAAS,CAACM,CAAC,EAAEJ,OAAO,CAAC;AAC5B;AAEA,EAAA,OAAOI,CAAC;AACV,CAAC;;;;;;;;;;;", "x_google_ignoreList": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58]}