{%  extends 'basic.html' %}


{% block content %}

<div class="page-header d-print-none mb-4">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="page-title m-0">Выпуски</h2>
        </div>
        <div class="col-auto ms-auto">
            <div class="btn-list">
                <a href="{{ url_for('issue_edit', issue_id=0) }}" class="btn btn-primary d-none d-sm-inline-block">
                    <i class="ti ti-plus"></i>
                    Добавить выпуск
                </a>
            </div>
        </div>
    </div>
</div>

<form method="get" action="{{ url_for('issues') }}">
  <div class="row mb-3">
    <div class="col-md-4">
      <div class="form-label">Название</div>
      <input type="text" class="form-control" placeholder="Введите название..." name="title" value="{{ search_title|default('') }}">
    </div>
    <div class="col-md-2">
      <div class="form-label">Том</div>
      <input type="text" class="form-control" placeholder="Том..." name="vol_no" value="{{ search_vol_no|default('') }}">
    </div>
    <div class="col-md-2">
      <div class="form-label">Номер</div>
      <input type="text" class="form-control" placeholder="Номер..." name="issue_no" value="{{ search_issue_no|default('') }}">
    </div>
    <div class="col-md-4">
      <div class="form-label">Статус</div>
      <select class="form-select" name="status">
        <option value="">Все</option>
        <option value="published" {% if search_status == 'published' %}selected{% endif %}>Опубликован</option>
        <option value="draft" {% if search_status == 'draft' %}selected{% endif %}>В работе</option>
      </select>
    </div>
  </div>
  <div class="mt-3 d-flex justify-content-end">
    <button class="btn btn-ghost-danger me-2" type="button" onclick="window.location='{{ url_for('issues') }}';">
      <i class="ti ti-x pe-2"></i> Очистить
    </button>
    <button class="btn btn-primary" type="submit">
      <i class="ti ti-search pe-2"></i> Поиск
    </button>
  </div>
</form>

<div class="card mt-4">
  <div class="table-responsive">
    <table class="table card-table table-hover">
      <thead>
        <tr>
          <th>Номер выпуска</th>
          <th>Название</th>
          <th>Дата публикации</th>
          <th>Статус</th>
          <th>Количество статей</th>
          <th class="w-1"></th>
        </tr>
      </thead>
      <tbody>
        {% for issue in issues %}
        <tr>
          <td class="align-middle">{{ issue.vol_no }}-{{ issue.issue_no }}</td>
          <td class="align-middle">{{ issue.title }}</td>
          <td class="align-middle">{{ issue.created_at | datetimeformat }}</td>
          <td class="align-middle">
            {% if issue.subscription_enable %}
              <span class="badge bg-success-lt">Опубликован</span>
            {% else %}
              <span class="badge bg-warning-lt">В работе</span>
            {% endif %}
          </td>
          <td class="align-middle">{{ issue_article_count[issue.id] }}</td>
          <td class="align-middle">
            <div class="d-flex">
              <a href="{{ url_for('issue_edit', issue_id=issue.id) }}" class="btn btn-icon btn-outline-primary bg-primary-lt btn-sm px-2 py-1 me-2">
                Редактировать
              </a>
              <a href="{{ url_for('articles', issue=issue.id) }}" class="btn btn-icon btn-outline-info bg-info-lt btn-sm px-2 py-1">
                Показать все статьи
              </a>
            </div>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  <div class="card-footer d-flex align-items-center">
    {% set start_idx = (page - 1) * 20 + 1 %}
    {% set end_idx = (page - 1) * 20 + issues|length %}
    <p class="m-0 text-secondary">Показано <span>{{ start_idx }}-{{ end_idx }}</span> из <span>{{ total_issues }}</span> записей</p>
    <ul class="pagination m-0 ms-auto">
      <li class="page-item {% if page <= 1 %}disabled{% endif %}">
        <a class="page-link" href="{{ url_for('issues', page=page-1, title=search_title, vol_no=search_vol_no, issue_no=search_issue_no, status=search_status) }}" tabindex="-1" aria-disabled="true">
          <i class="ti ti-chevron-left"></i>
        </a>
      </li>
      {% for p in range(1, total_pages+1) %}
        <li class="page-item {% if p == page %}active{% endif %}"><a class="page-link" href="{{ url_for('issues', page=p, title=search_title, vol_no=search_vol_no, issue_no=search_issue_no, status=search_status) }}">{{ p }}</a></li>
      {% endfor %}
      <li class="page-item {% if page >= total_pages %}disabled{% endif %}">
        <a class="page-link" href="{{ url_for('issues', page=page+1, title=search_title, vol_no=search_vol_no, issue_no=search_issue_no, status=search_status) }}">
          <i class="ti ti-chevron-right"></i>
        </a>
      </li>
    </ul>
  </div>
</div>


{% endblock %}

{% block scripts %}

{% endblock %}