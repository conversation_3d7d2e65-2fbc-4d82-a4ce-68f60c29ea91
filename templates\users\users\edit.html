{% extends 'basic.html' %}


{% block content %}

<div class="page-header d-print-none mb-4">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="page-title">Редактирование пользователя</h2>
            <a href="{{ url_for('users') }}" class="">
                <i class="ti ti-arrow-left"></i>
                Назад
            </a>
        </div>
        <div class="col-auto ms-auto">
            <div class="btn-list">
                <button id="save-user-btn-top" type="button" class="btn btn-primary d-none d-sm-inline-block">
                    <i class="ti ti-check icon icon-1"></i>
                    Сохранить
                </button>
            </div>
        </div>
    </div>
</div>

<form id="main-user-form" method="post" enctype="multipart/form-data">
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">ID</label>
                    <input type="text" class="form-control" value="{{ user.id }}" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">Имя</label>
                    <input type="text" class="form-control" name="name" value="{{ user.name }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Фамилия</label>
                    <input type="text" class="form-control" name="second_name" value="{{ user.second_name }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Отчество</label>
                    <input type="text" class="form-control" name="father_name" value="{{ user.father_name or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-control" name="email" value="{{ user.email }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Страна</label>
                    <select class="form-select" name="country_id">
                        <option value="">Не выбрано</option>
                        {% for country in countries %}
                        <option value="{{ country.id }}" {% if user.country_id == country.id %}selected{% endif %}>{{ country.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Регион</label>
                    <input type="text" class="form-control" name="region" value="{{ user.region or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Роль</label>
                    <select class="form-select" name="rolename">
                        <option value="user" {% if user.rolename == 'user' %}selected{% endif %}>Пользователь</option>
                        <option value="admin" {% if user.rolename == 'admin' %}selected{% endif %}>Админ</option>
                        <option value="editor" {% if user.rolename == 'editor' %}selected{% endif %}>Редактор</option>
                    </select>
                </div>
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" name="is_blocked" id="is_blocked" {% if user.is_blocked %}checked{% endif %}>
                    <label class="form-check-label" for="is_blocked">Заблокирован</label>
                </div>
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" name="is_notify" id="is_notify" {% if user.is_notify %}checked{% endif %}>
                    <label class="form-check-label" for="is_notify">Уведомления</label>
                </div>
                <div class="mb-3">
                    <label class="form-label">Время принятия правил</label>
                    <input type="datetime-local" class="form-control" value="{{ user.accept_rules_time | date_to_form_full }}" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">Последний онлайн</label>
                    <input type="datetime-local" class="form-control" value="{{ user.last_online | date_to_form_full }}" readonly>
                </div>
                {% if user.id != 0 %}
                <div class="mb-3">
                    <label class="form-label">Дата создания</label>
                    <input type="datetime-local" class="form-control" value="{{ user.created_at | date_to_form_full }}" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">Время регистрации</label>
                    <input type="datetime-local" class="form-control" value="{{ user.register_time | date_to_form_full }}" readonly>
                </div>
                {% endif %}
                <div class="mb-3">
                    <label class="form-label">Тариф</label>
                    <select class="form-select" name="tariff_id">
                        <option value="">Не выбрано</option>
                        {% for tariff in tariffs %}
                        <option value="{{ tariff.id }}" {% if user.tariff_id == tariff.id %}selected{% endif %}>
                            {{ tariff.name }}
                            {% if tariff.is_verified %} 🔒 (для верифицированных){% endif %}
                        </option>
                        {% endfor %}
                    </select>
                    <div class="form-text">🔒 Тарифы для верифицированных пользователей доступны только после загрузки документов</div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Дата окончания подписки</label>
                    <input type="date" class="form-control" name="subscription_end_date" value="{{ user.subscription_end_date | date_to_form }}">
                </div>
                {% if user.id == 0 and password %}
                <div class="alert alert-info">Пароль для нового пользователя: <b>{{ password }}</b></div>
                <input type="hidden" name="password" value="{{ password }}">
                {% endif %}
                <div class="d-flex justify-content-end">
                    <button id="save-user-btn" type="submit" class="btn btn-primary">Сохранить</button>
                </div>
            </div>
        </div>
    </div>
</div>
</form>

{% endblock %}

{% block scripts %}
<script>
document.getElementById('save-user-btn-top').addEventListener('click', function() {
    document.getElementById('save-user-btn').click();
});
</script>
{% endblock %}