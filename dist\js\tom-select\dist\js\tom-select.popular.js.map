{"version": 3, "file": "tom-select.popular.js", "sources": ["../../src/contrib/microevent.ts", "../../src/contrib/microplugin.ts", "../../node_modules/@orchidjs/unicode-variants/dist/esm/regex.js", "../../node_modules/@orchidjs/unicode-variants/dist/esm/strings.js", "../../node_modules/@orchidjs/unicode-variants/dist/esm/index.js", "../../node_modules/@orchidjs/sifter/dist/esm/utils.js", "../../node_modules/@orchidjs/sifter/dist/esm/sifter.js", "../../src/utils.ts", "../../src/vanilla.ts", "../../src/contrib/highlight.ts", "../../src/constants.ts", "../../src/defaults.ts", "../../src/getSettings.ts", "../../src/tom-select.ts", "../../src/plugins/caret_position/plugin.ts", "../../src/plugins/dropdown_input/plugin.ts", "../../src/plugins/no_backspace_delete/plugin.ts", "../../src/plugins/remove_button/plugin.ts", "../../src/plugins/restore_on_backspace/plugin.ts", "../../src/tom-select.popular.ts"], "sourcesContent": ["/**\n * MicroEvent - to make any js object an event emitter\n *\n * - pure javascript - server compatible, browser compatible\n * - dont rely on the browser doms\n * - super simple - you get it immediatly, no mistery, no magic involved\n *\n * <AUTHOR> (https://github.com/jero<PERSON>)\n */\n\ntype TCallback = (...args:any) => any;\n\n/**\n * Execute callback for each event in space separated list of event names\n *\n */\nfunction forEvents(events:string,callback:(event:string)=>any){\n\tevents.split(/\\s+/).forEach((event) =>{\n\t\tcallback(event);\n\t});\n}\n\nexport default class MicroEvent{\n\n\tpublic _events: {[key:string]:TCallback[]};\n\n\tconstructor(){\n\t\tthis._events = {};\n\t}\n\n\ton(events:string, fct:TCallback){\n\t\tforEvents(events,(event) => {\n\t\t\tconst event_array = this._events[event] || [];\n\t\t\tevent_array.push(fct);\n\t\t\tthis._events[event] = event_array;\n\t\t});\n\t}\n\n\toff(events:string, fct:TCallback){\n\t\tvar n = arguments.length;\n\t\tif( n === 0 ){\n\t\t\tthis._events = {};\n\t\t\treturn;\n\t\t}\n\n\t\tforEvents(events,(event) => {\n\n\t\t\tif (n === 1){\n\t\t\t\tdelete this._events[event];\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tconst event_array = this._events[event];\n\t\t\tif( event_array === undefined ) return;\n\n\t\t\tevent_array.splice(event_array.indexOf(fct), 1);\n\t\t\tthis._events[event] = event_array;\n\t\t});\n\t}\n\n\ttrigger(events:string, ...args:any){\n\t\tvar self = this;\n\n\t\tforEvents(events,(event) => {\n\t\t\tconst event_array = self._events[event];\n\t\t\tif( event_array === undefined ) return;\n\t\t\tevent_array.forEach(fct => {\n\t\t\t\tfct.apply(self, args );\n\t\t\t});\n\n\t\t});\n\t}\n};\n", "/**\n * microplugin.js\n * Copyright (c) 2013 <PERSON> & <PERSON>\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n * <AUTHOR> <<EMAIL>>\n */\n\ntype TSettings = {\n\t[key:string]:any\n}\n\ntype TPlugins = {\n\tnames: string[],\n\tsettings: TSettings,\n\trequested: {[key:string]:boolean},\n\tloaded: {[key:string]:any}\n};\n\nexport type TPluginItem = {name:string,options:{}};\nexport type TPluginHash = {[key:string]:{}};\n\n\n\n\nexport default function MicroPlugin(Interface: any ){\n\n\tInterface.plugins = {};\n\n\treturn class extends Interface{\n\n\t\tpublic plugins:TPlugins = {\n\t\t\tnames     : [],\n\t\t\tsettings  : {},\n\t\t\trequested : {},\n\t\t\tloaded    : {}\n\t\t};\n\n\t\t/**\n\t\t * Registers a plugin.\n\t\t *\n\t\t * @param {function} fn\n\t\t */\n\t\tstatic define(name:string, fn:(this:any,settings:TSettings)=>any){\n\t\t\tInterface.plugins[name] = {\n\t\t\t\t'name' : name,\n\t\t\t\t'fn'   : fn\n\t\t\t};\n\t\t}\n\n\n\t\t/**\n\t\t * Initializes the listed plugins (with options).\n\t\t * Acceptable formats:\n\t\t *\n\t\t * List (without options):\n\t\t *   ['a', 'b', 'c']\n\t\t *\n\t\t * List (with options):\n\t\t *   [{'name': 'a', options: {}}, {'name': 'b', options: {}}]\n\t\t *\n\t\t * Hash (with options):\n\t\t *   {'a': { ... }, 'b': { ... }, 'c': { ... }}\n\t\t *\n\t\t * @param {array|object} plugins\n\t\t */\n\t\tinitializePlugins(plugins:string[]|TPluginItem[]|TPluginHash) {\n\t\t\tvar key, name;\n\t\t\tconst self  = this;\n\t\t\tconst queue:string[] = [];\n\n\t\t\tif (Array.isArray(plugins)) {\n\t\t\t\tplugins.forEach((plugin:string|TPluginItem)=>{\n\t\t\t\t\tif (typeof plugin === 'string') {\n\t\t\t\t\t\tqueue.push(plugin);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.plugins.settings[plugin.name] = plugin.options;\n\t\t\t\t\t\tqueue.push(plugin.name);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t} else if (plugins) {\n\t\t\t\tfor (key in plugins) {\n\t\t\t\t\tif (plugins.hasOwnProperty(key)) {\n\t\t\t\t\t\tself.plugins.settings[key] = plugins[key];\n\t\t\t\t\t\tqueue.push(key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\twhile( name = queue.shift() ){\n\t\t\t\tself.require(name);\n\t\t\t}\n\t\t}\n\n\t\tloadPlugin(name:string) {\n\t\t\tvar self    = this;\n\t\t\tvar plugins = self.plugins;\n\t\t\tvar plugin  = Interface.plugins[name];\n\n\t\t\tif (!Interface.plugins.hasOwnProperty(name)) {\n\t\t\t\tthrow new Error('Unable to find \"' +  name + '\" plugin');\n\t\t\t}\n\n\t\t\tplugins.requested[name] = true;\n\t\t\tplugins.loaded[name] = plugin.fn.apply(self, [self.plugins.settings[name] || {}]);\n\t\t\tplugins.names.push(name);\n\t\t}\n\n\t\t/**\n\t\t * Initializes a plugin.\n\t\t *\n\t\t */\n\t\trequire(name:string) {\n\t\t\tvar self = this;\n\t\t\tvar plugins = self.plugins;\n\n\t\t\tif (!self.plugins.loaded.hasOwnProperty(name)) {\n\t\t\t\tif (plugins.requested[name]) {\n\t\t\t\t\tthrow new Error('Plugin has circular dependency (\"' + name + '\")');\n\t\t\t\t}\n\t\t\t\tself.loadPlugin(name);\n\t\t\t}\n\n\t\t\treturn plugins.loaded[name];\n\t\t}\n\n\t};\n\n}\n", "/**\n * Convert array of strings to a regular expression\n *\tex ['ab','a'] => (?:ab|a)\n * \tex ['a','b'] => [ab]\n */\nexport const arrayToPattern = (chars) => {\n    chars = chars.filter(Boolean);\n    if (chars.length < 2) {\n        return chars[0] || '';\n    }\n    return (maxValueLength(chars) == 1) ? '[' + chars.join('') + ']' : '(?:' + chars.join('|') + ')';\n};\nexport const sequencePattern = (array) => {\n    if (!hasDuplicates(array)) {\n        return array.join('');\n    }\n    let pattern = '';\n    let prev_char_count = 0;\n    const prev_pattern = () => {\n        if (prev_char_count > 1) {\n            pattern += '{' + prev_char_count + '}';\n        }\n    };\n    array.forEach((char, i) => {\n        if (char === array[i - 1]) {\n            prev_char_count++;\n            return;\n        }\n        prev_pattern();\n        pattern += char;\n        prev_char_count = 1;\n    });\n    prev_pattern();\n    return pattern;\n};\n/**\n * Convert array of strings to a regular expression\n *\tex ['ab','a'] => (?:ab|a)\n * \tex ['a','b'] => [ab]\n */\nexport const setToPattern = (chars) => {\n    let array = Array.from(chars);\n    return arrayToPattern(array);\n};\n/**\n * https://stackoverflow.com/questions/7376598/in-javascript-how-do-i-check-if-an-array-has-duplicate-values\n */\nexport const hasDuplicates = (array) => {\n    return (new Set(array)).size !== array.length;\n};\n/**\n * https://stackoverflow.com/questions/63006601/why-does-u-throw-an-invalid-escape-error\n */\nexport const escape_regex = (str) => {\n    return (str + '').replace(/([\\$\\(\\)\\*\\+\\.\\?\\[\\]\\^\\{\\|\\}\\\\])/gu, '\\\\$1');\n};\n/**\n * Return the max length of array values\n */\nexport const maxValueLength = (array) => {\n    return array.reduce((longest, value) => Math.max(longest, unicodeLength(value)), 0);\n};\nexport const unicodeLength = (str) => {\n    return Array.from(str).length;\n};\n//# sourceMappingURL=regex.js.map", "/**\n * Get all possible combinations of substrings that add up to the given string\n * https://stackoverflow.com/questions/30169587/find-all-the-combination-of-substrings-that-add-up-to-the-given-string\n */\nexport const allSubstrings = (input) => {\n    if (input.length === 1)\n        return [[input]];\n    let result = [];\n    const start = input.substring(1);\n    const suba = allSubstrings(start);\n    suba.forEach(function (subresult) {\n        let tmp = subresult.slice(0);\n        tmp[0] = input.charAt(0) + tmp[0];\n        result.push(tmp);\n        tmp = subresult.slice(0);\n        tmp.unshift(input.charAt(0));\n        result.push(tmp);\n    });\n    return result;\n};\n//# sourceMappingURL=strings.js.map", "import { setToPattern, arrayToPattern, escape_regex, sequencePattern } from \"./regex.js\";\nimport { allSubstrings } from \"./strings.js\";\nexport const code_points = [[0, 65535]];\nconst accent_pat = '[\\u0300-\\u036F\\u{b7}\\u{2be}\\u{2bc}]';\nexport let unicode_map;\nlet multi_char_reg;\nconst max_char_length = 3;\nconst latin_convert = {};\nconst latin_condensed = {\n    '/': '⁄∕',\n    '0': '߀',\n    \"a\": \"ⱥɐɑ\",\n    \"aa\": \"ꜳ\",\n    \"ae\": \"æǽǣ\",\n    \"ao\": \"ꜵ\",\n    \"au\": \"ꜷ\",\n    \"av\": \"ꜹꜻ\",\n    \"ay\": \"ꜽ\",\n    \"b\": \"ƀɓƃ\",\n    \"c\": \"ꜿƈȼↄ\",\n    \"d\": \"đɗɖᴅƌꮷԁɦ\",\n    \"e\": \"ɛǝᴇɇ\",\n    \"f\": \"ꝼƒ\",\n    \"g\": \"ǥɠꞡᵹꝿɢ\",\n    \"h\": \"ħⱨⱶɥ\",\n    \"i\": \"ɨı\",\n    \"j\": \"ɉȷ\",\n    \"k\": \"ƙⱪꝁꝃꝅꞣ\",\n    \"l\": \"łƚɫⱡꝉꝇꞁɭ\",\n    \"m\": \"ɱɯϻ\",\n    \"n\": \"ꞥƞɲꞑᴎлԉ\",\n    \"o\": \"øǿɔɵꝋꝍᴑ\",\n    \"oe\": \"œ\",\n    \"oi\": \"ƣ\",\n    \"oo\": \"ꝏ\",\n    \"ou\": \"ȣ\",\n    \"p\": \"ƥᵽꝑꝓꝕρ\",\n    \"q\": \"ꝗꝙɋ\",\n    \"r\": \"ɍɽꝛꞧꞃ\",\n    \"s\": \"ßȿꞩꞅʂ\",\n    \"t\": \"ŧƭʈⱦꞇ\",\n    \"th\": \"þ\",\n    \"tz\": \"ꜩ\",\n    \"u\": \"ʉ\",\n    \"v\": \"ʋꝟʌ\",\n    \"vy\": \"ꝡ\",\n    \"w\": \"ⱳ\",\n    \"y\": \"ƴɏỿ\",\n    \"z\": \"ƶȥɀⱬꝣ\",\n    \"hv\": \"ƕ\"\n};\nfor (let latin in latin_condensed) {\n    let unicode = latin_condensed[latin] || '';\n    for (let i = 0; i < unicode.length; i++) {\n        let char = unicode.substring(i, i + 1);\n        latin_convert[char] = latin;\n    }\n}\nconst convert_pat = new RegExp(Object.keys(latin_convert).join('|') + '|' + accent_pat, 'gu');\n/**\n * Initialize the unicode_map from the give code point ranges\n */\nexport const initialize = (_code_points) => {\n    if (unicode_map !== undefined)\n        return;\n    unicode_map = generateMap(_code_points || code_points);\n};\n/**\n * Helper method for normalize a string\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/normalize\n */\nexport const normalize = (str, form = 'NFKD') => str.normalize(form);\n/**\n * Remove accents without reordering string\n * calling str.normalize('NFKD') on \\u{594}\\u{595}\\u{596} becomes \\u{596}\\u{594}\\u{595}\n * via https://github.com/krisk/Fuse/issues/133#issuecomment-318692703\n */\nexport const asciifold = (str) => {\n    return Array.from(str).reduce(\n    /**\n     * @param {string} result\n     * @param {string} char\n     */\n    (result, char) => {\n        return result + _asciifold(char);\n    }, '');\n};\nexport const _asciifold = (str) => {\n    str = normalize(str)\n        .toLowerCase()\n        .replace(convert_pat, (/** @type {string} */ char) => {\n        return latin_convert[char] || '';\n    });\n    //return str;\n    return normalize(str, 'NFC');\n};\n/**\n * Generate a list of unicode variants from the list of code points\n */\nexport function* generator(code_points) {\n    for (const [code_point_min, code_point_max] of code_points) {\n        for (let i = code_point_min; i <= code_point_max; i++) {\n            let composed = String.fromCharCode(i);\n            let folded = asciifold(composed);\n            if (folded == composed.toLowerCase()) {\n                continue;\n            }\n            // skip when folded is a string longer than 3 characters long\n            // bc the resulting regex patterns will be long\n            // eg:\n            // folded صلى الله عليه وسلم length 18 code point 65018\n            // folded جل جلاله length 8 code point 65019\n            if (folded.length > max_char_length) {\n                continue;\n            }\n            if (folded.length == 0) {\n                continue;\n            }\n            yield { folded: folded, composed: composed, code_point: i };\n        }\n    }\n}\n/**\n * Generate a unicode map from the list of code points\n */\nexport const generateSets = (code_points) => {\n    const unicode_sets = {};\n    const addMatching = (folded, to_add) => {\n        /** @type {Set<string>} */\n        const folded_set = unicode_sets[folded] || new Set();\n        const patt = new RegExp('^' + setToPattern(folded_set) + '$', 'iu');\n        if (to_add.match(patt)) {\n            return;\n        }\n        folded_set.add(escape_regex(to_add));\n        unicode_sets[folded] = folded_set;\n    };\n    for (let value of generator(code_points)) {\n        addMatching(value.folded, value.folded);\n        addMatching(value.folded, value.composed);\n    }\n    return unicode_sets;\n};\n/**\n * Generate a unicode map from the list of code points\n * ae => (?:(?:ae|Æ|Ǽ|Ǣ)|(?:A|Ⓐ|Ａ...)(?:E|ɛ|Ⓔ...))\n */\nexport const generateMap = (code_points) => {\n    const unicode_sets = generateSets(code_points);\n    const unicode_map = {};\n    let multi_char = [];\n    for (let folded in unicode_sets) {\n        let set = unicode_sets[folded];\n        if (set) {\n            unicode_map[folded] = setToPattern(set);\n        }\n        if (folded.length > 1) {\n            multi_char.push(escape_regex(folded));\n        }\n    }\n    multi_char.sort((a, b) => b.length - a.length);\n    const multi_char_patt = arrayToPattern(multi_char);\n    multi_char_reg = new RegExp('^' + multi_char_patt, 'u');\n    return unicode_map;\n};\n/**\n * Map each element of an array from its folded value to all possible unicode matches\n */\nexport const mapSequence = (strings, min_replacement = 1) => {\n    let chars_replaced = 0;\n    strings = strings.map((str) => {\n        if (unicode_map[str]) {\n            chars_replaced += str.length;\n        }\n        return unicode_map[str] || str;\n    });\n    if (chars_replaced >= min_replacement) {\n        return sequencePattern(strings);\n    }\n    return '';\n};\n/**\n * Convert a short string and split it into all possible patterns\n * Keep a pattern only if min_replacement is met\n *\n * 'abc'\n * \t\t=> [['abc'],['ab','c'],['a','bc'],['a','b','c']]\n *\t\t=> ['abc-pattern','ab-c-pattern'...]\n */\nexport const substringsToPattern = (str, min_replacement = 1) => {\n    min_replacement = Math.max(min_replacement, str.length - 1);\n    return arrayToPattern(allSubstrings(str).map((sub_pat) => {\n        return mapSequence(sub_pat, min_replacement);\n    }));\n};\n/**\n * Convert an array of sequences into a pattern\n * [{start:0,end:3,length:3,substr:'iii'}...] => (?:iii...)\n */\nconst sequencesToPattern = (sequences, all = true) => {\n    let min_replacement = sequences.length > 1 ? 1 : 0;\n    return arrayToPattern(sequences.map((sequence) => {\n        let seq = [];\n        const len = all ? sequence.length() : sequence.length() - 1;\n        for (let j = 0; j < len; j++) {\n            seq.push(substringsToPattern(sequence.substrs[j] || '', min_replacement));\n        }\n        return sequencePattern(seq);\n    }));\n};\n/**\n * Return true if the sequence is already in the sequences\n */\nconst inSequences = (needle_seq, sequences) => {\n    for (const seq of sequences) {\n        if (seq.start != needle_seq.start || seq.end != needle_seq.end) {\n            continue;\n        }\n        if (seq.substrs.join('') !== needle_seq.substrs.join('')) {\n            continue;\n        }\n        let needle_parts = needle_seq.parts;\n        const filter = (part) => {\n            for (const needle_part of needle_parts) {\n                if (needle_part.start === part.start && needle_part.substr === part.substr) {\n                    return false;\n                }\n                if (part.length == 1 || needle_part.length == 1) {\n                    continue;\n                }\n                // check for overlapping parts\n                // a = ['::=','==']\n                // b = ['::','===']\n                // a = ['r','sm']\n                // b = ['rs','m']\n                if (part.start < needle_part.start && part.end > needle_part.start) {\n                    return true;\n                }\n                if (needle_part.start < part.start && needle_part.end > part.start) {\n                    return true;\n                }\n            }\n            return false;\n        };\n        let filtered = seq.parts.filter(filter);\n        if (filtered.length > 0) {\n            continue;\n        }\n        return true;\n    }\n    return false;\n};\nclass Sequence {\n    parts;\n    substrs;\n    start;\n    end;\n    constructor() {\n        this.parts = [];\n        this.substrs = [];\n        this.start = 0;\n        this.end = 0;\n    }\n    add(part) {\n        if (part) {\n            this.parts.push(part);\n            this.substrs.push(part.substr);\n            this.start = Math.min(part.start, this.start);\n            this.end = Math.max(part.end, this.end);\n        }\n    }\n    last() {\n        return this.parts[this.parts.length - 1];\n    }\n    length() {\n        return this.parts.length;\n    }\n    clone(position, last_piece) {\n        let clone = new Sequence();\n        let parts = JSON.parse(JSON.stringify(this.parts));\n        let last_part = parts.pop();\n        for (const part of parts) {\n            clone.add(part);\n        }\n        let last_substr = last_piece.substr.substring(0, position - last_part.start);\n        let clone_last_len = last_substr.length;\n        clone.add({ start: last_part.start, end: last_part.start + clone_last_len, length: clone_last_len, substr: last_substr });\n        return clone;\n    }\n}\n/**\n * Expand a regular expression pattern to include unicode variants\n * \teg /a/ becomes /aⓐａẚàáâầấẫẩãāăằắẵẳȧǡäǟảåǻǎȁȃạậặḁąⱥɐɑAⒶＡÀÁÂẦẤẪẨÃĀĂẰẮẴẲȦǠÄǞẢÅǺǍȀȂẠẬẶḀĄȺⱯ/\n *\n * Issue:\n *  ﺊﺋ [ 'ﺊ = \\\\u{fe8a}', 'ﺋ = \\\\u{fe8b}' ]\n *\tbecomes:\tئئ [ 'ي = \\\\u{64a}', 'ٔ = \\\\u{654}', 'ي = \\\\u{64a}', 'ٔ = \\\\u{654}' ]\n *\n *\tİĲ = IIJ = ⅡJ\n *\n * \t1/2/4\n */\nexport const getPattern = (str) => {\n    initialize();\n    str = asciifold(str);\n    let pattern = '';\n    let sequences = [new Sequence()];\n    for (let i = 0; i < str.length; i++) {\n        let substr = str.substring(i);\n        let match = substr.match(multi_char_reg);\n        const char = str.substring(i, i + 1);\n        const match_str = match ? match[0] : null;\n        // loop through sequences\n        // add either the char or multi_match\n        let overlapping = [];\n        let added_types = new Set();\n        for (const sequence of sequences) {\n            const last_piece = sequence.last();\n            if (!last_piece || last_piece.length == 1 || last_piece.end <= i) {\n                // if we have a multi match\n                if (match_str) {\n                    const len = match_str.length;\n                    sequence.add({ start: i, end: i + len, length: len, substr: match_str });\n                    added_types.add('1');\n                }\n                else {\n                    sequence.add({ start: i, end: i + 1, length: 1, substr: char });\n                    added_types.add('2');\n                }\n            }\n            else if (match_str) {\n                let clone = sequence.clone(i, last_piece);\n                const len = match_str.length;\n                clone.add({ start: i, end: i + len, length: len, substr: match_str });\n                overlapping.push(clone);\n            }\n            else {\n                // don't add char\n                // adding would create invalid patterns: 234 => [2,34,4]\n                added_types.add('3');\n            }\n        }\n        // if we have overlapping\n        if (overlapping.length > 0) {\n            // ['ii','iii'] before ['i','i','iii']\n            overlapping = overlapping.sort((a, b) => {\n                return a.length() - b.length();\n            });\n            for (let clone of overlapping) {\n                // don't add if we already have an equivalent sequence\n                if (inSequences(clone, sequences)) {\n                    continue;\n                }\n                sequences.push(clone);\n            }\n            continue;\n        }\n        // if we haven't done anything unique\n        // clean up the patterns\n        // helps keep patterns smaller\n        // if str = 'r₨㎧aarss', pattern will be 446 instead of 655\n        if (i > 0 && added_types.size == 1 && !added_types.has('3')) {\n            pattern += sequencesToPattern(sequences, false);\n            let new_seq = new Sequence();\n            const old_seq = sequences[0];\n            if (old_seq) {\n                new_seq.add(old_seq.last());\n            }\n            sequences = [new_seq];\n        }\n    }\n    pattern += sequencesToPattern(sequences, true);\n    return pattern;\n};\nexport { escape_regex };\n//# sourceMappingURL=index.js.map", "import { asciifold } from '@orchidjs/unicode-variants';\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\nexport const getAttr = (obj, name) => {\n    if (!obj)\n        return;\n    return obj[name];\n};\n/**\n * A property getter resolving dot-notation\n * @param  {Object}  obj     The root object to fetch property on\n * @param  {String}  name    The optionally dotted property name to fetch\n * @return {Object}          The resolved property value\n */\nexport const getAttrNesting = (obj, name) => {\n    if (!obj)\n        return;\n    var part, names = name.split(\".\");\n    while ((part = names.shift()) && (obj = obj[part]))\n        ;\n    return obj;\n};\n/**\n * Calculates how close of a match the\n * given value is against a search token.\n *\n */\nexport const scoreValue = (value, token, weight) => {\n    var score, pos;\n    if (!value)\n        return 0;\n    value = value + '';\n    if (token.regex == null)\n        return 0;\n    pos = value.search(token.regex);\n    if (pos === -1)\n        return 0;\n    score = token.string.length / value.length;\n    if (pos === 0)\n        score += 0.5;\n    return score * weight;\n};\n/**\n * Cast object property to an array if it exists and has a value\n *\n */\nexport const propToArray = (obj, key) => {\n    var value = obj[key];\n    if (typeof value == 'function')\n        return value;\n    if (value && !Array.isArray(value)) {\n        obj[key] = [value];\n    }\n};\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\nexport const iterate = (object, callback) => {\n    if (Array.isArray(object)) {\n        object.forEach(callback);\n    }\n    else {\n        for (var key in object) {\n            if (object.hasOwnProperty(key)) {\n                callback(object[key], key);\n            }\n        }\n    }\n};\nexport const cmp = (a, b) => {\n    if (typeof a === 'number' && typeof b === 'number') {\n        return a > b ? 1 : (a < b ? -1 : 0);\n    }\n    a = asciifold(a + '').toLowerCase();\n    b = asciifold(b + '').toLowerCase();\n    if (a > b)\n        return 1;\n    if (b > a)\n        return -1;\n    return 0;\n};\n//# sourceMappingURL=utils.js.map", "/**\n * sifter.js\n * Copyright (c) 2013–2020 <PERSON> & contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n * <AUTHOR> <<EMAIL>>\n */\nimport { scoreValue, getAttr, getAttrNesting, propToArray, iterate, cmp } from \"./utils.js\";\nimport { getPattern, escape_regex } from '@orchidjs/unicode-variants';\nclass Sifter {\n    items; // []|{};\n    settings;\n    /**\n     * Textually searches arrays and hashes of objects\n     * by property (or multiple properties). Designed\n     * specifically for autocomplete.\n     *\n     */\n    constructor(items, settings) {\n        this.items = items;\n        this.settings = settings || { diacritics: true };\n    }\n    ;\n    /**\n     * Splits a search string into an array of individual\n     * regexps to be used to match results.\n     *\n     */\n    tokenize(query, respect_word_boundaries, weights) {\n        if (!query || !query.length)\n            return [];\n        const tokens = [];\n        const words = query.split(/\\s+/);\n        var field_regex;\n        if (weights) {\n            field_regex = new RegExp('^(' + Object.keys(weights).map(escape_regex).join('|') + ')\\:(.*)$');\n        }\n        words.forEach((word) => {\n            let field_match;\n            let field = null;\n            let regex = null;\n            // look for \"field:query\" tokens\n            if (field_regex && (field_match = word.match(field_regex))) {\n                field = field_match[1];\n                word = field_match[2];\n            }\n            if (word.length > 0) {\n                if (this.settings.diacritics) {\n                    regex = getPattern(word) || null;\n                }\n                else {\n                    regex = escape_regex(word);\n                }\n                if (regex && respect_word_boundaries)\n                    regex = \"\\\\b\" + regex;\n            }\n            tokens.push({\n                string: word,\n                regex: regex ? new RegExp(regex, 'iu') : null,\n                field: field,\n            });\n        });\n        return tokens;\n    }\n    ;\n    /**\n     * Returns a function to be used to score individual results.\n     *\n     * Good matches will have a higher score than poor matches.\n     * If an item is not a match, 0 will be returned by the function.\n     *\n     * @returns {T.ScoreFn}\n     */\n    getScoreFunction(query, options) {\n        var search = this.prepareSearch(query, options);\n        return this._getScoreFunction(search);\n    }\n    /**\n     * @returns {T.ScoreFn}\n     *\n     */\n    _getScoreFunction(search) {\n        const tokens = search.tokens, token_count = tokens.length;\n        if (!token_count) {\n            return function () { return 0; };\n        }\n        const fields = search.options.fields, weights = search.weights, field_count = fields.length, getAttrFn = search.getAttrFn;\n        if (!field_count) {\n            return function () { return 1; };\n        }\n        /**\n         * Calculates the score of an object\n         * against the search query.\n         *\n         */\n        const scoreObject = (function () {\n            if (field_count === 1) {\n                return function (token, data) {\n                    const field = fields[0].field;\n                    return scoreValue(getAttrFn(data, field), token, weights[field] || 1);\n                };\n            }\n            return function (token, data) {\n                var sum = 0;\n                // is the token specific to a field?\n                if (token.field) {\n                    const value = getAttrFn(data, token.field);\n                    if (!token.regex && value) {\n                        sum += (1 / field_count);\n                    }\n                    else {\n                        sum += scoreValue(value, token, 1);\n                    }\n                }\n                else {\n                    iterate(weights, (weight, field) => {\n                        sum += scoreValue(getAttrFn(data, field), token, weight);\n                    });\n                }\n                return sum / field_count;\n            };\n        })();\n        if (token_count === 1) {\n            return function (data) {\n                return scoreObject(tokens[0], data);\n            };\n        }\n        if (search.options.conjunction === 'and') {\n            return function (data) {\n                var score, sum = 0;\n                for (let token of tokens) {\n                    score = scoreObject(token, data);\n                    if (score <= 0)\n                        return 0;\n                    sum += score;\n                }\n                return sum / token_count;\n            };\n        }\n        else {\n            return function (data) {\n                var sum = 0;\n                iterate(tokens, (token) => {\n                    sum += scoreObject(token, data);\n                });\n                return sum / token_count;\n            };\n        }\n    }\n    ;\n    /**\n     * Returns a function that can be used to compare two\n     * results, for sorting purposes. If no sorting should\n     * be performed, `null` will be returned.\n     *\n     * @return function(a,b)\n     */\n    getSortFunction(query, options) {\n        var search = this.prepareSearch(query, options);\n        return this._getSortFunction(search);\n    }\n    _getSortFunction(search) {\n        var implicit_score, sort_flds = [];\n        const self = this, options = search.options, sort = (!search.query && options.sort_empty) ? options.sort_empty : options.sort;\n        if (typeof sort == 'function') {\n            return sort.bind(this);\n        }\n        /**\n         * Fetches the specified sort field value\n         * from a search result item.\n         *\n         */\n        const get_field = function (name, result) {\n            if (name === '$score')\n                return result.score;\n            return search.getAttrFn(self.items[result.id], name);\n        };\n        // parse options\n        if (sort) {\n            for (let s of sort) {\n                if (search.query || s.field !== '$score') {\n                    sort_flds.push(s);\n                }\n            }\n        }\n        // the \"$score\" field is implied to be the primary\n        // sort field, unless it's manually specified\n        if (search.query) {\n            implicit_score = true;\n            for (let fld of sort_flds) {\n                if (fld.field === '$score') {\n                    implicit_score = false;\n                    break;\n                }\n            }\n            if (implicit_score) {\n                sort_flds.unshift({ field: '$score', direction: 'desc' });\n            }\n            // without a search.query, all items will have the same score\n        }\n        else {\n            sort_flds = sort_flds.filter((fld) => fld.field !== '$score');\n        }\n        // build function\n        const sort_flds_count = sort_flds.length;\n        if (!sort_flds_count) {\n            return null;\n        }\n        return function (a, b) {\n            var result, field;\n            for (let sort_fld of sort_flds) {\n                field = sort_fld.field;\n                let multiplier = sort_fld.direction === 'desc' ? -1 : 1;\n                result = multiplier * cmp(get_field(field, a), get_field(field, b));\n                if (result)\n                    return result;\n            }\n            return 0;\n        };\n    }\n    ;\n    /**\n     * Parses a search query and returns an object\n     * with tokens and fields ready to be populated\n     * with results.\n     *\n     */\n    prepareSearch(query, optsUser) {\n        const weights = {};\n        var options = Object.assign({}, optsUser);\n        propToArray(options, 'sort');\n        propToArray(options, 'sort_empty');\n        // convert fields to new format\n        if (options.fields) {\n            propToArray(options, 'fields');\n            const fields = [];\n            options.fields.forEach((field) => {\n                if (typeof field == 'string') {\n                    field = { field: field, weight: 1 };\n                }\n                fields.push(field);\n                weights[field.field] = ('weight' in field) ? field.weight : 1;\n            });\n            options.fields = fields;\n        }\n        return {\n            options: options,\n            query: query.toLowerCase().trim(),\n            tokens: this.tokenize(query, options.respect_word_boundaries, weights),\n            total: 0,\n            items: [],\n            weights: weights,\n            getAttrFn: (options.nesting) ? getAttrNesting : getAttr,\n        };\n    }\n    ;\n    /**\n     * Searches through all items and returns a sorted array of matches.\n     *\n     */\n    search(query, options) {\n        var self = this, score, search;\n        search = this.prepareSearch(query, options);\n        options = search.options;\n        query = search.query;\n        // generate result scoring function\n        const fn_score = options.score || self._getScoreFunction(search);\n        // perform search and sort\n        if (query.length) {\n            iterate(self.items, (item, id) => {\n                score = fn_score(item);\n                if (options.filter === false || score > 0) {\n                    search.items.push({ 'score': score, 'id': id });\n                }\n            });\n        }\n        else {\n            iterate(self.items, (_, id) => {\n                search.items.push({ 'score': 1, 'id': id });\n            });\n        }\n        const fn_sort = self._getSortFunction(search);\n        if (fn_sort)\n            search.items.sort(fn_sort);\n        // apply limits\n        search.total = search.items.length;\n        if (typeof options.limit === 'number') {\n            search.items = search.items.slice(0, options.limit);\n        }\n        return search;\n    }\n    ;\n}\nexport { Sifter, scoreValue, getAttr, getAttrNesting, propToArray, iterate, cmp, getPattern };\nexport * from \"./types.js\";\n//# sourceMappingURL=sifter.js.map", "\nimport type TomSelect from './tom-select.ts';\nimport { TomLoadCallback } from './types/index.ts';\n\n\n/**\n * Converts a scalar to its best string representation\n * for hash keys and HTML attribute values.\n *\n * Transformations:\n *   'str'     -> 'str'\n *   null      -> ''\n *   undefined -> ''\n *   true      -> '1'\n *   false     -> '0'\n *   0         -> '0'\n *   1         -> '1'\n *\n */\nexport const hash_key = (value:undefined|null|boolean|string|number):string|null => {\n\tif (typeof value === 'undefined' || value === null) return null;\n\treturn get_hash(value);\n};\n\nexport const get_hash = (value:boolean|string|number):string => {\n\tif (typeof value === 'boolean') return value ? '1' : '0';\n\treturn value + '';\n};\n\n/**\n * Escapes a string for use within HTML.\n *\n */\nexport const escape_html = (str:string):string => {\n\treturn (str + '')\n\t\t.replace(/&/g, '&amp;')\n\t\t.replace(/</g, '&lt;')\n\t\t.replace(/>/g, '&gt;')\n\t\t.replace(/\"/g, '&quot;');\n};\n\n\n/**\n * use setTimeout if timeout > 0 \n */\nexport const timeout = (fn:()=>void,timeout:number): number | null => {\n\tif( timeout > 0 ){\n\t\treturn window.setTimeout(fn,timeout);\n\t}\n\n\tfn.call(null);\n\treturn null;\n}\n\n/**\n * Debounce the user provided load function\n *\n */\nexport const loadDebounce = (fn:(value:string,callback:TomLoadCallback) => void,delay:number) => {\n\tvar timeout: null|ReturnType<typeof setTimeout>;\n\treturn function(this:TomSelect, value:string,callback:TomLoadCallback) {\n\t\tvar self = this;\n\n\t\tif( timeout ){\n\t\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\t\tclearTimeout(timeout);\n\t\t}\n\t\ttimeout = setTimeout(function() {\n\t\t\ttimeout = null;\n\t\t\tself.loadedSearches[value] = true;\n\t\t\tfn.call(self, value, callback);\n\n\t\t}, delay);\n\t};\n};\n\n\n/**\n * Debounce all fired events types listed in `types`\n * while executing the provided `fn`.\n *\n */\nexport const debounce_events = ( self:TomSelect, types:string[], fn:() => void ) => {\n\tvar type:string;\n\tvar trigger = self.trigger;\n\tvar event_args:{ [key: string]: any } = {};\n\n\t// override trigger method\n\tself.trigger = function(){\n\t\tvar type = arguments[0];\n\t\tif (types.indexOf(type) !== -1) {\n\t\t\tevent_args[type] = arguments;\n\t\t} else {\n\t\t\treturn trigger.apply(self, arguments);\n\t\t}\n\t};\n\n\t// invoke provided function\n\tfn.apply(self, []);\n\tself.trigger = trigger;\n\n\t// trigger queued events\n\tfor( type of types ){\n\t\tif( type in event_args ){\n\t\t\ttrigger.apply(self, event_args[type]);\n\t\t}\n\t}\n};\n\n\n/**\n * Determines the current selection within a text input control.\n * Returns an object containing:\n *   - start\n *   - length\n *\n * Note: \"selectionStart, selectionEnd ... apply only to inputs of types text, search, URL, tel and password\"\n * \t- https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange\n */\nexport const getSelection = (input:HTMLInputElement):{ start: number; length: number } => {\n\treturn {\n\t\tstart\t: input.selectionStart || 0,\n\t\tlength\t: (input.selectionEnd||0) - (input.selectionStart||0),\n\t};\n};\n\n\n/**\n * Prevent default\n *\n */\nexport const preventDefault = (evt?:Event, stop:boolean=false):void => {\n\tif( evt ){\n\t\tevt.preventDefault();\n\t\tif( stop ){\n\t\t\tevt.stopPropagation();\n\t\t}\n\t}\n}\n\n\n/**\n * Add event helper\n *\n */\nexport const addEvent = (target:EventTarget, type:string, callback:EventListenerOrEventListenerObject, options?:object):void => {\n\ttarget.addEventListener(type,callback,options);\n};\n\n\n/**\n * Return true if the requested key is down\n * Will return false if more than one control character is pressed ( when [ctrl+shift+a] != [ctrl+a] )\n * The current evt may not always set ( eg calling advanceSelection() )\n *\n */\nexport const isKeyDown = ( key_name:keyof (KeyboardEvent|MouseEvent), evt?:KeyboardEvent|MouseEvent ) => {\n\n\tif( !evt ){\n\t\treturn false;\n\t}\n\n\tif( !evt[key_name] ){\n\t\treturn false;\n\t}\n\n\tvar count = (evt.altKey?1:0) + (evt.ctrlKey?1:0) + (evt.shiftKey?1:0) + (evt.metaKey?1:0);\n\n\tif( count === 1 ){\n\t\treturn true;\n\t}\n\n\treturn false;\n};\n\n\n/**\n * Get the id of an element\n * If the id attribute is not set, set the attribute with the given id\n *\n */\nexport const getId = (el:Element,id:string) => {\n\tconst existing_id = el.getAttribute('id');\n\tif( existing_id ){\n\t\treturn existing_id;\n\t}\n\n\tel.setAttribute('id',id);\n\treturn id;\n};\n\n\n/**\n * Returns a string with backslashes added before characters that need to be escaped.\n */\nexport const addSlashes = (str:string):string => {\n\treturn str.replace(/[\\\\\"']/g, '\\\\$&');\n};\n\n/**\n *\n */\nexport const append = ( parent:Element|DocumentFragment, node: string|Node|null|undefined ):void =>{\n\tif( node ) parent.append(node);\n};\n\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\nexport const iterate = (object:[]|{[key:string]:any}, callback:(value:any,key:any)=>any) => {\n\n\tif ( Array.isArray(object)) {\n\t\tobject.forEach(callback);\n\n\t}else{\n\n\t\tfor (var key in object) {\n\t\t\tif (object.hasOwnProperty(key)) {\n\t\t\t\tcallback(object[key], key);\n\t\t\t}\n\t\t}\n\t}\n};\n", "\nimport { iterate } from './utils.ts';\n\n/**\n * Return a dom element from either a dom query string, jQuery object, a dom element or html string\n * https://stackoverflow.com/questions/494143/creating-a-new-dom-element-from-an-html-string-using-built-in-dom-methods-or-pro/35385518#35385518\n *\n * param query should be {}\n */\nexport const getDom = ( query:any ):HTMLElement => {\n\n\tif( query.jquery ){\n\t\treturn query[0];\n\t}\n\n\tif( query instanceof HTMLElement ){\n\t\treturn query;\n\t}\n\n\tif( isHtmlString(query) ){\n\t\tvar tpl = document.createElement('template');\n\t\ttpl.innerHTML = query.trim(); // Never return a text node of whitespace as the result\n\t\treturn tpl.content.firstChild as HTMLElement;\n\t}\n\n\treturn document.querySelector(query);\n};\n\nexport const isHtmlString = (arg:any): boolean => {\n\tif( typeof arg === 'string' && arg.indexOf('<') > -1 ){\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nexport const escapeQuery = (query:string):string => {\n\treturn query.replace(/['\"\\\\]/g, '\\\\$&');\n}\n\n/**\n * Dispatch an event\n *\n */\nexport const triggerEvent = ( dom_el:HTMLElement, event_name:string ):void => {\n\tvar event = document.createEvent('HTMLEvents');\n\tevent.initEvent(event_name, true, false);\n\tdom_el.dispatchEvent(event)\n};\n\n/**\n * Apply CSS rules to a dom element\n *\n */\nexport const applyCSS = ( dom_el:HTMLElement, css:{ [key: string]: string|number }):void => {\n\tObject.assign(dom_el.style, css);\n}\n\n\n/**\n * Add css classes\n *\n */\nexport const addClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n\tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map( cls => {\n\t\t\tel.classList.add( cls );\n\t\t});\n\t});\n}\n\n/**\n * Remove css classes\n *\n */\n export const removeClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n \tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map(cls => {\n\t \t\tel.classList.remove( cls );\n\t\t});\n \t});\n }\n\n\n/**\n * Return arguments\n *\n */\nexport const classesArray = (args:string[]|string[][]):string[] => {\n\tvar classes:string[] = [];\n\titerate( args, (_classes) =>{\n\t\tif( typeof _classes === 'string' ){\n\t\t\t_classes = _classes.trim().split(/[\\t\\n\\f\\r\\s]/);\n\t\t}\n\t\tif( Array.isArray(_classes) ){\n\t\t\tclasses = classes.concat(_classes);\n\t\t}\n\t});\n\n\treturn classes.filter(Boolean);\n}\n\n\n/**\n * Create an array from arg if it's not already an array\n *\n */\nexport const castAsArray = (arg:any):Array<any> => {\n\tif( !Array.isArray(arg) ){\n \t\targ = [arg];\n \t}\n\treturn arg;\n}\n\n\n/**\n * Get the closest node to the evt.target matching the selector\n * Stops at wrapper\n *\n */\nexport const parentMatch = ( target:null|HTMLElement, selector:string, wrapper?:HTMLElement ):HTMLElement|void => {\n\n\tif( wrapper && !wrapper.contains(target) ){\n\t\treturn;\n\t}\n\n\twhile( target && target.matches ){\n\n\t\tif( target.matches(selector) ){\n\t\t\treturn target;\n\t\t}\n\n\t\ttarget = target.parentNode as HTMLElement;\n\t}\n}\n\n\n/**\n * Get the first or last item from an array\n *\n * > 0 - right (last)\n * <= 0 - left (first)\n *\n */\nexport const getTail = ( list:Array<any>|NodeList, direction:number=0 ):any => {\n\n\tif( direction > 0 ){\n\t\treturn list[list.length-1];\n\t}\n\n\treturn list[0];\n}\n\n/**\n * Return true if an object is empty\n *\n */\nexport const isEmptyObject = (obj:object):boolean => {\n\treturn (Object.keys(obj).length === 0);\n}\n\n\n/**\n * Get the index of an element amongst sibling nodes of the same type\n *\n */\nexport const nodeIndex = ( el:null|Element, amongst?:string ):number => {\n\tif (!el) return -1;\n\n\tamongst = amongst || el.nodeName;\n\n\tvar i = 0;\n\twhile( el = el.previousElementSibling ){\n\n\t\tif( el.matches(amongst) ){\n\t\t\ti++;\n\t\t}\n\t}\n\treturn i;\n}\n\n\n/**\n * Set attributes of an element\n *\n */\nexport const setAttr = (el:Element,attrs:{ [key: string]: null|string|number }) => {\n\titerate( attrs,(val,attr) => {\n\t\tif( val == null ){\n\t\t\tel.removeAttribute(attr as string);\n\t\t}else{\n\t\t\tel.setAttribute(attr as string, ''+val);\n\t\t}\n\t});\n}\n\n\n/**\n * Replace a node\n */\nexport const replaceNode = ( existing:Node, replacement:Node ) => {\n\tif( existing.parentNode ) existing.parentNode.replaceChild(replacement, existing);\n}\n", "/**\n * highlight v3 | MIT license | <PERSON> <<EMAIL>>\n * Highlights arbitrary terms in a node.\n *\n * - Modified by <PERSON> <<EMAIL>> 2011-6-24 (added regex)\n * - Modified by <PERSON> <<EMAIL>> 2012-8-27 (cleanup)\n */\n\nimport {replaceNode} from '../vanilla.ts';\n\n\nexport const highlight = (element:HTMLElement, regex:string|RegExp) => {\n\n\tif( regex === null ) return;\n\n\t// convet string to regex\n\tif( typeof regex === 'string' ){\n\n\t\tif( !regex.length ) return;\n\t\tregex = new RegExp(regex, 'i');\n\t}\n\n\n\t// Wrap matching part of text node with highlighting <span>, e.g.\n\t// Soccer  ->  <span class=\"highlight\">Soc</span>cer  for regex = /soc/i\n\tconst highlightText = ( node:Text ):number => {\n\n\t\tvar match = node.data.match(regex);\n\t\tif( match && node.data.length > 0 ){\n\t\t\tvar spannode\t\t= document.createElement('span');\n\t\t\tspannode.className\t= 'highlight';\n\t\t\tvar middlebit\t\t= node.splitText(match.index as number);\n\n\t\t\tmiddlebit.splitText(match[0]!.length);\n\t\t\tvar middleclone\t\t= middlebit.cloneNode(true);\n\n\t\t\tspannode.appendChild(middleclone);\n\t\t\treplaceNode(middlebit, spannode);\n\t\t\treturn 1;\n\t\t}\n\n\t\treturn 0;\n\t};\n\n\t// Recurse element node, looking for child text nodes to highlight, unless element\n\t// is childless, <script>, <style>, or already highlighted: <span class=\"hightlight\">\n\tconst highlightChildren = ( node:Element ):void => {\n\t\tif( node.nodeType === 1 && node.childNodes && !/(script|style)/i.test(node.tagName) && ( node.className !== 'highlight' || node.tagName !== 'SPAN' ) ){\n\t\t\tArray.from(node.childNodes).forEach(element => {\n\t\t\t\thighlightRecursive(element);\n\t\t\t});\n\t\t}\n\t};\n\n\n\tconst highlightRecursive = ( node:Node|Element ):number => {\n\n\t\tif( node.nodeType === 3 ){\n\t\t\treturn highlightText(node as Text);\n\t\t}\n\n\t\thighlightChildren(node as Element);\n\n\t\treturn 0;\n\t};\n\n\thighlightRecursive( element );\n};\n\n/**\n * removeHighlight fn copied from highlight v5 and\n * edited to remove with(), pass js strict mode, and use without jquery\n */\nexport const removeHighlight = (el:HTMLElement) => {\n\tvar elements = el.querySelectorAll(\"span.highlight\");\n\tArray.prototype.forEach.call(elements, function(el:HTMLElement){\n\t\tvar parent = el.parentNode as Node;\n\t\tparent.replaceChild(el.firstChild as Node, el);\n\t\tparent.normalize();\n\t});\n};\n", "export const KEY_A\t\t\t\t= 65;\nexport const KEY_RETURN\t\t\t= 13;\nexport const KEY_ESC\t\t\t= 27;\nexport const KEY_LEFT\t\t\t= 37;\nexport const KEY_UP\t\t\t\t= 38;\nexport const KEY_RIGHT\t\t\t= 39;\nexport const KEY_DOWN\t\t\t= 40;\nexport const KEY_BACKSPACE\t\t= 8;\nexport const KEY_DELETE\t\t\t= 46;\nexport const KEY_TAB\t\t\t= 9;\n\nexport const IS_MAC      \t\t= typeof navigator === 'undefined' ? false : /Mac/.test(navigator.userAgent);\nexport const KEY_SHORTCUT\t\t= IS_MAC ? 'metaKey' : 'ctrlKey'; // ctrl key or apple key for ma\n", "\nexport default {\n\toptions: [],\n\toptgroups: [],\n\n\tplugins: [],\n\tdelimiter: ',',\n\tsplitOn: null, // regexp or string for splitting up values from a paste command\n\tpersist: true,\n\tdiacritics: true,\n\tcreate: null,\n\tcreateOnBlur: false,\n\tcreateFilter: null,\n\thighlight: true,\n\topenOnFocus: true,\n\tshouldOpen: null,\n\tmaxOptions: 50,\n\tmaxItems: null,\n\thideSelected: null,\n\tduplicates: false,\n\taddPrecedence: false,\n\tselectOnTab: false,\n\tpreload: null,\n\tallowEmptyOption: false,\n\t//closeAfterSelect: false,\n\trefreshThrottle: 300,\n\n\n\tloadThrottle: 300,\n\tloadingClass: 'loading',\n\n\tdataAttr: null, //'data-data',\n\toptgroupField: 'optgroup',\n\tvalueField: 'value',\n\tlabelField: 'text',\n\tdisabledField: 'disabled',\n\toptgroupLabelField: 'label',\n\toptgroupValueField: 'value',\n\tlockOptgroupOrder: false,\n\n\tsortField: '$order',\n\tsearchField: ['text'],\n\tsearchConjunction: 'and',\n\n\tmode: null,\n\twrapperClass: 'ts-wrapper',\n\tcontrolClass: 'ts-control',\n\tdropdownClass: 'ts-dropdown',\n\tdropdownContentClass: 'ts-dropdown-content',\n\titemClass: 'item',\n\toptionClass: 'option',\n\n\tdropdownParent: null,\n\tcontrolInput: '<input type=\"text\" autocomplete=\"off\" size=\"1\" />',\n\n\tcopyClassesToDropdown: false,\n\n\tplaceholder: null,\n\thidePlaceholder: null,\n\n\tshouldLoad: function(query:string):boolean{\n\t\treturn query.length > 0;\n\t},\n\n\t/*\n\tload                 : null, // function(query, callback) { ... }\n\tscore                : null, // function(search) { ... }\n\tonInitialize         : null, // function() { ... }\n\tonChange             : null, // function(value) { ... }\n\tonItemAdd            : null, // function(value, $item) { ... }\n\tonItemRemove         : null, // function(value) { ... }\n\tonClear              : null, // function() { ... }\n\tonOptionAdd          : null, // function(value, data) { ... }\n\tonOptionRemove       : null, // function(value) { ... }\n\tonOptionClear        : null, // function() { ... }\n\tonOptionGroupAdd     : null, // function(id, data) { ... }\n\tonOptionGroupRemove  : null, // function(id) { ... }\n\tonOptionGroupClear   : null, // function() { ... }\n\tonDropdownOpen       : null, // function(dropdown) { ... }\n\tonDropdownClose      : null, // function(dropdown) { ... }\n\tonType               : null, // function(str) { ... }\n\tonDelete             : null, // function(values) { ... }\n\t*/\n\n\trender: {\n\t\t/*\n\t\titem: null,\n\t\toptgroup: null,\n\t\toptgroup_header: null,\n\t\toption: null,\n\t\toption_create: null\n\t\t*/\n\t}\n};\n", "import defaults from './defaults.ts';\nimport { hash_key, iterate } from './utils.ts';\nimport { TomOption, TomSettings, RecursivePartial } from './types/index.ts';\nimport { TomInput } from './types/index.ts';\n\n\nexport default function getSettings( input:TomInput, settings_user:RecursivePartial<TomSettings>):TomSettings{\n\tvar settings:TomSettings\t= Object.assign({}, defaults, settings_user);\n\n\tvar attr_data\t\t\t\t= settings.dataAttr;\n\tvar field_label\t\t\t\t= settings.labelField;\n\tvar field_value\t\t\t\t= settings.valueField;\n\tvar field_disabled\t\t\t= settings.disabledField;\n\tvar field_optgroup\t\t\t= settings.optgroupField;\n\tvar field_optgroup_label\t= settings.optgroupLabelField;\n\tvar field_optgroup_value\t= settings.optgroupValueField;\n\n\tvar tag_name\t\t\t\t= input.tagName.toLowerCase();\n\tvar placeholder\t\t\t\t= input.getAttribute('placeholder') || input.getAttribute('data-placeholder');\n\n\tif (!placeholder && !settings.allowEmptyOption) {\n\t\tlet option\t\t= input.querySelector('option[value=\"\"]');\n\t\tif( option ){\n\t\t\tplaceholder = option.textContent;\n\t\t}\n\n\t}\n\n\tvar settings_element:{\n\t\tplaceholder\t: null|string,\n\t\toptions\t\t: TomOption[],\n\t\toptgroups\t: TomOption[],\n\t\titems\t\t: string[],\n\t\tmaxItems\t: null|number,\n\t} = {\n\t\tplaceholder\t: placeholder,\n\t\toptions\t\t: [],\n\t\toptgroups\t: [],\n\t\titems\t\t: [],\n\t\tmaxItems\t: null,\n\t};\n\n\n\t/**\n\t * Initialize from a <select> element.\n\t *\n\t */\n\tvar init_select = () => {\n\t\tvar tagName;\n\t\tvar options = settings_element.options;\n\t\tvar optionsMap:{[key:string]:any} = {};\n\t\tvar group_count = 1;\n\t\tlet $order = 0;\n\n\t\tvar readData = (el:HTMLElement):TomOption => {\n\n\t\t\tvar data\t= Object.assign({},el.dataset); // get plain object from DOMStringMap\n\t\t\tvar json\t= attr_data && data[attr_data];\n\n\t\t\tif( typeof json === 'string' && json.length ){\n\t\t\t\tdata = Object.assign(data,JSON.parse(json));\n\t\t\t}\n\n\t\t\treturn data;\n\t\t};\n\n\t\tvar addOption = (option:HTMLOptionElement, group?:string) => {\n\n\t\t\tvar value = hash_key(option.value);\n\t\t\tif ( value == null ) return;\n\t\t\tif ( !value && !settings.allowEmptyOption) return;\n\n\t\t\t// if the option already exists, it's probably been\n\t\t\t// duplicated in another optgroup. in this case, push\n\t\t\t// the current group to the \"optgroup\" property on the\n\t\t\t// existing option so that it's rendered in both places.\n\t\t\tif (optionsMap.hasOwnProperty(value)) {\n\t\t\t\tif (group) {\n\t\t\t\t\tvar arr = optionsMap[value][field_optgroup];\n\t\t\t\t\tif (!arr) {\n\t\t\t\t\t\toptionsMap[value][field_optgroup] = group;\n\t\t\t\t\t} else if (!Array.isArray(arr)) {\n\t\t\t\t\t\toptionsMap[value][field_optgroup] = [arr, group];\n\t\t\t\t\t} else {\n\t\t\t\t\t\tarr.push(group);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t}else{\n\n\t\t\t\tvar option_data             = readData(option);\n\t\t\t\toption_data[field_label]    = option_data[field_label] || option.textContent;\n\t\t\t\toption_data[field_value]    = option_data[field_value] || value;\n\t\t\t\toption_data[field_disabled] = option_data[field_disabled] || option.disabled;\n\t\t\t\toption_data[field_optgroup] = option_data[field_optgroup] || group;\n\t\t\t\toption_data.$option\t\t\t= option;\n\t\t\t\toption_data.$order\t\t\t= option_data.$order || ++$order;\n\n\t\t\t\toptionsMap[value] = option_data;\n\t\t\t\toptions.push(option_data);\n\t\t\t}\n\n\t\t\tif( option.selected ){\n\t\t\t\tsettings_element.items.push(value);\n\t\t\t}\n\t\t};\n\n\t\tvar addGroup = ( optgroup:HTMLOptGroupElement ) => {\n\t\t\tvar id:string, optgroup_data\n\n\t\t\toptgroup_data\t\t\t\t\t\t\t= readData(optgroup);\n\t\t\toptgroup_data[field_optgroup_label]\t\t= optgroup_data[field_optgroup_label] || optgroup.getAttribute('label') || '';\n\t\t\toptgroup_data[field_optgroup_value]\t\t= optgroup_data[field_optgroup_value] || group_count++;\n\t\t\toptgroup_data[field_disabled]\t\t\t= optgroup_data[field_disabled] || optgroup.disabled;\n\t\t\toptgroup_data.$order\t\t\t\t\t= optgroup_data.$order || ++$order;\n\n\t\t\tsettings_element.optgroups.push(optgroup_data);\n\n\t\t\tid = optgroup_data[field_optgroup_value];\n\n\t\t\titerate(optgroup.children, (option)=>{\n\t\t\t\taddOption(option as HTMLOptionElement, id);\n\t\t\t});\n\n\t\t};\n\n\t\tsettings_element.maxItems = input.hasAttribute('multiple') ? null : 1;\n\n\t\titerate(input.children,(child)=>{\n\t\t\ttagName = child.tagName.toLowerCase();\n\t\t\tif (tagName === 'optgroup') {\n\t\t\t\taddGroup(child as HTMLOptGroupElement);\n\t\t\t} else if (tagName === 'option') {\n\t\t\t\taddOption(child as HTMLOptionElement);\n\t\t\t}\n\t\t});\n\n\t};\n\n\n\t/**\n\t * Initialize from a <input type=\"text\"> element.\n\t *\n\t */\n\tvar init_textbox = () => {\n\t\tconst data_raw = input.getAttribute(attr_data);\n\n\t\tif (!data_raw) {\n\t\t\tvar value = input.value.trim() || '';\n\t\t\tif (!settings.allowEmptyOption && !value.length) return;\n\t\t\tconst values = value.split(settings.delimiter);\n\n\t\t\titerate( values, (value) => {\n\t\t\t\tconst option:TomOption = {};\n\t\t\t\toption[field_label] = value;\n\t\t\t\toption[field_value] = value;\n\t\t\t\tsettings_element.options.push(option);\n\t\t\t});\n\t\t\tsettings_element.items = values;\n\t\t} else {\n\t\t\tsettings_element.options = JSON.parse(data_raw);\n\t\t\titerate( settings_element.options, (opt) => {\n\t\t\t\tsettings_element.items.push(opt[field_value]);\n\t\t\t});\n\t\t}\n\t};\n\n\n\tif (tag_name === 'select') {\n\t\tinit_select();\n\t} else {\n\t\tinit_textbox();\n\t}\n\n\treturn Object.assign( {}, defaults, settings_element, settings_user) as TomSettings;\n};\n", "\nimport MicroEvent from './contrib/microevent.ts';\nimport MicroPlugin from './contrib/microplugin.ts';\nimport { Sifter } from '@orchidjs/sifter';\nimport { escape_regex } from '@orchidjs/unicode-variants';\nimport { TomInput, TomArgObject, TomOption, TomOptions, TomCreateFilter, TomCreateCallback, TomItem, TomSettings, TomTemplateNames, TomClearFilter, RecursivePartial } from './types/index.ts';\nimport {highlight, removeHighlight} from './contrib/highlight.ts';\nimport * as constants from './constants.ts';\nimport getSettings from './getSettings.ts';\nimport {\n\thash_key,\n\tget_hash,\n\tescape_html,\n\tdebounce_events,\n\tgetSelection,\n\tpreventDefault,\n\taddEvent,\n\tloadDebounce,\n\ttimeout,\n\tisKeyDown,\n\tgetId,\n\taddSlashes,\n\tappend,\n\titerate\n} from './utils.ts';\n\nimport {\n\tgetDom,\n\tisHtmlString,\n\tescapeQuery,\n\ttriggerEvent,\n\tapplyCSS,\n\taddClasses,\n\tremoveClasses,\n\tparentMatch,\n\tgetTail,\n\tisEmptyObject,\n\tnodeIndex,\n\tsetAttr,\n\treplaceNode\n} from './vanilla.ts';\n\nvar instance_i = 0;\n\nexport default class TomSelect extends MicroPlugin(MicroEvent){\n\n\tpublic control_input\t\t\t: HTMLInputElement;\n\tpublic wrapper\t\t\t\t\t: HTMLElement;\n\tpublic dropdown\t\t\t\t\t: HTMLElement;\n\tpublic control\t\t\t\t\t: HTMLElement;\n\tpublic dropdown_content\t\t\t: HTMLElement;\n\tpublic focus_node\t\t\t\t: HTMLElement;\n\n\tpublic order\t\t\t\t\t: number = 0;\n\tpublic settings\t\t\t\t\t: TomSettings;\n\tpublic input\t\t\t\t\t: TomInput;\n\tpublic tabIndex\t\t\t\t\t: number;\n\tpublic is_select_tag\t\t\t: boolean;\n\tpublic rtl\t\t\t\t\t\t: boolean;\n\tprivate inputId\t\t\t\t\t: string;\n\n\tprivate _destroy\t\t\t\t!: () => void;\n\tpublic sifter\t\t\t\t\t: Sifter;\n\n\n\tpublic isOpen\t\t\t\t\t: boolean = false;\n\tpublic isDisabled\t\t\t\t: boolean = false;\n\tpublic isReadOnly\t\t\t\t: boolean = false;\n\tpublic isRequired\t\t\t\t: boolean;\n\tpublic isInvalid\t\t\t\t: boolean = false; // @deprecated 1.8\n\tpublic isValid\t\t\t\t\t: boolean = true;\n\tpublic isLocked\t\t\t\t\t: boolean = false;\n\tpublic isFocused\t\t\t\t: boolean = false;\n\tpublic isInputHidden\t\t\t: boolean = false;\n\tpublic isSetup\t\t\t\t\t: boolean = false;\n\tpublic ignoreFocus\t\t\t\t: boolean = false;\n\tpublic ignoreHover\t\t\t\t: boolean = false;\n\tpublic hasOptions\t\t\t\t: boolean = false;\n\tpublic currentResults\t\t\t?: ReturnType<Sifter['search']>;\n\tpublic lastValue\t\t\t\t: string = '';\n\tpublic caretPos\t\t\t\t\t: number = 0;\n\tpublic loading\t\t\t\t\t: number = 0;\n\tpublic loadedSearches\t\t\t: { [key: string]: boolean } = {};\n\n\tpublic activeOption\t\t\t\t: null|HTMLElement = null;\n\tpublic activeItems\t\t\t\t: TomItem[] = [];\n\n\tpublic optgroups\t\t\t\t: TomOptions = {};\n\tpublic options\t\t\t\t\t: TomOptions = {};\n\tpublic userOptions\t\t\t\t: {[key:string]:boolean} = {};\n\tpublic items\t\t\t\t\t: string[] = [];\n\n\tprivate refreshTimeout\t\t\t: null|number = null;\n\n\n\tconstructor( input_arg: string|TomInput, user_settings:RecursivePartial<TomSettings> ){\n\t\tsuper();\n\n\t\tinstance_i++;\n\n\t\tvar dir;\n\t\tvar input\t\t\t\t= getDom( input_arg ) as TomInput;\n\n\t\tif( input.tomselect ){\n\t\t\tthrow new Error('Tom Select already initialized on this element');\n\t\t}\n\n\n\t\tinput.tomselect\t\t\t= this;\n\n\n\t\t// detect rtl environment\n\t\tvar computedStyle\t\t= window.getComputedStyle && window.getComputedStyle(input, null);\n\t\tdir\t\t\t\t\t\t= computedStyle.getPropertyValue('direction');\n\n\t\t// setup default state\n\t\tconst settings\t\t\t= getSettings( input, user_settings );\n\t\tthis.settings\t\t\t= settings;\n\t\tthis.input\t\t\t\t= input;\n\t\tthis.tabIndex\t\t\t= input.tabIndex || 0;\n\t\tthis.is_select_tag\t\t= input.tagName.toLowerCase() === 'select';\n\t\tthis.rtl\t\t\t\t= /rtl/i.test(dir);\n\t\tthis.inputId\t\t\t= getId(input, 'tomselect-'+instance_i);\n\t\tthis.isRequired\t\t\t= input.required;\n\n\n\t\t// search system\n\t\tthis.sifter = new Sifter(this.options, {diacritics: settings.diacritics});\n\n\t\t// option-dependent defaults\n\t\tsettings.mode = settings.mode || (settings.maxItems === 1 ? 'single' : 'multi');\n\t\tif (typeof settings.hideSelected !== 'boolean') {\n\t\t\tsettings.hideSelected = settings.mode === 'multi';\n\t\t}\n\n\t\tif( typeof settings.hidePlaceholder !== 'boolean' ){\n\t\t\tsettings.hidePlaceholder = settings.mode !== 'multi';\n\t\t}\n\n\t\t// set up createFilter callback\n\t\tvar filter = settings.createFilter;\n\t\tif( typeof filter !== 'function' ){\n\n\t\t\tif( typeof filter === 'string' ){\n\t\t\t\tfilter = new RegExp(filter);\n\t\t\t}\n\n\t\t\tif( filter instanceof RegExp ){\n\t\t\t\tsettings.createFilter = (input: string) => (filter as RegExp).test(input);\n\t\t\t}else{\n\t\t\t\tsettings.createFilter = (value: string) => {\n\t\t\t\t\treturn this.settings.duplicates || !this.options[value];\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\n\n\t\tthis.initializePlugins(settings.plugins);\n\t\tthis.setupCallbacks();\n\t\tthis.setupTemplates();\n\n\n\t\t// Create all elements\n\t\tconst wrapper\t\t\t= getDom('<div>');\n\t\tconst control\t\t\t= getDom('<div>');\n\t\tconst dropdown\t\t\t= this._render('dropdown');\n\t\tconst dropdown_content\t= getDom(`<div role=\"listbox\" tabindex=\"-1\">`);\n\n\t\tconst classes\t\t\t= this.input.getAttribute('class') || '';\n\t\tconst inputMode\t\t\t= settings.mode;\n\n\t\tvar control_input: HTMLInputElement;\n\n\n\t\taddClasses( wrapper, settings.wrapperClass, classes, inputMode);\n\n\n\t\taddClasses(control,settings.controlClass);\n\t\tappend( wrapper, control );\n\n\n\t\taddClasses(dropdown, settings.dropdownClass, inputMode);\n\t\tif( settings.copyClassesToDropdown ){\n\t\t\taddClasses( dropdown, classes);\n\t\t}\n\n\n\t\taddClasses(dropdown_content, settings.dropdownContentClass);\n\t\tappend( dropdown, dropdown_content );\n\n\t\tgetDom( settings.dropdownParent || wrapper ).appendChild( dropdown );\n\n\n\t\t// default controlInput\n\t\tif( isHtmlString(settings.controlInput) ){\n\t\t\tcontrol_input\t\t= getDom(settings.controlInput ) as HTMLInputElement;\n\n\t\t\t// set attributes\n\t\t\tvar attrs = ['autocorrect','autocapitalize','autocomplete','spellcheck'];\n\t\t\titerate(attrs,(attr:string) => {\n\t\t\t\tif( input.getAttribute(attr) ){\n\t\t\t\t\tsetAttr(control_input,{[attr]:input.getAttribute(attr)});\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tcontrol_input.tabIndex = -1;\n\t\t\tcontrol.appendChild( control_input );\n\t\t\tthis.focus_node\t\t= control_input;\n\n\t\t// dom element\n\t\t}else if( settings.controlInput ){\n\t\t\tcontrol_input\t\t= getDom( settings.controlInput ) as HTMLInputElement;\n\t\t\tthis.focus_node\t\t= control_input;\n\n\t\t}else{\n\t\t\tcontrol_input\t\t= getDom('<input/>') as HTMLInputElement;\n\t\t\tthis.focus_node\t\t= control;\n\t\t}\n\n\t\tthis.wrapper\t\t\t= wrapper;\n\t\tthis.dropdown\t\t\t= dropdown;\n\t\tthis.dropdown_content\t= dropdown_content;\n\t\tthis.control \t\t\t= control;\n\t\tthis.control_input\t\t= control_input;\n\n\t\tthis.setup();\n\t}\n\n\t/**\n\t * set up event bindings.\n\t *\n\t */\n\tsetup(){\n\n\t\tconst self = this;\n\t\tconst settings\t\t\t\t= self.settings;\n\t\tconst control_input\t\t\t= self.control_input;\n\t\tconst dropdown\t\t\t\t= self.dropdown;\n\t\tconst dropdown_content\t\t= self.dropdown_content;\n\t\tconst wrapper\t\t\t\t= self.wrapper;\n\t\tconst control\t\t\t\t= self.control;\n\t\tconst input\t\t\t\t\t= self.input;\n\t\tconst focus_node\t\t\t= self.focus_node;\n\t\tconst passive_event\t\t\t= { passive: true };\n\t\tconst listboxId\t\t\t\t= self.inputId +'-ts-dropdown';\n\n\n\t\tsetAttr(dropdown_content,{\n\t\t\tid: listboxId\n\t\t});\n\n\t\tsetAttr(focus_node,{\n\t\t\trole:'combobox',\n\t\t\t'aria-haspopup':'listbox',\n\t\t\t'aria-expanded':'false',\n\t\t\t'aria-controls':listboxId\n\t\t});\n\n\t\tconst control_id\t= getId(focus_node,self.inputId + '-ts-control');\n\t\tconst query\t\t\t= \"label[for='\"+escapeQuery(self.inputId)+\"']\";\n\t\tconst label\t\t\t= document.querySelector(query);\n\t\tconst label_click\t= self.focus.bind(self);\n\t\tif( label ){\n\t\t\taddEvent(label,'click', label_click );\n\t\t\tsetAttr(label,{for:control_id});\n\t\t\tconst label_id = getId(label,self.inputId+'-ts-label');\n\t\t\tsetAttr(focus_node,{'aria-labelledby':label_id});\n\t\t\tsetAttr(dropdown_content,{'aria-labelledby':label_id});\n\t\t}\n\n\t\twrapper.style.width = input.style.width;\n\n\t\tif (self.plugins.names.length) {\n\t\t\tconst classes_plugins = 'plugin-' + self.plugins.names.join(' plugin-');\n\t\t\taddClasses( [wrapper,dropdown], classes_plugins);\n\t\t}\n\n\t\tif ((settings.maxItems === null || settings.maxItems > 1) && self.is_select_tag ){\n\t\t\tsetAttr(input,{multiple:'multiple'});\n\t\t}\n\n\t\tif (settings.placeholder) {\n\t\t\tsetAttr(control_input,{placeholder:settings.placeholder});\n\t\t}\n\n\t\t// if splitOn was not passed in, construct it from the delimiter to allow pasting universally\n\t\tif (!settings.splitOn && settings.delimiter) {\n\t\t\tsettings.splitOn = new RegExp('\\\\s*' + escape_regex(settings.delimiter) + '+\\\\s*');\n\t\t}\n\n\t\t// debounce user defined load() if loadThrottle > 0\n\t\t// after initializePlugins() so plugins can create/modify user defined loaders\n\t\tif( settings.load && settings.loadThrottle ){\n\t\t\tsettings.load = loadDebounce(settings.load,settings.loadThrottle)\n\t\t}\n\n\t\taddEvent(dropdown,'mousemove', () => {\n\t\t\tself.ignoreHover = false;\n\t\t});\n\n\t\taddEvent(dropdown,'mouseenter', (e) => {\n\n\t\t\tvar target_match = parentMatch(e.target as HTMLElement, '[data-selectable]', dropdown);\n\t\t\tif( target_match ) self.onOptionHover( e as MouseEvent, target_match );\n\n\t\t}, {capture:true});\n\n\t\t// clicking on an option should select it\n\t\taddEvent(dropdown,'click',(evt) => {\n\t\t\tconst option = parentMatch(evt.target as HTMLElement, '[data-selectable]');\n\t\t\tif( option ){\n\t\t\t\tself.onOptionSelect( evt as MouseEvent, option );\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t}\n\t\t});\n\n\t\taddEvent(control,'click', (evt) => {\n\n\t\t\tvar target_match = parentMatch( evt.target as HTMLElement, '[data-ts-item]', control);\n\t\t\tif( target_match && self.onItemSelect(evt as MouseEvent, target_match as TomItem) ){\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// retain focus (see control_input mousedown)\n\t\t\tif( control_input.value != '' ){\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tself.onClick();\n\t\t\tpreventDefault(evt,true);\n\t\t});\n\n\n\t\t// keydown on focus_node for arrow_down/arrow_up\n\t\taddEvent(focus_node,'keydown',\t\t(e) => self.onKeyDown(e as KeyboardEvent) );\n\n\t\t// keypress and input/keyup\n\t\taddEvent(control_input,'keypress',\t(e) => self.onKeyPress(e as KeyboardEvent) );\n\t\taddEvent(control_input,'input',\t\t(e) => self.onInput(e as KeyboardEvent) );\n\t\taddEvent(focus_node,'blur', \t\t(e) => self.onBlur(e as FocusEvent) );\n\t\taddEvent(focus_node,'focus',\t\t(e) => self.onFocus(e as MouseEvent) );\n\t\taddEvent(control_input,'paste',\t\t(e) => self.onPaste(e as MouseEvent) );\n\n\n\t\tconst doc_mousedown = (evt:Event) => {\n\n\t\t\t// blur if target is outside of this instance\n\t\t\t// dropdown is not always inside wrapper\n\t\t\tconst target = evt.composedPath()[0];\n\t\t\tif( !wrapper.contains(target as HTMLElement) && !dropdown.contains(target as HTMLElement) ){\n\t\t\t\tif (self.isFocused) {\n\t\t\t\t\tself.blur();\n\t\t\t\t}\n\t\t\t\tself.inputState();\n\t\t\t\treturn;\n\t\t\t}\n\n\n\t\t\t// retain focus by preventing native handling. if the\n\t\t\t// event target is the input it should not be modified.\n\t\t\t// otherwise, text selection within the input won't work.\n\t\t\t// Fixes bug #212 which is no covered by tests\n\t\t\tif( target == control_input && self.isOpen ){\n\t\t\t\tevt.stopPropagation();\n\n\t\t\t// clicking anywhere in the control should not blur the control_input (which would close the dropdown)\n\t\t\t}else{\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t}\n\n\t\t};\n\n\t\tconst win_scroll = () => {\n\t\t\tif (self.isOpen) {\n\t\t\t\tself.positionDropdown();\n\t\t\t}\n\t\t};\n\n\n\t\taddEvent(document,'mousedown', doc_mousedown);\n\t\taddEvent(window,'scroll', win_scroll, passive_event);\n\t\taddEvent(window,'resize', win_scroll, passive_event);\n\n\t\tthis._destroy = () => {\n\t\t\tdocument.removeEventListener('mousedown',doc_mousedown);\n\t\t\twindow.removeEventListener('scroll',win_scroll);\n\t\t\twindow.removeEventListener('resize',win_scroll);\n\t\t\tif( label ) label.removeEventListener('click',label_click);\n\t\t};\n\n\t\t// store original html and tab index so that they can be\n\t\t// restored when the destroy() method is called.\n\t\tthis.revertSettings = {\n\t\t\tinnerHTML : input.innerHTML,\n\t\t\ttabIndex : input.tabIndex\n\t\t};\n\n\n\t\tinput.tabIndex = -1;\n\t\tinput.insertAdjacentElement('afterend', self.wrapper);\n\n\t\tself.sync(false);\n\t\tsettings.items = [];\n\t\tdelete settings.optgroups;\n\t\tdelete settings.options;\n\n\t\taddEvent(input,'invalid', () => {\n\t\t\tif( self.isValid ){\n\t\t\t\tself.isValid = false;\n\t\t\t\tself.isInvalid = true;\n\t\t\t\tself.refreshState();\n\t\t\t}\n\t\t});\n\n\t\tself.updateOriginalInput();\n\t\tself.refreshItems();\n\t\tself.close(false);\n\t\tself.inputState();\n\t\tself.isSetup = true;\n\n\t\tif( input.disabled ){\n\t\t\tself.disable();\n\t\t}else if( input.readOnly ){\n\t\t\tself.setReadOnly(true);\n\t\t}else{\n\t\t\tself.enable(); //sets tabIndex\n\t\t}\n\n\t\tself.on('change', this.onChange);\n\n\t\taddClasses(input,'tomselected','ts-hidden-accessible');\n\t\tself.trigger('initialize');\n\n\t\t// preload options\n\t\tif (settings.preload === true) {\n\t\t\tself.preload();\n\t\t}\n\n\t}\n\n\n\t/**\n\t * Register options and optgroups\n\t *\n\t */\n\tsetupOptions(options:TomOption[] = [], optgroups:TomOption[] = []){\n\n\t\t// build options table\n\t\tthis.addOptions(options);\n\n\n\t\t// build optgroup table\n\t\titerate( optgroups, (optgroup:TomOption) => {\n\t\t\tthis.registerOptionGroup(optgroup);\n\t\t});\n\t}\n\n\t/**\n\t * Sets up default rendering functions.\n\t */\n\tsetupTemplates() {\n\t\tvar self = this;\n\t\tvar field_label = self.settings.labelField;\n\t\tvar field_optgroup = self.settings.optgroupLabelField;\n\n\t\tvar templates = {\n\t\t\t'optgroup': (data:TomOption) => {\n\t\t\t\tlet optgroup = document.createElement('div');\n\t\t\t\toptgroup.className = 'optgroup';\n\t\t\t\toptgroup.appendChild(data.options);\n\t\t\t\treturn optgroup;\n\n\t\t\t},\n\t\t\t'optgroup_header': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div class=\"optgroup-header\">' + escape(data[field_optgroup]) + '</div>';\n\t\t\t},\n\t\t\t'option': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div>' + escape(data[field_label]) + '</div>';\n\t\t\t},\n\t\t\t'item': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div>' + escape(data[field_label]) + '</div>';\n\t\t\t},\n\t\t\t'option_create': (data:TomOption, escape:typeof escape_html) => {\n\t\t\t\treturn '<div class=\"create\">Add <strong>' + escape(data.input) + '</strong>&hellip;</div>';\n\t\t\t},\n\t\t\t'no_results':() => {\n\t\t\t\treturn '<div class=\"no-results\">No results found</div>';\n\t\t\t},\n\t\t\t'loading':() => {\n\t\t\t\treturn '<div class=\"spinner\"></div>';\n\t\t\t},\n\t\t\t'not_loading':() => {},\n\t\t\t'dropdown':() => {\n\t\t\t\treturn '<div></div>';\n\t\t\t}\n\t\t};\n\n\n\t\tself.settings.render = Object.assign({}, templates, self.settings.render);\n\t}\n\n\t/**\n\t * Maps fired events to callbacks provided\n\t * in the settings used when creating the control.\n\t */\n\tsetupCallbacks() {\n\t\tvar key, fn;\n\t\tvar callbacks:{[key:string]:string} = {\n\t\t\t'initialize'      : 'onInitialize',\n\t\t\t'change'          : 'onChange',\n\t\t\t'item_add'        : 'onItemAdd',\n\t\t\t'item_remove'     : 'onItemRemove',\n\t\t\t'item_select'     : 'onItemSelect',\n\t\t\t'clear'           : 'onClear',\n\t\t\t'option_add'      : 'onOptionAdd',\n\t\t\t'option_remove'   : 'onOptionRemove',\n\t\t\t'option_clear'    : 'onOptionClear',\n\t\t\t'optgroup_add'    : 'onOptionGroupAdd',\n\t\t\t'optgroup_remove' : 'onOptionGroupRemove',\n\t\t\t'optgroup_clear'  : 'onOptionGroupClear',\n\t\t\t'dropdown_open'   : 'onDropdownOpen',\n\t\t\t'dropdown_close'  : 'onDropdownClose',\n\t\t\t'type'            : 'onType',\n\t\t\t'load'            : 'onLoad',\n\t\t\t'focus'           : 'onFocus',\n\t\t\t'blur'            : 'onBlur'\n\t\t};\n\n\t\tfor (key in callbacks) {\n\n\t\t\tfn = this.settings[callbacks[key] as (keyof TomSettings)];\n\t\t\tif (fn) this.on(key, fn);\n\n\t\t}\n\t}\n\n\t/**\n\t * Sync the Tom Select instance with the original input or select\n\t *\n\t */\n\tsync(get_settings:boolean=true):void{\n\t\tconst self\t\t= this;\n\t\tconst settings\t= get_settings ? getSettings( self.input, {delimiter:self.settings.delimiter} as RecursivePartial<TomSettings> ) : self.settings;\n\n\t\tself.setupOptions(settings.options,settings.optgroups);\n\n\t\tself.setValue(settings.items||[],true); // silent prevents recursion\n\n\t\tself.lastQuery = null; // so updated options will be displayed in dropdown\n\t}\n\n\t/**\n\t * Triggered when the main control element\n\t * has a click event.\n\t *\n\t */\n\tonClick():void {\n\t\tvar self = this;\n\n\t\tif( self.activeItems.length > 0 ){\n\t\t\tself.clearActiveItems();\n\t\t\tself.focus();\n\t\t\treturn;\n\t\t}\n\n\t\tif( self.isFocused && self.isOpen ){\n\t\t\tself.blur();\n\t\t} else {\n\t\t\tself.focus();\n\t\t}\n\t}\n\n\t/**\n\t * @deprecated v1.7\n\t *\n\t */\n\tonMouseDown():void {}\n\n\t/**\n\t * Triggered when the value of the control has been changed.\n\t * This should propagate the event to the original DOM\n\t * input / select element.\n\t */\n\tonChange() {\n\t\ttriggerEvent(this.input, 'input');\n\t\ttriggerEvent(this.input, 'change');\n\t}\n\n\t/**\n\t * Triggered on <input> paste.\n\t *\n\t */\n\tonPaste(e:MouseEvent|KeyboardEvent):void {\n\t\tvar self = this;\n\n\t\tif( self.isInputHidden || self.isLocked ){\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\n\t\t// If a regex or string is included, this will split the pasted\n\t\t// input and create Items for each separate value\n\t\tif( !self.settings.splitOn ){\n\t\t\treturn;\n\t\t}\n\n\t\t// Wait for pasted text to be recognized in value\n\t\tsetTimeout(() => {\n\t\t\tvar pastedText = self.inputValue();\n\t\t\tif( !pastedText.match(self.settings.splitOn)){\n\t\t\t\treturn\n\t\t\t}\n\n\t\t\tvar splitInput = pastedText.trim().split(self.settings.splitOn);\n\t\t\titerate( splitInput, (piece:string) => {\n\n\t\t\t\tconst hash = hash_key(piece);\n\t\t\t\tif( hash ){\n\t\t\t\t\tif( this.options[piece] ){\n\t\t\t\t\t\tself.addItem(piece);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tself.createItem(piece);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t}, 0);\n\n\t}\n\n\t/**\n\t * Triggered on <input> keypress.\n\t *\n\t */\n\tonKeyPress(e:KeyboardEvent):void {\n\t\tvar self = this;\n\t\tif(self.isLocked){\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\t\tvar character = String.fromCharCode(e.keyCode || e.which);\n\t\tif (self.settings.create && self.settings.mode === 'multi' && character === self.settings.delimiter) {\n\t\t\tself.createItem();\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\t}\n\n\t/**\n\t * Triggered on <input> keydown.\n\t *\n\t */\n\tonKeyDown(e:KeyboardEvent):void {\n\t\tvar self = this;\n\n\t\tself.ignoreHover = true;\n\n\t\tif (self.isLocked) {\n\t\t\tif (e.keyCode !== constants.KEY_TAB) {\n\t\t\t\tpreventDefault(e);\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\tswitch (e.keyCode) {\n\n\t\t\t// ctrl+A: select all\n\t\t\tcase constants.KEY_A:\n\t\t\t\tif( isKeyDown(constants.KEY_SHORTCUT,e) ){\n\t\t\t\t\tif( self.control_input.value == '' ){\n\t\t\t\t\t\tpreventDefault(e);\n\t\t\t\t\t\tself.selectAll();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tbreak;\n\n\t\t\t// esc: close dropdown\n\t\t\tcase constants.KEY_ESC:\n\t\t\t\tif (self.isOpen) {\n\t\t\t\t\tpreventDefault(e,true);\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\t\t\t\tself.clearActiveItems();\n\t\t\t\treturn;\n\n\t\t\t// down: open dropdown or move selection down\n\t\t\tcase constants.KEY_DOWN:\n\t\t\t\tif (!self.isOpen && self.hasOptions) {\n\t\t\t\t\tself.open();\n\t\t\t\t} else if (self.activeOption) {\n\t\t\t\t\tlet next = self.getAdjacent(self.activeOption, 1);\n\t\t\t\t\tif (next) self.setActiveOption(next);\n\t\t\t\t}\n\t\t\t\tpreventDefault(e);\n\t\t\t\treturn;\n\n\t\t\t// up: move selection up\n\t\t\tcase constants.KEY_UP:\n\t\t\t\tif (self.activeOption) {\n\t\t\t\t\tlet prev = self.getAdjacent(self.activeOption, -1);\n\t\t\t\t\tif (prev) self.setActiveOption(prev);\n\t\t\t\t}\n\t\t\t\tpreventDefault(e);\n\t\t\t\treturn;\n\n\t\t\t// return: select active option\n\t\t\tcase constants.KEY_RETURN:\n\t\t\t\tif( self.canSelect(self.activeOption) ){\n\t\t\t\t\tself.onOptionSelect(e,self.activeOption!);\n\t\t\t\t\tpreventDefault(e);\n\n\t\t\t\t// if the option_create=null, the dropdown might be closed\n\t\t\t\t}else if (self.settings.create && self.createItem()) {\n\t\t\t\t\tpreventDefault(e);\n\n\t\t\t\t// don't submit form when searching for a value\n\t\t\t\t}else if( document.activeElement == self.control_input && self.isOpen ){\n\t\t\t\t\tpreventDefault(e);\n\t\t\t\t}\n\n\t\t\t\treturn;\n\n\t\t\t// left: modifiy item selection to the left\n\t\t\tcase constants.KEY_LEFT:\n\t\t\t\tself.advanceSelection(-1, e);\n\t\t\t\treturn;\n\n\t\t\t// right: modifiy item selection to the right\n\t\t\tcase constants.KEY_RIGHT:\n\t\t\t\tself.advanceSelection(1, e);\n\t\t\t\treturn;\n\n\t\t\t// tab: select active option and/or create item\n\t\t\tcase constants.KEY_TAB:\n\n\t\t\t\tif( self.settings.selectOnTab ){\n\t\t\t\t\tif( self.canSelect(self.activeOption) ){\n\t\t\t\t\t\tself.onOptionSelect(e,self.activeOption!);\n\n\t\t\t\t\t\t// prevent default [tab] behaviour of jump to the next field\n\t\t\t\t\t\t// if select isFull, then the dropdown won't be open and [tab] will work normally\n\t\t\t\t\t\tpreventDefault(e);\n\t\t\t\t\t}\n\t\t\t\t\tif (self.settings.create && self.createItem()) {\n\t\t\t\t\t\tpreventDefault(e);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn;\n\n\t\t\t// delete|backspace: delete items\n\t\t\tcase constants.KEY_BACKSPACE:\n\t\t\tcase constants.KEY_DELETE:\n\t\t\t\tself.deleteSelection(e);\n\t\t\t\treturn;\n\t\t}\n\n\t\t// don't enter text in the control_input when active items are selected\n\t\tif( self.isInputHidden && !isKeyDown(constants.KEY_SHORTCUT,e) ){\n\t\t\tpreventDefault(e);\n\t\t}\n\t}\n\n\t/**\n\t * Triggered on <input> keyup.\n\t *\n\t */\n\tonInput(e:MouseEvent|KeyboardEvent):void {\n\t\t\n\t\tif( this.isLocked ){\n\t\t\treturn;\n\t\t}\n\n\t\tconst value = this.inputValue();\n\t\tif( this.lastValue === value ) return;\n\t\tthis.lastValue = value;\n\t\t\n\t\tif( value == '' ){\n\t\t\tthis._onInput();\n\t\t\treturn;\n\t\t}\n\n\t\tif( this.refreshTimeout ){\n\t\t\twindow.clearTimeout(this.refreshTimeout);\n\t\t}\n\n\t\tthis.refreshTimeout = timeout(()=> {\n\t\t\tthis.refreshTimeout = null;\n\t\t\tthis._onInput();\n\t\t}, this.settings.refreshThrottle);\n\t}\n\n\t_onInput():void {\n\t\tconst value = this.lastValue;\n\n\t\tif( this.settings.shouldLoad.call(this,value) ){\n\t\t\tthis.load(value);\n\t\t}\n\n\t\tthis.refreshOptions();\n\t\tthis.trigger('type', value);\n\t}\n\n\t/**\n\t * Triggered when the user rolls over\n\t * an option in the autocomplete dropdown menu.\n\t *\n\t */\n\tonOptionHover( evt:MouseEvent|KeyboardEvent, option:HTMLElement ):void{\n\t\tif( this.ignoreHover ) return;\n\t\tthis.setActiveOption(option, false);\n\t}\n\n\t/**\n\t * Triggered on <input> focus.\n\t *\n\t */\n\tonFocus(e?:MouseEvent|KeyboardEvent):void {\n\t\tvar self = this;\n\t\tvar wasFocused = self.isFocused;\n\n\t\tif( self.isDisabled || self.isReadOnly ){\n\t\t\tself.blur();\n\t\t\tpreventDefault(e);\n\t\t\treturn;\n\t\t}\n\n\t\tif (self.ignoreFocus) return;\n\t\tself.isFocused = true;\n\t\tif( self.settings.preload === 'focus' ) self.preload();\n\n\t\tif (!wasFocused) self.trigger('focus');\n\n\t\tif (!self.activeItems.length) {\n\t\t\tself.inputState();\n\t\t\tself.refreshOptions(!!self.settings.openOnFocus);\n\t\t}\n\n\t\tself.refreshState();\n\t}\n\n\t/**\n\t * Triggered on <input> blur.\n\t *\n\t */\n\tonBlur(e?:FocusEvent):void {\n\n\t\tif( document.hasFocus() === false ) return;\n\n\t\tvar self = this;\n\t\tif (!self.isFocused) return;\n\t\tself.isFocused = false;\n\t\tself.ignoreFocus = false;\n\n\t\tvar deactivate = () => {\n\t\t\tself.close();\n\t\t\tself.setActiveItem();\n\t\t\tself.setCaret(self.items.length);\n\t\t\tself.trigger('blur');\n\t\t};\n\n\t\tif (self.settings.create && self.settings.createOnBlur) {\n\t\t\tself.createItem(null, deactivate);\n\t\t} else {\n\t\t\tdeactivate();\n\t\t}\n\t}\n\n\n\t/**\n\t * Triggered when the user clicks on an option\n\t * in the autocomplete dropdown menu.\n\t *\n\t */\n\tonOptionSelect( evt:MouseEvent|KeyboardEvent, option:HTMLElement ){\n\t\tvar value, self = this;\n\n\n\t\t// should not be possible to trigger a option under a disabled optgroup\n\t\tif( option.parentElement && option.parentElement.matches('[data-disabled]') ){\n\t\t\treturn;\n\t\t}\n\n\n\t\tif( option.classList.contains('create') ){\n\t\t\tself.createItem(null, () => {\n\t\t\t\tif (self.settings.closeAfterSelect) {\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tvalue = option.dataset.value;\n\t\t\tif (typeof value !== 'undefined') {\n\t\t\t\tself.lastQuery = null;\n\t\t\t\tself.addItem(value);\n\t\t\t\tif (self.settings.closeAfterSelect) {\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\n\t\t\t\tif( !self.settings.hideSelected && evt.type && /click/.test(evt.type) ){\n\t\t\t\t\tself.setActiveOption(option);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Return true if the given option can be selected\n\t *\n\t */\n\tcanSelect(option:HTMLElement|null):boolean{\n\n\t\tif( this.isOpen && option && this.dropdown_content.contains(option) ) {\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * Triggered when the user clicks on an item\n\t * that has been selected.\n\t *\n\t */\n\tonItemSelect( evt?:MouseEvent, item?:TomItem ):boolean{\n\t\tvar self = this;\n\n\t\tif( !self.isLocked && self.settings.mode === 'multi' ){\n\t\t\tpreventDefault(evt);\n\t\t\tself.setActiveItem(item, evt);\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * Determines whether or not to invoke\n\t * the user-provided option provider / loader\n\t *\n\t * Note, there is a subtle difference between\n\t * this.canLoad() and this.settings.shouldLoad();\n\t *\n\t *\t- settings.shouldLoad() is a user-input validator.\n\t *\tWhen false is returned, the not_loading template\n\t *\twill be added to the dropdown\n\t *\n\t *\t- canLoad() is lower level validator that checks\n\t * \tthe Tom Select instance. There is no inherent user\n\t *\tfeedback when canLoad returns false\n\t *\n\t */\n\tcanLoad(value:string):boolean{\n\n\t\tif( !this.settings.load ) return false;\n\t\tif( this.loadedSearches.hasOwnProperty(value) ) return false;\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Invokes the user-provided option provider / loader.\n\t *\n\t */\n\tload(value:string):void {\n\t\tconst self = this;\n\n\t\tif( !self.canLoad(value) ) return;\n\n\t\taddClasses(self.wrapper,self.settings.loadingClass);\n\t\tself.loading++;\n\n\t\tconst callback = self.loadCallback.bind(self);\n\t\tself.settings.load.call(self, value, callback);\n\t}\n\n\t/**\n\t * Invoked by the user-provided option provider\n\t *\n\t */\n\tloadCallback( options:TomOption[], optgroups:TomOption[] ):void{\n\t\tconst self = this;\n\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\tself.lastQuery = null;\n\n\t\tself.clearActiveOption(); // when new results load, focus should be on first option\n\t\tself.setupOptions(options,optgroups);\n\n\t\tself.refreshOptions(self.isFocused && !self.isInputHidden);\n\n\t\tif (!self.loading) {\n\t\t\tremoveClasses(self.wrapper,self.settings.loadingClass);\n\t\t}\n\n\t\tself.trigger('load', options, optgroups);\n\t}\n\n\tpreload():void{\n\t\tvar classList = this.wrapper.classList;\n\t\tif( classList.contains('preloaded') ) return;\n\t\tclassList.add('preloaded');\n\t\tthis.load('');\n\t}\n\n\n\t/**\n\t * Sets the input field of the control to the specified value.\n\t *\n\t */\n\tsetTextboxValue(value:string = '') {\n\t\tvar input = this.control_input;\n\t\tvar changed = input.value !== value;\n\t\tif (changed) {\n\t\t\tinput.value = value;\n\t\t\ttriggerEvent(input,'update');\n\t\t\tthis.lastValue = value;\n\t\t}\n\t}\n\n\t/**\n\t * Returns the value of the control. If multiple items\n\t * can be selected (e.g. <select multiple>), this returns\n\t * an array. If only one item can be selected, this\n\t * returns a string.\n\t *\n\t */\n\tgetValue():string|string[] {\n\n\t\tif( this.is_select_tag && this.input.hasAttribute('multiple')) {\n\t\t\treturn this.items;\n\t\t}\n\n\t\treturn this.items.join(this.settings.delimiter);\n\t}\n\n\t/**\n\t * Resets the selected items to the given value.\n\t *\n\t */\n\tsetValue( value:string|string[], silent?:boolean ):void{\n\t\tvar events = silent ? [] : ['change'];\n\n\t\tdebounce_events(this, events,() => {\n\t\t\tthis.clear(silent);\n\t\t\tthis.addItems(value, silent);\n\t\t});\n\t}\n\n\n\t/**\n\t * Resets the number of max items to the given value\n\t *\n\t */\n\tsetMaxItems(value:null|number){\n\t\tif(value === 0) value = null; //reset to unlimited items.\n\t\tthis.settings.maxItems = value;\n\t\tthis.refreshState();\n\t}\n\n\t/**\n\t * Sets the selected item.\n\t *\n\t */\n\tsetActiveItem( item?:TomItem, e?:MouseEvent|KeyboardEvent ){\n\t\tvar self = this;\n\t\tvar eventName;\n\t\tvar i, begin, end, swap;\n\t\tvar last;\n\n\t\tif (self.settings.mode === 'single') return;\n\n\t\t// clear the active selection\n\t\tif( !item ){\n\t\t\tself.clearActiveItems();\n\t\t\tif (self.isFocused) {\n\t\t\t\tself.inputState();\n\t\t\t}\n\t\t\treturn;\n\t\t}\n\n\t\t// modify selection\n\t\teventName = e && e.type.toLowerCase();\n\n\t\tif (eventName === 'click' && isKeyDown('shiftKey',e) && self.activeItems.length) {\n\t\t\tlast\t= self.getLastActive();\n\t\t\tbegin\t= Array.prototype.indexOf.call(self.control.children, last);\n\t\t\tend\t\t= Array.prototype.indexOf.call(self.control.children, item);\n\n\t\t\tif (begin > end) {\n\t\t\t\tswap  = begin;\n\t\t\t\tbegin = end;\n\t\t\t\tend   = swap;\n\t\t\t}\n\t\t\tfor (i = begin; i <= end; i++) {\n\t\t\t\titem = self.control.children[i] as TomItem;\n\t\t\t\tif (self.activeItems.indexOf(item) === -1) {\n\t\t\t\t\tself.setActiveItemClass(item);\n\t\t\t\t}\n\t\t\t}\n\t\t\tpreventDefault(e);\n\t\t} else if ((eventName === 'click' && isKeyDown(constants.KEY_SHORTCUT,e) ) || (eventName === 'keydown' && isKeyDown('shiftKey',e))) {\n\t\t\tif( item.classList.contains('active') ){\n\t\t\t\tself.removeActiveItem( item );\n\t\t\t} else {\n\t\t\t\tself.setActiveItemClass(item);\n\t\t\t}\n\t\t} else {\n\t\t\tself.clearActiveItems();\n\t\t\tself.setActiveItemClass(item);\n\t\t}\n\n\t\t// ensure control has focus\n\t\tself.inputState();\n\t\tif (!self.isFocused) {\n\t\t\tself.focus();\n\t\t}\n\t}\n\n\t/**\n\t * Set the active and last-active classes\n\t *\n\t */\n\tsetActiveItemClass( item:TomItem ){\n\t\tconst self = this;\n\t\tconst last_active = self.control.querySelector('.last-active');\n\t\tif( last_active ) removeClasses(last_active as HTMLElement,'last-active');\n\n\t\taddClasses(item,'active last-active');\n\t\tself.trigger('item_select', item);\n\t\tif( self.activeItems.indexOf(item) == -1 ){\n\t\t\tself.activeItems.push( item );\n\t\t}\n\t}\n\n\t/**\n\t * Remove active item\n\t *\n\t */\n\tremoveActiveItem( item:TomItem ){\n\t\tvar idx = this.activeItems.indexOf(item);\n\t\tthis.activeItems.splice(idx, 1);\n\t\tremoveClasses(item,'active');\n\t}\n\n\t/**\n\t * Clears all the active items\n\t *\n\t */\n\tclearActiveItems(){\n\t\tremoveClasses(this.activeItems,'active');\n\t\tthis.activeItems = [];\n\t}\n\n\t/**\n\t * Sets the selected item in the dropdown menu\n\t * of available options.\n\t *\n\t */\n\tsetActiveOption( option:null|HTMLElement,scroll:boolean=true ):void{\n\n\t\tif( option === this.activeOption ){\n\t\t\treturn;\n\t\t}\n\n\t\tthis.clearActiveOption();\n\t\tif( !option ) return;\n\n\t\tthis.activeOption = option;\n\t\tsetAttr(this.focus_node,{'aria-activedescendant':option.getAttribute('id')});\n\t\tsetAttr(option,{'aria-selected':'true'});\n\t\taddClasses(option,'active');\n\t\tif( scroll ) this.scrollToOption(option);\n\t}\n\n\t/**\n\t * Sets the dropdown_content scrollTop to display the option\n\t *\n\t */\n\tscrollToOption( option:null|HTMLElement, behavior?:string ):void{\n\n\t\tif( !option ) return;\n\n\t\tconst content\t\t= this.dropdown_content;\n\t\tconst height_menu\t= content.clientHeight;\n\t\tconst scrollTop\t\t= content.scrollTop || 0;\n\t\tconst height_item\t= option.offsetHeight;\n\t\tconst y\t\t\t\t= option.getBoundingClientRect().top - content.getBoundingClientRect().top + scrollTop;\n\n\t\tif (y + height_item > height_menu + scrollTop) {\n\t\t\tthis.scroll(y - height_menu + height_item, behavior);\n\n\t\t} else if (y < scrollTop) {\n\t\t\tthis.scroll(y, behavior);\n\t\t}\n\t}\n\n\t/**\n\t * Scroll the dropdown to the given position\n\t *\n\t */\n\tscroll( scrollTop:number, behavior?:string ):void{\n\t\tconst content = this.dropdown_content;\n\t\tif( behavior ){\n\t\t\tcontent.style.scrollBehavior = behavior;\n\t\t}\n\t\tcontent.scrollTop = scrollTop;\n\t\tcontent.style.scrollBehavior = '';\n\t}\n\n\t/**\n\t * Clears the active option\n\t *\n\t */\n\tclearActiveOption(){\n\t\tif( this.activeOption ){\n\t\t\tremoveClasses(this.activeOption,'active');\n\t\t\tsetAttr(this.activeOption,{'aria-selected':null});\n\t\t}\n\t\tthis.activeOption = null;\n\t\tsetAttr(this.focus_node,{'aria-activedescendant':null});\n\t}\n\n\n\t/**\n\t * Selects all items (CTRL + A).\n\t */\n\tselectAll() {\n\t\tconst self = this;\n\n\t\tif (self.settings.mode === 'single') return;\n\n\t\tconst activeItems = self.controlChildren();\n\n\t\tif( !activeItems.length ) return;\n\n\t\tself.inputState();\n\t\tself.close();\n\n\t\tself.activeItems = activeItems;\n\t\titerate( activeItems, (item:TomItem) => {\n\t\t\tself.setActiveItemClass(item);\n\t\t});\n\n\t}\n\n\t/**\n\t * Determines if the control_input should be in a hidden or visible state\n\t *\n\t */\n\tinputState(){\n\t\tvar self = this;\n\n\t\tif( !self.control.contains(self.control_input) ) return;\n\n\t\tsetAttr(self.control_input,{placeholder:self.settings.placeholder});\n\n\t\tif( self.activeItems.length > 0 || (!self.isFocused && self.settings.hidePlaceholder && self.items.length > 0) ){\n\t\t\tself.setTextboxValue();\n\t\t\tself.isInputHidden = true;\n\n\t\t}else{\n\n\t\t\tif( self.settings.hidePlaceholder && self.items.length > 0 ){\n\t\t\t\tsetAttr(self.control_input,{placeholder:''});\n\t\t\t}\n\t\t\tself.isInputHidden = false;\n\t\t}\n\n\t\tself.wrapper.classList.toggle('input-hidden', self.isInputHidden );\n\t}\n\n\t/**\n\t * Get the input value\n\t */\n\tinputValue(){\n\t\treturn this.control_input.value.trim();\n\t}\n\n\t/**\n\t * Gives the control focus.\n\t */\n\tfocus() {\n\t\tvar self = this;\n\t\tif( self.isDisabled || self.isReadOnly) return;\n\n\t\tself.ignoreFocus = true;\n\n\t\tif( self.control_input.offsetWidth ){\n\t\t\tself.control_input.focus();\n\t\t}else{\n\t\t\tself.focus_node.focus();\n\t\t}\n\n\t\tsetTimeout(() => {\n\t\t\tself.ignoreFocus = false;\n\t\t\tself.onFocus();\n\t\t}, 0);\n\t}\n\n\t/**\n\t * Forces the control out of focus.\n\t *\n\t */\n\tblur():void {\n\t\tthis.focus_node.blur();\n\t\tthis.onBlur();\n\t}\n\n\t/**\n\t * Returns a function that scores an object\n\t * to show how good of a match it is to the\n\t * provided query.\n\t *\n\t * @return {function}\n\t */\n\tgetScoreFunction(query:string) {\n\t\treturn this.sifter.getScoreFunction(query, this.getSearchOptions());\n\t}\n\n\t/**\n\t * Returns search options for sifter (the system\n\t * for scoring and sorting results).\n\t *\n\t * @see https://github.com/orchidjs/sifter.js\n\t * @return {object}\n\t */\n\tgetSearchOptions() {\n\t\tvar settings = this.settings;\n\t\tvar sort = settings.sortField;\n\t\tif (typeof settings.sortField === 'string') {\n\t\t\tsort = [{field: settings.sortField}];\n\t\t}\n\n\t\treturn {\n\t\t\tfields      : settings.searchField,\n\t\t\tconjunction : settings.searchConjunction,\n\t\t\tsort        : sort,\n\t\t\tnesting     : settings.nesting\n\t\t};\n\t}\n\n\t/**\n\t * Searches through available options and returns\n\t * a sorted array of matches.\n\t *\n\t */\n\tsearch(query:string) : ReturnType<Sifter['search']>{\n\t\tvar result, calculateScore;\n\t\tvar self     = this;\n\t\tvar options  = this.getSearchOptions();\n\n\t\t// validate user-provided result scoring function\n\t\tif ( self.settings.score ){\n\t\t\tcalculateScore = self.settings.score.call(self,query);\n\t\t\tif (typeof calculateScore !== 'function') {\n\t\t\t\tthrow new Error('Tom Select \"score\" setting must be a function that returns a function');\n\t\t\t}\n\t\t}\n\n\t\t// perform search\n\t\tif (query !== self.lastQuery) {\n\t\t\tself.lastQuery\t\t\t= query;\n\t\t\tresult\t\t\t\t\t= self.sifter.search(query, Object.assign(options, {score: calculateScore}));\n\t\t\tself.currentResults\t\t= result;\n\t\t} else {\n\t\t\tresult\t\t\t\t\t= Object.assign( {}, self.currentResults);\n\t\t}\n\n\t\t// filter out selected items\n\t\tif( self.settings.hideSelected ){\n\t\t\tresult.items = result.items.filter((item) => {\n\t\t\t\tlet hashed = hash_key(item.id);\n\t\t\t\treturn !(hashed && self.items.indexOf(hashed) !== -1 );\n\t\t\t});\n\t\t}\n\n\t\treturn result;\n\t}\n\n\t/**\n\t * Refreshes the list of available options shown\n\t * in the autocomplete dropdown menu.\n\t *\n\t */\n\trefreshOptions( triggerDropdown:boolean = true ){\n\t\tvar i, j, k, n, optgroup, optgroups, html:DocumentFragment, has_create_option, active_group;\n\t\tvar create;\n\n\t\ttype Group = {fragment:DocumentFragment,order:number,optgroup:string}\n\t\tconst groups: {[key:string]:number} = {};\n\t\tconst groups_order:Group[]\t= [];\n\n\t\tvar self\t\t\t\t\t= this;\n\t\tvar query\t\t\t\t\t= self.inputValue();\n\t\tconst same_query\t\t\t= query === self.lastQuery || (query == '' && self.lastQuery == null);\n\t\tvar results\t\t\t\t\t= self.search(query);\n\t\tvar active_option:HTMLElement|null = null;\n\t\tvar show_dropdown\t\t\t= self.settings.shouldOpen || false;\n\t\tvar dropdown_content\t\t= self.dropdown_content;\n\n\n\t\tif( same_query ){\n\t\t\tactive_option\t\t\t= self.activeOption;\n\n\t\t\tif( active_option ){\n\t\t\t\tactive_group = active_option.closest('[data-group]') as HTMLElement;\n\t\t\t}\n\t\t}\n\n\t\t// build markup\n\t\tn = results.items.length;\n\t\tif (typeof self.settings.maxOptions === 'number') {\n\t\t\tn = Math.min(n, self.settings.maxOptions);\n\t\t}\n\n\t\tif( n > 0 ){\n\t\t\tshow_dropdown = true;\n\t\t}\n\n\t\t// get fragment for group and the position of the group in group_order\n\t\tconst getGroupFragment = (optgroup:string,order:number):[number,DocumentFragment] => {\n\n\t\t\tlet group_order_i = groups[optgroup];\n\n\t\t\tif( group_order_i !== undefined ){\n\t\t\t\tlet order_group = groups_order[group_order_i];\n\t\t\t\tif( order_group !== undefined ){\n\t\t\t\t\treturn [group_order_i,order_group.fragment];\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet group_fragment = document.createDocumentFragment();\n\t\t\tgroup_order_i = groups_order.length;\n\t\t\tgroups_order.push({fragment:group_fragment,order,optgroup});\n\n\t\t\treturn [group_order_i,group_fragment]\n\t\t}\n\n\t\t// render and group available options individually\n\t\tfor (i = 0; i < n; i++) {\n\n\t\t\t// get option dom element\n\t\t\tlet item\t\t\t= results.items[i];\n\t\t\tif( !item ) continue;\n\n\t\t\tlet opt_value\t\t= item.id;\n\t\t\tlet option\t\t\t= self.options[opt_value];\n\n\t\t\tif( option === undefined ) continue;\n\n\t\t\tlet opt_hash\t\t= get_hash(opt_value);\n\t\t\tlet option_el\t\t= self.getOption(opt_hash,true) as HTMLElement;\n\n\t\t\t// toggle 'selected' class\n\t\t\tif( !self.settings.hideSelected ){\n\t\t\t\toption_el.classList.toggle('selected', self.items.includes(opt_hash) );\n\t\t\t}\n\n\t\t\toptgroup    = option[self.settings.optgroupField] || '';\n\t\t\toptgroups   = Array.isArray(optgroup) ? optgroup : [optgroup];\n\t\t\t\n\n\t\t\tfor (j = 0, k = optgroups && optgroups.length; j < k; j++) {\n\t\t\t\toptgroup = optgroups[j];\n\n\t\t\t\tlet order = option.$order;\n\t\t\t\tlet self_optgroup = self.optgroups[optgroup];\n\t\t\t\tif( self_optgroup === undefined ){\t\t\t\t\t\n\t\t\t\t\toptgroup = '';\n\t\t\t\t}else{\n\t\t\t\t\torder = self_optgroup.$order;\n\t\t\t\t}\n\n\t\t\t\tconst [group_order_i,group_fragment] = getGroupFragment(optgroup,order);\n\n\n\t\t\t\t// nodes can only have one parent, so if the option is in mutple groups, we need a clone\n\t\t\t\tif( j > 0 ){\n\t\t\t\t\toption_el = option_el.cloneNode(true) as HTMLElement;\n\t\t\t\t\tsetAttr(option_el,{id: option.$id+'-clone-'+j,'aria-selected':null});\n\t\t\t\t\toption_el.classList.add('ts-cloned');\n\t\t\t\t\tremoveClasses(option_el,'active');\n\n\n\t\t\t\t\t// make sure we keep the activeOption in the same group\n\t\t\t\t\tif( self.activeOption && self.activeOption.dataset.value == opt_value ){\n\t\t\t\t\t\tif( active_group && active_group.dataset.group === optgroup.toString() ){\n\t\t\t\t\t\t\tactive_option = option_el;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\t\n\t\t\t\t\n\t\t\t\tgroup_fragment.appendChild(option_el);\n\t\t\t\tif( optgroup != '' ){\n\t\t\t\t\tgroups[optgroup] = group_order_i;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// sort optgroups\n\t\tif( self.settings.lockOptgroupOrder ){\n\t\t\tgroups_order.sort((a, b) => {\n\t\t\t\treturn a.order - b.order;\n\t\t\t});\n\t\t}\n\n\t\t// render optgroup headers & join groups\n\t\thtml = document.createDocumentFragment();\n\t\titerate( groups_order, (group_order:Group) => {\n\n\t\t\tlet group_fragment = group_order.fragment;\n\t\t\tlet optgroup = group_order.optgroup\n\n\t\t\tif( !group_fragment || !group_fragment.children.length ) return;\n\n\t\t\tlet group_heading = self.optgroups[optgroup];\n\n\t\t\tif( group_heading !== undefined ){\n\n\t\t\t\tlet group_options = document.createDocumentFragment();\n\t\t\t\tlet header = self.render('optgroup_header', group_heading);\n\t\t\t\tappend( group_options, header );\n\t\t\t\tappend( group_options, group_fragment );\n\n\t\t\t\tlet group_html = self.render('optgroup', {group:group_heading,options:group_options} );\n\n\t\t\t\tappend( html, group_html );\n\n\t\t\t} else {\n\t\t\t\tappend( html, group_fragment );\n\t\t\t}\n\t\t});\n\n\t\tdropdown_content.innerHTML = '';\n\t\tappend( dropdown_content, html );\n\n\t\t// highlight matching terms inline\n\t\tif (self.settings.highlight) {\n\t\t\tremoveHighlight( dropdown_content );\n\t\t\tif (results.query.length && results.tokens.length) {\n\t\t\t\titerate( results.tokens, (tok) => {\n\t\t\t\t\thighlight( dropdown_content, tok.regex);\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\t// helper method for adding templates to dropdown\n\t\tvar add_template = (template:TomTemplateNames) => {\n\t\t\tlet content = self.render(template,{input:query});\n\t\t\tif( content ){\n\t\t\t\tshow_dropdown = true;\n\t\t\t\tdropdown_content.insertBefore(content, dropdown_content.firstChild);\n\t\t\t}\n\t\t\treturn content;\n\t\t};\n\n\n\t\t// add loading message\n\t\tif( self.loading ){\n\t\t\tadd_template('loading');\n\n\t\t// invalid query\n\t\t}else if( !self.settings.shouldLoad.call(self,query) ){\n\t\t\tadd_template('not_loading');\n\n\t\t// add no_results message\n\t\t}else if( results.items.length === 0 ){\n\t\t\tadd_template('no_results');\n\n\t\t}\n\n\n\n\t\t// add create option\n\t\thas_create_option = self.canCreate(query);\n\t\tif (has_create_option) {\n\t\t\tcreate = add_template('option_create');\n\t\t}\n\n\n\t\t// activate\n\t\tself.hasOptions = results.items.length > 0 || has_create_option;\n\t\tif( show_dropdown ){\n\n\t\t\tif (results.items.length > 0) {\n\n\t\t\t\tif( !active_option && self.settings.mode === 'single' && self.items[0] != undefined ){\n\t\t\t\t\tactive_option = self.getOption(self.items[0]);\n\t\t\t\t}\n\n\t\t\t\tif( !dropdown_content.contains(active_option)  ){\n\n\t\t\t\t\tlet active_index = 0;\n\t\t\t\t\tif( create && !self.settings.addPrecedence ){\n\t\t\t\t\t\tactive_index = 1;\n\t\t\t\t\t}\n\t\t\t\t\tactive_option = self.selectable()[active_index] as HTMLElement;\n\t\t\t\t}\n\n\t\t\t}else if( create ){\n\t\t\t\tactive_option = create;\n\t\t\t}\n\n\t\t\tif( triggerDropdown && !self.isOpen ){\n\t\t\t\tself.open();\n\t\t\t\tself.scrollToOption(active_option,'auto');\n\t\t\t}\n\t\t\tself.setActiveOption(active_option);\n\n\t\t}else{\n\t\t\tself.clearActiveOption();\n\t\t\tif( triggerDropdown && self.isOpen ){\n\t\t\t\tself.close(false); // if create_option=null, we want the dropdown to close but not reset the textbox value\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Return list of selectable options\n\t *\n\t */\n\tselectable():NodeList{\n\t\treturn this.dropdown_content.querySelectorAll('[data-selectable]');\n\t}\n\n\n\n\t/**\n\t * Adds an available option. If it already exists,\n\t * nothing will happen. Note: this does not refresh\n\t * the options list dropdown (use `refreshOptions`\n\t * for that).\n\t *\n\t * Usage:\n\t *\n\t *   this.addOption(data)\n\t *\n\t */\n\taddOption( data:TomOption, user_created = false ):false|string {\n\t\tconst self = this;\n\n\t\t// @deprecated 1.7.7\n\t\t// use addOptions( array, user_created ) for adding multiple options\n\t\tif( Array.isArray(data) ){\n\t\t\tself.addOptions( data, user_created);\n\t\t\treturn false;\n\t\t}\n\n\t\tconst key = hash_key(data[self.settings.valueField]);\n\t\tif( key === null || self.options.hasOwnProperty(key) ){\n\t\t\treturn false;\n\t\t}\n\n\t\tdata.$order\t\t\t= data.$order || ++self.order;\n\t\tdata.$id\t\t\t= self.inputId + '-opt-' + data.$order;\n\t\tself.options[key]\t= data;\n\t\tself.lastQuery\t\t= null;\n\n\t\tif( user_created ){\n\t\t\tself.userOptions[key] = user_created;\n\t\t\tself.trigger('option_add', key, data);\n\t\t}\n\n\t\treturn key;\n\t}\n\n\t/**\n\t * Add multiple options\n\t *\n\t */\n\taddOptions( data:TomOption[], user_created = false ):void{\n\t\titerate( data, (dat:TomOption) => {\n\t\t\tthis.addOption(dat, user_created);\n\t\t});\n\t}\n\n\t/**\n\t * @deprecated 1.7.7\n\t */\n\tregisterOption( data:TomOption ):false|string {\n\t\treturn this.addOption(data);\n\t}\n\n\t/**\n\t * Registers an option group to the pool of option groups.\n\t *\n\t * @return {boolean|string}\n\t */\n\tregisterOptionGroup(data:TomOption) {\n\t\tvar key = hash_key(data[this.settings.optgroupValueField]);\n\n\t\tif ( key === null ) return false;\n\n\t\tdata.$order = data.$order || ++this.order;\n\t\tthis.optgroups[key] = data;\n\t\treturn key;\n\t}\n\n\t/**\n\t * Registers a new optgroup for options\n\t * to be bucketed into.\n\t *\n\t */\n\taddOptionGroup(id:string, data:TomOption) {\n\t\tvar hashed_id;\n\t\tdata[this.settings.optgroupValueField] = id;\n\n\t\tif( hashed_id = this.registerOptionGroup(data) ){\n\t\t\tthis.trigger('optgroup_add', hashed_id, data);\n\t\t}\n\t}\n\n\t/**\n\t * Removes an existing option group.\n\t *\n\t */\n\tremoveOptionGroup(id:string) {\n\t\tif (this.optgroups.hasOwnProperty(id)) {\n\t\t\tdelete this.optgroups[id];\n\t\t\tthis.clearCache();\n\t\t\tthis.trigger('optgroup_remove', id);\n\t\t}\n\t}\n\n\t/**\n\t * Clears all existing option groups.\n\t */\n\tclearOptionGroups() {\n\t\tthis.optgroups = {};\n\t\tthis.clearCache();\n\t\tthis.trigger('optgroup_clear');\n\t}\n\n\t/**\n\t * Updates an option available for selection. If\n\t * it is visible in the selected items or options\n\t * dropdown, it will be re-rendered automatically.\n\t *\n\t */\n\tupdateOption(value:string, data:TomOption) {\n\t\tconst self = this;\n\t\tvar item_new;\n\t\tvar index_item;\n\n\t\tconst value_old\t\t= hash_key(value);\n\t\tconst value_new\t\t= hash_key(data[self.settings.valueField]);\n\n\t\t// sanity checks\n\t\tif( value_old === null ) return;\n\n\t\tconst data_old\t\t= self.options[value_old];\n\n\t\tif( data_old == undefined ) return;\n\t\tif( typeof value_new !== 'string' ) throw new Error('Value must be set in option data');\n\n\n\t\tconst option\t\t= self.getOption(value_old);\n\t\tconst item\t\t\t= self.getItem(value_old);\n\n\n\t\tdata.$order = data.$order || data_old.$order;\n\t\tdelete self.options[value_old];\n\n\t\t// invalidate render cache\n\t\t// don't remove existing node yet, we'll remove it after replacing it\n\t\tself.uncacheValue(value_new);\n\n\t\tself.options[value_new] = data;\n\n\t\t// update the option if it's in the dropdown\n\t\tif( option ){\n\t\t\tif( self.dropdown_content.contains(option) ){\n\n\t\t\t\tconst option_new\t= self._render('option', data);\n\t\t\t\treplaceNode(option, option_new);\n\n\t\t\t\tif( self.activeOption === option ){\n\t\t\t\t\tself.setActiveOption(option_new);\n\t\t\t\t}\n\t\t\t}\n\t\t\toption.remove();\n\t\t}\n\n\t\t// update the item if we have one\n\t\tif( item ){\n\t\t\tindex_item = self.items.indexOf(value_old);\n\t\t\tif (index_item !== -1) {\n\t\t\t\tself.items.splice(index_item, 1, value_new);\n\t\t\t}\n\n\t\t\titem_new\t= self._render('item', data);\n\n\t\t\tif( item.classList.contains('active') ) addClasses(item_new,'active');\n\n\t\t\treplaceNode( item, item_new);\n\t\t}\n\n\t\t// invalidate last query because we might have updated the sortField\n\t\tself.lastQuery = null;\n\t}\n\n\t/**\n\t * Removes a single option.\n\t *\n\t */\n\tremoveOption(value:string, silent?:boolean):void {\n\t\tconst self = this;\n\t\tvalue = get_hash(value);\n\n\t\tself.uncacheValue(value);\n\n\t\tdelete self.userOptions[value];\n\t\tdelete self.options[value];\n\t\tself.lastQuery = null;\n\t\tself.trigger('option_remove', value);\n\t\tself.removeItem(value, silent);\n\t}\n\n\t/**\n\t * Clears all options.\n\t */\n\tclearOptions(filter?:TomClearFilter ) {\n\n\t\tconst boundFilter = (filter || this.clearFilter).bind(this);\n\n\t\tthis.loadedSearches\t\t= {};\n\t\tthis.userOptions\t\t= {};\n\t\tthis.clearCache();\n\n\t\tconst selected:TomOptions\t= {};\n\t\titerate(this.options,(option:TomOption,key:string)=>{\n\t\t\tif( boundFilter(option,key as string) ){\n\t\t\t\tselected[key] = option;\n\t\t\t}\n\t\t});\n\n\t\tthis.options = this.sifter.items = selected;\n\t\tthis.lastQuery = null;\n\t\tthis.trigger('option_clear');\n\t}\n\n\t/**\n\t * Used by clearOptions() to decide whether or not an option should be removed\n\t * Return true to keep an option, false to remove\n\t *\n\t */\n\tclearFilter(option:TomOption,value:string){\n\t\tif( this.items.indexOf(value) >= 0 ){\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * Returns the dom element of the option\n\t * matching the given value.\n\t *\n\t */\n\tgetOption(value:undefined|null|boolean|string|number, create:boolean=false):null|HTMLElement {\n\n\t\tconst hashed = hash_key(value);\n\t\tif( hashed === null ) return null;\n\n\t\tconst option = this.options[hashed];\n\t\tif( option != undefined ){\n\n\t\t\tif( option.$div ){\n\t\t\t\treturn option.$div;\n\t\t\t}\n\n\t\t\tif( create ){\n\t\t\t\treturn this._render('option', option);\n\t\t\t}\n\t\t}\n\n\t\treturn null;\n\t}\n\n\t/**\n\t * Returns the dom element of the next or previous dom element of the same type\n\t * Note: adjacent options may not be adjacent DOM elements (optgroups)\n\t *\n\t */\n\tgetAdjacent( option:null|HTMLElement, direction:number, type:string = 'option' ) : HTMLElement|null{\n\t\tvar self = this, all;\n\n\t\tif( !option ){\n\t\t\treturn null;\n\t\t}\n\n\t\tif( type == 'item' ){\n\t\t\tall\t\t\t= self.controlChildren();\n\t\t}else{\n\t\t\tall\t\t\t= self.dropdown_content.querySelectorAll('[data-selectable]');\n\t\t}\n\n\t\tfor( let i = 0; i < all.length; i++ ){\n\t\t\tif( all[i] != option ){\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif( direction > 0 ){\n\t\t\t\treturn all[i+1] as HTMLElement;\n\t\t\t}\n\n\t\t\treturn all[i-1] as HTMLElement;\n\t\t}\n\t\treturn null;\n\t}\n\n\n\t/**\n\t * Returns the dom element of the item\n\t * matching the given value.\n\t *\n\t */\n\tgetItem(item:string|TomItem|null):null|TomItem {\n\n\t\tif( typeof item == 'object' ){\n\t\t\treturn item;\n\t\t}\n\n\t\tvar value = hash_key(item);\n\t\treturn value !== null\n\t\t\t? this.control.querySelector(`[data-value=\"${addSlashes(value)}\"]`)\n\t\t\t: null;\n\t}\n\n\t/**\n\t * \"Selects\" multiple items at once. Adds them to the list\n\t * at the current caret position.\n\t *\n\t */\n\taddItems( values:string|string[], silent?:boolean ):void{\n\t\tvar self = this;\n\n\t\tvar items = Array.isArray(values) ? values : [values];\n\t\titems = items.filter(x => self.items.indexOf(x) === -1);\n\t\tconst last_item = items[items.length - 1];\n\t\titems.forEach(item => {\n\t\t\tself.isPending = (item !== last_item);\n\t\t\tself.addItem(item, silent);\n\t\t});\n\t}\n\n\t/**\n\t * \"Selects\" an item. Adds it to the list\n\t * at the current caret position.\n\t *\n\t */\n\taddItem( value:string, silent?:boolean ):void{\n\t\tvar events = silent ? [] : ['change','dropdown_close'];\n\n\t\tdebounce_events(this, events, () => {\n\t\t\tvar item, wasFull;\n\t\t\tconst self = this;\n\t\t \tconst inputMode = self.settings.mode;\n\t\t\tconst hashed = hash_key(value);\n\n\t\t\tif( hashed && self.items.indexOf(hashed) !== -1 ){\n\n\t\t\t\tif( inputMode === 'single' ){\n\t\t\t\t\tself.close();\n\t\t\t\t}\n\n\t\t\t\tif( inputMode === 'single' || !self.settings.duplicates ){\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (hashed === null || !self.options.hasOwnProperty(hashed)) return;\n\t\t\tif (inputMode === 'single') self.clear(silent);\n\t\t\tif (inputMode === 'multi' && self.isFull()) return;\n\n\t\t\titem = self._render('item', self.options[hashed]);\n\n\t\t\tif( self.control.contains(item) ){ // duplicates\n\t\t\t\titem = item.cloneNode(true) as HTMLElement;\n\t\t\t}\n\n\t\t\twasFull = self.isFull();\n\t\t\tself.items.splice(self.caretPos, 0, hashed);\n\t\t\tself.insertAtCaret(item);\n\n\t\t\tif (self.isSetup) {\n\n\t\t\t\t// update menu / remove the option (if this is not one item being added as part of series)\n\t\t\t\tif( !self.isPending && self.settings.hideSelected ){\n\t\t\t\t\tlet option = self.getOption(hashed);\n\t\t\t\t\tlet next = self.getAdjacent(option, 1);\n\t\t\t\t\tif( next ){\n\t\t\t\t\t\tself.setActiveOption(next);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// refreshOptions after setActiveOption(),\n\t\t\t\t// otherwise setActiveOption() will be called by refreshOptions() with the wrong value\n\t\t\t\tif( !self.isPending && !self.settings.closeAfterSelect ){\n\t\t\t\t\tself.refreshOptions(self.isFocused && inputMode !== 'single');\n\t\t\t\t}\n\n\t\t\t\t// hide the menu if the maximum number of items have been selected or no options are left\n\t\t\t\tif( self.settings.closeAfterSelect != false && self.isFull() ){\n\t\t\t\t\tself.close();\n\t\t\t\t} else if (!self.isPending) {\n\t\t\t\t\tself.positionDropdown();\n\t\t\t\t}\n\n\t\t\t\tself.trigger('item_add', hashed, item);\n\n\t\t\t\tif (!self.isPending) {\n\t\t\t\t\tself.updateOriginalInput({silent: silent});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (!self.isPending || (!wasFull && self.isFull())) {\n\t\t\t\tself.inputState();\n\t\t\t\tself.refreshState();\n\t\t\t}\n\n\t\t});\n\t}\n\n\t/**\n\t * Removes the selected item matching\n\t * the provided value.\n\t *\n\t */\n\tremoveItem( item:string|TomItem|null=null, silent?:boolean ){\n\t\tconst self\t\t= this;\n\t\titem\t\t\t= self.getItem(item);\n\n\t\tif( !item ) return;\n\n\t\tvar i,idx;\n\t\tconst value\t= item.dataset.value;\n\t\ti = nodeIndex(item);\n\n\t\titem.remove();\n\t\tif( item.classList.contains('active') ){\n\t\t\tidx = self.activeItems.indexOf(item);\n\t\t\tself.activeItems.splice(idx, 1);\n\t\t\tremoveClasses(item,'active');\n\t\t}\n\n\t\tself.items.splice(i, 1);\n\t\tself.lastQuery = null;\n\t\tif (!self.settings.persist && self.userOptions.hasOwnProperty(value)) {\n\t\t\tself.removeOption(value, silent);\n\t\t}\n\n\t\tif (i < self.caretPos) {\n\t\t\tself.setCaret(self.caretPos - 1);\n\t\t}\n\n\t\tself.updateOriginalInput({silent: silent});\n\t\tself.refreshState();\n\t\tself.positionDropdown();\n\t\tself.trigger('item_remove', value, item);\n\n\t}\n\n\t/**\n\t * Invokes the `create` method provided in the\n\t * TomSelect options that should provide the data\n\t * for the new item, given the user input.\n\t *\n\t * Once this completes, it will be added\n\t * to the item list.\n\t *\n\t */\n\tcreateItem( input:null|string=null, callback:TomCreateCallback = ()=>{} ):boolean{\n\n\t\t// triggerDropdown parameter @deprecated 2.1.1\n\t\tif( arguments.length === 3 ){\n\t\t\tcallback = arguments[2];\n\t\t}\n\t\tif( typeof callback != 'function' ){\n\t\t\tcallback = () => {};\n\t\t}\n\n\t\tvar self  = this;\n\t\tvar caret = self.caretPos;\n\t\tvar output;\n\t\tinput = input || self.inputValue();\n\n\t\tif (!self.canCreate(input)) {\n\t\t\tcallback();\n\t\t\treturn false;\n\t\t}\n\n\t\tself.lock();\n\n\t\tvar created = false;\n\t\tvar create = (data?:boolean|TomOption) => {\n\t\t\tself.unlock();\n\n\t\t\tif (!data || typeof data !== 'object') return callback();\n\t\t\tvar value = hash_key(data[self.settings.valueField]);\n\t\t\tif( typeof value !== 'string' ){\n\t\t\t\treturn callback();\n\t\t\t}\n\n\t\t\tself.setTextboxValue();\n\t\t\tself.addOption(data,true);\n\t\t\tself.setCaret(caret);\n\t\t\tself.addItem(value);\n\t\t\tcallback(data);\n\t\t\tcreated = true;\n\t\t};\n\n\t\tif( typeof self.settings.create === 'function' ){\n\t\t\toutput = self.settings.create.call(this, input, create);\n\t\t}else{\n\t\t\toutput = {\n\t\t\t\t[self.settings.labelField]: input,\n\t\t\t\t[self.settings.valueField]: input,\n\t\t\t};\n\t\t}\n\n\t\tif( !created ){\n\t\t\tcreate(output);\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Re-renders the selected item lists.\n\t */\n\trefreshItems() {\n\t\tvar self = this;\n\t\tself.lastQuery = null;\n\n\t\tif (self.isSetup) {\n\t\t\tself.addItems(self.items);\n\t\t}\n\n\t\tself.updateOriginalInput();\n\t\tself.refreshState();\n\t}\n\n\t/**\n\t * Updates all state-dependent attributes\n\t * and CSS classes.\n\t */\n\trefreshState() {\n\t\tconst self     = this;\n\n\t\tself.refreshValidityState();\n\n\t\tconst isFull\t= self.isFull();\n\t\tconst isLocked\t= self.isLocked;\n\n\t\tself.wrapper.classList.toggle('rtl',self.rtl);\n\n\n\t\tconst wrap_classList = self.wrapper.classList;\n\n\t\twrap_classList.toggle('focus', self.isFocused)\n\t\twrap_classList.toggle('disabled', self.isDisabled)\n\t\twrap_classList.toggle('readonly', self.isReadOnly)\n\t\twrap_classList.toggle('required', self.isRequired)\n\t\twrap_classList.toggle('invalid', !self.isValid)\n\t\twrap_classList.toggle('locked', isLocked)\n\t\twrap_classList.toggle('full', isFull)\n\t\twrap_classList.toggle('input-active', self.isFocused && !self.isInputHidden)\n\t\twrap_classList.toggle('dropdown-active', self.isOpen)\n\t\twrap_classList.toggle('has-options', isEmptyObject(self.options) )\n\t\twrap_classList.toggle('has-items', self.items.length > 0);\n\n\t}\n\n\n\t/**\n\t * Update the `required` attribute of both input and control input.\n\t *\n\t * The `required` property needs to be activated on the control input\n\t * for the error to be displayed at the right place. `required` also\n\t * needs to be temporarily deactivated on the input since the input is\n\t * hidden and can't show errors.\n\t */\n\trefreshValidityState() {\n\t\tvar self = this;\n\n\t\tif( !self.input.validity ){\n\t\t\treturn;\n\t\t}\n\n\t\tself.isValid = self.input.validity.valid;\n\t\tself.isInvalid = !self.isValid;\n\t}\n\n\t/**\n\t * Determines whether or not more items can be added\n\t * to the control without exceeding the user-defined maximum.\n\t *\n\t * @returns {boolean}\n\t */\n\tisFull() {\n\t\treturn this.settings.maxItems !== null && this.items.length >= this.settings.maxItems;\n\t}\n\n\t/**\n\t * Refreshes the original <select> or <input>\n\t * element to reflect the current state.\n\t *\n\t */\n\tupdateOriginalInput( opts:TomArgObject = {} ){\n\t\tconst self = this;\n\t\tvar option, label;\n\n\t\tconst empty_option = self.input.querySelector('option[value=\"\"]') as HTMLOptionElement;\n\n\t\tif( self.is_select_tag ){\n\n\t\t\tconst selected:HTMLOptionElement[]\t\t= [];\n\t\t\tconst has_selected:number\t\t\t\t= self.input.querySelectorAll('option:checked').length;\n\n\t\t\tfunction AddSelected(option_el:HTMLOptionElement|null, value:string, label:string):HTMLOptionElement{\n\n\t\t\t\tif( !option_el ){\n\t\t\t\t\toption_el = getDom('<option value=\"' + escape_html(value) + '\">' + escape_html(label) + '</option>') as HTMLOptionElement;\n\t\t\t\t}\n\n\t\t\t\t// don't move empty option from top of list\n\t\t\t\t// fixes bug in firefox https://bugzilla.mozilla.org/show_bug.cgi?id=1725293\n\t\t\t\tif( option_el != empty_option ){\n\t\t\t\t\tself.input.append(option_el);\n\t\t\t\t}\n\n\t\t\t\tselected.push(option_el);\n\n\t\t\t\t// marking empty option as selected can break validation\n\t\t\t\t// fixes https://github.com/orchidjs/tom-select/issues/303\n\t\t\t\tif( option_el != empty_option || has_selected > 0 ){\n\t\t\t\t\toption_el.selected = true;\n\t\t\t\t}\n\n\t\t\t\treturn option_el;\n\t\t\t}\n\n\t\t\t// unselect all selected options\n\t\t\tself.input.querySelectorAll('option:checked').forEach((option_el:Element) => {\n\t\t\t\t(<HTMLOptionElement>option_el).selected = false;\n\t\t\t});\n\n\n\t\t\t// nothing selected?\n\t\t\tif( self.items.length == 0 && self.settings.mode == 'single' ){\n\n\t\t\t\tAddSelected(empty_option, \"\", \"\");\n\n\t\t\t// order selected <option> tags for values in self.items\n\t\t\t}else{\n\n\t\t\t\tself.items.forEach((value)=>{\n\t\t\t\t\toption\t\t\t= self.options[value]!;\n\t\t\t\t\tlabel\t\t\t= option[self.settings.labelField] || '';\n\n\t\t\t\t\tif( selected.includes(option.$option) ){\n\t\t\t\t\t\tconst reuse_opt = self.input.querySelector(`option[value=\"${addSlashes(value)}\"]:not(:checked)`) as HTMLOptionElement;\n\t\t\t\t\t\tAddSelected(reuse_opt, value, label);\n\t\t\t\t\t}else{\n\t\t\t\t\t\toption.$option\t= AddSelected(option.$option, value, label);\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t}\n\n\t\t} else {\n\t\t\tself.input.value = self.getValue() as string;\n\t\t}\n\n\t\tif (self.isSetup) {\n\t\t\tif (!opts.silent) {\n\t\t\t\tself.trigger('change', self.getValue() );\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Shows the autocomplete dropdown containing\n\t * the available options.\n\t */\n\topen() {\n\t\tvar self = this;\n\n\t\tif (self.isLocked || self.isOpen || (self.settings.mode === 'multi' && self.isFull())) return;\n\t\tself.isOpen = true;\n\t\tsetAttr(self.focus_node,{'aria-expanded': 'true'});\n\t\tself.refreshState();\n\t\tapplyCSS(self.dropdown,{visibility: 'hidden', display: 'block'});\n\t\tself.positionDropdown();\n\t\tapplyCSS(self.dropdown,{visibility: 'visible', display: 'block'});\n\t\tself.focus();\n\t\tself.trigger('dropdown_open', self.dropdown);\n\t}\n\n\t/**\n\t * Closes the autocomplete dropdown menu.\n\t */\n\tclose(setTextboxValue=true) {\n\t\tvar self = this;\n\t\tvar trigger = self.isOpen;\n\n\t\tif( setTextboxValue ){\n\n\t\t\t// before blur() to prevent form onchange event\n\t\t\tself.setTextboxValue();\n\n\t\t\tif (self.settings.mode === 'single' && self.items.length) {\n\t\t\t\tself.inputState();\n\t\t\t}\n\t\t}\n\n\t\tself.isOpen = false;\n\t\tsetAttr(self.focus_node,{'aria-expanded': 'false'});\n\t\tapplyCSS(self.dropdown,{display: 'none'});\n\t\tif( self.settings.hideSelected ){\n\t\t\tself.clearActiveOption();\n\t\t}\n\t\tself.refreshState();\n\n\t\tif (trigger) self.trigger('dropdown_close', self.dropdown);\n\t}\n\n\t/**\n\t * Calculates and applies the appropriate\n\t * position of the dropdown if dropdownParent = 'body'.\n\t * Otherwise, position is determined by css\n\t */\n\tpositionDropdown(){\n\n\t\tif( this.settings.dropdownParent !== 'body' ){\n\t\t\treturn;\n\t\t}\n\n\t\tvar context\t\t\t= this.control;\n\t\tvar rect\t\t\t= context.getBoundingClientRect();\n\t\tvar top\t\t\t\t= context.offsetHeight + rect.top  + window.scrollY;\n\t\tvar left\t\t\t= rect.left + window.scrollX;\n\n\n\t\tapplyCSS(this.dropdown,{\n\t\t\twidth : rect.width + 'px',\n\t\t\ttop   : top + 'px',\n\t\t\tleft  : left + 'px'\n\t\t});\n\n\t}\n\n\t/**\n\t * Resets / clears all selected items\n\t * from the control.\n\t *\n\t */\n\tclear(silent?:boolean) {\n\t\tvar self = this;\n\n\t\tif (!self.items.length) return;\n\n\t\tvar items = self.controlChildren();\n\t\titerate(items,(item:TomItem)=>{\n\t\t\tself.removeItem(item,true);\n\t\t});\n\n\t\tself.inputState();\n\t\tif( !silent ) self.updateOriginalInput();\n\t\tself.trigger('clear');\n\t}\n\n\t/**\n\t * A helper method for inserting an element\n\t * at the current caret position.\n\t *\n\t */\n\tinsertAtCaret(el:HTMLElement) {\n\t\tconst self\t\t= this;\n\t\tconst caret\t\t= self.caretPos;\n\t\tconst target\t= self.control;\n\n\t\ttarget.insertBefore(el, target.children[caret] || null);\n\t\tself.setCaret(caret + 1);\n\t}\n\n\t/**\n\t * Removes the current selected item(s).\n\t *\n\t */\n\tdeleteSelection(e:KeyboardEvent):boolean {\n\t\tvar direction, selection, caret, tail;\n\t\tvar self = this;\n\n\t\tdirection = (e && e.keyCode === constants.KEY_BACKSPACE) ? -1 : 1;\n\t\tselection = getSelection(self.control_input);\n\n\n\t\t// determine items that will be removed\n\t\tconst rm_items:TomItem[]\t= [];\n\n\t\tif (self.activeItems.length) {\n\n\t\t\ttail = getTail(self.activeItems, direction);\n\t\t\tcaret = nodeIndex(tail);\n\n\t\t\tif (direction > 0) { caret++; }\n\n\t\t\titerate(self.activeItems, (item:TomItem) => rm_items.push(item) );\n\n\t\t} else if ((self.isFocused || self.settings.mode === 'single') && self.items.length) {\n\t\t\tconst items = self.controlChildren();\n\t\t\tlet rm_item;\n\t\t\tif( direction < 0 && selection.start === 0 && selection.length === 0 ){\n\t\t\t\trm_item = items[self.caretPos - 1];\n\n\t\t\t}else if( direction > 0 && selection.start === self.inputValue().length ){\n\t\t\t\trm_item = items[self.caretPos];\n\t\t\t}\n\n\t\t\tif( rm_item !== undefined ){\n\t\t\t\trm_items.push( rm_item );\n\t\t\t}\n\t\t}\n\n\t\tif( !self.shouldDelete(rm_items,e) ){\n\t\t\treturn false;\n\t\t}\n\n\t\tpreventDefault(e,true);\n\n\t\t// perform removal\n\t\tif (typeof caret !== 'undefined') {\n\t\t\tself.setCaret(caret);\n\t\t}\n\n\t\twhile( rm_items.length ){\n\t\t\tself.removeItem(rm_items.pop());\n\t\t}\n\n\t\tself.inputState();\n\t\tself.positionDropdown();\n\t\tself.refreshOptions(false);\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Return true if the items should be deleted\n\t */\n\tshouldDelete(items:TomItem[],evt:MouseEvent|KeyboardEvent){\n\n\t\tconst values = items.map(item => item.dataset.value);\n\n\t\t// allow the callback to abort\n\t\tif( !values.length || (typeof this.settings.onDelete === 'function' && this.settings.onDelete(values,evt) === false) ){\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/**\n\t * Selects the previous / next item (depending on the `direction` argument).\n\t *\n\t * > 0 - right\n\t * < 0 - left\n\t *\n\t */\n\tadvanceSelection(direction:number, e?:MouseEvent|KeyboardEvent) {\n\t\tvar last_active, adjacent, self = this;\n\n\t\tif (self.rtl) direction *= -1;\n\t\tif( self.inputValue().length ) return;\n\n\n\t\t// add or remove to active items\n\t\tif( isKeyDown(constants.KEY_SHORTCUT,e) || isKeyDown('shiftKey',e) ){\n\n\t\t\tlast_active\t\t\t= self.getLastActive(direction);\n\t\t\tif( last_active ){\n\n\t\t\t\tif( !last_active.classList.contains('active') ){\n\t\t\t\t\tadjacent\t\t\t= last_active;\n\t\t\t\t}else{\n\t\t\t\t\tadjacent\t\t\t= self.getAdjacent(last_active,direction,'item');\n\t\t\t\t}\n\n\t\t\t// if no active item, get items adjacent to the control input\n\t\t\t}else if( direction > 0 ){\n\t\t\t\tadjacent\t\t\t= self.control_input.nextElementSibling;\n\t\t\t}else{\n\t\t\t\tadjacent\t\t\t= self.control_input.previousElementSibling;\n\t\t\t}\n\n\n\t\t\tif( adjacent ){\n\t\t\t\tif( adjacent.classList.contains('active') ){\n\t\t\t\t\tself.removeActiveItem(last_active);\n\t\t\t\t}\n\t\t\t\tself.setActiveItemClass(adjacent); // mark as last_active !! after removeActiveItem() on last_active\n\t\t\t}\n\n\t\t// move caret to the left or right\n\t\t}else{\n\t\t\tself.moveCaret(direction);\n\t\t}\n\t}\n\n\tmoveCaret(direction:number){}\n\n\t/**\n\t * Get the last active item\n\t *\n\t */\n\tgetLastActive(direction?:number){\n\n\t\tlet last_active = this.control.querySelector('.last-active');\n\t\tif( last_active ){\n\t\t\treturn last_active;\n\t\t}\n\n\n\t\tvar result = this.control.querySelectorAll('.active');\n\t\tif( result ){\n\t\t\treturn getTail(result,direction);\n\t\t}\n\t}\n\n\n\t/**\n\t * Moves the caret to the specified index.\n\t *\n\t * The input must be moved by leaving it in place and moving the\n\t * siblings, due to the fact that focus cannot be restored once lost\n\t * on mobile webkit devices\n\t *\n\t */\n\tsetCaret(new_pos:number) {\n\t\tthis.caretPos = this.items.length;\n\t}\n\n\t/**\n\t * Return list of item dom elements\n\t *\n\t */\n\tcontrolChildren():TomItem[]{\n\t\treturn Array.from( this.control.querySelectorAll('[data-ts-item]') ) as TomItem[];\n\t}\n\n\t/**\n\t * Disables user input on the control. Used while\n\t * items are being asynchronously created.\n\t */\n\tlock() {\n\t\tthis.setLocked(true);\n\t}\n\n\t/**\n\t * Re-enables user input on the control.\n\t */\n\tunlock() {\n\t\tthis.setLocked(false);\n\t}\n\n\t/**\n\t * Disable or enable user input on the control\n\t */\n\tsetLocked( lock:boolean = this.isReadOnly || this.isDisabled ){\n\t\tthis.isLocked = lock;\n\t\tthis.refreshState();\n\t}\n\n\t/**\n\t * Disables user input on the control completely.\n\t * While disabled, it cannot receive focus.\n\t */\n\tdisable() {\n\t\tthis.setDisabled(true);\n\t\tthis.close();\n\t}\n\n\t/**\n\t * Enables the control so that it can respond\n\t * to focus and user input.\n\t */\n\tenable() {\n\t\tthis.setDisabled(false);\n\t}\n\n\tsetDisabled(disabled:boolean){\n\t\tthis.focus_node.tabIndex\t\t= disabled ? -1 : this.tabIndex;\n\t\tthis.isDisabled\t\t\t\t\t= disabled;\n\t\tthis.input.disabled\t\t\t\t= disabled;\n\t\tthis.control_input.disabled\t\t= disabled;\n\t\tthis.setLocked();\n\t}\n\n\tsetReadOnly(isReadOnly:boolean){\n\t\tthis.isReadOnly\t\t\t\t\t= isReadOnly;\n\t\tthis.input.readOnly\t\t\t\t= isReadOnly;\n\t\tthis.control_input.readOnly\t\t= isReadOnly;\n\t\tthis.setLocked();\n\t}\n\n\t/**\n\t * Completely destroys the control and\n\t * unbinds all event listeners so that it can\n\t * be garbage collected.\n\t */\n\tdestroy() {\n\t\tvar self = this;\n\t\tvar revertSettings = self.revertSettings;\n\n\t\tself.trigger('destroy');\n\t\tself.off();\n\t\tself.wrapper.remove();\n\t\tself.dropdown.remove();\n\n\t\tself.input.innerHTML = revertSettings.innerHTML;\n\t\tself.input.tabIndex = revertSettings.tabIndex;\n\n\t\tremoveClasses(self.input,'tomselected','ts-hidden-accessible');\n\n\t\tself._destroy();\n\n\t\tdelete self.input.tomselect;\n\t}\n\n\t/**\n\t * A helper method for rendering \"item\" and\n\t * \"option\" templates, given the data.\n\t *\n\t */\n\trender( templateName:TomTemplateNames, data?:any ):null|HTMLElement{\n\t\tvar id, html;\n\t\tconst self = this;\n\n\t\tif( typeof this.settings.render[templateName] !== 'function' ){\n\t\t\treturn null;\n\t\t}\n\n\t\t// render markup\n\t\thtml = self.settings.render[templateName].call(this, data, escape_html);\n\n\t\tif( !html ){\n\t\t\treturn null;\n\t\t}\n\n\t\thtml = getDom( html );\n\n\t\t// add mandatory attributes\n\t\tif (templateName === 'option' || templateName === 'option_create') {\n\n\t\t\tif( data[self.settings.disabledField] ){\n\t\t\t\tsetAttr(html,{'aria-disabled':'true'});\n\t\t\t}else{\n\t\t\t\tsetAttr(html,{'data-selectable': ''});\n\t\t\t}\n\n\t\t}else if (templateName === 'optgroup') {\n\t\t\tid = data.group[self.settings.optgroupValueField];\n\t\t\tsetAttr(html,{'data-group': id});\n\t\t\tif(data.group[self.settings.disabledField]) {\n\t\t\t\tsetAttr(html,{'data-disabled': ''});\n\t\t\t}\n\t\t}\n\n\t\tif (templateName === 'option' || templateName === 'item') {\n\t\t\tconst value\t= get_hash(data[self.settings.valueField]);\n\t\t\tsetAttr(html,{'data-value': value });\n\n\n\t\t\t// make sure we have some classes if a template is overwritten\n\t\t\tif( templateName === 'item' ){\n\t\t\t\taddClasses(html,self.settings.itemClass);\n\t\t\t\tsetAttr(html,{'data-ts-item':''});\n\t\t\t}else{\n\t\t\t\taddClasses(html,self.settings.optionClass);\n\t\t\t\tsetAttr(html,{\n\t\t\t\t\trole:'option',\n\t\t\t\t\tid:data.$id\n\t\t\t\t});\n\n\t\t\t\t// update cache\n\t\t\t\tdata.$div = html;\n\t\t\t\tself.options[value] = data;\n\t\t\t}\n\n\n\t\t}\n\n\t\treturn html;\n\n\t}\n\n\n\t/**\n\t * Type guarded rendering\n\t *\n\t */\n\t_render( templateName:TomTemplateNames, data?:any ):HTMLElement{\n\t\tconst html = this.render(templateName, data);\n\n\t\tif( html == null ){\n\t\t\tthrow 'HTMLElement expected';\n\t\t}\n\t\treturn html;\n\t}\n\n\n\t/**\n\t * Clears the render cache for a template. If\n\t * no template is given, clears all render\n\t * caches.\n\t *\n\t */\n\tclearCache():void{\n\n\t\titerate(this.options, (option:TomOption)=>{\n\t\t\tif( option.$div ){\n\t\t\t\toption.$div.remove();\n\t\t\t\tdelete option.$div;\n\t\t\t}\n\t\t});\n\n\t}\n\n\t/**\n\t * Removes a value from item and option caches\n\t *\n\t */\n\tuncacheValue(value:string){\n\n\t\tconst option_el\t\t\t= this.getOption(value);\n\t\tif( option_el ) option_el.remove();\n\n\t}\n\n\t/**\n\t * Determines whether or not to display the\n\t * create item prompt, given a user input.\n\t *\n\t */\n\tcanCreate( input:string ):boolean {\n\t\treturn this.settings.create && (input.length > 0) && (this.settings.createFilter as TomCreateFilter ).call(this, input);\n\t}\n\n\n\t/**\n\t * Wraps this.`method` so that `new_fn` can be invoked 'before', 'after', or 'instead' of the original method\n\t *\n\t * this.hook('instead','onKeyDown',function( arg1, arg2 ...){\n\t *\n\t * });\n\t */\n\thook( when:string, method:string, new_fn:any ){\n\t\tvar self = this;\n\t\tvar orig_method = self[method];\n\n\n\t\tself[method] = function(){\n\t\t\tvar result, result_new;\n\n\t\t\tif( when === 'after' ){\n\t\t\t\tresult = orig_method.apply(self, arguments);\n\t\t\t}\n\n\t\t\tresult_new = new_fn.apply(self, arguments );\n\n\t\t\tif( when === 'instead' ){\n\t\t\t\treturn result_new;\n\t\t\t}\n\n\t\t\tif( when === 'before' ){\n\t\t\t\tresult = orig_method.apply(self, arguments);\n\t\t\t}\n\n\t\t\treturn result;\n\t\t};\n\n\t}\n\n};\n", "/**\n * Plugin: \"dropdown_input\" (Tom <PERSON>)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport type TomSelect from '../../tom-select.ts';\nimport { nodeIndex, removeClasses } from '../../vanilla.ts';\n\n\nexport default function(this:TomSelect) {\n\tvar self = this;\n\n\t/**\n\t * Moves the caret to the specified index.\n\t *\n\t * The input must be moved by leaving it in place and moving the\n\t * siblings, due to the fact that focus cannot be restored once lost\n\t * on mobile webkit devices\n\t *\n\t */\n\tself.hook('instead','setCaret',(new_pos:number) => {\n\n\t\tif( self.settings.mode === 'single' || !self.control.contains(self.control_input) ) {\n\t\t\tnew_pos = self.items.length;\n\t\t} else {\n\t\t\tnew_pos = Math.max(0, Math.min(self.items.length, new_pos));\n\n\t\t\tif( new_pos != self.caretPos && !self.isPending ){\n\n\t\t\t\tself.controlChildren().forEach((child,j) => {\n\t\t\t\t\tif( j < new_pos ){\n\t\t\t\t\t\tself.control_input.insertAdjacentElement('beforebegin', child );\n\t\t\t\t\t} else {\n\t\t\t\t\t\tself.control.appendChild( child );\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tself.caretPos = new_pos;\n\t});\n\n\tself.hook('instead','moveCaret',(direction:number) => {\n\n\t\tif( !self.isFocused ) return;\n\n\t\t// move caret before or after selected items\n\t\tconst last_active\t\t= self.getLastActive(direction);\n\t\tif( last_active ){\n\t\t\tconst idx = nodeIndex(last_active);\n\t\t\tself.setCaret(direction > 0 ? idx + 1: idx);\n\t\t\tself.setActiveItem();\n\t\t\tremoveClasses(last_active as HTMLElement,'last-active');\n\n\t\t// move caret left or right of current position\n\t\t}else{\n\t\t\tself.setCaret(self.caretPos + direction);\n\n\t\t}\n\n\t});\n\n};\n", "/**\n * Plugin: \"dropdown_input\" (<PERSON>)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport type TomSelect from '../../tom-select.ts';\nimport * as constants from '../../constants.ts';\nimport { getDom, addClasses } from '../../vanilla.ts';\nimport { addEvent, preventDefault } from '../../utils.ts';\n\n\nexport default function(this:TomSelect) {\n\tconst self = this;\n\n\tself.settings.shouldOpen = true; // make sure the input is shown even if there are no options to display in the dropdown\n\n\tself.hook('before','setup',()=>{\n\t\tself.focus_node\t\t= self.control;\n\n\t\taddClasses( self.control_input, 'dropdown-input');\n\n\t \tconst div = getDom('<div class=\"dropdown-input-wrap\">');\n\t\tdiv.append(self.control_input);\n\t\tself.dropdown.insertBefore(div, self.dropdown.firstChild);\n\n\t\t// set a placeholder in the select control\n\t\tconst placeholder = getDom('<input class=\"items-placeholder\" tabindex=\"-1\" />') as HTMLInputElement;\n\t\tplaceholder.placeholder = self.settings.placeholder ||'';\n\t\tself.control.append(placeholder);\n\n\t});\n\n\n\tself.on('initialize',()=>{\n\n\t\t// set tabIndex on control to -1, otherwise [shift+tab] will put focus right back on control_input\n\t\tself.control_input.addEventListener('keydown',(evt:KeyboardEvent) =>{\n\t\t//addEvent(self.control_input,'keydown' as const,(evt:KeyboardEvent) =>{\n\t\t\tswitch( evt.keyCode ){\n\t\t\t\tcase constants.KEY_ESC:\n\t\t\t\t\tif (self.isOpen) {\n\t\t\t\t\t\tpreventDefault(evt,true);\n\t\t\t\t\t\tself.close();\n\t\t\t\t\t}\n\t\t\t\t\tself.clearActiveItems();\n\t\t\t\treturn;\n\t\t\t\tcase constants.KEY_TAB:\n\t\t\t\t\tself.focus_node.tabIndex = -1;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\treturn self.onKeyDown.call(self,evt);\n\t\t});\n\n\t\tself.on('blur',()=>{\n\t\t\tself.focus_node.tabIndex = self.isDisabled ? -1 : self.tabIndex;\n\t\t});\n\n\n\t\t// give the control_input focus when the dropdown is open\n\t\tself.on('dropdown_open',() =>{\n\t\t\tself.control_input.focus();\n\t\t});\n\n\t\t// prevent onBlur from closing when focus is on the control_input\n\t\tconst orig_onBlur = self.onBlur;\n\t\tself.hook('instead','onBlur',(evt?:FocusEvent)=>{\n\t\t\tif( evt && evt.relatedTarget == self.control_input ) return;\n\t\t\treturn orig_onBlur.call(self);\n\t\t});\n\n\t\taddEvent(self.control_input,'blur', () => self.onBlur() );\n\n\t\t// return focus to control to allow further keyboard input\n\t\tself.hook('before','close',() =>{\n\n\t\t\tif( !self.isOpen ) return;\n\t\t\tself.focus_node.focus({preventScroll: true});\n\t\t});\n\n\t});\n\n};\n", "/**\n * Plugin: \"input_autogrow\" (Tom <PERSON>)\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport type TomSelect from '../../tom-select.ts';\n\nexport default function(this:TomSelect) {\n\tvar self = this;\n\tvar orig_deleteSelection = self.deleteSelection;\n\n\tthis.hook('instead','deleteSelection',(evt:KeyboardEvent) => {\n\n\t\tif( self.activeItems.length ){\n\t\t\treturn orig_deleteSelection.call(self, evt);\n\t\t}\n\n\t\treturn false;\n\t});\n\n};\n", "/**\n * Plugin: \"remove_button\" (Tom Select)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport type TomSelect from '../../tom-select.ts';\nimport { getDom } from '../../vanilla.ts';\nimport { escape_html, preventDefault, addEvent } from '../../utils.ts';\nimport { TomOption, TomItem } from '../../types/index.ts';\nimport { RBOptions } from './types.ts';\n\nexport default function(this:TomSelect, userOptions:RBOptions) {\n\n\tconst options = Object.assign({\n\t\t\tlabel     : '&times;',\n\t\t\ttitle     : 'Remove',\n\t\t\tclassName : 'remove',\n\t\t\tappend    : true\n\t\t}, userOptions);\n\n\n\t//options.className = 'remove-single';\n\tvar self\t\t\t= this;\n\n\t// override the render method to add remove button to each item\n\tif( !options.append ){\n\t\treturn;\n\t}\n\n\tvar html = '<a href=\"javascript:void(0)\" class=\"' + options.className + '\" tabindex=\"-1\" title=\"' + escape_html(options.title) + '\">' + options.label + '</a>';\n\n\tself.hook('after','setupTemplates',() => {\n\n\t\tvar orig_render_item = self.settings.render.item;\n\n\t\tself.settings.render.item = (data:TomOption, escape:typeof escape_html) => {\n\n\t\t\tvar item = getDom(orig_render_item.call(self, data, escape)) as TomItem;\n\n\t\t\tvar close_button = getDom(html);\n\t\t\titem.appendChild(close_button);\n\n\t\t\taddEvent(close_button,'mousedown',(evt) => {\n\t\t\t\tpreventDefault(evt,true);\n\t\t\t});\n\n\t\t\taddEvent(close_button,'click',(evt) => {\n\n\t\t\t\tif( self.isLocked ) return;\n\n\t\t\t\t// propagating will trigger the dropdown to show for single mode\n\t\t\t\tpreventDefault(evt,true);\n\n\t\t\t\tif( self.isLocked ) return;\n\t\t\t\tif( !self.shouldDelete([item],evt as MouseEvent) ) return;\n\n\t\t\t\tself.removeItem(item);\n\t\t\t\tself.refreshOptions(false);\n\t\t\t\tself.inputState();\n\t\t\t});\n\n\t\t\treturn item;\n\t\t};\n\n\t});\n\n\n};\n", "/**\n * Plugin: \"restore_on_backspace\" (Tom <PERSON>)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\nimport type TomSelect from '../../tom-select.ts';\nimport { TomOption } from '../../types/index.ts';\n\ntype TPluginOptions = {\n\ttext?:(option:TomOption)=>string,\n};\n\nexport default function(this:TomSelect, userOptions:TPluginOptions) {\n\tconst self = this;\n\n\tconst options = Object.assign({\n\t\ttext: (option:TomOption) => {\n\t\t\treturn option[self.settings.labelField];\n\t\t}\n\t},userOptions);\n\n\tself.on('item_remove',function(value:string){\n\t\tif( !self.isFocused ){\n\t\t\treturn;\n\t\t}\n\n\t\tif( self.control_input.value.trim() === '' ){\n\t\t\tvar option = self.options[value];\n\t\t\tif( option ){\n\t\t\t\tself.setTextboxValue(options.text.call(self, option));\n\t\t\t}\n\t\t}\n\t});\n\n};\n", "import Tom<PERSON>elect from './tom-select.ts';\n\nimport caret_position from './plugins/caret_position/plugin.ts';\nimport dropdown_input from './plugins/dropdown_input/plugin.ts';\nimport no_backspace_delete from './plugins/no_backspace_delete/plugin.ts';\nimport remove_button from './plugins/remove_button/plugin.ts';\nimport restore_on_backspace from './plugins/restore_on_backspace/plugin.ts';\n\nTomSelect.define('caret_position', caret_position);\nTomSelect.define('dropdown_input', dropdown_input);\nTomSelect.define('no_backspace_delete', no_backspace_delete);\nTomSelect.define('remove_button', remove_button);\nTomSelect.define('restore_on_backspace', restore_on_backspace);\n\nexport default TomSelect;\n"], "names": ["forEvents", "events", "callback", "split", "for<PERSON>ach", "event", "MicroEvent", "constructor", "_events", "on", "fct", "event_array", "push", "off", "n", "arguments", "length", "undefined", "splice", "indexOf", "trigger", "args", "self", "apply", "MicroPlugin", "Interface", "plugins", "names", "settings", "requested", "loaded", "define", "name", "fn", "initializePlugins", "key", "queue", "Array", "isArray", "plugin", "options", "hasOwnProperty", "shift", "require", "loadPlugin", "Error", "iterate", "hash_key", "value", "get_hash", "escape_html", "str", "replace", "timeout", "window", "setTimeout", "call", "loadDebounce", "delay", "loading", "Math", "max", "clearTimeout", "loadedSearches", "debounce_events", "types", "type", "event_args", "getSelection", "input", "start", "selectionStart", "selectionEnd", "preventDefault", "evt", "stop", "stopPropagation", "addEvent", "target", "addEventListener", "isKeyDown", "key_name", "count", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "metaKey", "getId", "el", "id", "existing_id", "getAttribute", "setAttribute", "addSlashes", "append", "parent", "node", "object", "getDom", "query", "j<PERSON>y", "HTMLElement", "isHtmlString", "tpl", "document", "createElement", "innerHTML", "trim", "content", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "arg", "escape<PERSON><PERSON>y", "triggerEvent", "dom_el", "event_name", "createEvent", "initEvent", "dispatchEvent", "applyCSS", "css", "Object", "assign", "style", "addClasses", "elmts", "classes", "norm_classes", "classesArray", "cast<PERSON><PERSON><PERSON><PERSON>", "map", "cls", "classList", "add", "removeClasses", "remove", "_classes", "concat", "filter", "Boolean", "parentMatch", "selector", "wrapper", "contains", "matches", "parentNode", "getTail", "list", "direction", "isEmptyObject", "obj", "keys", "nodeIndex", "amongst", "nodeName", "i", "previousElementSibling", "setAttr", "attrs", "val", "attr", "removeAttribute", "replaceNode", "existing", "replacement", "<PERSON><PERSON><PERSON><PERSON>", "highlight", "element", "regex", "RegExp", "highlightText", "match", "data", "spannode", "className", "middlebit", "splitText", "index", "middle<PERSON>lone", "cloneNode", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType", "childNodes", "test", "tagName", "from", "highlightRecursive", "removeHighlight", "elements", "querySelectorAll", "prototype", "normalize", "KEY_A", "KEY_RETURN", "KEY_ESC", "KEY_LEFT", "KEY_UP", "KEY_RIGHT", "KEY_DOWN", "KEY_BACKSPACE", "KEY_DELETE", "KEY_TAB", "IS_MAC", "navigator", "userAgent", "KEY_SHORTCUT", "optgroups", "delimiter", "splitOn", "persist", "diacritics", "create", "createOnBlur", "createFilter", "openOnFocus", "shouldOpen", "maxOptions", "maxItems", "hideSelected", "duplicates", "addPrecedence", "selectOnTab", "preload", "allowEmptyOption", "refreshThrottle", "loadThrottle", "loadingClass", "dataAttr", "optgroupField", "valueField", "labelField", "<PERSON><PERSON><PERSON>", "optgroupLabelField", "optgroupValueField", "lockOptgroupOrder", "sortField", "searchField", "searchConjunction", "mode", "wrapperClass", "controlClass", "dropdownClass", "dropdownContentClass", "itemClass", "optionClass", "dropdownParent", "controlInput", "copyClassesToDropdown", "placeholder", "hidePlaceholder", "shouldLoad", "render", "getSettings", "settings_user", "defaults", "attr_data", "field_label", "field_value", "field_disabled", "field_optgroup", "field_optgroup_label", "field_optgroup_value", "tag_name", "toLowerCase", "option", "textContent", "settings_element", "items", "init_select", "optionsMap", "group_count", "$order", "readData", "dataset", "json", "JSON", "parse", "addOption", "group", "arr", "option_data", "disabled", "$option", "selected", "addGroup", "optgroup", "optgroup_data", "children", "hasAttribute", "child", "init_textbox", "data_raw", "values", "opt", "instance_i", "TomSelect", "input_arg", "user_settings", "order", "isOpen", "isDisabled", "isReadOnly", "isInvalid", "<PERSON><PERSON><PERSON><PERSON>", "isLocked", "isFocused", "isInputHidden", "isSetup", "ignoreFocus", "ignoreHover", "hasOptions", "lastValue", "caretPos", "activeOption", "activeItems", "userOptions", "refreshTimeout", "dir", "tomselect", "computedStyle", "getComputedStyle", "getPropertyValue", "tabIndex", "is_select_tag", "rtl", "inputId", "isRequired", "required", "sifter", "Sifter", "setupCallbacks", "setupTemplates", "control", "dropdown", "_render", "dropdown_content", "inputMode", "control_input", "focus_node", "setup", "passive_event", "passive", "listboxId", "role", "control_id", "label", "label_click", "focus", "bind", "for", "label_id", "width", "classes_plugins", "join", "multiple", "escape_regex", "load", "e", "target_match", "onOptionHover", "capture", "onOptionSelect", "onItemSelect", "onClick", "onKeyDown", "onKeyPress", "onInput", "onBlur", "onFocus", "onPaste", "doc_mousedown", "<PERSON><PERSON><PERSON>", "blur", "inputState", "win_scroll", "positionDropdown", "_destroy", "removeEventListener", "revertSettings", "insertAdjacentElement", "sync", "refreshState", "updateOriginalInput", "refreshItems", "close", "disable", "readOnly", "setReadOnly", "enable", "onChange", "setupOptions", "addOptions", "registerOptionGroup", "templates", "optgroup_header", "escape", "item", "option_create", "no_results", "not_loading", "callbacks", "get_settings", "setValue", "last<PERSON><PERSON>y", "clearActiveItems", "onMouseDown", "pastedText", "inputValue", "splitInput", "piece", "hash", "addItem", "createItem", "character", "String", "fromCharCode", "keyCode", "which", "constants", "selectAll", "open", "next", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setActiveOption", "prev", "canSelect", "activeElement", "advanceSelection", "deleteSelection", "_onInput", "refreshOptions", "wasFocused", "hasFocus", "deactivate", "setActiveItem", "setCaret", "parentElement", "closeAfterSelect", "canLoad", "loadCallback", "clearActiveOption", "setTextboxValue", "changed", "getValue", "silent", "clear", "addItems", "setMaxItems", "eventName", "begin", "end", "swap", "last", "getLastActive", "setActiveItemClass", "removeActiveItem", "last_active", "idx", "scroll", "scrollToOption", "behavior", "height_menu", "clientHeight", "scrollTop", "height_item", "offsetHeight", "y", "getBoundingClientRect", "top", "scroll<PERSON>eh<PERSON>or", "controlChildren", "toggle", "offsetWidth", "getScoreFunction", "getSearchOptions", "sort", "field", "fields", "conjunction", "nesting", "search", "result", "calculateScore", "score", "currentResults", "hashed", "triggerDropdown", "j", "k", "html", "has_create_option", "active_group", "groups", "groups_order", "same_query", "results", "active_option", "show_dropdown", "closest", "min", "getGroupFragment", "group_order_i", "order_group", "fragment", "group_fragment", "createDocumentFragment", "opt_value", "opt_hash", "option_el", "getOption", "includes", "self_optgroup", "$id", "toString", "a", "b", "group_order", "group_heading", "group_options", "header", "group_html", "tokens", "tok", "add_template", "template", "insertBefore", "canCreate", "active_index", "selectable", "user_created", "dat", "registerOption", "addOptionGroup", "hashed_id", "removeOptionGroup", "clearCache", "clearOptionGroups", "updateOption", "item_new", "index_item", "value_old", "value_new", "data_old", "getItem", "uncacheValue", "option_new", "removeOption", "removeItem", "clearOptions", "boundFilter", "clearFilter", "$div", "all", "x", "last_item", "isPending", "<PERSON><PERSON><PERSON>", "isFull", "insertAtCaret", "caret", "output", "lock", "created", "unlock", "refreshValidityState", "wrap_classList", "validity", "valid", "opts", "empty_option", "has_selected", "AddSelected", "reuse_opt", "visibility", "display", "context", "rect", "scrollY", "left", "scrollX", "selection", "tail", "rm_items", "rm_item", "shouldDelete", "pop", "onDelete", "adjacent", "nextElement<PERSON><PERSON>ling", "moveCaret", "new_pos", "setLocked", "setDisabled", "destroy", "templateName", "hook", "when", "method", "new_fn", "orig_method", "result_new", "div", "orig_onBlur", "relatedTarget", "preventScroll", "orig_deleteSelection", "title", "orig_render_item", "close_button", "text", "caret_position", "dropdown_input", "no_backspace_delete", "remove_button", "restore_on_backspace"], "mappings": ";;;;;;;;;;;CAAA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAIA;CACA;CACA;CACA;CACA,SAASA,SAASA,CAACC,MAAa,EAACC,QAA4B,EAAC;GAC7DD,MAAM,CAACE,KAAK,CAAC,KAAK,CAAC,CAACC,OAAO,CAAEC,KAAK,IAAI;KACrCH,QAAQ,CAACG,KAAK,CAAC;CAChB,GAAC,CAAC;CACH;CAEe,MAAMC,UAAU,CAAA;CAI9BC,EAAAA,WAAWA,GAAE;CACZ,IAAA,IAAI,CAACC,OAAO,GAAG,EAAE;CAClB;CAEAC,EAAAA,EAAEA,CAACR,MAAa,EAAES,GAAa,EAAC;CAC/BV,IAAAA,SAAS,CAACC,MAAM,EAAEI,KAAK,IAAK;OAC3B,MAAMM,WAAW,GAAG,IAAI,CAACH,OAAO,CAACH,KAAK,CAAC,IAAI,EAAE;CAC7CM,MAAAA,WAAW,CAACC,IAAI,CAACF,GAAG,CAAC;CACrB,MAAA,IAAI,CAACF,OAAO,CAACH,KAAK,CAAC,GAAGM,WAAW;CAClC,KAAC,CAAC;CACH;CAEAE,EAAAA,GAAGA,CAACZ,MAAa,EAAES,GAAa,EAAC;CAChC,IAAA,IAAII,CAAC,GAAGC,SAAS,CAACC,MAAM;KACxB,IAAIF,CAAC,KAAK,CAAC,EAAE;CACZ,MAAA,IAAI,CAACN,OAAO,GAAG,EAAE;CACjB,MAAA;CACD;CAEAR,IAAAA,SAAS,CAACC,MAAM,EAAEI,KAAK,IAAK;OAE3B,IAAIS,CAAC,KAAK,CAAC,EAAC;CACX,QAAA,OAAO,IAAI,CAACN,OAAO,CAACH,KAAK,CAAC;CAC1B,QAAA;CACD;CAEA,MAAA,MAAMM,WAAW,GAAG,IAAI,CAACH,OAAO,CAACH,KAAK,CAAC;OACvC,IAAIM,WAAW,KAAKM,SAAS,EAAG;OAEhCN,WAAW,CAACO,MAAM,CAACP,WAAW,CAACQ,OAAO,CAACT,GAAG,CAAC,EAAE,CAAC,CAAC;CAC/C,MAAA,IAAI,CAACF,OAAO,CAACH,KAAK,CAAC,GAAGM,WAAW;CAClC,KAAC,CAAC;CACH;CAEAS,EAAAA,OAAOA,CAACnB,MAAa,EAAE,GAAGoB,IAAQ,EAAC;KAClC,IAAIC,IAAI,GAAG,IAAI;CAEftB,IAAAA,SAAS,CAACC,MAAM,EAAEI,KAAK,IAAK;CAC3B,MAAA,MAAMM,WAAW,GAAGW,IAAI,CAACd,OAAO,CAACH,KAAK,CAAC;OACvC,IAAIM,WAAW,KAAKM,SAAS,EAAG;CAChCN,MAAAA,WAAW,CAACP,OAAO,CAACM,GAAG,IAAI;CAC1BA,QAAAA,GAAG,CAACa,KAAK,CAACD,IAAI,EAAED,IAAK,CAAC;CACvB,OAAC,CAAC;CAEH,KAAC,CAAC;CACH;CACD;;CCxEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAmBe,SAASG,WAAWA,CAACC,SAAc,EAAE;CAEnDA,EAAAA,SAAS,CAACC,OAAO,GAAG,EAAE;CAEtB,EAAA,OAAO,cAAcD,SAAS,CAAA;CAAAlB,IAAAA,WAAAA,CAAA,GAAAc,IAAA,EAAA;CAAA,MAAA,KAAA,CAAA,GAAAA,IAAA,CAAA;CAAA,MAAA,IAAA,CAEtBK,OAAO,GAAY;CACzBC,QAAAA,KAAK,EAAO,EAAE;SACdC,QAAQ,EAAI,EAAE;SACdC,SAAS,EAAG,EAAE;CACdC,QAAAA,MAAM,EAAM;QACZ;CAAA;CAED;CACF;CACA;CACA;CACA;CACE,IAAA,OAAOC,MAAMA,CAACC,IAAW,EAAEC,EAAqC,EAAC;CAChER,MAAAA,SAAS,CAACC,OAAO,CAACM,IAAI,CAAC,GAAG;CACzB,QAAA,MAAM,EAAGA,IAAI;CACb,QAAA,IAAI,EAAKC;QACT;CACF;;CAGA;CACF;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;KACEC,iBAAiBA,CAACR,OAA0C,EAAE;OAC7D,IAAIS,GAAG,EAAEH,IAAI;OACb,MAAMV,IAAI,GAAI,IAAI;OAClB,MAAMc,KAAc,GAAG,EAAE;CAEzB,MAAA,IAAIC,KAAK,CAACC,OAAO,CAACZ,OAAO,CAAC,EAAE;CAC3BA,QAAAA,OAAO,CAACtB,OAAO,CAAEmC,MAAyB,IAAG;CAC5C,UAAA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;CAC/BH,YAAAA,KAAK,CAACxB,IAAI,CAAC2B,MAAM,CAAC;CACnB,WAAC,MAAM;CACNjB,YAAAA,IAAI,CAACI,OAAO,CAACE,QAAQ,CAACW,MAAM,CAACP,IAAI,CAAC,GAAGO,MAAM,CAACC,OAAO;CACnDJ,YAAAA,KAAK,CAACxB,IAAI,CAAC2B,MAAM,CAACP,IAAI,CAAC;CACxB;CACD,SAAC,CAAC;QACF,MAAM,IAAIN,OAAO,EAAE;SACnB,KAAKS,GAAG,IAAIT,OAAO,EAAE;CACpB,UAAA,IAAIA,OAAO,CAACe,cAAc,CAACN,GAAG,CAAC,EAAE;aAChCb,IAAI,CAACI,OAAO,CAACE,QAAQ,CAACO,GAAG,CAAC,GAAGT,OAAO,CAACS,GAAG,CAAC;CACzCC,YAAAA,KAAK,CAACxB,IAAI,CAACuB,GAAG,CAAC;CAChB;CACD;CACD;CAEA,MAAA,OAAOH,IAAI,GAAGI,KAAK,CAACM,KAAK,EAAE,EAAE;CAC5BpB,QAAAA,IAAI,CAACqB,OAAO,CAACX,IAAI,CAAC;CACnB;CACD;KAEAY,UAAUA,CAACZ,IAAW,EAAE;OACvB,IAAIV,IAAI,GAAM,IAAI;CAClB,MAAA,IAAII,OAAO,GAAGJ,IAAI,CAACI,OAAO;CAC1B,MAAA,IAAIa,MAAM,GAAId,SAAS,CAACC,OAAO,CAACM,IAAI,CAAC;OAErC,IAAI,CAACP,SAAS,CAACC,OAAO,CAACe,cAAc,CAACT,IAAI,CAAC,EAAE;SAC5C,MAAM,IAAIa,KAAK,CAAC,kBAAkB,GAAIb,IAAI,GAAG,UAAU,CAAC;CACzD;CAEAN,MAAAA,OAAO,CAACG,SAAS,CAACG,IAAI,CAAC,GAAG,IAAI;OAC9BN,OAAO,CAACI,MAAM,CAACE,IAAI,CAAC,GAAGO,MAAM,CAACN,EAAE,CAACV,KAAK,CAACD,IAAI,EAAE,CAACA,IAAI,CAACI,OAAO,CAACE,QAAQ,CAACI,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;CACjFN,MAAAA,OAAO,CAACC,KAAK,CAACf,IAAI,CAACoB,IAAI,CAAC;CACzB;;CAEA;CACF;CACA;CACA;KACEW,OAAOA,CAACX,IAAW,EAAE;OACpB,IAAIV,IAAI,GAAG,IAAI;CACf,MAAA,IAAII,OAAO,GAAGJ,IAAI,CAACI,OAAO;OAE1B,IAAI,CAACJ,IAAI,CAACI,OAAO,CAACI,MAAM,CAACW,cAAc,CAACT,IAAI,CAAC,EAAE;CAC9C,QAAA,IAAIN,OAAO,CAACG,SAAS,CAACG,IAAI,CAAC,EAAE;WAC5B,MAAM,IAAIa,KAAK,CAAC,mCAAmC,GAAGb,IAAI,GAAG,IAAI,CAAC;CACnE;CACAV,QAAAA,IAAI,CAACsB,UAAU,CAACZ,IAAI,CAAC;CACtB;CAEA,MAAA,OAAON,OAAO,CAACI,MAAM,CAACE,IAAI,CAAC;CAC5B;IAEA;CAEF;;CCxIA;CACA;CACA;CACA;CACA;CACO,MAAM,cAAc,GAAG,CAAC,KAAK,KAAK;CACzC,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC;CACjC,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;CAC1B,QAAQ,OAAO,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE;CAC7B;CACA,IAAI,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;CACpG,CAAC;CACM,MAAM,eAAe,GAAG,CAAC,KAAK,KAAK;CAC1C,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;CAC/B,QAAQ,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;CAC7B;CACA,IAAI,IAAI,OAAO,GAAG,EAAE;CACpB,IAAI,IAAI,eAAe,GAAG,CAAC;CAC3B,IAAI,MAAM,YAAY,GAAG,MAAM;CAC/B,QAAQ,IAAI,eAAe,GAAG,CAAC,EAAE;CACjC,YAAY,OAAO,IAAI,GAAG,GAAG,eAAe,GAAG,GAAG;CAClD;CACA,KAAK;CACL,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,KAAK;CAC/B,QAAQ,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE;CACnC,YAAY,eAAe,EAAE;CAC7B,YAAY;CACZ;CACA,QAAQ,YAAY,EAAE;CACtB,QAAQ,OAAO,IAAI,IAAI;CACvB,QAAQ,eAAe,GAAG,CAAC;CAC3B,KAAK,CAAC;CACN,IAAI,YAAY,EAAE;CAClB,IAAI,OAAO,OAAO;CAClB,CAAC;CACD;CACA;CACA;CACA;CACA;CACO,MAAM,YAAY,GAAG,CAAC,KAAK,KAAK;CACvC,IAAI,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;CACjC,IAAI,OAAO,cAAc,CAAC,KAAK,CAAC;CAChC,CAAC;CACD;CACA;CACA;CACO,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK;CACxC,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,EAAE,IAAI,KAAK,KAAK,CAAC,MAAM;CACjD,CAAC;CACD;CACA;CACA;CACO,MAAM,YAAY,GAAG,CAAC,GAAG,KAAK;CACrC,IAAI,OAAO,CAAC,GAAG,GAAG,EAAE,EAAE,OAAO,CAAC,oCAAoC,EAAE,MAAM,CAAC;CAC3E,CAAC;CACD;CACA;CACA;CACO,MAAM,cAAc,GAAG,CAAC,KAAK,KAAK;CACzC,IAAI,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,KAAK,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;CACvF,CAAC;CACM,MAAM,aAAa,GAAG,CAAC,GAAG,KAAK;CACtC,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM;CACjC,CAAC;;CChED;CACA;CACA;CACA;CACO,MAAM,aAAa,GAAG,CAAC,KAAK,KAAK;CACxC,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;CAC1B,QAAQ,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC;CACxB,IAAI,IAAI,MAAM,GAAG,EAAE;CACnB,IAAI,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;CACpC,IAAI,MAAM,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC;CACrC,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;CACtC,QAAQ,IAAI,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;CACpC,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;CACzC,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;CACxB,QAAQ,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;CAChC,QAAQ,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;CACpC,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;CACxB,KAAK,CAAC;CACN,IAAI,OAAO,MAAM;CACjB,CAAC;;CCjBM,MAAM,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;CACvC,MAAM,UAAU,GAAG,qCAAqC;CACjD,IAAI,WAAW;CACtB,IAAI,cAAc;CAClB,MAAM,eAAe,GAAG,CAAC;CACzB,MAAM,aAAa,GAAG,EAAE;CACxB,MAAM,eAAe,GAAG;CACxB,IAAI,GAAG,EAAE,IAAI;CACb,IAAI,GAAG,EAAE,GAAG;CACZ,IAAI,GAAG,EAAE,KAAK;CACd,IAAI,IAAI,EAAE,GAAG;CACb,IAAI,IAAI,EAAE,KAAK;CACf,IAAI,IAAI,EAAE,GAAG;CACb,IAAI,IAAI,EAAE,GAAG;CACb,IAAI,IAAI,EAAE,IAAI;CACd,IAAI,IAAI,EAAE,GAAG;CACb,IAAI,GAAG,EAAE,KAAK;CACd,IAAI,GAAG,EAAE,MAAM;CACf,IAAI,GAAG,EAAE,UAAU;CACnB,IAAI,GAAG,EAAE,MAAM;CACf,IAAI,GAAG,EAAE,IAAI;CACb,IAAI,GAAG,EAAE,QAAQ;CACjB,IAAI,GAAG,EAAE,MAAM;CACf,IAAI,GAAG,EAAE,IAAI;CACb,IAAI,GAAG,EAAE,IAAI;CACb,IAAI,GAAG,EAAE,QAAQ;CACjB,IAAI,GAAG,EAAE,UAAU;CACnB,IAAI,GAAG,EAAE,KAAK;CACd,IAAI,GAAG,EAAE,SAAS;CAClB,IAAI,GAAG,EAAE,SAAS;CAClB,IAAI,IAAI,EAAE,GAAG;CACb,IAAI,IAAI,EAAE,GAAG;CACb,IAAI,IAAI,EAAE,GAAG;CACb,IAAI,IAAI,EAAE,GAAG;CACb,IAAI,GAAG,EAAE,QAAQ;CACjB,IAAI,GAAG,EAAE,KAAK;CACd,IAAI,GAAG,EAAE,OAAO;CAChB,IAAI,GAAG,EAAE,OAAO;CAChB,IAAI,GAAG,EAAE,OAAO;CAChB,IAAI,IAAI,EAAE,GAAG;CACb,IAAI,IAAI,EAAE,GAAG;CACb,IAAI,GAAG,EAAE,GAAG;CACZ,IAAI,GAAG,EAAE,KAAK;CACd,IAAI,IAAI,EAAE,GAAG;CACb,IAAI,GAAG,EAAE,GAAG;CACZ,IAAI,GAAG,EAAE,KAAK;CACd,IAAI,GAAG,EAAE,OAAO;CAChB,IAAI,IAAI,EAAE;CACV,CAAC;CACD,KAAK,IAAI,KAAK,IAAI,eAAe,EAAE;CACnC,IAAI,IAAI,OAAO,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE;CAC9C,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC7C,QAAQ,IAAI,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;CAC9C,QAAQ,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK;CACnC;CACA;CACA,MAAM,WAAW,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,UAAU,EAAE,IAAI,CAAC;CAC7F;CACA;CACA;CACO,MAAM,UAAU,GAAG,CAAC,YAAY,KAAK;CAC5C,IAAI,IAAI,WAAW,KAAK,SAAS;CACjC,QAAQ;CACR,IAAI,WAAW,GAAG,WAAW,CAAiB,WAAW,CAAC;CAC1D,CAAC;CACD;CACA;CACA;CACA;CACO,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,MAAM,KAAK,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;CACpE;CACA;CACA;CACA;CACA;CACO,MAAM,SAAS,GAAG,CAAC,GAAG,KAAK;CAClC,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM;CACjC;CACA;CACA;CACA;CACA,IAAI,CAAC,MAAM,EAAE,IAAI,KAAK;CACtB,QAAQ,OAAO,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC;CACxC,KAAK,EAAE,EAAE,CAAC;CACV,CAAC;CACM,MAAM,UAAU,GAAG,CAAC,GAAG,KAAK;CACnC,IAAI,GAAG,GAAG,SAAS,CAAC,GAAG;CACvB,SAAS,WAAW;CACpB,SAAS,OAAO,CAAC,WAAW,EAAE,uBAAuB,IAAI,KAAK;CAC9D,QAAQ,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE;CACxC,KAAK,CAAC;CACN;CACA,IAAI,OAAO,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC;CAChC,CAAC;CACD;CACA;CACA;CACO,UAAU,SAAS,CAAC,WAAW,EAAE;CACxC,IAAI,KAAK,MAAM,CAAC,cAAc,EAAE,cAAc,CAAC,IAAI,WAAW,EAAE;CAChE,QAAQ,KAAK,IAAI,CAAC,GAAG,cAAc,EAAE,CAAC,IAAI,cAAc,EAAE,CAAC,EAAE,EAAE;CAC/D,YAAY,IAAI,QAAQ,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;CACjD,YAAY,IAAI,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC;CAC5C,YAAY,IAAI,MAAM,IAAI,QAAQ,CAAC,WAAW,EAAE,EAAE;CAClD,gBAAgB;CAChB;CACA;CACA;CACA;CACA;CACA;CACA,YAAY,IAAI,MAAM,CAAC,MAAM,GAAG,eAAe,EAAE;CACjD,gBAAgB;CAChB;CACA,YAAY,IAAI,MAAM,CAAC,MAAM,IAAI,CAAC,EAAE;CACpC,gBAAgB;CAChB;CACA,YAAY,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,EAAE;CACvE;CACA;CACA;CACA;CACA;CACA;CACO,MAAM,YAAY,GAAG,CAAC,WAAW,KAAK;CAC7C,IAAI,MAAM,YAAY,GAAG,EAAE;CAC3B,IAAI,MAAM,WAAW,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK;CAC5C;CACA,QAAQ,MAAM,UAAU,GAAG,YAAY,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG,EAAE;CAC5D,QAAQ,MAAM,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,YAAY,CAAC,UAAU,CAAC,GAAG,GAAG,EAAE,IAAI,CAAC;CAC3E,QAAQ,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;CAChC,YAAY;CACZ;CACA,QAAQ,UAAU,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;CAC5C,QAAQ,YAAY,CAAC,MAAM,CAAC,GAAG,UAAU;CACzC,KAAK;CACL,IAAI,KAAK,IAAI,KAAK,IAAI,SAAS,CAAC,WAAW,CAAC,EAAE;CAC9C,QAAQ,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC;CAC/C,QAAQ,WAAW,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,QAAQ,CAAC;CACjD;CACA,IAAI,OAAO,YAAY;CACvB,CAAC;CACD;CACA;CACA;CACA;CACO,MAAM,WAAW,GAAG,CAAC,WAAW,KAAK;CAC5C,IAAI,MAAM,YAAY,GAAG,YAAY,CAAC,WAAW,CAAC;CAClD,IAAI,MAAM,WAAW,GAAG,EAAE;CAC1B,IAAI,IAAI,UAAU,GAAG,EAAE;CACvB,IAAI,KAAK,IAAI,MAAM,IAAI,YAAY,EAAE;CACrC,QAAQ,IAAI,GAAG,GAAG,YAAY,CAAC,MAAM,CAAC;CACtC,QAAQ,IAAI,GAAG,EAAE;CACjB,YAAY,WAAW,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,GAAG,CAAC;CACnD;CACA,QAAQ,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;CAC/B,YAAY,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;CACjD;CACA;CACA,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;CAClD,IAAI,MAAM,eAAe,GAAG,cAAc,CAAC,UAAU,CAAC;CACtD,IAAI,cAAc,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,eAAe,EAAE,GAAG,CAAC;CAC3D,IAAI,OAAO,WAAW;CACtB,CAAC;CACD;CACA;CACA;CACO,MAAM,WAAW,GAAG,CAAC,OAAO,EAAE,eAAe,GAAG,CAAC,KAAK;CAC7D,IAAI,IAAI,cAAc,GAAG,CAAC;CAC1B,IAAI,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK;CACnC,QAAQ,IAAI,WAAW,CAAC,GAAG,CAAC,EAAE;CAC9B,YAAY,cAAc,IAAI,GAAG,CAAC,MAAM;CACxC;CACA,QAAQ,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,GAAG;CACtC,KAAK,CAAC;CACN,IAAI,IAAI,cAAc,IAAI,eAAe,EAAE;CAC3C,QAAQ,OAAO,eAAe,CAAC,OAAO,CAAC;CACvC;CACA,IAAI,OAAO,EAAE;CACb,CAAC;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,MAAM,mBAAmB,GAAG,CAAC,GAAG,EAAE,eAAe,GAAG,CAAC,KAAK;CACjE,IAAI,eAAe,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;CAC/D,IAAI,OAAO,cAAc,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK;CAC9D,QAAQ,OAAO,WAAW,CAAC,OAAO,EAAE,eAAe,CAAC;CACpD,KAAK,CAAC,CAAC;CACP,CAAC;CACD;CACA;CACA;CACA;CACA,MAAM,kBAAkB,GAAG,CAAC,SAAS,EAAE,GAAG,GAAG,IAAI,KAAK;CACtD,IAAI,IAAI,eAAe,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;CACtD,IAAI,OAAO,cAAc,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,KAAK;CACtD,QAAQ,IAAI,GAAG,GAAG,EAAE;CACpB,QAAQ,MAAM,GAAG,GAAG,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,MAAM,EAAE,GAAG,CAAC;CACnE,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;CACtC,YAAY,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE,eAAe,CAAC,CAAC;CACrF;CACA,QAAQ,OAAO,eAAe,CAAC,GAAG,CAAC;CACnC,KAAK,CAAC,CAAC;CACP,CAAC;CACD;CACA;CACA;CACA,MAAM,WAAW,GAAG,CAAC,UAAU,EAAE,SAAS,KAAK;CAC/C,IAAI,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE;CACjC,QAAQ,IAAI,GAAG,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,UAAU,CAAC,GAAG,EAAE;CACxE,YAAY;CACZ;CACA,QAAQ,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,KAAK,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;CAClE,YAAY;CACZ;CACA,QAAQ,IAAI,YAAY,GAAG,UAAU,CAAC,KAAK;CAC3C,QAAQ,MAAM,MAAM,GAAG,CAAC,IAAI,KAAK;CACjC,YAAY,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;CACpD,gBAAgB,IAAI,WAAW,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,IAAI,WAAW,CAAC,MAAM,KAAK,IAAI,CAAC,MAAM,EAAE;CAC5F,oBAAoB,OAAO,KAAK;CAChC;CACA,gBAAgB,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,WAAW,CAAC,MAAM,IAAI,CAAC,EAAE;CACjE,oBAAoB;CACpB;CACA;CACA;CACA;CACA;CACA;CACA,gBAAgB,IAAI,IAAI,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC,KAAK,EAAE;CACpF,oBAAoB,OAAO,IAAI;CAC/B;CACA,gBAAgB,IAAI,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,IAAI,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,EAAE;CACpF,oBAAoB,OAAO,IAAI;CAC/B;CACA;CACA,YAAY,OAAO,KAAK;CACxB,SAAS;CACT,QAAQ,IAAI,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;CAC/C,QAAQ,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;CACjC,YAAY;CACZ;CACA,QAAQ,OAAO,IAAI;CACnB;CACA,IAAI,OAAO,KAAK;CAChB,CAAC;CACD,MAAM,QAAQ,CAAC;CACf,IAAI,KAAK;CACT,IAAI,OAAO;CACX,IAAI,KAAK;CACT,IAAI,GAAG;CACP,IAAI,WAAW,GAAG;CAClB,QAAQ,IAAI,CAAC,KAAK,GAAG,EAAE;CACvB,QAAQ,IAAI,CAAC,OAAO,GAAG,EAAE;CACzB,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAC;CACtB,QAAQ,IAAI,CAAC,GAAG,GAAG,CAAC;CACpB;CACA,IAAI,GAAG,CAAC,IAAI,EAAE;CACd,QAAQ,IAAI,IAAI,EAAE;CAClB,YAAY,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;CACjC,YAAY,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;CAC1C,YAAY,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC;CACzD,YAAY,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC;CACnD;CACA;CACA,IAAI,IAAI,GAAG;CACX,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;CAChD;CACA,IAAI,MAAM,GAAG;CACb,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;CAChC;CACA,IAAI,KAAK,CAAC,QAAQ,EAAE,UAAU,EAAE;CAChC,QAAQ,IAAI,KAAK,GAAG,IAAI,QAAQ,EAAE;CAClC,QAAQ,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAC1D,QAAQ,IAAI,SAAS,GAAG,KAAK,CAAC,GAAG,EAAE;CACnC,QAAQ,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;CAClC,YAAY,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC;CAC3B;CACA,QAAQ,IAAI,WAAW,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,GAAG,SAAS,CAAC,KAAK,CAAC;CACpF,QAAQ,IAAI,cAAc,GAAG,WAAW,CAAC,MAAM;CAC/C,QAAQ,KAAK,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,SAAS,CAAC,KAAK,GAAG,cAAc,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC;CACjI,QAAQ,OAAO,KAAK;CACpB;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,MAAM,UAAU,GAAG,CAAC,GAAG,KAAK;CACnC,IAAI,UAAU,EAAE;CAChB,IAAI,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC;CACxB,IAAI,IAAI,OAAO,GAAG,EAAE;CACpB,IAAI,IAAI,SAAS,GAAG,CAAC,IAAI,QAAQ,EAAE,CAAC;CACpC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACzC,QAAQ,IAAI,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC;CACrC,QAAQ,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC;CAChD,QAAQ,MAAM,IAAI,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;CAC5C,QAAQ,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI;CACjD;CACA;CACA,QAAQ,IAAI,WAAW,GAAG,EAAE;CAC5B,QAAQ,IAAI,WAAW,GAAG,IAAI,GAAG,EAAE;CACnC,QAAQ,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;CAC1C,YAAY,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,EAAE;CAC9C,YAAY,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,IAAI,CAAC,IAAI,UAAU,CAAC,GAAG,IAAI,CAAC,EAAE;CAC9E;CACA,gBAAgB,IAAI,SAAS,EAAE;CAC/B,oBAAoB,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM;CAChD,oBAAoB,QAAQ,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;CAC5F,oBAAoB,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC;CACxC;CACA,qBAAqB;CACrB,oBAAoB,QAAQ,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;CACnF,oBAAoB,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC;CACxC;CACA;CACA,iBAAiB,IAAI,SAAS,EAAE;CAChC,gBAAgB,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC;CACzD,gBAAgB,MAAM,GAAG,GAAG,SAAS,CAAC,MAAM;CAC5C,gBAAgB,KAAK,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,GAAG,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;CACrF,gBAAgB,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC;CACvC;CACA,iBAAiB;CACjB;CACA;CACA,gBAAgB,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC;CACpC;CACA;CACA;CACA,QAAQ,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE;CACpC;CACA,YAAY,WAAW,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;CACrD,gBAAgB,OAAO,CAAC,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,MAAM,EAAE;CAC9C,aAAa,CAAC;CACd,YAAY,KAAK,IAAI,KAAK,IAAI,WAAW,EAAE;CAC3C;CACA,gBAAgB,IAAI,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE;CACnD,oBAAoB;CACpB;CACA,gBAAgB,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC;CACrC;CACA,YAAY;CACZ;CACA;CACA;CACA;CACA;CACA,QAAQ,IAAI,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;CACrE,YAAY,OAAO,IAAI,kBAAkB,CAAC,SAAS,EAAE,KAAK,CAAC;CAC3D,YAAY,IAAI,OAAO,GAAG,IAAI,QAAQ,EAAE;CACxC,YAAY,MAAM,OAAO,GAAG,SAAS,CAAC,CAAC,CAAC;CACxC,YAAY,IAAI,OAAO,EAAE;CACzB,gBAAgB,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;CAC3C;CACA,YAAY,SAAS,GAAG,CAAC,OAAO,CAAC;CACjC;CACA;CACA,IAAI,OAAO,IAAI,kBAAkB,CAAC,SAAS,EAAE,IAAI,CAAC;CAClD,IAAI,OAAO,OAAO;CAClB,CAAC;;CCpXD;CACA;CACA;CACA;CACA;CACA;CACO,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK;CACtC,IAAI,IAAI,CAAC,GAAG;CACZ,QAAQ;CACR,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC;CACpB,CAAC;CACD;CACA;CACA;CACA;CACA;CACA;CACO,MAAM,cAAc,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK;CAC7C,IAAI,IAAI,CAAC,GAAG;CACZ,QAAQ;CACR,IAAI,IAAI,IAAI,EAAE,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;CACrC,IAAI,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,EAAE,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;CACtD,QAAQ;CACR,IAAI,OAAO,GAAG;CACd,CAAC;CACD;CACA;CACA;CACA;CACA;CACO,MAAM,UAAU,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,KAAK;CACpD,IAAI,IAAI,KAAK,EAAE,GAAG;CAClB,IAAI,IAAI,CAAC,KAAK;CACd,QAAQ,OAAO,CAAC;CAChB,IAAI,KAAK,GAAG,KAAK,GAAG,EAAE;CACtB,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI;CAC3B,QAAQ,OAAO,CAAC;CAChB,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;CACnC,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;CAClB,QAAQ,OAAO,CAAC;CAChB,IAAI,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM;CAC9C,IAAI,IAAI,GAAG,KAAK,CAAC;CACjB,QAAQ,KAAK,IAAI,GAAG;CACpB,IAAI,OAAO,KAAK,GAAG,MAAM;CACzB,CAAC;CACD;CACA;CACA;CACA;CACO,MAAM,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,KAAK;CACzC,IAAI,IAAI,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC;CACxB,IAAI,IAAI,OAAO,KAAK,IAAI,UAAU;CAClC,QAAQ,OAAO,KAAK;CACpB,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;CACxC,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC;CAC1B;CACA,CAAC;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,MAAMc,SAAO,GAAG,CAAC,MAAM,EAAE,QAAQ,KAAK;CAC7C,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;CAC/B,QAAQ,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;CAChC;CACA,SAAS;CACT,QAAQ,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;CAChC,YAAY,IAAI,MAAM,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE;CAC5C,gBAAgB,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;CAC1C;CACA;CACA;CACA,CAAC;CACM,MAAM,GAAG,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK;CAC7B,IAAI,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE;CACxD,QAAQ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;CAC3C;CACA,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,WAAW,EAAE;CACvC,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,WAAW,EAAE;CACvC,IAAI,IAAI,CAAC,GAAG,CAAC;CACb,QAAQ,OAAO,CAAC;CAChB,IAAI,IAAI,CAAC,GAAG,CAAC;CACb,QAAQ,OAAO,CAAC,CAAC;CACjB,IAAI,OAAO,CAAC;CACZ,CAAC;;CC3FD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CAGA,MAAM,MAAM,CAAC;CACb,IAAI,KAAK,CAAC;CACV,IAAI,QAAQ;CACZ;CACA;CACA;CACA;CACA;CACA;CACA,IAAI,WAAW,CAAC,KAAK,EAAE,QAAQ,EAAE;CACjC,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK;CAC1B,QAAQ,IAAI,CAAC,QAAQ,GAAG,QAAQ,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE;CACxD;CACA;CACA;CACA;CACA;CACA;CACA;CACA,IAAI,QAAQ,CAAC,KAAK,EAAE,uBAAuB,EAAE,OAAO,EAAE;CACtD,QAAQ,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;CACnC,YAAY,OAAO,EAAE;CACrB,QAAQ,MAAM,MAAM,GAAG,EAAE;CACzB,QAAQ,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC;CACxC,QAAQ,IAAI,WAAW;CACvB,QAAQ,IAAI,OAAO,EAAE;CACrB,YAAY,WAAW,GAAG,IAAI,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC;CAC1G;CACA,QAAQ,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;CAChC,YAAY,IAAI,WAAW;CAC3B,YAAY,IAAI,KAAK,GAAG,IAAI;CAC5B,YAAY,IAAI,KAAK,GAAG,IAAI;CAC5B;CACA,YAAY,IAAI,WAAW,KAAK,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,EAAE;CACxE,gBAAgB,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC;CACtC,gBAAgB,IAAI,GAAG,WAAW,CAAC,CAAC,CAAC;CACrC;CACA,YAAY,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;CACjC,gBAAgB,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;CAC9C,oBAAoB,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI;CACpD;CACA,qBAAqB;CACrB,oBAAoB,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC;CAC9C;CACA,gBAAgB,IAAI,KAAK,IAAI,uBAAuB;CACpD,oBAAoB,KAAK,GAAG,KAAK,GAAG,KAAK;CACzC;CACA,YAAY,MAAM,CAAC,IAAI,CAAC;CACxB,gBAAgB,MAAM,EAAE,IAAI;CAC5B,gBAAgB,KAAK,EAAE,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,IAAI;CAC7D,gBAAgB,KAAK,EAAE,KAAK;CAC5B,aAAa,CAAC;CACd,SAAS,CAAC;CACV,QAAQ,OAAO,MAAM;CACrB;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,IAAI,gBAAgB,CAAC,KAAK,EAAE,OAAO,EAAE;CACrC,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC;CACvD,QAAQ,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;CAC7C;CACA;CACA;CACA;CACA;CACA,IAAI,iBAAiB,CAAC,MAAM,EAAE;CAC9B,QAAQ,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,WAAW,GAAG,MAAM,CAAC,MAAM;CACjE,QAAQ,IAAI,CAAC,WAAW,EAAE;CAC1B,YAAY,OAAO,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE;CAC5C;CACA,QAAQ,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,GAAG,MAAM,CAAC,OAAO,EAAE,WAAW,GAAG,MAAM,CAAC,MAAM,EAAE,SAAS,GAAG,MAAM,CAAC,SAAS;CACjI,QAAQ,IAAI,CAAC,WAAW,EAAE;CAC1B,YAAY,OAAO,YAAY,EAAE,OAAO,CAAC,CAAC,EAAE;CAC5C;CACA;CACA;CACA;CACA;CACA;CACA,QAAQ,MAAM,WAAW,GAAG,CAAC,YAAY;CACzC,YAAY,IAAI,WAAW,KAAK,CAAC,EAAE;CACnC,gBAAgB,OAAO,UAAU,KAAK,EAAE,IAAI,EAAE;CAC9C,oBAAoB,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK;CACjD,oBAAoB,OAAO,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;CACzF,iBAAiB;CACjB;CACA,YAAY,OAAO,UAAU,KAAK,EAAE,IAAI,EAAE;CAC1C,gBAAgB,IAAI,GAAG,GAAG,CAAC;CAC3B;CACA,gBAAgB,IAAI,KAAK,CAAC,KAAK,EAAE;CACjC,oBAAoB,MAAM,KAAK,GAAG,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC;CAC9D,oBAAoB,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,EAAE;CAC/C,wBAAwB,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC;CAChD;CACA,yBAAyB;CACzB,wBAAwB,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;CAC1D;CACA;CACA,qBAAqB;CACrB,oBAAoBA,SAAO,CAAC,OAAO,EAAE,CAAC,MAAM,EAAE,KAAK,KAAK;CACxD,wBAAwB,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,MAAM,CAAC;CAChF,qBAAqB,CAAC;CACtB;CACA,gBAAgB,OAAO,GAAG,GAAG,WAAW;CACxC,aAAa;CACb,SAAS,GAAG;CACZ,QAAQ,IAAI,WAAW,KAAK,CAAC,EAAE;CAC/B,YAAY,OAAO,UAAU,IAAI,EAAE;CACnC,gBAAgB,OAAO,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;CACnD,aAAa;CACb;CACA,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,WAAW,KAAK,KAAK,EAAE;CAClD,YAAY,OAAO,UAAU,IAAI,EAAE;CACnC,gBAAgB,IAAI,KAAK,EAAE,GAAG,GAAG,CAAC;CAClC,gBAAgB,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE;CAC1C,oBAAoB,KAAK,GAAG,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;CACpD,oBAAoB,IAAI,KAAK,IAAI,CAAC;CAClC,wBAAwB,OAAO,CAAC;CAChC,oBAAoB,GAAG,IAAI,KAAK;CAChC;CACA,gBAAgB,OAAO,GAAG,GAAG,WAAW;CACxC,aAAa;CACb;CACA,aAAa;CACb,YAAY,OAAO,UAAU,IAAI,EAAE;CACnC,gBAAgB,IAAI,GAAG,GAAG,CAAC;CAC3B,gBAAgBA,SAAO,CAAC,MAAM,EAAE,CAAC,KAAK,KAAK;CAC3C,oBAAoB,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;CACnD,iBAAiB,CAAC;CAClB,gBAAgB,OAAO,GAAG,GAAG,WAAW;CACxC,aAAa;CACb;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,IAAI,eAAe,CAAC,KAAK,EAAE,OAAO,EAAE;CACpC,QAAQ,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC;CACvD,QAAQ,OAAO,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;CAC5C;CACA,IAAI,gBAAgB,CAAC,MAAM,EAAE;CAC7B,QAAQ,IAAI,cAAc,EAAE,SAAS,GAAG,EAAE;CAC1C,QAAQ,MAAM,IAAI,GAAG,IAAI,EAAE,OAAO,GAAG,MAAM,CAAC,OAAO,EAAE,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI;CACrI,QAAQ,IAAI,OAAO,IAAI,IAAI,UAAU,EAAE;CACvC,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;CAClC;CACA;CACA;CACA;CACA;CACA;CACA,QAAQ,MAAM,SAAS,GAAG,UAAU,IAAI,EAAE,MAAM,EAAE;CAClD,YAAY,IAAI,IAAI,KAAK,QAAQ;CACjC,gBAAgB,OAAO,MAAM,CAAC,KAAK;CACnC,YAAY,OAAO,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC;CAChE,SAAS;CACT;CACA,QAAQ,IAAI,IAAI,EAAE;CAClB,YAAY,KAAK,IAAI,CAAC,IAAI,IAAI,EAAE;CAChC,gBAAgB,IAAI,MAAM,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,KAAK,QAAQ,EAAE;CAC1D,oBAAoB,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;CACrC;CACA;CACA;CACA;CACA;CACA,QAAQ,IAAI,MAAM,CAAC,KAAK,EAAE;CAC1B,YAAY,cAAc,GAAG,IAAI;CACjC,YAAY,KAAK,IAAI,GAAG,IAAI,SAAS,EAAE;CACvC,gBAAgB,IAAI,GAAG,CAAC,KAAK,KAAK,QAAQ,EAAE;CAC5C,oBAAoB,cAAc,GAAG,KAAK;CAC1C,oBAAoB;CACpB;CACA;CACA,YAAY,IAAI,cAAc,EAAE;CAChC,gBAAgB,SAAS,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,CAAC;CACzE;CACA;CACA;CACA,aAAa;CACb,YAAY,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC;CACzE;CACA;CACA,QAAQ,MAAM,eAAe,GAAG,SAAS,CAAC,MAAM;CAChD,QAAQ,IAAI,CAAC,eAAe,EAAE;CAC9B,YAAY,OAAO,IAAI;CACvB;CACA,QAAQ,OAAO,UAAU,CAAC,EAAE,CAAC,EAAE;CAC/B,YAAY,IAAI,MAAM,EAAE,KAAK;CAC7B,YAAY,KAAK,IAAI,QAAQ,IAAI,SAAS,EAAE;CAC5C,gBAAgB,KAAK,GAAG,QAAQ,CAAC,KAAK;CACtC,gBAAgB,IAAI,UAAU,GAAG,QAAQ,CAAC,SAAS,KAAK,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC;CACvE,gBAAgB,MAAM,GAAG,UAAU,GAAG,GAAG,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;CACnF,gBAAgB,IAAI,MAAM;CAC1B,oBAAoB,OAAO,MAAM;CACjC;CACA,YAAY,OAAO,CAAC;CACpB,SAAS;CACT;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,IAAI,aAAa,CAAC,KAAK,EAAE,QAAQ,EAAE;CACnC,QAAQ,MAAM,OAAO,GAAG,EAAE;CAC1B,QAAQ,IAAI,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,QAAQ,CAAC;CACjD,QAAQ,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC;CACpC,QAAQ,WAAW,CAAC,OAAO,EAAE,YAAY,CAAC;CAC1C;CACA,QAAQ,IAAI,OAAO,CAAC,MAAM,EAAE;CAC5B,YAAY,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC;CAC1C,YAAY,MAAM,MAAM,GAAG,EAAE;CAC7B,YAAY,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;CAC9C,gBAAgB,IAAI,OAAO,KAAK,IAAI,QAAQ,EAAE;CAC9C,oBAAoB,KAAK,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,EAAE;CACvD;CACA,gBAAgB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;CAClC,gBAAgB,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;CAC7E,aAAa,CAAC;CACd,YAAY,OAAO,CAAC,MAAM,GAAG,MAAM;CACnC;CACA,QAAQ,OAAO;CACf,YAAY,OAAO,EAAE,OAAO;CAC5B,YAAY,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE;CAC7C,YAAY,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAC,uBAAuB,EAAE,OAAO,CAAC;CAClF,YAAY,KAAK,EAAE,CAAC;CACpB,YAAY,KAAK,EAAE,EAAE;CACrB,YAAY,OAAO,EAAE,OAAO;CAC5B,YAAY,SAAS,EAAE,CAAC,OAAO,CAAC,OAAO,IAAI,cAAc,GAAG,OAAO;CACnE,SAAS;CACT;CACA;CACA;CACA;CACA;CACA;CACA,IAAI,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE;CAC3B,QAAQ,IAAI,IAAI,GAAG,IAAI,EAAE,KAAK,EAAE,MAAM;CACtC,QAAQ,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,CAAC;CACnD,QAAQ,OAAO,GAAG,MAAM,CAAC,OAAO;CAChC,QAAQ,KAAK,GAAG,MAAM,CAAC,KAAK;CAC5B;CACA,QAAQ,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;CACxE;CACA,QAAQ,IAAI,KAAK,CAAC,MAAM,EAAE;CAC1B,YAAYA,SAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE,KAAK;CAC9C,gBAAgB,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC;CACtC,gBAAgB,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE;CAC3D,oBAAoB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;CACnE;CACA,aAAa,CAAC;CACd;CACA,aAAa;CACb,YAAYA,SAAO,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK;CAC3C,gBAAgB,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;CAC3D,aAAa,CAAC;CACd;CACA,QAAQ,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC;CACrD,QAAQ,IAAI,OAAO;CACnB,YAAY,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC;CACtC;CACA,QAAQ,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM;CAC1C,QAAQ,IAAI,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,EAAE;CAC/C,YAAY,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC;CAC/D;CACA,QAAQ,OAAO,MAAM;CACrB;CACA;CACA;;CCvSA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,MAAMC,QAAQ,GAAIC,KAA0C,IAAiB;GACnF,IAAI,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAO,IAAI;GAC/D,OAAOC,QAAQ,CAACD,KAAK,CAAC;CACvB,CAAC;CAEM,MAAMC,QAAQ,GAAID,KAA2B,IAAY;GAC/D,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE,OAAOA,KAAK,GAAG,GAAG,GAAG,GAAG;GACxD,OAAOA,KAAK,GAAG,EAAE;CAClB,CAAC;;CAED;CACA;CACA;CACA;CACO,MAAME,WAAW,GAAIC,GAAU,IAAY;CACjD,EAAA,OAAO,CAACA,GAAG,GAAG,EAAE,EACdC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CACtBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;CAC1B,CAAC;;CAGD;CACA;CACA;CACO,MAAMC,OAAO,GAAGA,CAACpB,EAAW,EAACoB,OAAc,KAAoB;GACrE,IAAIA,OAAO,GAAG,CAAC,EAAE;CAChB,IAAA,OAAOC,MAAM,CAACC,UAAU,CAACtB,EAAE,EAACoB,OAAO,CAAC;CACrC;CAEApB,EAAAA,EAAE,CAACuB,IAAI,CAAC,IAAI,CAAC;CACb,EAAA,OAAO,IAAI;CACZ,CAAC;;CAED;CACA;CACA;CACA;CACO,MAAMC,YAAY,GAAGA,CAACxB,EAAkD,EAACyB,KAAY,KAAK;CAChG,EAAA,IAAIL,OAA2C;CAC/C,EAAA,OAAO,UAAyBL,KAAY,EAAC9C,QAAwB,EAAE;KACtE,IAAIoB,IAAI,GAAG,IAAI;CAEf,IAAA,IAAI+B,OAAO,EAAE;CACZ/B,MAAAA,IAAI,CAACqC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACvC,IAAI,CAACqC,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;OAC5CG,YAAY,CAACT,OAAO,CAAC;CACtB;KACAA,OAAO,GAAGE,UAAU,CAAC,YAAW;CAC/BF,MAAAA,OAAO,GAAG,IAAI;CACd/B,MAAAA,IAAI,CAACyC,cAAc,CAACf,KAAK,CAAC,GAAG,IAAI;OACjCf,EAAE,CAACuB,IAAI,CAAClC,IAAI,EAAE0B,KAAK,EAAE9C,QAAQ,CAAC;MAE9B,EAAEwD,KAAK,CAAC;IACT;CACF,CAAC;;CAGD;CACA;CACA;CACA;CACA;CACO,MAAMM,eAAe,GAAGA,CAAE1C,IAAc,EAAE2C,KAAc,EAAEhC,EAAa,KAAM;CACnF,EAAA,IAAIiC,IAAW;CACf,EAAA,IAAI9C,OAAO,GAAGE,IAAI,CAACF,OAAO;GAC1B,IAAI+C,UAAiC,GAAG,EAAE;;CAE1C;GACA7C,IAAI,CAACF,OAAO,GAAG,YAAU;CACxB,IAAA,IAAI8C,IAAI,GAAGnD,SAAS,CAAC,CAAC,CAAC;KACvB,IAAIkD,KAAK,CAAC9C,OAAO,CAAC+C,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;CAC/BC,MAAAA,UAAU,CAACD,IAAI,CAAC,GAAGnD,SAAS;CAC7B,KAAC,MAAM;CACN,MAAA,OAAOK,OAAO,CAACG,KAAK,CAACD,IAAI,EAAEP,SAAS,CAAC;CACtC;IACA;;CAED;CACAkB,EAAAA,EAAE,CAACV,KAAK,CAACD,IAAI,EAAE,EAAE,CAAC;GAClBA,IAAI,CAACF,OAAO,GAAGA,OAAO;;CAEtB;GACA,KAAK8C,IAAI,IAAID,KAAK,EAAE;KACnB,IAAIC,IAAI,IAAIC,UAAU,EAAE;OACvB/C,OAAO,CAACG,KAAK,CAACD,IAAI,EAAE6C,UAAU,CAACD,IAAI,CAAC,CAAC;CACtC;CACD;CACD,CAAC;;CAGD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,MAAME,YAAY,GAAIC,KAAsB,IAAuC;GACzF,OAAO;CACNC,IAAAA,KAAK,EAAGD,KAAK,CAACE,cAAc,IAAI,CAAC;CACjCvD,IAAAA,MAAM,EAAG,CAACqD,KAAK,CAACG,YAAY,IAAE,CAAC,KAAKH,KAAK,CAACE,cAAc,IAAE,CAAC;IAC3D;CACF,CAAC;;CAGD;CACA;CACA;CACA;CACO,MAAME,cAAc,GAAGA,CAACC,GAAU,EAAEC,IAAY,GAAC,KAAK,KAAU;CACtE,EAAA,IAAID,GAAG,EAAE;KACRA,GAAG,CAACD,cAAc,EAAE;CACpB,IAAA,IAAIE,IAAI,EAAE;OACTD,GAAG,CAACE,eAAe,EAAE;CACtB;CACD;CACD,CAAC;;CAGD;CACA;CACA;CACA;CACO,MAAMC,QAAQ,GAAGA,CAACC,MAAkB,EAAEZ,IAAW,EAAEhE,QAA2C,EAAEsC,OAAe,KAAU;GAC/HsC,MAAM,CAACC,gBAAgB,CAACb,IAAI,EAAChE,QAAQ,EAACsC,OAAO,CAAC;CAC/C,CAAC;;CAGD;CACA;CACA;CACA;CACA;CACA;CACO,MAAMwC,SAAS,GAAGA,CAAEC,QAAyC,EAAEP,GAA6B,KAAM;GAExG,IAAI,CAACA,GAAG,EAAE;CACT,IAAA,OAAO,KAAK;CACb;CAEA,EAAA,IAAI,CAACA,GAAG,CAACO,QAAQ,CAAC,EAAE;CACnB,IAAA,OAAO,KAAK;CACb;CAEA,EAAA,IAAIC,KAAK,GAAG,CAACR,GAAG,CAACS,MAAM,GAAC,CAAC,GAAC,CAAC,KAAKT,GAAG,CAACU,OAAO,GAAC,CAAC,GAAC,CAAC,CAAC,IAAIV,GAAG,CAACW,QAAQ,GAAC,CAAC,GAAC,CAAC,CAAC,IAAIX,GAAG,CAACY,OAAO,GAAC,CAAC,GAAC,CAAC,CAAC;GAEzF,IAAIJ,KAAK,KAAK,CAAC,EAAE;CAChB,IAAA,OAAO,IAAI;CACZ;CAEA,EAAA,OAAO,KAAK;CACb,CAAC;;CAGD;CACA;CACA;CACA;CACA;CACO,MAAMK,KAAK,GAAGA,CAACC,EAAU,EAACC,EAAS,KAAK;CAC9C,EAAA,MAAMC,WAAW,GAAGF,EAAE,CAACG,YAAY,CAAC,IAAI,CAAC;CACzC,EAAA,IAAID,WAAW,EAAE;CAChB,IAAA,OAAOA,WAAW;CACnB;CAEAF,EAAAA,EAAE,CAACI,YAAY,CAAC,IAAI,EAACH,EAAE,CAAC;CACxB,EAAA,OAAOA,EAAE;CACV,CAAC;;CAGD;CACA;CACA;CACO,MAAMI,UAAU,GAAI1C,GAAU,IAAY;CAChD,EAAA,OAAOA,GAAG,CAACC,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;CACtC,CAAC;;CAED;CACA;CACA;CACO,MAAM0C,MAAM,GAAGA,CAAEC,MAA+B,EAAEC,IAAgC,KAAU;CAClG,EAAA,IAAIA,IAAI,EAAGD,MAAM,CAACD,MAAM,CAACE,IAAI,CAAC;CAC/B,CAAC;;CAED;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,MAAMlD,OAAO,GAAGA,CAACmD,MAA4B,EAAE/F,QAAiC,KAAK;CAE3F,EAAA,IAAKmC,KAAK,CAACC,OAAO,CAAC2D,MAAM,CAAC,EAAE;CAC3BA,IAAAA,MAAM,CAAC7F,OAAO,CAACF,QAAQ,CAAC;CAEzB,GAAC,MAAI;CAEJ,IAAA,KAAK,IAAIiC,GAAG,IAAI8D,MAAM,EAAE;CACvB,MAAA,IAAIA,MAAM,CAACxD,cAAc,CAACN,GAAG,CAAC,EAAE;CAC/BjC,QAAAA,QAAQ,CAAC+F,MAAM,CAAC9D,GAAG,CAAC,EAAEA,GAAG,CAAC;CAC3B;CACD;CACD;CACD,CAAC;;CClOD;CACA;CACA;CACA;CACA;CACA;CACO,MAAM+D,MAAM,GAAKC,KAAS,IAAkB;GAElD,IAAIA,KAAK,CAACC,MAAM,EAAE;KACjB,OAAOD,KAAK,CAAC,CAAC,CAAC;CAChB;GAEA,IAAIA,KAAK,YAAYE,WAAW,EAAE;CACjC,IAAA,OAAOF,KAAK;CACb;CAEA,EAAA,IAAIG,YAAY,CAACH,KAAK,CAAC,EAAE;CACxB,IAAA,IAAII,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;KAC5CF,GAAG,CAACG,SAAS,GAAGP,KAAK,CAACQ,IAAI,EAAE,CAAC;CAC7B,IAAA,OAAOJ,GAAG,CAACK,OAAO,CAACC,UAAU;CAC9B;CAEA,EAAA,OAAOL,QAAQ,CAACM,aAAa,CAACX,KAAK,CAAC;CACrC,CAAC;CAEM,MAAMG,YAAY,GAAIS,GAAO,IAAc;CACjD,EAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAAC5F,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;CACrD,IAAA,OAAO,IAAI;CACZ;CACA,EAAA,OAAO,KAAK;CACb,CAAC;CAEM,MAAM6F,WAAW,GAAIb,KAAY,IAAY;CACnD,EAAA,OAAOA,KAAK,CAAC/C,OAAO,CAAC,SAAS,EAAE,MAAM,CAAC;CACxC,CAAC;;CAED;CACA;CACA;CACA;CACO,MAAM6D,YAAY,GAAGA,CAAEC,MAAkB,EAAEC,UAAiB,KAAW;CAC7E,EAAA,IAAI9G,KAAK,GAAGmG,QAAQ,CAACY,WAAW,CAAC,YAAY,CAAC;GAC9C/G,KAAK,CAACgH,SAAS,CAACF,UAAU,EAAE,IAAI,EAAE,KAAK,CAAC;CACxCD,EAAAA,MAAM,CAACI,aAAa,CAACjH,KAAK,CAAC;CAC5B,CAAC;;CAED;CACA;CACA;CACA;CACO,MAAMkH,QAAQ,GAAGA,CAAEL,MAAkB,EAAEM,GAAoC,KAAU;GAC3FC,MAAM,CAACC,MAAM,CAACR,MAAM,CAACS,KAAK,EAAEH,GAAG,CAAC;CACjC,CAAC;;CAGD;CACA;CACA;CACA;CACO,MAAMI,UAAU,GAAGA,CAAEC,KAA+B,EAAE,GAAGC,OAA2B,KAAM;CAEhG,EAAA,IAAIC,YAAY,GAAIC,YAAY,CAACF,OAAO,CAAC;CACzCD,EAAAA,KAAK,GAAMI,WAAW,CAACJ,KAAK,CAAC;CAE7BA,EAAAA,KAAK,CAACK,GAAG,CAAE1C,EAAE,IAAI;CAChBuC,IAAAA,YAAY,CAACG,GAAG,CAAEC,GAAG,IAAI;CACxB3C,MAAAA,EAAE,CAAC4C,SAAS,CAACC,GAAG,CAAEF,GAAI,CAAC;CACxB,KAAC,CAAC;CACH,GAAC,CAAC;CACH,CAAC;;CAED;CACA;CACA;CACA;CACQ,MAAMG,aAAa,GAAGA,CAAET,KAA+B,EAAE,GAAGC,OAA2B,KAAM;CAEnG,EAAA,IAAIC,YAAY,GAAIC,YAAY,CAACF,OAAO,CAAC;CAC1CD,EAAAA,KAAK,GAAMI,WAAW,CAACJ,KAAK,CAAC;CAE7BA,EAAAA,KAAK,CAACK,GAAG,CAAE1C,EAAE,IAAI;CAChBuC,IAAAA,YAAY,CAACG,GAAG,CAACC,GAAG,IAAI;CACtB3C,MAAAA,EAAE,CAAC4C,SAAS,CAACG,MAAM,CAAEJ,GAAI,CAAC;CAC5B,KAAC,CAAC;CACF,GAAC,CAAC;CACH,CAAC;;CAGF;CACA;CACA;CACA;CACO,MAAMH,YAAY,GAAI3G,IAAwB,IAAc;GAClE,IAAIyG,OAAgB,GAAG,EAAE;CACzBhF,EAAAA,OAAO,CAAEzB,IAAI,EAAGmH,QAAQ,IAAI;CAC3B,IAAA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;OACjCA,QAAQ,GAAGA,QAAQ,CAAC7B,IAAI,EAAE,CAACxG,KAAK,CAAC,cAAc,CAAC;CACjD;CACA,IAAA,IAAIkC,KAAK,CAACC,OAAO,CAACkG,QAAQ,CAAC,EAAE;CAC5BV,MAAAA,OAAO,GAAGA,OAAO,CAACW,MAAM,CAACD,QAAQ,CAAC;CACnC;CACD,GAAC,CAAC;CAEF,EAAA,OAAOV,OAAO,CAACY,MAAM,CAACC,OAAO,CAAC;CAC/B,CAAC;;CAGD;CACA;CACA;CACA;CACO,MAAMV,WAAW,GAAIlB,GAAO,IAAgB;CAClD,EAAA,IAAI,CAAC1E,KAAK,CAACC,OAAO,CAACyE,GAAG,CAAC,EAAE;KACvBA,GAAG,GAAG,CAACA,GAAG,CAAC;CACZ;CACD,EAAA,OAAOA,GAAG;CACX,CAAC;;CAGD;CACA;CACA;CACA;CACA;CACO,MAAM6B,WAAW,GAAGA,CAAE9D,MAAuB,EAAE+D,QAAe,EAAEC,OAAoB,KAAuB;GAEjH,IAAIA,OAAO,IAAI,CAACA,OAAO,CAACC,QAAQ,CAACjE,MAAM,CAAC,EAAE;CACzC,IAAA;CACD;CAEA,EAAA,OAAOA,MAAM,IAAIA,MAAM,CAACkE,OAAO,EAAE;CAEhC,IAAA,IAAIlE,MAAM,CAACkE,OAAO,CAACH,QAAQ,CAAC,EAAE;CAC7B,MAAA,OAAO/D,MAAM;CACd;KAEAA,MAAM,GAAGA,MAAM,CAACmE,UAAyB;CAC1C;CACD,CAAC;;CAGD;CACA;CACA;CACA;CACA;CACA;CACA;CACO,MAAMC,OAAO,GAAGA,CAAEC,IAAwB,EAAEC,SAAgB,GAAC,CAAC,KAAU;GAE9E,IAAIA,SAAS,GAAG,CAAC,EAAE;CAClB,IAAA,OAAOD,IAAI,CAACA,IAAI,CAACnI,MAAM,GAAC,CAAC,CAAC;CAC3B;GAEA,OAAOmI,IAAI,CAAC,CAAC,CAAC;CACf,CAAC;;CAED;CACA;CACA;CACA;CACO,MAAME,aAAa,GAAIC,GAAU,IAAa;GACpD,OAAQ7B,MAAM,CAAC8B,IAAI,CAACD,GAAG,CAAC,CAACtI,MAAM,KAAK,CAAC;CACtC,CAAC;;CAGD;CACA;CACA;CACA;CACO,MAAMwI,SAAS,GAAGA,CAAEhE,EAAe,EAAEiE,OAAe,KAAa;CACvE,EAAA,IAAI,CAACjE,EAAE,EAAE,OAAO,CAAC,CAAC;CAElBiE,EAAAA,OAAO,GAAGA,OAAO,IAAIjE,EAAE,CAACkE,QAAQ;GAEhC,IAAIC,CAAC,GAAG,CAAC;CACT,EAAA,OAAOnE,EAAE,GAAGA,EAAE,CAACoE,sBAAsB,EAAE;CAEtC,IAAA,IAAIpE,EAAE,CAACwD,OAAO,CAACS,OAAO,CAAC,EAAE;CACxBE,MAAAA,CAAC,EAAE;CACJ;CACD;CACA,EAAA,OAAOA,CAAC;CACT,CAAC;;CAGD;CACA;CACA;CACA;CACO,MAAME,OAAO,GAAGA,CAACrE,EAAU,EAACsE,KAA2C,KAAK;CAClFhH,EAAAA,OAAO,CAAEgH,KAAK,EAAC,CAACC,GAAG,EAACC,IAAI,KAAK;KAC5B,IAAID,GAAG,IAAI,IAAI,EAAE;CAChBvE,MAAAA,EAAE,CAACyE,eAAe,CAACD,IAAc,CAAC;CACnC,KAAC,MAAI;OACJxE,EAAE,CAACI,YAAY,CAACoE,IAAI,EAAY,EAAE,GAACD,GAAG,CAAC;CACxC;CACD,GAAC,CAAC;CACH,CAAC;;CAGD;CACA;CACA;CACO,MAAMG,WAAW,GAAGA,CAAEC,QAAa,EAAEC,WAAgB,KAAM;CACjE,EAAA,IAAID,QAAQ,CAAClB,UAAU,EAAGkB,QAAQ,CAAClB,UAAU,CAACoB,YAAY,CAACD,WAAW,EAAED,QAAQ,CAAC;CAClF,CAAC;;CCjND;CACA;CACA;CACA;CACA;CACA;CACA;;CAKO,MAAMG,SAAS,GAAGA,CAACC,OAAmB,EAAEC,KAAmB,KAAK;GAEtE,IAAIA,KAAK,KAAK,IAAI,EAAG;;CAErB;CACA,EAAA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;CAE9B,IAAA,IAAI,CAACA,KAAK,CAACxJ,MAAM,EAAG;CACpBwJ,IAAAA,KAAK,GAAG,IAAIC,MAAM,CAACD,KAAK,EAAE,GAAG,CAAC;CAC/B;;CAGA;CACA;GACA,MAAME,aAAa,GAAK1E,IAAS,IAAa;KAE7C,IAAI2E,KAAK,GAAG3E,IAAI,CAAC4E,IAAI,CAACD,KAAK,CAACH,KAAK,CAAC;KAClC,IAAIG,KAAK,IAAI3E,IAAI,CAAC4E,IAAI,CAAC5J,MAAM,GAAG,CAAC,EAAE;CAClC,MAAA,IAAI6J,QAAQ,GAAIrE,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;OAC9CoE,QAAQ,CAACC,SAAS,GAAG,WAAW;OAChC,IAAIC,SAAS,GAAI/E,IAAI,CAACgF,SAAS,CAACL,KAAK,CAACM,KAAe,CAAC;OAEtDF,SAAS,CAACC,SAAS,CAACL,KAAK,CAAC,CAAC,CAAC,CAAE3J,MAAM,CAAC;CACrC,MAAA,IAAIkK,WAAW,GAAIH,SAAS,CAACI,SAAS,CAAC,IAAI,CAAC;CAE5CN,MAAAA,QAAQ,CAACO,WAAW,CAACF,WAAW,CAAC;CACjChB,MAAAA,WAAW,CAACa,SAAS,EAAEF,QAAQ,CAAC;CAChC,MAAA,OAAO,CAAC;CACT;CAEA,IAAA,OAAO,CAAC;IACR;;CAED;CACA;GACA,MAAMQ,iBAAiB,GAAKrF,IAAY,IAAW;CAClD,IAAA,IAAIA,IAAI,CAACsF,QAAQ,KAAK,CAAC,IAAItF,IAAI,CAACuF,UAAU,IAAI,CAAC,iBAAiB,CAACC,IAAI,CAACxF,IAAI,CAACyF,OAAO,CAAC,KAAMzF,IAAI,CAAC8E,SAAS,KAAK,WAAW,IAAI9E,IAAI,CAACyF,OAAO,KAAK,MAAM,CAAE,EAAE;OACrJpJ,KAAK,CAACqJ,IAAI,CAAC1F,IAAI,CAACuF,UAAU,CAAC,CAACnL,OAAO,CAACmK,OAAO,IAAI;SAC9CoB,kBAAkB,CAACpB,OAAO,CAAC;CAC5B,OAAC,CAAC;CACH;IACA;GAGD,MAAMoB,kBAAkB,GAAK3F,IAAiB,IAAa;CAE1D,IAAA,IAAIA,IAAI,CAACsF,QAAQ,KAAK,CAAC,EAAE;OACxB,OAAOZ,aAAa,CAAC1E,IAAY,CAAC;CACnC;KAEAqF,iBAAiB,CAACrF,IAAe,CAAC;CAElC,IAAA,OAAO,CAAC;IACR;GAED2F,kBAAkB,CAAEpB,OAAQ,CAAC;CAC9B,CAAC;;CAED;CACA;CACA;CACA;CACO,MAAMqB,eAAe,GAAIpG,EAAc,IAAK;CAClD,EAAA,IAAIqG,QAAQ,GAAGrG,EAAE,CAACsG,gBAAgB,CAAC,gBAAgB,CAAC;GACpDzJ,KAAK,CAAC0J,SAAS,CAAC3L,OAAO,CAACoD,IAAI,CAACqI,QAAQ,EAAE,UAASrG,EAAc,EAAC;CAC9D,IAAA,IAAIO,MAAM,GAAGP,EAAE,CAACyD,UAAkB;KAClClD,MAAM,CAACsE,YAAY,CAAC7E,EAAE,CAACqB,UAAU,EAAUrB,EAAE,CAAC;KAC9CO,MAAM,CAACiG,SAAS,EAAE;CACnB,GAAC,CAAC;CACH,CAAC;;CChFM,MAAMC,KAAK,GAAM,EAAE;CACnB,MAAMC,UAAU,GAAK,EAAE;CACvB,MAAMC,OAAO,GAAK,EAAE;CACpB,MAAMC,QAAQ,GAAK,EAAE;CACrB,MAAMC,MAAM,GAAM,EAAE;CACpB,MAAMC,SAAS,GAAK,EAAE;CACtB,MAAMC,QAAQ,GAAK,EAAE;CACrB,MAAMC,aAAa,GAAI,CAAC;CACxB,MAAMC,UAAU,GAAK,EAAE;CACvB,MAAMC,OAAO,GAAK,CAAC;CAEnB,MAAMC,MAAM,GAAU,OAAOC,SAAS,KAAK,WAAW,GAAG,KAAK,GAAG,KAAK,CAACpB,IAAI,CAACoB,SAAS,CAACC,SAAS,CAAC;CAChG,MAAMC,YAAY,GAAIH,MAAM,GAAG,SAAS,GAAG,SAAS,CAAC;;ACX5D,gBAAe;CACdnK,EAAAA,OAAO,EAAE,EAAE;CACXuK,EAAAA,SAAS,EAAE,EAAE;CAEbrL,EAAAA,OAAO,EAAE,EAAE;CACXsL,EAAAA,SAAS,EAAE,GAAG;CACdC,EAAAA,OAAO,EAAE,IAAI;CAAE;CACfC,EAAAA,OAAO,EAAE,IAAI;CACbC,EAAAA,UAAU,EAAE,IAAI;CAChBC,EAAAA,MAAM,EAAE,IAAI;CACZC,EAAAA,YAAY,EAAE,KAAK;CACnBC,EAAAA,YAAY,EAAE,IAAI;CAClBhD,EAAAA,SAAS,EAAE,IAAI;CACfiD,EAAAA,WAAW,EAAE,IAAI;CACjBC,EAAAA,UAAU,EAAE,IAAI;CAChBC,EAAAA,UAAU,EAAE,EAAE;CACdC,EAAAA,QAAQ,EAAE,IAAI;CACdC,EAAAA,YAAY,EAAE,IAAI;CAClBC,EAAAA,UAAU,EAAE,KAAK;CACjBC,EAAAA,aAAa,EAAE,KAAK;CACpBC,EAAAA,WAAW,EAAE,KAAK;CAClBC,EAAAA,OAAO,EAAE,IAAI;CACbC,EAAAA,gBAAgB,EAAE,KAAK;CACvB;CACAC,EAAAA,eAAe,EAAE,GAAG;CAGpBC,EAAAA,YAAY,EAAE,GAAG;CACjBC,EAAAA,YAAY,EAAE,SAAS;CAEvBC,EAAAA,QAAQ,EAAE,IAAI;CAAE;CAChBC,EAAAA,aAAa,EAAE,UAAU;CACzBC,EAAAA,UAAU,EAAE,OAAO;CACnBC,EAAAA,UAAU,EAAE,MAAM;CAClBC,EAAAA,aAAa,EAAE,UAAU;CACzBC,EAAAA,kBAAkB,EAAE,OAAO;CAC3BC,EAAAA,kBAAkB,EAAE,OAAO;CAC3BC,EAAAA,iBAAiB,EAAE,KAAK;CAExBC,EAAAA,SAAS,EAAE,QAAQ;GACnBC,WAAW,EAAE,CAAC,MAAM,CAAC;CACrBC,EAAAA,iBAAiB,EAAE,KAAK;CAExBC,EAAAA,IAAI,EAAE,IAAI;CACVC,EAAAA,YAAY,EAAE,YAAY;CAC1BC,EAAAA,YAAY,EAAE,YAAY;CAC1BC,EAAAA,aAAa,EAAE,aAAa;CAC5BC,EAAAA,oBAAoB,EAAE,qBAAqB;CAC3CC,EAAAA,SAAS,EAAE,MAAM;CACjBC,EAAAA,WAAW,EAAE,QAAQ;CAErBC,EAAAA,cAAc,EAAE,IAAI;CACpBC,EAAAA,YAAY,EAAE,mDAAmD;CAEjEC,EAAAA,qBAAqB,EAAE,KAAK;CAE5BC,EAAAA,WAAW,EAAE,IAAI;CACjBC,EAAAA,eAAe,EAAE,IAAI;CAErBC,EAAAA,UAAU,EAAE,UAASxJ,KAAY,EAAS;CACzC,IAAA,OAAOA,KAAK,CAACnF,MAAM,GAAG,CAAC;IACvB;CAED;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAEC4O,EAAAA,MAAM,EAAE;CACP;CACF;CACA;CACA;CACA;CACA;CACA;CANE;CAQF,CAAC;;CCvFc,SAASC,WAAWA,CAAExL,KAAc,EAAEyL,aAA2C,EAAa;CAC5G,EAAA,IAAIlO,QAAoB,GAAG6F,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEqI,QAAQ,EAAED,aAAa,CAAC;CAErE,EAAA,IAAIE,SAAS,GAAMpO,QAAQ,CAACwM,QAAQ;CACpC,EAAA,IAAI6B,WAAW,GAAMrO,QAAQ,CAAC2M,UAAU;CACxC,EAAA,IAAI2B,WAAW,GAAMtO,QAAQ,CAAC0M,UAAU;CACxC,EAAA,IAAI6B,cAAc,GAAKvO,QAAQ,CAAC4M,aAAa;CAC7C,EAAA,IAAI4B,cAAc,GAAKxO,QAAQ,CAACyM,aAAa;CAC7C,EAAA,IAAIgC,oBAAoB,GAAGzO,QAAQ,CAAC6M,kBAAkB;CACtD,EAAA,IAAI6B,oBAAoB,GAAG1O,QAAQ,CAAC8M,kBAAkB;GAEtD,IAAI6B,QAAQ,GAAMlM,KAAK,CAACoH,OAAO,CAAC+E,WAAW,EAAE;CAC7C,EAAA,IAAIf,WAAW,GAAMpL,KAAK,CAACsB,YAAY,CAAC,aAAa,CAAC,IAAItB,KAAK,CAACsB,YAAY,CAAC,kBAAkB,CAAC;CAEhG,EAAA,IAAI,CAAC8J,WAAW,IAAI,CAAC7N,QAAQ,CAACoM,gBAAgB,EAAE;CAC/C,IAAA,IAAIyC,MAAM,GAAIpM,KAAK,CAACyC,aAAa,CAAC,kBAAkB,CAAC;CACrD,IAAA,IAAI2J,MAAM,EAAE;OACXhB,WAAW,GAAGgB,MAAM,CAACC,WAAW;CACjC;CAED;CAEA,EAAA,IAAIC,gBAMH,GAAG;CACHlB,IAAAA,WAAW,EAAGA,WAAW;CACzBjN,IAAAA,OAAO,EAAI,EAAE;CACbuK,IAAAA,SAAS,EAAG,EAAE;CACd6D,IAAAA,KAAK,EAAI,EAAE;CACXlD,IAAAA,QAAQ,EAAG;IACX;;CAGD;CACD;CACA;CACA;GACC,IAAImD,WAAW,GAAGA,MAAM;CACvB,IAAA,IAAIpF,OAAO;CACX,IAAA,IAAIjJ,OAAO,GAAGmO,gBAAgB,CAACnO,OAAO;KACtC,IAAIsO,UAA6B,GAAG,EAAE;KACtC,IAAIC,WAAW,GAAG,CAAC;KACnB,IAAIC,MAAM,GAAG,CAAC;KAEd,IAAIC,QAAQ,GAAIzL,EAAc,IAAe;CAE5C,MAAA,IAAIoF,IAAI,GAAGnD,MAAM,CAACC,MAAM,CAAC,EAAE,EAAClC,EAAE,CAAC0L,OAAO,CAAC,CAAC;CACxC,MAAA,IAAIC,IAAI,GAAGnB,SAAS,IAAIpF,IAAI,CAACoF,SAAS,CAAC;OAEvC,IAAI,OAAOmB,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACnQ,MAAM,EAAE;CAC5C4J,QAAAA,IAAI,GAAGnD,MAAM,CAACC,MAAM,CAACkD,IAAI,EAACwG,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC,CAAC;CAC5C;CAEA,MAAA,OAAOvG,IAAI;MACX;CAED,IAAA,IAAI0G,SAAS,GAAGA,CAACb,MAAwB,EAAEc,KAAa,KAAK;CAE5D,MAAA,IAAIvO,KAAK,GAAGD,QAAQ,CAAC0N,MAAM,CAACzN,KAAK,CAAC;OAClC,IAAKA,KAAK,IAAI,IAAI,EAAG;CACrB,MAAA,IAAK,CAACA,KAAK,IAAI,CAACpB,QAAQ,CAACoM,gBAAgB,EAAE;;CAE3C;CACA;CACA;CACA;CACA,MAAA,IAAI8C,UAAU,CAACrO,cAAc,CAACO,KAAK,CAAC,EAAE;CACrC,QAAA,IAAIuO,KAAK,EAAE;WACV,IAAIC,GAAG,GAAGV,UAAU,CAAC9N,KAAK,CAAC,CAACoN,cAAc,CAAC;WAC3C,IAAI,CAACoB,GAAG,EAAE;CACTV,YAAAA,UAAU,CAAC9N,KAAK,CAAC,CAACoN,cAAc,CAAC,GAAGmB,KAAK;YACzC,MAAM,IAAI,CAAClP,KAAK,CAACC,OAAO,CAACkP,GAAG,CAAC,EAAE;aAC/BV,UAAU,CAAC9N,KAAK,CAAC,CAACoN,cAAc,CAAC,GAAG,CAACoB,GAAG,EAAED,KAAK,CAAC;CACjD,WAAC,MAAM;CACNC,YAAAA,GAAG,CAAC5Q,IAAI,CAAC2Q,KAAK,CAAC;CAChB;CACD;CAED,OAAC,MAAI;CAEJ,QAAA,IAAIE,WAAW,GAAeR,QAAQ,CAACR,MAAM,CAAC;SAC9CgB,WAAW,CAACxB,WAAW,CAAC,GAAMwB,WAAW,CAACxB,WAAW,CAAC,IAAIQ,MAAM,CAACC,WAAW;SAC5Ee,WAAW,CAACvB,WAAW,CAAC,GAAMuB,WAAW,CAACvB,WAAW,CAAC,IAAIlN,KAAK;SAC/DyO,WAAW,CAACtB,cAAc,CAAC,GAAGsB,WAAW,CAACtB,cAAc,CAAC,IAAIM,MAAM,CAACiB,QAAQ;SAC5ED,WAAW,CAACrB,cAAc,CAAC,GAAGqB,WAAW,CAACrB,cAAc,CAAC,IAAImB,KAAK;SAClEE,WAAW,CAACE,OAAO,GAAKlB,MAAM;SAC9BgB,WAAW,CAACT,MAAM,GAAKS,WAAW,CAACT,MAAM,IAAI,EAAEA,MAAM;CAErDF,QAAAA,UAAU,CAAC9N,KAAK,CAAC,GAAGyO,WAAW;CAC/BjP,QAAAA,OAAO,CAAC5B,IAAI,CAAC6Q,WAAW,CAAC;CAC1B;OAEA,IAAIhB,MAAM,CAACmB,QAAQ,EAAE;CACpBjB,QAAAA,gBAAgB,CAACC,KAAK,CAAChQ,IAAI,CAACoC,KAAK,CAAC;CACnC;MACA;KAED,IAAI6O,QAAQ,GAAKC,QAA4B,IAAM;OAClD,IAAIrM,EAAS,EAAEsM,aAAa;CAE5BA,MAAAA,aAAa,GAASd,QAAQ,CAACa,QAAQ,CAAC;CACxCC,MAAAA,aAAa,CAAC1B,oBAAoB,CAAC,GAAI0B,aAAa,CAAC1B,oBAAoB,CAAC,IAAIyB,QAAQ,CAACnM,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;OAClHoM,aAAa,CAACzB,oBAAoB,CAAC,GAAIyB,aAAa,CAACzB,oBAAoB,CAAC,IAAIS,WAAW,EAAE;OAC3FgB,aAAa,CAAC5B,cAAc,CAAC,GAAK4B,aAAa,CAAC5B,cAAc,CAAC,IAAI2B,QAAQ,CAACJ,QAAQ;OACpFK,aAAa,CAACf,MAAM,GAAOe,aAAa,CAACf,MAAM,IAAI,EAAEA,MAAM;CAE3DL,MAAAA,gBAAgB,CAAC5D,SAAS,CAACnM,IAAI,CAACmR,aAAa,CAAC;CAE9CtM,MAAAA,EAAE,GAAGsM,aAAa,CAACzB,oBAAoB,CAAC;CAExCxN,MAAAA,OAAO,CAACgP,QAAQ,CAACE,QAAQ,EAAGvB,MAAM,IAAG;CACpCa,QAAAA,SAAS,CAACb,MAAM,EAAuBhL,EAAE,CAAC;CAC3C,OAAC,CAAC;MAEF;CAEDkL,IAAAA,gBAAgB,CAACjD,QAAQ,GAAGrJ,KAAK,CAAC4N,YAAY,CAAC,UAAU,CAAC,GAAG,IAAI,GAAG,CAAC;CAErEnP,IAAAA,OAAO,CAACuB,KAAK,CAAC2N,QAAQ,EAAEE,KAAK,IAAG;CAC/BzG,MAAAA,OAAO,GAAGyG,KAAK,CAACzG,OAAO,CAAC+E,WAAW,EAAE;OACrC,IAAI/E,OAAO,KAAK,UAAU,EAAE;SAC3BoG,QAAQ,CAACK,KAA4B,CAAC;CACvC,OAAC,MAAM,IAAIzG,OAAO,KAAK,QAAQ,EAAE;SAChC6F,SAAS,CAACY,KAA0B,CAAC;CACtC;CACD,KAAC,CAAC;IAEF;;CAGD;CACD;CACA;CACA;GACC,IAAIC,YAAY,GAAGA,MAAM;CACxB,IAAA,MAAMC,QAAQ,GAAG/N,KAAK,CAACsB,YAAY,CAACqK,SAAS,CAAC;KAE9C,IAAI,CAACoC,QAAQ,EAAE;OACd,IAAIpP,KAAK,GAAGqB,KAAK,CAACrB,KAAK,CAAC2D,IAAI,EAAE,IAAI,EAAE;OACpC,IAAI,CAAC/E,QAAQ,CAACoM,gBAAgB,IAAI,CAAChL,KAAK,CAAChC,MAAM,EAAE;OACjD,MAAMqR,MAAM,GAAGrP,KAAK,CAAC7C,KAAK,CAACyB,QAAQ,CAACoL,SAAS,CAAC;CAE9ClK,MAAAA,OAAO,CAAEuP,MAAM,EAAGrP,KAAK,IAAK;SAC3B,MAAMyN,MAAgB,GAAG,EAAE;CAC3BA,QAAAA,MAAM,CAACR,WAAW,CAAC,GAAGjN,KAAK;CAC3ByN,QAAAA,MAAM,CAACP,WAAW,CAAC,GAAGlN,KAAK;CAC3B2N,QAAAA,gBAAgB,CAACnO,OAAO,CAAC5B,IAAI,CAAC6P,MAAM,CAAC;CACtC,OAAC,CAAC;OACFE,gBAAgB,CAACC,KAAK,GAAGyB,MAAM;CAChC,KAAC,MAAM;OACN1B,gBAAgB,CAACnO,OAAO,GAAG4O,IAAI,CAACC,KAAK,CAACe,QAAQ,CAAC;CAC/CtP,MAAAA,OAAO,CAAE6N,gBAAgB,CAACnO,OAAO,EAAG8P,GAAG,IAAK;SAC3C3B,gBAAgB,CAACC,KAAK,CAAChQ,IAAI,CAAC0R,GAAG,CAACpC,WAAW,CAAC,CAAC;CAC9C,OAAC,CAAC;CACH;IACA;GAGD,IAAIK,QAAQ,KAAK,QAAQ,EAAE;CAC1BM,IAAAA,WAAW,EAAE;CACd,GAAC,MAAM;CACNsB,IAAAA,YAAY,EAAE;CACf;CAEA,EAAA,OAAO1K,MAAM,CAACC,MAAM,CAAE,EAAE,EAAEqI,QAAQ,EAAEY,gBAAgB,EAAEb,aAAa,CAAC;CACrE;;CCrIA,IAAIyC,UAAU,GAAG,CAAC;CAEH,MAAMC,SAAS,SAAShR,WAAW,CAAClB,UAAU,CAAC,CAAA;CAmD7DC,EAAAA,WAAWA,CAAEkS,SAA0B,EAAEC,aAA2C,EAAE;CACrF,IAAA,KAAK,EAAE;KAAC,IA3CFC,CAAAA,KAAK,GAAgB,CAAC;KAAA,IAYtBC,CAAAA,MAAM,GAAiB,KAAK;KAAA,IAC5BC,CAAAA,UAAU,GAAgB,KAAK;KAAA,IAC/BC,CAAAA,UAAU,GAAgB,KAAK;KAAA,IAE/BC,CAAAA,SAAS,GAAgB,KAAK;CAAE;KAAA,IAChCC,CAAAA,OAAO,GAAiB,IAAI;KAAA,IAC5BC,CAAAA,QAAQ,GAAiB,KAAK;KAAA,IAC9BC,CAAAA,SAAS,GAAgB,KAAK;KAAA,IAC9BC,CAAAA,aAAa,GAAe,KAAK;KAAA,IACjCC,CAAAA,OAAO,GAAiB,KAAK;KAAA,IAC7BC,CAAAA,WAAW,GAAgB,KAAK;KAAA,IAChCC,CAAAA,WAAW,GAAgB,KAAK;KAAA,IAChCC,CAAAA,UAAU,GAAgB,KAAK;KAAA,IAE/BC,CAAAA,SAAS,GAAe,EAAE;KAAA,IAC1BC,CAAAA,QAAQ,GAAgB,CAAC;KAAA,IACzB9P,CAAAA,OAAO,GAAgB,CAAC;KAAA,IACxBI,CAAAA,cAAc,GAAkC,EAAE;KAAA,IAElD2P,CAAAA,YAAY,GAAyB,IAAI;KAAA,IACzCC,CAAAA,WAAW,GAAkB,EAAE;KAAA,IAE/B5G,CAAAA,SAAS,GAAmB,EAAE;KAAA,IAC9BvK,CAAAA,OAAO,GAAoB,EAAE;KAAA,IAC7BoR,CAAAA,WAAW,GAA+B,EAAE;KAAA,IAC5ChD,CAAAA,KAAK,GAAkB,EAAE;KAAA,IAExBiD,CAAAA,cAAc,GAAmB,IAAI;CAM5CtB,IAAAA,UAAU,EAAE;CAEZ,IAAA,IAAIuB,GAAG;CACP,IAAA,IAAIzP,KAAK,GAAM6B,MAAM,CAAEuM,SAAU,CAAa;KAE9C,IAAIpO,KAAK,CAAC0P,SAAS,EAAE;CACpB,MAAA,MAAM,IAAIlR,KAAK,CAAC,gDAAgD,CAAC;CAClE;KAGAwB,KAAK,CAAC0P,SAAS,GAAK,IAAI;;CAGxB;CACA,IAAA,IAAIC,aAAa,GAAI1Q,MAAM,CAAC2Q,gBAAgB,IAAI3Q,MAAM,CAAC2Q,gBAAgB,CAAC5P,KAAK,EAAE,IAAI,CAAC;CACpFyP,IAAAA,GAAG,GAAQE,aAAa,CAACE,gBAAgB,CAAC,WAAW,CAAC;;CAEtD;CACA,IAAA,MAAMtS,QAAQ,GAAKiO,WAAW,CAAExL,KAAK,EAAEqO,aAAc,CAAC;KACtD,IAAI,CAAC9Q,QAAQ,GAAKA,QAAQ;KAC1B,IAAI,CAACyC,KAAK,GAAMA,KAAK;CACrB,IAAA,IAAI,CAAC8P,QAAQ,GAAK9P,KAAK,CAAC8P,QAAQ,IAAI,CAAC;KACrC,IAAI,CAACC,aAAa,GAAI/P,KAAK,CAACoH,OAAO,CAAC+E,WAAW,EAAE,KAAK,QAAQ;KAC9D,IAAI,CAAC6D,GAAG,GAAM,MAAM,CAAC7I,IAAI,CAACsI,GAAG,CAAC;KAC9B,IAAI,CAACQ,OAAO,GAAK/O,KAAK,CAAClB,KAAK,EAAE,YAAY,GAACkO,UAAU,CAAC;CACtD,IAAA,IAAI,CAACgC,UAAU,GAAKlQ,KAAK,CAACmQ,QAAQ;;CAGlC;KACA,IAAI,CAACC,MAAM,GAAG,IAAIC,MAAM,CAAC,IAAI,CAAClS,OAAO,EAAE;OAAC2K,UAAU,EAAEvL,QAAQ,CAACuL;CAAU,KAAC,CAAC;;CAEzE;CACAvL,IAAAA,QAAQ,CAACmN,IAAI,GAAGnN,QAAQ,CAACmN,IAAI,KAAKnN,QAAQ,CAAC8L,QAAQ,KAAK,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;CAC/E,IAAA,IAAI,OAAO9L,QAAQ,CAAC+L,YAAY,KAAK,SAAS,EAAE;CAC/C/L,MAAAA,QAAQ,CAAC+L,YAAY,GAAG/L,QAAQ,CAACmN,IAAI,KAAK,OAAO;CAClD;CAEA,IAAA,IAAI,OAAOnN,QAAQ,CAAC8N,eAAe,KAAK,SAAS,EAAE;CAClD9N,MAAAA,QAAQ,CAAC8N,eAAe,GAAG9N,QAAQ,CAACmN,IAAI,KAAK,OAAO;CACrD;;CAEA;CACA,IAAA,IAAIrG,MAAM,GAAG9G,QAAQ,CAAC0L,YAAY;CAClC,IAAA,IAAI,OAAO5E,MAAM,KAAK,UAAU,EAAE;CAEjC,MAAA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;CAC/BA,QAAAA,MAAM,GAAG,IAAI+B,MAAM,CAAC/B,MAAM,CAAC;CAC5B;OAEA,IAAIA,MAAM,YAAY+B,MAAM,EAAE;SAC7B7I,QAAQ,CAAC0L,YAAY,GAAIjJ,KAAa,IAAMqE,MAAM,CAAY8C,IAAI,CAACnH,KAAK,CAAC;CAC1E,OAAC,MAAI;CACJzC,QAAAA,QAAQ,CAAC0L,YAAY,GAAItK,KAAa,IAAK;CAC1C,UAAA,OAAO,IAAI,CAACpB,QAAQ,CAACgM,UAAU,IAAI,CAAC,IAAI,CAACpL,OAAO,CAACQ,KAAK,CAAC;UACvD;CACF;CACD;CAGA,IAAA,IAAI,CAACd,iBAAiB,CAACN,QAAQ,CAACF,OAAO,CAAC;KACxC,IAAI,CAACiT,cAAc,EAAE;KACrB,IAAI,CAACC,cAAc,EAAE;;CAGrB;CACA,IAAA,MAAM9L,OAAO,GAAK5C,MAAM,CAAC,OAAO,CAAC;CACjC,IAAA,MAAM2O,OAAO,GAAK3O,MAAM,CAAC,OAAO,CAAC;CACjC,IAAA,MAAM4O,QAAQ,GAAK,IAAI,CAACC,OAAO,CAAC,UAAU,CAAC;CAC3C,IAAA,MAAMC,gBAAgB,GAAG9O,MAAM,CAAC,oCAAoC,CAAC;KAErE,MAAM4B,OAAO,GAAK,IAAI,CAACzD,KAAK,CAACsB,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE;CACxD,IAAA,MAAMsP,SAAS,GAAKrT,QAAQ,CAACmN,IAAI;CAEjC,IAAA,IAAImG,aAA+B;KAGnCtN,UAAU,CAAEkB,OAAO,EAAElH,QAAQ,CAACoN,YAAY,EAAElH,OAAO,EAAEmN,SAAS,CAAC;CAG/DrN,IAAAA,UAAU,CAACiN,OAAO,EAACjT,QAAQ,CAACqN,YAAY,CAAC;CACzCnJ,IAAAA,MAAM,CAAEgD,OAAO,EAAE+L,OAAQ,CAAC;KAG1BjN,UAAU,CAACkN,QAAQ,EAAElT,QAAQ,CAACsN,aAAa,EAAE+F,SAAS,CAAC;KACvD,IAAIrT,QAAQ,CAAC4N,qBAAqB,EAAE;CACnC5H,MAAAA,UAAU,CAAEkN,QAAQ,EAAEhN,OAAO,CAAC;CAC/B;CAGAF,IAAAA,UAAU,CAACoN,gBAAgB,EAAEpT,QAAQ,CAACuN,oBAAoB,CAAC;CAC3DrJ,IAAAA,MAAM,CAAEgP,QAAQ,EAAEE,gBAAiB,CAAC;KAEpC9O,MAAM,CAAEtE,QAAQ,CAAC0N,cAAc,IAAIxG,OAAQ,CAAC,CAACsC,WAAW,CAAE0J,QAAS,CAAC;;CAGpE;CACA,IAAA,IAAIxO,YAAY,CAAC1E,QAAQ,CAAC2N,YAAY,CAAC,EAAE;CACxC2F,MAAAA,aAAa,GAAIhP,MAAM,CAACtE,QAAQ,CAAC2N,YAAa,CAAqB;;CAEnE;OACA,IAAIzF,KAAK,GAAG,CAAC,aAAa,EAAC,gBAAgB,EAAC,cAAc,EAAC,YAAY,CAAC;CACxEhH,MAAAA,OAAO,CAACgH,KAAK,EAAEE,IAAW,IAAK;CAC9B,QAAA,IAAI3F,KAAK,CAACsB,YAAY,CAACqE,IAAI,CAAC,EAAE;WAC7BH,OAAO,CAACqL,aAAa,EAAC;CAAC,YAAA,CAAClL,IAAI,GAAE3F,KAAK,CAACsB,YAAY,CAACqE,IAAI;CAAC,WAAC,CAAC;CACzD;CACD,OAAC,CAAC;CAEFkL,MAAAA,aAAa,CAACf,QAAQ,GAAG,CAAC,CAAC;CAC3BU,MAAAA,OAAO,CAACzJ,WAAW,CAAE8J,aAAc,CAAC;OACpC,IAAI,CAACC,UAAU,GAAID,aAAa;;CAEjC;CACA,KAAC,MAAK,IAAItT,QAAQ,CAAC2N,YAAY,EAAE;CAChC2F,MAAAA,aAAa,GAAIhP,MAAM,CAAEtE,QAAQ,CAAC2N,YAAa,CAAqB;OACpE,IAAI,CAAC4F,UAAU,GAAID,aAAa;CAEjC,KAAC,MAAI;CACJA,MAAAA,aAAa,GAAIhP,MAAM,CAAC,UAAU,CAAqB;OACvD,IAAI,CAACiP,UAAU,GAAIN,OAAO;CAC3B;KAEA,IAAI,CAAC/L,OAAO,GAAKA,OAAO;KACxB,IAAI,CAACgM,QAAQ,GAAKA,QAAQ;KAC1B,IAAI,CAACE,gBAAgB,GAAGA,gBAAgB;KACxC,IAAI,CAACH,OAAO,GAAMA,OAAO;KACzB,IAAI,CAACK,aAAa,GAAIA,aAAa;KAEnC,IAAI,CAACE,KAAK,EAAE;CACb;;CAEA;CACD;CACA;CACA;CACCA,EAAAA,KAAKA,GAAE;KAEN,MAAM9T,IAAI,GAAG,IAAI;CACjB,IAAA,MAAMM,QAAQ,GAAMN,IAAI,CAACM,QAAQ;CACjC,IAAA,MAAMsT,aAAa,GAAK5T,IAAI,CAAC4T,aAAa;CAC1C,IAAA,MAAMJ,QAAQ,GAAMxT,IAAI,CAACwT,QAAQ;CACjC,IAAA,MAAME,gBAAgB,GAAI1T,IAAI,CAAC0T,gBAAgB;CAC/C,IAAA,MAAMlM,OAAO,GAAMxH,IAAI,CAACwH,OAAO;CAC/B,IAAA,MAAM+L,OAAO,GAAMvT,IAAI,CAACuT,OAAO;CAC/B,IAAA,MAAMxQ,KAAK,GAAO/C,IAAI,CAAC+C,KAAK;CAC5B,IAAA,MAAM8Q,UAAU,GAAK7T,IAAI,CAAC6T,UAAU;CACpC,IAAA,MAAME,aAAa,GAAK;CAAEC,MAAAA,OAAO,EAAE;MAAM;CACzC,IAAA,MAAMC,SAAS,GAAMjU,IAAI,CAACgT,OAAO,GAAE,cAAc;KAGjDzK,OAAO,CAACmL,gBAAgB,EAAC;CACxBvP,MAAAA,EAAE,EAAE8P;CACL,KAAC,CAAC;KAEF1L,OAAO,CAACsL,UAAU,EAAC;CAClBK,MAAAA,IAAI,EAAC,UAAU;CACf,MAAA,eAAe,EAAC,SAAS;CACzB,MAAA,eAAe,EAAC,OAAO;CACvB,MAAA,eAAe,EAACD;CACjB,KAAC,CAAC;KAEF,MAAME,UAAU,GAAGlQ,KAAK,CAAC4P,UAAU,EAAC7T,IAAI,CAACgT,OAAO,GAAG,aAAa,CAAC;KACjE,MAAMnO,KAAK,GAAK,aAAa,GAACa,WAAW,CAAC1F,IAAI,CAACgT,OAAO,CAAC,GAAC,IAAI;CAC5D,IAAA,MAAMoB,KAAK,GAAKlP,QAAQ,CAACM,aAAa,CAACX,KAAK,CAAC;KAC7C,MAAMwP,WAAW,GAAGrU,IAAI,CAACsU,KAAK,CAACC,IAAI,CAACvU,IAAI,CAAC;CACzC,IAAA,IAAIoU,KAAK,EAAE;CACV7Q,MAAAA,QAAQ,CAAC6Q,KAAK,EAAC,OAAO,EAAEC,WAAY,CAAC;OACrC9L,OAAO,CAAC6L,KAAK,EAAC;CAACI,QAAAA,GAAG,EAACL;CAAU,OAAC,CAAC;OAC/B,MAAMM,QAAQ,GAAGxQ,KAAK,CAACmQ,KAAK,EAACpU,IAAI,CAACgT,OAAO,GAAC,WAAW,CAAC;OACtDzK,OAAO,CAACsL,UAAU,EAAC;CAAC,QAAA,iBAAiB,EAACY;CAAQ,OAAC,CAAC;OAChDlM,OAAO,CAACmL,gBAAgB,EAAC;CAAC,QAAA,iBAAiB,EAACe;CAAQ,OAAC,CAAC;CACvD;KAEAjN,OAAO,CAACnB,KAAK,CAACqO,KAAK,GAAG3R,KAAK,CAACsD,KAAK,CAACqO,KAAK;CAEvC,IAAA,IAAI1U,IAAI,CAACI,OAAO,CAACC,KAAK,CAACX,MAAM,EAAE;CAC9B,MAAA,MAAMiV,eAAe,GAAG,SAAS,GAAG3U,IAAI,CAACI,OAAO,CAACC,KAAK,CAACuU,IAAI,CAAC,UAAU,CAAC;OACvEtO,UAAU,CAAE,CAACkB,OAAO,EAACgM,QAAQ,CAAC,EAAEmB,eAAe,CAAC;CACjD;CAEA,IAAA,IAAI,CAACrU,QAAQ,CAAC8L,QAAQ,KAAK,IAAI,IAAI9L,QAAQ,CAAC8L,QAAQ,GAAG,CAAC,KAAKpM,IAAI,CAAC8S,aAAa,EAAE;OAChFvK,OAAO,CAACxF,KAAK,EAAC;CAAC8R,QAAAA,QAAQ,EAAC;CAAU,OAAC,CAAC;CACrC;KAEA,IAAIvU,QAAQ,CAAC6N,WAAW,EAAE;OACzB5F,OAAO,CAACqL,aAAa,EAAC;SAACzF,WAAW,EAAC7N,QAAQ,CAAC6N;CAAW,OAAC,CAAC;CAC1D;;CAEA;KACA,IAAI,CAAC7N,QAAQ,CAACqL,OAAO,IAAIrL,QAAQ,CAACoL,SAAS,EAAE;CAC5CpL,MAAAA,QAAQ,CAACqL,OAAO,GAAG,IAAIxC,MAAM,CAAC,MAAM,GAAG2L,YAAY,CAACxU,QAAQ,CAACoL,SAAS,CAAC,GAAG,OAAO,CAAC;CACnF;;CAEA;CACA;CACA,IAAA,IAAIpL,QAAQ,CAACyU,IAAI,IAAIzU,QAAQ,CAACsM,YAAY,EAAE;CAC3CtM,MAAAA,QAAQ,CAACyU,IAAI,GAAG5S,YAAY,CAAC7B,QAAQ,CAACyU,IAAI,EAACzU,QAAQ,CAACsM,YAAY,CAAC;CAClE;CAEArJ,IAAAA,QAAQ,CAACiQ,QAAQ,EAAC,WAAW,EAAE,MAAM;OACpCxT,IAAI,CAACgS,WAAW,GAAG,KAAK;CACzB,KAAC,CAAC;CAEFzO,IAAAA,QAAQ,CAACiQ,QAAQ,EAAC,YAAY,EAAGwB,CAAC,IAAK;OAEtC,IAAIC,YAAY,GAAG3N,WAAW,CAAC0N,CAAC,CAACxR,MAAM,EAAiB,mBAAmB,EAAEgQ,QAAQ,CAAC;OACtF,IAAIyB,YAAY,EAAGjV,IAAI,CAACkV,aAAa,CAAEF,CAAC,EAAgBC,YAAa,CAAC;CAEvE,KAAC,EAAE;CAACE,MAAAA,OAAO,EAAC;CAAI,KAAC,CAAC;;CAElB;CACA5R,IAAAA,QAAQ,CAACiQ,QAAQ,EAAC,OAAO,EAAEpQ,GAAG,IAAK;OAClC,MAAM+L,MAAM,GAAG7H,WAAW,CAAClE,GAAG,CAACI,MAAM,EAAiB,mBAAmB,CAAC;CAC1E,MAAA,IAAI2L,MAAM,EAAE;CACXnP,QAAAA,IAAI,CAACoV,cAAc,CAAEhS,GAAG,EAAgB+L,MAAO,CAAC;CAChDhM,QAAAA,cAAc,CAACC,GAAG,EAAC,IAAI,CAAC;CACzB;CACD,KAAC,CAAC;CAEFG,IAAAA,QAAQ,CAACgQ,OAAO,EAAC,OAAO,EAAGnQ,GAAG,IAAK;OAElC,IAAI6R,YAAY,GAAG3N,WAAW,CAAElE,GAAG,CAACI,MAAM,EAAiB,gBAAgB,EAAE+P,OAAO,CAAC;OACrF,IAAI0B,YAAY,IAAIjV,IAAI,CAACqV,YAAY,CAACjS,GAAG,EAAgB6R,YAAuB,CAAC,EAAE;CAClF9R,QAAAA,cAAc,CAACC,GAAG,EAAC,IAAI,CAAC;CACxB,QAAA;CACD;;CAEA;CACA,MAAA,IAAIwQ,aAAa,CAAClS,KAAK,IAAI,EAAE,EAAE;CAC9B,QAAA;CACD;OAEA1B,IAAI,CAACsV,OAAO,EAAE;CACdnS,MAAAA,cAAc,CAACC,GAAG,EAAC,IAAI,CAAC;CACzB,KAAC,CAAC;;CAGF;CACAG,IAAAA,QAAQ,CAACsQ,UAAU,EAAC,SAAS,EAAImB,CAAC,IAAKhV,IAAI,CAACuV,SAAS,CAACP,CAAkB,CAAE,CAAC;;CAE3E;CACAzR,IAAAA,QAAQ,CAACqQ,aAAa,EAAC,UAAU,EAAGoB,CAAC,IAAKhV,IAAI,CAACwV,UAAU,CAACR,CAAkB,CAAE,CAAC;CAC/EzR,IAAAA,QAAQ,CAACqQ,aAAa,EAAC,OAAO,EAAIoB,CAAC,IAAKhV,IAAI,CAACyV,OAAO,CAACT,CAAkB,CAAE,CAAC;CAC1EzR,IAAAA,QAAQ,CAACsQ,UAAU,EAAC,MAAM,EAAKmB,CAAC,IAAKhV,IAAI,CAAC0V,MAAM,CAACV,CAAe,CAAE,CAAC;CACnEzR,IAAAA,QAAQ,CAACsQ,UAAU,EAAC,OAAO,EAAImB,CAAC,IAAKhV,IAAI,CAAC2V,OAAO,CAACX,CAAe,CAAE,CAAC;CACpEzR,IAAAA,QAAQ,CAACqQ,aAAa,EAAC,OAAO,EAAIoB,CAAC,IAAKhV,IAAI,CAAC4V,OAAO,CAACZ,CAAe,CAAE,CAAC;KAGvE,MAAMa,aAAa,GAAIzS,GAAS,IAAK;CAEpC;CACA;OACA,MAAMI,MAAM,GAAGJ,GAAG,CAAC0S,YAAY,EAAE,CAAC,CAAC,CAAC;CACpC,MAAA,IAAI,CAACtO,OAAO,CAACC,QAAQ,CAACjE,MAAqB,CAAC,IAAI,CAACgQ,QAAQ,CAAC/L,QAAQ,CAACjE,MAAqB,CAAC,EAAE;SAC1F,IAAIxD,IAAI,CAAC4R,SAAS,EAAE;WACnB5R,IAAI,CAAC+V,IAAI,EAAE;CACZ;SACA/V,IAAI,CAACgW,UAAU,EAAE;CACjB,QAAA;CACD;;CAGA;CACA;CACA;CACA;CACA,MAAA,IAAIxS,MAAM,IAAIoQ,aAAa,IAAI5T,IAAI,CAACsR,MAAM,EAAE;SAC3ClO,GAAG,CAACE,eAAe,EAAE;;CAEtB;CACA,OAAC,MAAI;CACJH,QAAAA,cAAc,CAACC,GAAG,EAAC,IAAI,CAAC;CACzB;MAEA;KAED,MAAM6S,UAAU,GAAGA,MAAM;OACxB,IAAIjW,IAAI,CAACsR,MAAM,EAAE;SAChBtR,IAAI,CAACkW,gBAAgB,EAAE;CACxB;MACA;CAGD3S,IAAAA,QAAQ,CAAC2B,QAAQ,EAAC,WAAW,EAAE2Q,aAAa,CAAC;KAC7CtS,QAAQ,CAACvB,MAAM,EAAC,QAAQ,EAAEiU,UAAU,EAAElC,aAAa,CAAC;KACpDxQ,QAAQ,CAACvB,MAAM,EAAC,QAAQ,EAAEiU,UAAU,EAAElC,aAAa,CAAC;KAEpD,IAAI,CAACoC,QAAQ,GAAG,MAAM;CACrBjR,MAAAA,QAAQ,CAACkR,mBAAmB,CAAC,WAAW,EAACP,aAAa,CAAC;CACvD7T,MAAAA,MAAM,CAACoU,mBAAmB,CAAC,QAAQ,EAACH,UAAU,CAAC;CAC/CjU,MAAAA,MAAM,CAACoU,mBAAmB,CAAC,QAAQ,EAACH,UAAU,CAAC;OAC/C,IAAI7B,KAAK,EAAGA,KAAK,CAACgC,mBAAmB,CAAC,OAAO,EAAC/B,WAAW,CAAC;MAC1D;;CAED;CACA;KACA,IAAI,CAACgC,cAAc,GAAG;OACrBjR,SAAS,EAAGrC,KAAK,CAACqC,SAAS;OAC3ByN,QAAQ,EAAG9P,KAAK,CAAC8P;MACjB;CAGD9P,IAAAA,KAAK,CAAC8P,QAAQ,GAAG,CAAC,CAAC;KACnB9P,KAAK,CAACuT,qBAAqB,CAAC,UAAU,EAAEtW,IAAI,CAACwH,OAAO,CAAC;CAErDxH,IAAAA,IAAI,CAACuW,IAAI,CAAC,KAAK,CAAC;KAChBjW,QAAQ,CAACgP,KAAK,GAAG,EAAE;KACnB,OAAOhP,QAAQ,CAACmL,SAAS;KACzB,OAAOnL,QAAQ,CAACY,OAAO;CAEvBqC,IAAAA,QAAQ,CAACR,KAAK,EAAC,SAAS,EAAE,MAAM;OAC/B,IAAI/C,IAAI,CAAC0R,OAAO,EAAE;SACjB1R,IAAI,CAAC0R,OAAO,GAAG,KAAK;SACpB1R,IAAI,CAACyR,SAAS,GAAG,IAAI;SACrBzR,IAAI,CAACwW,YAAY,EAAE;CACpB;CACD,KAAC,CAAC;KAEFxW,IAAI,CAACyW,mBAAmB,EAAE;KAC1BzW,IAAI,CAAC0W,YAAY,EAAE;CACnB1W,IAAAA,IAAI,CAAC2W,KAAK,CAAC,KAAK,CAAC;KACjB3W,IAAI,CAACgW,UAAU,EAAE;KACjBhW,IAAI,CAAC8R,OAAO,GAAG,IAAI;KAEnB,IAAI/O,KAAK,CAACqN,QAAQ,EAAE;OACnBpQ,IAAI,CAAC4W,OAAO,EAAE;CACf,KAAC,MAAK,IAAI7T,KAAK,CAAC8T,QAAQ,EAAE;CACzB7W,MAAAA,IAAI,CAAC8W,WAAW,CAAC,IAAI,CAAC;CACvB,KAAC,MAAI;CACJ9W,MAAAA,IAAI,CAAC+W,MAAM,EAAE,CAAC;CACf;KAEA/W,IAAI,CAACb,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC6X,QAAQ,CAAC;CAEhC1Q,IAAAA,UAAU,CAACvD,KAAK,EAAC,aAAa,EAAC,sBAAsB,CAAC;CACtD/C,IAAAA,IAAI,CAACF,OAAO,CAAC,YAAY,CAAC;;CAE1B;CACA,IAAA,IAAIQ,QAAQ,CAACmM,OAAO,KAAK,IAAI,EAAE;OAC9BzM,IAAI,CAACyM,OAAO,EAAE;CACf;CAED;;CAGA;CACD;CACA;CACA;GACCwK,YAAYA,CAAC/V,OAAmB,GAAG,EAAE,EAAEuK,SAAqB,GAAG,EAAE,EAAC;CAEjE;CACA,IAAA,IAAI,CAACyL,UAAU,CAAChW,OAAO,CAAC;;CAGxB;CACAM,IAAAA,OAAO,CAAEiK,SAAS,EAAG+E,QAAkB,IAAK;CAC3C,MAAA,IAAI,CAAC2G,mBAAmB,CAAC3G,QAAQ,CAAC;CACnC,KAAC,CAAC;CACH;;CAEA;CACD;CACA;CACC8C,EAAAA,cAAcA,GAAG;KAChB,IAAItT,IAAI,GAAG,IAAI;CACf,IAAA,IAAI2O,WAAW,GAAG3O,IAAI,CAACM,QAAQ,CAAC2M,UAAU;CAC1C,IAAA,IAAI6B,cAAc,GAAG9O,IAAI,CAACM,QAAQ,CAAC6M,kBAAkB;CAErD,IAAA,IAAIiK,SAAS,GAAG;OACf,UAAU,EAAG9N,IAAc,IAAK;CAC/B,QAAA,IAAIkH,QAAQ,GAAGtL,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;SAC5CqL,QAAQ,CAAChH,SAAS,GAAG,UAAU;CAC/BgH,QAAAA,QAAQ,CAAC1G,WAAW,CAACR,IAAI,CAACpI,OAAO,CAAC;CAClC,QAAA,OAAOsP,QAAQ;QAEf;CACD,MAAA,iBAAiB,EAAE6G,CAAC/N,IAAc,EAAEgO,MAAyB,KAAK;SACjE,OAAO,+BAA+B,GAAGA,MAAM,CAAChO,IAAI,CAACwF,cAAc,CAAC,CAAC,GAAG,QAAQ;QAChF;CACD,MAAA,QAAQ,EAAEK,CAAC7F,IAAc,EAAEgO,MAAyB,KAAK;SACxD,OAAO,OAAO,GAAGA,MAAM,CAAChO,IAAI,CAACqF,WAAW,CAAC,CAAC,GAAG,QAAQ;QACrD;CACD,MAAA,MAAM,EAAE4I,CAACjO,IAAc,EAAEgO,MAAyB,KAAK;SACtD,OAAO,OAAO,GAAGA,MAAM,CAAChO,IAAI,CAACqF,WAAW,CAAC,CAAC,GAAG,QAAQ;QACrD;CACD,MAAA,eAAe,EAAE6I,CAAClO,IAAc,EAAEgO,MAAyB,KAAK;SAC/D,OAAO,kCAAkC,GAAGA,MAAM,CAAChO,IAAI,CAACvG,KAAK,CAAC,GAAG,yBAAyB;QAC1F;OACD,YAAY,EAAC0U,MAAM;CAClB,QAAA,OAAO,gDAAgD;QACvD;OACD,SAAS,EAACpV,MAAM;CACf,QAAA,OAAO,6BAA6B;QACpC;CACD,MAAA,aAAa,EAACqV,MAAM,EAAE;OACtB,UAAU,EAAClE,MAAM;CAChB,QAAA,OAAO,aAAa;CACrB;MACA;CAGDxT,IAAAA,IAAI,CAACM,QAAQ,CAACgO,MAAM,GAAGnI,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEgR,SAAS,EAAEpX,IAAI,CAACM,QAAQ,CAACgO,MAAM,CAAC;CAC1E;;CAEA;CACD;CACA;CACA;CACC+E,EAAAA,cAAcA,GAAG;KAChB,IAAIxS,GAAG,EAAEF,EAAE;CACX,IAAA,IAAIgX,SAA+B,GAAG;CACrC,MAAA,YAAY,EAAQ,cAAc;CAClC,MAAA,QAAQ,EAAY,UAAU;CAC9B,MAAA,UAAU,EAAU,WAAW;CAC/B,MAAA,aAAa,EAAO,cAAc;CAClC,MAAA,aAAa,EAAO,cAAc;CAClC,MAAA,OAAO,EAAa,SAAS;CAC7B,MAAA,YAAY,EAAQ,aAAa;CACjC,MAAA,eAAe,EAAK,gBAAgB;CACpC,MAAA,cAAc,EAAM,eAAe;CACnC,MAAA,cAAc,EAAM,kBAAkB;CACtC,MAAA,iBAAiB,EAAG,qBAAqB;CACzC,MAAA,gBAAgB,EAAI,oBAAoB;CACxC,MAAA,eAAe,EAAK,gBAAgB;CACpC,MAAA,gBAAgB,EAAI,iBAAiB;CACrC,MAAA,MAAM,EAAc,QAAQ;CAC5B,MAAA,MAAM,EAAc,QAAQ;CAC5B,MAAA,OAAO,EAAa,SAAS;CAC7B,MAAA,MAAM,EAAc;MACpB;KAED,KAAK9W,GAAG,IAAI8W,SAAS,EAAE;OAEtBhX,EAAE,GAAG,IAAI,CAACL,QAAQ,CAACqX,SAAS,CAAC9W,GAAG,CAAC,CAAwB;OACzD,IAAIF,EAAE,EAAE,IAAI,CAACxB,EAAE,CAAC0B,GAAG,EAAEF,EAAE,CAAC;CAEzB;CACD;;CAEA;CACD;CACA;CACA;CACC4V,EAAAA,IAAIA,CAACqB,YAAoB,GAAC,IAAI,EAAM;KACnC,MAAM5X,IAAI,GAAI,IAAI;KAClB,MAAMM,QAAQ,GAAGsX,YAAY,GAAGrJ,WAAW,CAAEvO,IAAI,CAAC+C,KAAK,EAAE;CAAC2I,MAAAA,SAAS,EAAC1L,IAAI,CAACM,QAAQ,CAACoL;CAAS,KAAmC,CAAC,GAAG1L,IAAI,CAACM,QAAQ;KAE/IN,IAAI,CAACiX,YAAY,CAAC3W,QAAQ,CAACY,OAAO,EAACZ,QAAQ,CAACmL,SAAS,CAAC;CAEtDzL,IAAAA,IAAI,CAAC6X,QAAQ,CAACvX,QAAQ,CAACgP,KAAK,IAAE,EAAE,EAAC,IAAI,CAAC,CAAC;;CAEvCtP,IAAAA,IAAI,CAAC8X,SAAS,GAAG,IAAI,CAAC;CACvB;;CAEA;CACD;CACA;CACA;CACA;CACCxC,EAAAA,OAAOA,GAAQ;KACd,IAAItV,IAAI,GAAG,IAAI;CAEf,IAAA,IAAIA,IAAI,CAACqS,WAAW,CAAC3S,MAAM,GAAG,CAAC,EAAE;OAChCM,IAAI,CAAC+X,gBAAgB,EAAE;OACvB/X,IAAI,CAACsU,KAAK,EAAE;CACZ,MAAA;CACD;CAEA,IAAA,IAAItU,IAAI,CAAC4R,SAAS,IAAI5R,IAAI,CAACsR,MAAM,EAAE;OAClCtR,IAAI,CAAC+V,IAAI,EAAE;CACZ,KAAC,MAAM;OACN/V,IAAI,CAACsU,KAAK,EAAE;CACb;CACD;;CAEA;CACD;CACA;CACA;GACC0D,WAAWA,GAAQ;;CAEnB;CACD;CACA;CACA;CACA;CACChB,EAAAA,QAAQA,GAAG;CACVrR,IAAAA,YAAY,CAAC,IAAI,CAAC5C,KAAK,EAAE,OAAO,CAAC;CACjC4C,IAAAA,YAAY,CAAC,IAAI,CAAC5C,KAAK,EAAE,QAAQ,CAAC;CACnC;;CAEA;CACD;CACA;CACA;GACC6S,OAAOA,CAACZ,CAA0B,EAAO;KACxC,IAAIhV,IAAI,GAAG,IAAI;CAEf,IAAA,IAAIA,IAAI,CAAC6R,aAAa,IAAI7R,IAAI,CAAC2R,QAAQ,EAAE;OACxCxO,cAAc,CAAC6R,CAAC,CAAC;CACjB,MAAA;CACD;;CAEA;CACA;CACA,IAAA,IAAI,CAAChV,IAAI,CAACM,QAAQ,CAACqL,OAAO,EAAE;CAC3B,MAAA;CACD;;CAEA;CACA1J,IAAAA,UAAU,CAAC,MAAM;CAChB,MAAA,IAAIgW,UAAU,GAAGjY,IAAI,CAACkY,UAAU,EAAE;OAClC,IAAI,CAACD,UAAU,CAAC5O,KAAK,CAACrJ,IAAI,CAACM,QAAQ,CAACqL,OAAO,CAAC,EAAC;CAC5C,QAAA;CACD;CAEA,MAAA,IAAIwM,UAAU,GAAGF,UAAU,CAAC5S,IAAI,EAAE,CAACxG,KAAK,CAACmB,IAAI,CAACM,QAAQ,CAACqL,OAAO,CAAC;CAC/DnK,MAAAA,OAAO,CAAE2W,UAAU,EAAGC,KAAY,IAAK;CAEtC,QAAA,MAAMC,IAAI,GAAG5W,QAAQ,CAAC2W,KAAK,CAAC;CAC5B,QAAA,IAAIC,IAAI,EAAE;CACT,UAAA,IAAI,IAAI,CAACnX,OAAO,CAACkX,KAAK,CAAC,EAAE;CACxBpY,YAAAA,IAAI,CAACsY,OAAO,CAACF,KAAK,CAAC;CACpB,WAAC,MAAI;CACJpY,YAAAA,IAAI,CAACuY,UAAU,CAACH,KAAK,CAAC;CACvB;CACD;CACD,OAAC,CAAC;MACF,EAAE,CAAC,CAAC;CAEN;;CAEA;CACD;CACA;CACA;GACC5C,UAAUA,CAACR,CAAe,EAAO;KAChC,IAAIhV,IAAI,GAAG,IAAI;KACf,IAAGA,IAAI,CAAC2R,QAAQ,EAAC;OAChBxO,cAAc,CAAC6R,CAAC,CAAC;CACjB,MAAA;CACD;CACA,IAAA,IAAIwD,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC1D,CAAC,CAAC2D,OAAO,IAAI3D,CAAC,CAAC4D,KAAK,CAAC;KACzD,IAAI5Y,IAAI,CAACM,QAAQ,CAACwL,MAAM,IAAI9L,IAAI,CAACM,QAAQ,CAACmN,IAAI,KAAK,OAAO,IAAI+K,SAAS,KAAKxY,IAAI,CAACM,QAAQ,CAACoL,SAAS,EAAE;OACpG1L,IAAI,CAACuY,UAAU,EAAE;OACjBpV,cAAc,CAAC6R,CAAC,CAAC;CACjB,MAAA;CACD;CACD;;CAEA;CACD;CACA;CACA;GACCO,SAASA,CAACP,CAAe,EAAO;KAC/B,IAAIhV,IAAI,GAAG,IAAI;KAEfA,IAAI,CAACgS,WAAW,GAAG,IAAI;KAEvB,IAAIhS,IAAI,CAAC2R,QAAQ,EAAE;CAClB,MAAA,IAAIqD,CAAC,CAAC2D,OAAO,KAAKE,OAAiB,EAAE;SACpC1V,cAAc,CAAC6R,CAAC,CAAC;CAClB;CACA,MAAA;CACD;KAEA,QAAQA,CAAC,CAAC2D,OAAO;CAEhB;OACA,KAAKE,KAAe;SACnB,IAAInV,SAAS,CAACmV,YAAsB,EAAC7D,CAAC,CAAC,EAAE;CACxC,UAAA,IAAIhV,IAAI,CAAC4T,aAAa,CAAClS,KAAK,IAAI,EAAE,EAAE;aACnCyB,cAAc,CAAC6R,CAAC,CAAC;aACjBhV,IAAI,CAAC8Y,SAAS,EAAE;CAChB,YAAA;CACD;CACD;CACA,QAAA;;CAED;OACA,KAAKD,OAAiB;SACrB,IAAI7Y,IAAI,CAACsR,MAAM,EAAE;CAChBnO,UAAAA,cAAc,CAAC6R,CAAC,EAAC,IAAI,CAAC;WACtBhV,IAAI,CAAC2W,KAAK,EAAE;CACb;SACA3W,IAAI,CAAC+X,gBAAgB,EAAE;CACvB,QAAA;;CAED;OACA,KAAKc,QAAkB;SACtB,IAAI,CAAC7Y,IAAI,CAACsR,MAAM,IAAItR,IAAI,CAACiS,UAAU,EAAE;WACpCjS,IAAI,CAAC+Y,IAAI,EAAE;CACZ,SAAC,MAAM,IAAI/Y,IAAI,CAACoS,YAAY,EAAE;WAC7B,IAAI4G,IAAI,GAAGhZ,IAAI,CAACiZ,WAAW,CAACjZ,IAAI,CAACoS,YAAY,EAAE,CAAC,CAAC;CACjD,UAAA,IAAI4G,IAAI,EAAEhZ,IAAI,CAACkZ,eAAe,CAACF,IAAI,CAAC;CACrC;SACA7V,cAAc,CAAC6R,CAAC,CAAC;CACjB,QAAA;;CAED;OACA,KAAK6D,MAAgB;SACpB,IAAI7Y,IAAI,CAACoS,YAAY,EAAE;CACtB,UAAA,IAAI+G,IAAI,GAAGnZ,IAAI,CAACiZ,WAAW,CAACjZ,IAAI,CAACoS,YAAY,EAAE,CAAC,CAAC,CAAC;CAClD,UAAA,IAAI+G,IAAI,EAAEnZ,IAAI,CAACkZ,eAAe,CAACC,IAAI,CAAC;CACrC;SACAhW,cAAc,CAAC6R,CAAC,CAAC;CACjB,QAAA;;CAED;OACA,KAAK6D,UAAoB;SACxB,IAAI7Y,IAAI,CAACoZ,SAAS,CAACpZ,IAAI,CAACoS,YAAY,CAAC,EAAE;WACtCpS,IAAI,CAACoV,cAAc,CAACJ,CAAC,EAAChV,IAAI,CAACoS,YAAa,CAAC;WACzCjP,cAAc,CAAC6R,CAAC,CAAC;;CAElB;CACA,SAAC,MAAK,IAAIhV,IAAI,CAACM,QAAQ,CAACwL,MAAM,IAAI9L,IAAI,CAACuY,UAAU,EAAE,EAAE;WACpDpV,cAAc,CAAC6R,CAAC,CAAC;;CAElB;CACA,SAAC,MAAK,IAAI9P,QAAQ,CAACmU,aAAa,IAAIrZ,IAAI,CAAC4T,aAAa,IAAI5T,IAAI,CAACsR,MAAM,EAAE;WACtEnO,cAAc,CAAC6R,CAAC,CAAC;CAClB;CAEA,QAAA;;CAED;OACA,KAAK6D,QAAkB;CACtB7Y,QAAAA,IAAI,CAACsZ,gBAAgB,CAAC,CAAC,CAAC,EAAEtE,CAAC,CAAC;CAC5B,QAAA;;CAED;OACA,KAAK6D,SAAmB;CACvB7Y,QAAAA,IAAI,CAACsZ,gBAAgB,CAAC,CAAC,EAAEtE,CAAC,CAAC;CAC3B,QAAA;;CAED;OACA,KAAK6D,OAAiB;CAErB,QAAA,IAAI7Y,IAAI,CAACM,QAAQ,CAACkM,WAAW,EAAE;WAC9B,IAAIxM,IAAI,CAACoZ,SAAS,CAACpZ,IAAI,CAACoS,YAAY,CAAC,EAAE;aACtCpS,IAAI,CAACoV,cAAc,CAACJ,CAAC,EAAChV,IAAI,CAACoS,YAAa,CAAC;;CAEzC;CACA;aACAjP,cAAc,CAAC6R,CAAC,CAAC;CAClB;WACA,IAAIhV,IAAI,CAACM,QAAQ,CAACwL,MAAM,IAAI9L,IAAI,CAACuY,UAAU,EAAE,EAAE;aAC9CpV,cAAc,CAAC6R,CAAC,CAAC;CAClB;CACD;CACA,QAAA;;CAED;OACA,KAAK6D,aAAuB;OAC5B,KAAKA,UAAoB;CACxB7Y,QAAAA,IAAI,CAACuZ,eAAe,CAACvE,CAAC,CAAC;CACvB,QAAA;CACF;;CAEA;CACA,IAAA,IAAIhV,IAAI,CAAC6R,aAAa,IAAI,CAACnO,SAAS,CAACmV,YAAsB,EAAC7D,CAAC,CAAC,EAAE;OAC/D7R,cAAc,CAAC6R,CAAC,CAAC;CAClB;CACD;;CAEA;CACD;CACA;CACA;GACCS,OAAOA,CAACT,CAA0B,EAAO;KAExC,IAAI,IAAI,CAACrD,QAAQ,EAAE;CAClB,MAAA;CACD;CAEA,IAAA,MAAMjQ,KAAK,GAAG,IAAI,CAACwW,UAAU,EAAE;CAC/B,IAAA,IAAI,IAAI,CAAChG,SAAS,KAAKxQ,KAAK,EAAG;KAC/B,IAAI,CAACwQ,SAAS,GAAGxQ,KAAK;KAEtB,IAAIA,KAAK,IAAI,EAAE,EAAE;OAChB,IAAI,CAAC8X,QAAQ,EAAE;CACf,MAAA;CACD;KAEA,IAAI,IAAI,CAACjH,cAAc,EAAE;CACxBvQ,MAAAA,MAAM,CAACQ,YAAY,CAAC,IAAI,CAAC+P,cAAc,CAAC;CACzC;CAEA,IAAA,IAAI,CAACA,cAAc,GAAGxQ,OAAO,CAAC,MAAK;OAClC,IAAI,CAACwQ,cAAc,GAAG,IAAI;OAC1B,IAAI,CAACiH,QAAQ,EAAE;CAChB,KAAC,EAAE,IAAI,CAAClZ,QAAQ,CAACqM,eAAe,CAAC;CAClC;CAEA6M,EAAAA,QAAQA,GAAQ;CACf,IAAA,MAAM9X,KAAK,GAAG,IAAI,CAACwQ,SAAS;CAE5B,IAAA,IAAI,IAAI,CAAC5R,QAAQ,CAAC+N,UAAU,CAACnM,IAAI,CAAC,IAAI,EAACR,KAAK,CAAC,EAAE;CAC9C,MAAA,IAAI,CAACqT,IAAI,CAACrT,KAAK,CAAC;CACjB;KAEA,IAAI,CAAC+X,cAAc,EAAE;CACrB,IAAA,IAAI,CAAC3Z,OAAO,CAAC,MAAM,EAAE4B,KAAK,CAAC;CAC5B;;CAEA;CACD;CACA;CACA;CACA;CACCwT,EAAAA,aAAaA,CAAE9R,GAA4B,EAAE+L,MAAkB,EAAO;KACrE,IAAI,IAAI,CAAC6C,WAAW,EAAG;CACvB,IAAA,IAAI,CAACkH,eAAe,CAAC/J,MAAM,EAAE,KAAK,CAAC;CACpC;;CAEA;CACD;CACA;CACA;GACCwG,OAAOA,CAACX,CAA2B,EAAO;KACzC,IAAIhV,IAAI,GAAG,IAAI;CACf,IAAA,IAAI0Z,UAAU,GAAG1Z,IAAI,CAAC4R,SAAS;CAE/B,IAAA,IAAI5R,IAAI,CAACuR,UAAU,IAAIvR,IAAI,CAACwR,UAAU,EAAE;OACvCxR,IAAI,CAAC+V,IAAI,EAAE;OACX5S,cAAc,CAAC6R,CAAC,CAAC;CACjB,MAAA;CACD;KAEA,IAAIhV,IAAI,CAAC+R,WAAW,EAAE;KACtB/R,IAAI,CAAC4R,SAAS,GAAG,IAAI;CACrB,IAAA,IAAI5R,IAAI,CAACM,QAAQ,CAACmM,OAAO,KAAK,OAAO,EAAGzM,IAAI,CAACyM,OAAO,EAAE;KAEtD,IAAI,CAACiN,UAAU,EAAE1Z,IAAI,CAACF,OAAO,CAAC,OAAO,CAAC;CAEtC,IAAA,IAAI,CAACE,IAAI,CAACqS,WAAW,CAAC3S,MAAM,EAAE;OAC7BM,IAAI,CAACgW,UAAU,EAAE;OACjBhW,IAAI,CAACyZ,cAAc,CAAC,CAAC,CAACzZ,IAAI,CAACM,QAAQ,CAAC2L,WAAW,CAAC;CACjD;KAEAjM,IAAI,CAACwW,YAAY,EAAE;CACpB;;CAEA;CACD;CACA;CACA;GACCd,MAAMA,CAACV,CAAa,EAAO;CAE1B,IAAA,IAAI9P,QAAQ,CAACyU,QAAQ,EAAE,KAAK,KAAK,EAAG;KAEpC,IAAI3Z,IAAI,GAAG,IAAI;CACf,IAAA,IAAI,CAACA,IAAI,CAAC4R,SAAS,EAAE;KACrB5R,IAAI,CAAC4R,SAAS,GAAG,KAAK;KACtB5R,IAAI,CAAC+R,WAAW,GAAG,KAAK;KAExB,IAAI6H,UAAU,GAAGA,MAAM;OACtB5Z,IAAI,CAAC2W,KAAK,EAAE;OACZ3W,IAAI,CAAC6Z,aAAa,EAAE;OACpB7Z,IAAI,CAAC8Z,QAAQ,CAAC9Z,IAAI,CAACsP,KAAK,CAAC5P,MAAM,CAAC;CAChCM,MAAAA,IAAI,CAACF,OAAO,CAAC,MAAM,CAAC;MACpB;KAED,IAAIE,IAAI,CAACM,QAAQ,CAACwL,MAAM,IAAI9L,IAAI,CAACM,QAAQ,CAACyL,YAAY,EAAE;CACvD/L,MAAAA,IAAI,CAACuY,UAAU,CAAC,IAAI,EAAEqB,UAAU,CAAC;CAClC,KAAC,MAAM;CACNA,MAAAA,UAAU,EAAE;CACb;CACD;;CAGA;CACD;CACA;CACA;CACA;CACCxE,EAAAA,cAAcA,CAAEhS,GAA4B,EAAE+L,MAAkB,EAAE;CACjE,IAAA,IAAIzN,KAAK;CAAE1B,MAAAA,IAAI,GAAG,IAAI;;CAGtB;CACA,IAAA,IAAImP,MAAM,CAAC4K,aAAa,IAAI5K,MAAM,CAAC4K,aAAa,CAACrS,OAAO,CAAC,iBAAiB,CAAC,EAAE;CAC5E,MAAA;CACD;KAGA,IAAIyH,MAAM,CAACrI,SAAS,CAACW,QAAQ,CAAC,QAAQ,CAAC,EAAE;CACxCzH,MAAAA,IAAI,CAACuY,UAAU,CAAC,IAAI,EAAE,MAAM;CAC3B,QAAA,IAAIvY,IAAI,CAACM,QAAQ,CAAC0Z,gBAAgB,EAAE;WACnCha,IAAI,CAAC2W,KAAK,EAAE;CACb;CACD,OAAC,CAAC;CACH,KAAC,MAAM;CACNjV,MAAAA,KAAK,GAAGyN,MAAM,CAACS,OAAO,CAAClO,KAAK;CAC5B,MAAA,IAAI,OAAOA,KAAK,KAAK,WAAW,EAAE;SACjC1B,IAAI,CAAC8X,SAAS,GAAG,IAAI;CACrB9X,QAAAA,IAAI,CAACsY,OAAO,CAAC5W,KAAK,CAAC;CACnB,QAAA,IAAI1B,IAAI,CAACM,QAAQ,CAAC0Z,gBAAgB,EAAE;WACnCha,IAAI,CAAC2W,KAAK,EAAE;CACb;CAEA,QAAA,IAAI,CAAC3W,IAAI,CAACM,QAAQ,CAAC+L,YAAY,IAAIjJ,GAAG,CAACR,IAAI,IAAI,OAAO,CAACsH,IAAI,CAAC9G,GAAG,CAACR,IAAI,CAAC,EAAE;CACtE5C,UAAAA,IAAI,CAACkZ,eAAe,CAAC/J,MAAM,CAAC;CAC7B;CACD;CACD;CACD;;CAEA;CACD;CACA;CACA;GACCiK,SAASA,CAACjK,MAAuB,EAAS;CAEzC,IAAA,IAAI,IAAI,CAACmC,MAAM,IAAInC,MAAM,IAAI,IAAI,CAACuE,gBAAgB,CAACjM,QAAQ,CAAC0H,MAAM,CAAC,EAAG;CACrE,MAAA,OAAO,IAAI;CACZ;CACA,IAAA,OAAO,KAAK;CACb;;CAEA;CACD;CACA;CACA;CACA;CACCkG,EAAAA,YAAYA,CAAEjS,GAAe,EAAEmU,IAAa,EAAU;KACrD,IAAIvX,IAAI,GAAG,IAAI;CAEf,IAAA,IAAI,CAACA,IAAI,CAAC2R,QAAQ,IAAI3R,IAAI,CAACM,QAAQ,CAACmN,IAAI,KAAK,OAAO,EAAE;OACrDtK,cAAc,CAACC,GAAG,CAAC;CACnBpD,MAAAA,IAAI,CAAC6Z,aAAa,CAACtC,IAAI,EAAEnU,GAAG,CAAC;CAC7B,MAAA,OAAO,IAAI;CACZ;CACA,IAAA,OAAO,KAAK;CACb;;CAEA;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;GACC6W,OAAOA,CAACvY,KAAY,EAAS;KAE5B,IAAI,CAAC,IAAI,CAACpB,QAAQ,CAACyU,IAAI,EAAG,OAAO,KAAK;KACtC,IAAI,IAAI,CAACtS,cAAc,CAACtB,cAAc,CAACO,KAAK,CAAC,EAAG,OAAO,KAAK;CAE5D,IAAA,OAAO,IAAI;CACZ;;CAEA;CACD;CACA;CACA;GACCqT,IAAIA,CAACrT,KAAY,EAAO;KACvB,MAAM1B,IAAI,GAAG,IAAI;CAEjB,IAAA,IAAI,CAACA,IAAI,CAACia,OAAO,CAACvY,KAAK,CAAC,EAAG;KAE3B4E,UAAU,CAACtG,IAAI,CAACwH,OAAO,EAACxH,IAAI,CAACM,QAAQ,CAACuM,YAAY,CAAC;KACnD7M,IAAI,CAACqC,OAAO,EAAE;KAEd,MAAMzD,QAAQ,GAAGoB,IAAI,CAACka,YAAY,CAAC3F,IAAI,CAACvU,IAAI,CAAC;CAC7CA,IAAAA,IAAI,CAACM,QAAQ,CAACyU,IAAI,CAAC7S,IAAI,CAAClC,IAAI,EAAE0B,KAAK,EAAE9C,QAAQ,CAAC;CAC/C;;CAEA;CACD;CACA;CACA;CACCsb,EAAAA,YAAYA,CAAEhZ,OAAmB,EAAEuK,SAAqB,EAAO;KAC9D,MAAMzL,IAAI,GAAG,IAAI;CACjBA,IAAAA,IAAI,CAACqC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACvC,IAAI,CAACqC,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;KAC5CrC,IAAI,CAAC8X,SAAS,GAAG,IAAI;CAErB9X,IAAAA,IAAI,CAACma,iBAAiB,EAAE,CAAC;CACzBna,IAAAA,IAAI,CAACiX,YAAY,CAAC/V,OAAO,EAACuK,SAAS,CAAC;KAEpCzL,IAAI,CAACyZ,cAAc,CAACzZ,IAAI,CAAC4R,SAAS,IAAI,CAAC5R,IAAI,CAAC6R,aAAa,CAAC;CAE1D,IAAA,IAAI,CAAC7R,IAAI,CAACqC,OAAO,EAAE;OAClB2E,aAAa,CAAChH,IAAI,CAACwH,OAAO,EAACxH,IAAI,CAACM,QAAQ,CAACuM,YAAY,CAAC;CACvD;KAEA7M,IAAI,CAACF,OAAO,CAAC,MAAM,EAAEoB,OAAO,EAAEuK,SAAS,CAAC;CACzC;CAEAgB,EAAAA,OAAOA,GAAO;CACb,IAAA,IAAI3F,SAAS,GAAG,IAAI,CAACU,OAAO,CAACV,SAAS;CACtC,IAAA,IAAIA,SAAS,CAACW,QAAQ,CAAC,WAAW,CAAC,EAAG;CACtCX,IAAAA,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;CAC1B,IAAA,IAAI,CAACgO,IAAI,CAAC,EAAE,CAAC;CACd;;CAGA;CACD;CACA;CACA;CACCqF,EAAAA,eAAeA,CAAC1Y,KAAY,GAAG,EAAE,EAAE;CAClC,IAAA,IAAIqB,KAAK,GAAG,IAAI,CAAC6Q,aAAa;CAC9B,IAAA,IAAIyG,OAAO,GAAGtX,KAAK,CAACrB,KAAK,KAAKA,KAAK;CACnC,IAAA,IAAI2Y,OAAO,EAAE;OACZtX,KAAK,CAACrB,KAAK,GAAGA,KAAK;CACnBiE,MAAAA,YAAY,CAAC5C,KAAK,EAAC,QAAQ,CAAC;OAC5B,IAAI,CAACmP,SAAS,GAAGxQ,KAAK;CACvB;CACD;;CAEA;CACD;CACA;CACA;CACA;CACA;CACA;CACC4Y,EAAAA,QAAQA,GAAmB;CAE1B,IAAA,IAAI,IAAI,CAACxH,aAAa,IAAI,IAAI,CAAC/P,KAAK,CAAC4N,YAAY,CAAC,UAAU,CAAC,EAAE;OAC9D,OAAO,IAAI,CAACrB,KAAK;CAClB;KAEA,OAAO,IAAI,CAACA,KAAK,CAACsF,IAAI,CAAC,IAAI,CAACtU,QAAQ,CAACoL,SAAS,CAAC;CAChD;;CAEA;CACD;CACA;CACA;CACCmM,EAAAA,QAAQA,CAAEnW,KAAqB,EAAE6Y,MAAe,EAAO;KACtD,IAAI5b,MAAM,GAAG4b,MAAM,GAAG,EAAE,GAAG,CAAC,QAAQ,CAAC;CAErC7X,IAAAA,eAAe,CAAC,IAAI,EAAE/D,MAAM,EAAC,MAAM;CAClC,MAAA,IAAI,CAAC6b,KAAK,CAACD,MAAM,CAAC;CAClB,MAAA,IAAI,CAACE,QAAQ,CAAC/Y,KAAK,EAAE6Y,MAAM,CAAC;CAC7B,KAAC,CAAC;CACH;;CAGA;CACD;CACA;CACA;GACCG,WAAWA,CAAChZ,KAAiB,EAAC;KAC7B,IAAGA,KAAK,KAAK,CAAC,EAAEA,KAAK,GAAG,IAAI,CAAC;CAC7B,IAAA,IAAI,CAACpB,QAAQ,CAAC8L,QAAQ,GAAG1K,KAAK;KAC9B,IAAI,CAAC8U,YAAY,EAAE;CACpB;;CAEA;CACD;CACA;CACA;CACCqD,EAAAA,aAAaA,CAAEtC,IAAa,EAAEvC,CAA2B,EAAE;KAC1D,IAAIhV,IAAI,GAAG,IAAI;CACf,IAAA,IAAI2a,SAAS;CACb,IAAA,IAAItS,CAAC,EAAEuS,KAAK,EAAEC,GAAG,EAAEC,IAAI;CACvB,IAAA,IAAIC,IAAI;CAER,IAAA,IAAI/a,IAAI,CAACM,QAAQ,CAACmN,IAAI,KAAK,QAAQ,EAAE;;CAErC;KACA,IAAI,CAAC8J,IAAI,EAAE;OACVvX,IAAI,CAAC+X,gBAAgB,EAAE;OACvB,IAAI/X,IAAI,CAAC4R,SAAS,EAAE;SACnB5R,IAAI,CAACgW,UAAU,EAAE;CAClB;CACA,MAAA;CACD;;CAEA;KACA2E,SAAS,GAAG3F,CAAC,IAAIA,CAAC,CAACpS,IAAI,CAACsM,WAAW,EAAE;CAErC,IAAA,IAAIyL,SAAS,KAAK,OAAO,IAAIjX,SAAS,CAAC,UAAU,EAACsR,CAAC,CAAC,IAAIhV,IAAI,CAACqS,WAAW,CAAC3S,MAAM,EAAE;CAChFqb,MAAAA,IAAI,GAAG/a,IAAI,CAACgb,aAAa,EAAE;CAC3BJ,MAAAA,KAAK,GAAG7Z,KAAK,CAAC0J,SAAS,CAAC5K,OAAO,CAACqC,IAAI,CAAClC,IAAI,CAACuT,OAAO,CAAC7C,QAAQ,EAAEqK,IAAI,CAAC;CACjEF,MAAAA,GAAG,GAAI9Z,KAAK,CAAC0J,SAAS,CAAC5K,OAAO,CAACqC,IAAI,CAAClC,IAAI,CAACuT,OAAO,CAAC7C,QAAQ,EAAE6G,IAAI,CAAC;OAEhE,IAAIqD,KAAK,GAAGC,GAAG,EAAE;CAChBC,QAAAA,IAAI,GAAIF,KAAK;CACbA,QAAAA,KAAK,GAAGC,GAAG;CACXA,QAAAA,GAAG,GAAKC,IAAI;CACb;OACA,KAAKzS,CAAC,GAAGuS,KAAK,EAAEvS,CAAC,IAAIwS,GAAG,EAAExS,CAAC,EAAE,EAAE;SAC9BkP,IAAI,GAAGvX,IAAI,CAACuT,OAAO,CAAC7C,QAAQ,CAACrI,CAAC,CAAY;SAC1C,IAAIrI,IAAI,CAACqS,WAAW,CAACxS,OAAO,CAAC0X,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;CAC1CvX,UAAAA,IAAI,CAACib,kBAAkB,CAAC1D,IAAI,CAAC;CAC9B;CACD;OACApU,cAAc,CAAC6R,CAAC,CAAC;MACjB,MAAM,IAAK2F,SAAS,KAAK,OAAO,IAAIjX,SAAS,CAACmV,YAAsB,EAAC7D,CAAC,CAAC,IAAO2F,SAAS,KAAK,SAAS,IAAIjX,SAAS,CAAC,UAAU,EAACsR,CAAC,CAAE,EAAE;OACnI,IAAIuC,IAAI,CAACzQ,SAAS,CAACW,QAAQ,CAAC,QAAQ,CAAC,EAAE;CACtCzH,QAAAA,IAAI,CAACkb,gBAAgB,CAAE3D,IAAK,CAAC;CAC9B,OAAC,MAAM;CACNvX,QAAAA,IAAI,CAACib,kBAAkB,CAAC1D,IAAI,CAAC;CAC9B;CACD,KAAC,MAAM;OACNvX,IAAI,CAAC+X,gBAAgB,EAAE;CACvB/X,MAAAA,IAAI,CAACib,kBAAkB,CAAC1D,IAAI,CAAC;CAC9B;;CAEA;KACAvX,IAAI,CAACgW,UAAU,EAAE;CACjB,IAAA,IAAI,CAAChW,IAAI,CAAC4R,SAAS,EAAE;OACpB5R,IAAI,CAACsU,KAAK,EAAE;CACb;CACD;;CAEA;CACD;CACA;CACA;GACC2G,kBAAkBA,CAAE1D,IAAY,EAAE;KACjC,MAAMvX,IAAI,GAAG,IAAI;KACjB,MAAMmb,WAAW,GAAGnb,IAAI,CAACuT,OAAO,CAAC/N,aAAa,CAAC,cAAc,CAAC;CAC9D,IAAA,IAAI2V,WAAW,EAAGnU,aAAa,CAACmU,WAAW,EAAgB,aAAa,CAAC;CAEzE7U,IAAAA,UAAU,CAACiR,IAAI,EAAC,oBAAoB,CAAC;CACrCvX,IAAAA,IAAI,CAACF,OAAO,CAAC,aAAa,EAAEyX,IAAI,CAAC;KACjC,IAAIvX,IAAI,CAACqS,WAAW,CAACxS,OAAO,CAAC0X,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;CACzCvX,MAAAA,IAAI,CAACqS,WAAW,CAAC/S,IAAI,CAAEiY,IAAK,CAAC;CAC9B;CACD;;CAEA;CACD;CACA;CACA;GACC2D,gBAAgBA,CAAE3D,IAAY,EAAE;KAC/B,IAAI6D,GAAG,GAAG,IAAI,CAAC/I,WAAW,CAACxS,OAAO,CAAC0X,IAAI,CAAC;KACxC,IAAI,CAAClF,WAAW,CAACzS,MAAM,CAACwb,GAAG,EAAE,CAAC,CAAC;CAC/BpU,IAAAA,aAAa,CAACuQ,IAAI,EAAC,QAAQ,CAAC;CAC7B;;CAEA;CACD;CACA;CACA;CACCQ,EAAAA,gBAAgBA,GAAE;CACjB/Q,IAAAA,aAAa,CAAC,IAAI,CAACqL,WAAW,EAAC,QAAQ,CAAC;KACxC,IAAI,CAACA,WAAW,GAAG,EAAE;CACtB;;CAEA;CACD;CACA;CACA;CACA;CACC6G,EAAAA,eAAeA,CAAE/J,MAAuB,EAACkM,MAAc,GAAC,IAAI,EAAO;CAElE,IAAA,IAAIlM,MAAM,KAAK,IAAI,CAACiD,YAAY,EAAE;CACjC,MAAA;CACD;KAEA,IAAI,CAAC+H,iBAAiB,EAAE;KACxB,IAAI,CAAChL,MAAM,EAAG;KAEd,IAAI,CAACiD,YAAY,GAAGjD,MAAM;CAC1B5G,IAAAA,OAAO,CAAC,IAAI,CAACsL,UAAU,EAAC;CAAC,MAAA,uBAAuB,EAAC1E,MAAM,CAAC9K,YAAY,CAAC,IAAI;CAAC,KAAC,CAAC;KAC5EkE,OAAO,CAAC4G,MAAM,EAAC;CAAC,MAAA,eAAe,EAAC;CAAM,KAAC,CAAC;CACxC7I,IAAAA,UAAU,CAAC6I,MAAM,EAAC,QAAQ,CAAC;CAC3B,IAAA,IAAIkM,MAAM,EAAG,IAAI,CAACC,cAAc,CAACnM,MAAM,CAAC;CACzC;;CAEA;CACD;CACA;CACA;CACCmM,EAAAA,cAAcA,CAAEnM,MAAuB,EAAEoM,QAAgB,EAAO;KAE/D,IAAI,CAACpM,MAAM,EAAG;CAEd,IAAA,MAAM7J,OAAO,GAAI,IAAI,CAACoO,gBAAgB;CACtC,IAAA,MAAM8H,WAAW,GAAGlW,OAAO,CAACmW,YAAY;CACxC,IAAA,MAAMC,SAAS,GAAIpW,OAAO,CAACoW,SAAS,IAAI,CAAC;CACzC,IAAA,MAAMC,WAAW,GAAGxM,MAAM,CAACyM,YAAY;CACvC,IAAA,MAAMC,CAAC,GAAM1M,MAAM,CAAC2M,qBAAqB,EAAE,CAACC,GAAG,GAAGzW,OAAO,CAACwW,qBAAqB,EAAE,CAACC,GAAG,GAAGL,SAAS;CAEjG,IAAA,IAAIG,CAAC,GAAGF,WAAW,GAAGH,WAAW,GAAGE,SAAS,EAAE;OAC9C,IAAI,CAACL,MAAM,CAACQ,CAAC,GAAGL,WAAW,GAAGG,WAAW,EAAEJ,QAAQ,CAAC;CAErD,KAAC,MAAM,IAAIM,CAAC,GAAGH,SAAS,EAAE;CACzB,MAAA,IAAI,CAACL,MAAM,CAACQ,CAAC,EAAEN,QAAQ,CAAC;CACzB;CACD;;CAEA;CACD;CACA;CACA;CACCF,EAAAA,MAAMA,CAAEK,SAAgB,EAAEH,QAAgB,EAAO;CAChD,IAAA,MAAMjW,OAAO,GAAG,IAAI,CAACoO,gBAAgB;CACrC,IAAA,IAAI6H,QAAQ,EAAE;CACbjW,MAAAA,OAAO,CAACe,KAAK,CAAC2V,cAAc,GAAGT,QAAQ;CACxC;KACAjW,OAAO,CAACoW,SAAS,GAAGA,SAAS;CAC7BpW,IAAAA,OAAO,CAACe,KAAK,CAAC2V,cAAc,GAAG,EAAE;CAClC;;CAEA;CACD;CACA;CACA;CACC7B,EAAAA,iBAAiBA,GAAE;KAClB,IAAI,IAAI,CAAC/H,YAAY,EAAE;CACtBpL,MAAAA,aAAa,CAAC,IAAI,CAACoL,YAAY,EAAC,QAAQ,CAAC;CACzC7J,MAAAA,OAAO,CAAC,IAAI,CAAC6J,YAAY,EAAC;CAAC,QAAA,eAAe,EAAC;CAAI,OAAC,CAAC;CAClD;KACA,IAAI,CAACA,YAAY,GAAG,IAAI;CACxB7J,IAAAA,OAAO,CAAC,IAAI,CAACsL,UAAU,EAAC;CAAC,MAAA,uBAAuB,EAAC;CAAI,KAAC,CAAC;CACxD;;CAGA;CACD;CACA;CACCiF,EAAAA,SAASA,GAAG;KACX,MAAM9Y,IAAI,GAAG,IAAI;CAEjB,IAAA,IAAIA,IAAI,CAACM,QAAQ,CAACmN,IAAI,KAAK,QAAQ,EAAE;CAErC,IAAA,MAAM4E,WAAW,GAAGrS,IAAI,CAACic,eAAe,EAAE;CAE1C,IAAA,IAAI,CAAC5J,WAAW,CAAC3S,MAAM,EAAG;KAE1BM,IAAI,CAACgW,UAAU,EAAE;KACjBhW,IAAI,CAAC2W,KAAK,EAAE;KAEZ3W,IAAI,CAACqS,WAAW,GAAGA,WAAW;CAC9B7Q,IAAAA,OAAO,CAAE6Q,WAAW,EAAGkF,IAAY,IAAK;CACvCvX,MAAAA,IAAI,CAACib,kBAAkB,CAAC1D,IAAI,CAAC;CAC9B,KAAC,CAAC;CAEH;;CAEA;CACD;CACA;CACA;CACCvB,EAAAA,UAAUA,GAAE;KACX,IAAIhW,IAAI,GAAG,IAAI;KAEf,IAAI,CAACA,IAAI,CAACuT,OAAO,CAAC9L,QAAQ,CAACzH,IAAI,CAAC4T,aAAa,CAAC,EAAG;CAEjDrL,IAAAA,OAAO,CAACvI,IAAI,CAAC4T,aAAa,EAAC;CAACzF,MAAAA,WAAW,EAACnO,IAAI,CAACM,QAAQ,CAAC6N;CAAW,KAAC,CAAC;KAEnE,IAAInO,IAAI,CAACqS,WAAW,CAAC3S,MAAM,GAAG,CAAC,IAAK,CAACM,IAAI,CAAC4R,SAAS,IAAI5R,IAAI,CAACM,QAAQ,CAAC8N,eAAe,IAAIpO,IAAI,CAACsP,KAAK,CAAC5P,MAAM,GAAG,CAAE,EAAE;OAC/GM,IAAI,CAACoa,eAAe,EAAE;OACtBpa,IAAI,CAAC6R,aAAa,GAAG,IAAI;CAE1B,KAAC,MAAI;CAEJ,MAAA,IAAI7R,IAAI,CAACM,QAAQ,CAAC8N,eAAe,IAAIpO,IAAI,CAACsP,KAAK,CAAC5P,MAAM,GAAG,CAAC,EAAE;CAC3D6I,QAAAA,OAAO,CAACvI,IAAI,CAAC4T,aAAa,EAAC;CAACzF,UAAAA,WAAW,EAAC;CAAE,SAAC,CAAC;CAC7C;OACAnO,IAAI,CAAC6R,aAAa,GAAG,KAAK;CAC3B;CAEA7R,IAAAA,IAAI,CAACwH,OAAO,CAACV,SAAS,CAACoV,MAAM,CAAC,cAAc,EAAElc,IAAI,CAAC6R,aAAc,CAAC;CACnE;;CAEA;CACD;CACA;CACCqG,EAAAA,UAAUA,GAAE;KACX,OAAO,IAAI,CAACtE,aAAa,CAAClS,KAAK,CAAC2D,IAAI,EAAE;CACvC;;CAEA;CACD;CACA;CACCiP,EAAAA,KAAKA,GAAG;KACP,IAAItU,IAAI,GAAG,IAAI;CACf,IAAA,IAAIA,IAAI,CAACuR,UAAU,IAAIvR,IAAI,CAACwR,UAAU,EAAE;KAExCxR,IAAI,CAAC+R,WAAW,GAAG,IAAI;CAEvB,IAAA,IAAI/R,IAAI,CAAC4T,aAAa,CAACuI,WAAW,EAAE;CACnCnc,MAAAA,IAAI,CAAC4T,aAAa,CAACU,KAAK,EAAE;CAC3B,KAAC,MAAI;CACJtU,MAAAA,IAAI,CAAC6T,UAAU,CAACS,KAAK,EAAE;CACxB;CAEArS,IAAAA,UAAU,CAAC,MAAM;OAChBjC,IAAI,CAAC+R,WAAW,GAAG,KAAK;OACxB/R,IAAI,CAAC2V,OAAO,EAAE;MACd,EAAE,CAAC,CAAC;CACN;;CAEA;CACD;CACA;CACA;CACCI,EAAAA,IAAIA,GAAQ;CACX,IAAA,IAAI,CAAClC,UAAU,CAACkC,IAAI,EAAE;KACtB,IAAI,CAACL,MAAM,EAAE;CACd;;CAEA;CACD;CACA;CACA;CACA;CACA;CACA;GACC0G,gBAAgBA,CAACvX,KAAY,EAAE;CAC9B,IAAA,OAAO,IAAI,CAACsO,MAAM,CAACiJ,gBAAgB,CAACvX,KAAK,EAAE,IAAI,CAACwX,gBAAgB,EAAE,CAAC;CACpE;;CAEA;CACD;CACA;CACA;CACA;CACA;CACA;CACCA,EAAAA,gBAAgBA,GAAG;CAClB,IAAA,IAAI/b,QAAQ,GAAG,IAAI,CAACA,QAAQ;CAC5B,IAAA,IAAIgc,IAAI,GAAGhc,QAAQ,CAACgN,SAAS;CAC7B,IAAA,IAAI,OAAOhN,QAAQ,CAACgN,SAAS,KAAK,QAAQ,EAAE;CAC3CgP,MAAAA,IAAI,GAAG,CAAC;SAACC,KAAK,EAAEjc,QAAQ,CAACgN;CAAS,OAAC,CAAC;CACrC;KAEA,OAAO;OACNkP,MAAM,EAAQlc,QAAQ,CAACiN,WAAW;OAClCkP,WAAW,EAAGnc,QAAQ,CAACkN,iBAAiB;CACxC8O,MAAAA,IAAI,EAAUA,IAAI;OAClBI,OAAO,EAAOpc,QAAQ,CAACoc;MACvB;CACF;;CAEA;CACD;CACA;CACA;CACA;GACCC,MAAMA,CAAC9X,KAAY,EAAgC;KAClD,IAAI+X,MAAM,EAAEC,cAAc;KAC1B,IAAI7c,IAAI,GAAO,IAAI;CACnB,IAAA,IAAIkB,OAAO,GAAI,IAAI,CAACmb,gBAAgB,EAAE;;CAEtC;CACA,IAAA,IAAKrc,IAAI,CAACM,QAAQ,CAACwc,KAAK,EAAE;CACzBD,MAAAA,cAAc,GAAG7c,IAAI,CAACM,QAAQ,CAACwc,KAAK,CAAC5a,IAAI,CAAClC,IAAI,EAAC6E,KAAK,CAAC;CACrD,MAAA,IAAI,OAAOgY,cAAc,KAAK,UAAU,EAAE;CACzC,QAAA,MAAM,IAAItb,KAAK,CAAC,uEAAuE,CAAC;CACzF;CACD;;CAEA;CACA,IAAA,IAAIsD,KAAK,KAAK7E,IAAI,CAAC8X,SAAS,EAAE;OAC7B9X,IAAI,CAAC8X,SAAS,GAAKjT,KAAK;CACxB+X,MAAAA,MAAM,GAAO5c,IAAI,CAACmT,MAAM,CAACwJ,MAAM,CAAC9X,KAAK,EAAEsB,MAAM,CAACC,MAAM,CAAClF,OAAO,EAAE;CAAC4b,QAAAA,KAAK,EAAED;CAAc,OAAC,CAAC,CAAC;OACvF7c,IAAI,CAAC+c,cAAc,GAAIH,MAAM;CAC9B,KAAC,MAAM;OACNA,MAAM,GAAOzW,MAAM,CAACC,MAAM,CAAE,EAAE,EAAEpG,IAAI,CAAC+c,cAAc,CAAC;CACrD;;CAEA;CACA,IAAA,IAAI/c,IAAI,CAACM,QAAQ,CAAC+L,YAAY,EAAE;OAC/BuQ,MAAM,CAACtN,KAAK,GAAGsN,MAAM,CAACtN,KAAK,CAAClI,MAAM,CAAEmQ,IAAI,IAAK;CAC5C,QAAA,IAAIyF,MAAM,GAAGvb,QAAQ,CAAC8V,IAAI,CAACpT,EAAE,CAAC;CAC9B,QAAA,OAAO,EAAE6Y,MAAM,IAAIhd,IAAI,CAACsP,KAAK,CAACzP,OAAO,CAACmd,MAAM,CAAC,KAAK,CAAC,CAAC,CAAE;CACvD,OAAC,CAAC;CACH;CAEA,IAAA,OAAOJ,MAAM;CACd;;CAEA;CACD;CACA;CACA;CACA;CACCnD,EAAAA,cAAcA,CAAEwD,eAAuB,GAAG,IAAI,EAAE;CAC/C,IAAA,IAAI5U,CAAC,EAAE6U,CAAC,EAAEC,CAAC,EAAE3d,CAAC,EAAEgR,QAAQ,EAAE/E,SAAS,EAAE2R,IAAqB,EAAEC,iBAAiB,EAAEC,YAAY;CAC3F,IAAA,IAAIxR,MAAM;KAGV,MAAMyR,MAA6B,GAAG,EAAE;KACxC,MAAMC,YAAoB,GAAG,EAAE;KAE/B,IAAIxd,IAAI,GAAO,IAAI;CACnB,IAAA,IAAI6E,KAAK,GAAO7E,IAAI,CAACkY,UAAU,EAAE;CACjC,IAAA,MAAMuF,UAAU,GAAK5Y,KAAK,KAAK7E,IAAI,CAAC8X,SAAS,IAAKjT,KAAK,IAAI,EAAE,IAAI7E,IAAI,CAAC8X,SAAS,IAAI,IAAK;CACxF,IAAA,IAAI4F,OAAO,GAAO1d,IAAI,CAAC2c,MAAM,CAAC9X,KAAK,CAAC;KACpC,IAAI8Y,aAA8B,GAAG,IAAI;KACzC,IAAIC,aAAa,GAAK5d,IAAI,CAACM,QAAQ,CAAC4L,UAAU,IAAI,KAAK;CACvD,IAAA,IAAIwH,gBAAgB,GAAI1T,IAAI,CAAC0T,gBAAgB;CAG7C,IAAA,IAAI+J,UAAU,EAAE;OACfE,aAAa,GAAK3d,IAAI,CAACoS,YAAY;CAEnC,MAAA,IAAIuL,aAAa,EAAE;CAClBL,QAAAA,YAAY,GAAGK,aAAa,CAACE,OAAO,CAAC,cAAc,CAAgB;CACpE;CACD;;CAEA;CACAre,IAAAA,CAAC,GAAGke,OAAO,CAACpO,KAAK,CAAC5P,MAAM;KACxB,IAAI,OAAOM,IAAI,CAACM,QAAQ,CAAC6L,UAAU,KAAK,QAAQ,EAAE;CACjD3M,MAAAA,CAAC,GAAG8C,IAAI,CAACwb,GAAG,CAACte,CAAC,EAAEQ,IAAI,CAACM,QAAQ,CAAC6L,UAAU,CAAC;CAC1C;KAEA,IAAI3M,CAAC,GAAG,CAAC,EAAE;CACVoe,MAAAA,aAAa,GAAG,IAAI;CACrB;;CAEA;CACA,IAAA,MAAMG,gBAAgB,GAAGA,CAACvN,QAAe,EAACa,KAAY,KAA+B;CAEpF,MAAA,IAAI2M,aAAa,GAAGT,MAAM,CAAC/M,QAAQ,CAAC;OAEpC,IAAIwN,aAAa,KAAKre,SAAS,EAAE;CAChC,QAAA,IAAIse,WAAW,GAAGT,YAAY,CAACQ,aAAa,CAAC;SAC7C,IAAIC,WAAW,KAAKte,SAAS,EAAE;CAC9B,UAAA,OAAO,CAACqe,aAAa,EAACC,WAAW,CAACC,QAAQ,CAAC;CAC5C;CACD;CAEA,MAAA,IAAIC,cAAc,GAAGjZ,QAAQ,CAACkZ,sBAAsB,EAAE;OACtDJ,aAAa,GAAGR,YAAY,CAAC9d,MAAM;OACnC8d,YAAY,CAACle,IAAI,CAAC;CAAC4e,QAAAA,QAAQ,EAACC,cAAc;SAAC9M,KAAK;CAACb,QAAAA;CAAQ,OAAC,CAAC;CAE3D,MAAA,OAAO,CAACwN,aAAa,EAACG,cAAc,CAAC;MACrC;;CAED;KACA,KAAK9V,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7I,CAAC,EAAE6I,CAAC,EAAE,EAAE;CAEvB;CACA,MAAA,IAAIkP,IAAI,GAAKmG,OAAO,CAACpO,KAAK,CAACjH,CAAC,CAAC;OAC7B,IAAI,CAACkP,IAAI,EAAG;CAEZ,MAAA,IAAI8G,SAAS,GAAI9G,IAAI,CAACpT,EAAE;CACxB,MAAA,IAAIgL,MAAM,GAAKnP,IAAI,CAACkB,OAAO,CAACmd,SAAS,CAAC;OAEtC,IAAIlP,MAAM,KAAKxP,SAAS,EAAG;CAE3B,MAAA,IAAI2e,QAAQ,GAAI3c,QAAQ,CAAC0c,SAAS,CAAC;OACnC,IAAIE,SAAS,GAAIve,IAAI,CAACwe,SAAS,CAACF,QAAQ,EAAC,IAAI,CAAgB;;CAE7D;CACA,MAAA,IAAI,CAACte,IAAI,CAACM,QAAQ,CAAC+L,YAAY,EAAE;CAChCkS,QAAAA,SAAS,CAACzX,SAAS,CAACoV,MAAM,CAAC,UAAU,EAAElc,IAAI,CAACsP,KAAK,CAACmP,QAAQ,CAACH,QAAQ,CAAE,CAAC;CACvE;OAEA9N,QAAQ,GAAMrB,MAAM,CAACnP,IAAI,CAACM,QAAQ,CAACyM,aAAa,CAAC,IAAI,EAAE;CACvDtB,MAAAA,SAAS,GAAK1K,KAAK,CAACC,OAAO,CAACwP,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;CAG7D,MAAA,KAAK0M,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG1R,SAAS,IAAIA,SAAS,CAAC/L,MAAM,EAAEwd,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;CAC1D1M,QAAAA,QAAQ,GAAG/E,SAAS,CAACyR,CAAC,CAAC;CAEvB,QAAA,IAAI7L,KAAK,GAAGlC,MAAM,CAACO,MAAM;CACzB,QAAA,IAAIgP,aAAa,GAAG1e,IAAI,CAACyL,SAAS,CAAC+E,QAAQ,CAAC;SAC5C,IAAIkO,aAAa,KAAK/e,SAAS,EAAE;CAChC6Q,UAAAA,QAAQ,GAAG,EAAE;CACd,SAAC,MAAI;WACJa,KAAK,GAAGqN,aAAa,CAAChP,MAAM;CAC7B;SAEA,MAAM,CAACsO,aAAa,EAACG,cAAc,CAAC,GAAGJ,gBAAgB,CAACvN,QAAQ,EAACa,KAAK,CAAC;;CAGvE;SACA,IAAI6L,CAAC,GAAG,CAAC,EAAE;CACVqB,UAAAA,SAAS,GAAGA,SAAS,CAAC1U,SAAS,CAAC,IAAI,CAAgB;WACpDtB,OAAO,CAACgW,SAAS,EAAC;CAACpa,YAAAA,EAAE,EAAEgL,MAAM,CAACwP,GAAG,GAAC,SAAS,GAACzB,CAAC;CAAC,YAAA,eAAe,EAAC;CAAI,WAAC,CAAC;CACpEqB,UAAAA,SAAS,CAACzX,SAAS,CAACC,GAAG,CAAC,WAAW,CAAC;CACpCC,UAAAA,aAAa,CAACuX,SAAS,EAAC,QAAQ,CAAC;;CAGjC;CACA,UAAA,IAAIve,IAAI,CAACoS,YAAY,IAAIpS,IAAI,CAACoS,YAAY,CAACxC,OAAO,CAAClO,KAAK,IAAI2c,SAAS,EAAE;CACtE,YAAA,IAAIf,YAAY,IAAIA,YAAY,CAAC1N,OAAO,CAACK,KAAK,KAAKO,QAAQ,CAACoO,QAAQ,EAAE,EAAE;CACvEjB,cAAAA,aAAa,GAAGY,SAAS;CAC1B;CACD;CACD;CAEAJ,QAAAA,cAAc,CAACrU,WAAW,CAACyU,SAAS,CAAC;SACrC,IAAI/N,QAAQ,IAAI,EAAE,EAAE;CACnB+M,UAAAA,MAAM,CAAC/M,QAAQ,CAAC,GAAGwN,aAAa;CACjC;CACD;CACD;;CAEA;CACA,IAAA,IAAIhe,IAAI,CAACM,QAAQ,CAAC+M,iBAAiB,EAAE;CACpCmQ,MAAAA,YAAY,CAAClB,IAAI,CAAC,CAACuC,CAAC,EAAEC,CAAC,KAAK;CAC3B,QAAA,OAAOD,CAAC,CAACxN,KAAK,GAAGyN,CAAC,CAACzN,KAAK;CACzB,OAAC,CAAC;CACH;;CAEA;CACA+L,IAAAA,IAAI,GAAGlY,QAAQ,CAACkZ,sBAAsB,EAAE;CACxC5c,IAAAA,OAAO,CAAEgc,YAAY,EAAGuB,WAAiB,IAAK;CAE7C,MAAA,IAAIZ,cAAc,GAAGY,WAAW,CAACb,QAAQ;CACzC,MAAA,IAAI1N,QAAQ,GAAGuO,WAAW,CAACvO,QAAQ;OAEnC,IAAI,CAAC2N,cAAc,IAAI,CAACA,cAAc,CAACzN,QAAQ,CAAChR,MAAM,EAAG;CAEzD,MAAA,IAAIsf,aAAa,GAAGhf,IAAI,CAACyL,SAAS,CAAC+E,QAAQ,CAAC;OAE5C,IAAIwO,aAAa,KAAKrf,SAAS,EAAE;CAEhC,QAAA,IAAIsf,aAAa,GAAG/Z,QAAQ,CAACkZ,sBAAsB,EAAE;SACrD,IAAIc,MAAM,GAAGlf,IAAI,CAACsO,MAAM,CAAC,iBAAiB,EAAE0Q,aAAa,CAAC;CAC1Dxa,QAAAA,MAAM,CAAEya,aAAa,EAAEC,MAAO,CAAC;CAC/B1a,QAAAA,MAAM,CAAEya,aAAa,EAAEd,cAAe,CAAC;CAEvC,QAAA,IAAIgB,UAAU,GAAGnf,IAAI,CAACsO,MAAM,CAAC,UAAU,EAAE;CAAC2B,UAAAA,KAAK,EAAC+O,aAAa;CAAC9d,UAAAA,OAAO,EAAC+d;CAAa,SAAE,CAAC;CAEtFza,QAAAA,MAAM,CAAE4Y,IAAI,EAAE+B,UAAW,CAAC;CAE3B,OAAC,MAAM;CACN3a,QAAAA,MAAM,CAAE4Y,IAAI,EAAEe,cAAe,CAAC;CAC/B;CACD,KAAC,CAAC;KAEFzK,gBAAgB,CAACtO,SAAS,GAAG,EAAE;CAC/BZ,IAAAA,MAAM,CAAEkP,gBAAgB,EAAE0J,IAAK,CAAC;;CAEhC;CACA,IAAA,IAAIpd,IAAI,CAACM,QAAQ,CAAC0I,SAAS,EAAE;OAC5BsB,eAAe,CAAEoJ,gBAAiB,CAAC;OACnC,IAAIgK,OAAO,CAAC7Y,KAAK,CAACnF,MAAM,IAAIge,OAAO,CAAC0B,MAAM,CAAC1f,MAAM,EAAE;CAClD8B,QAAAA,OAAO,CAAEkc,OAAO,CAAC0B,MAAM,EAAGC,GAAG,IAAK;CACjCrW,UAAAA,SAAS,CAAE0K,gBAAgB,EAAE2L,GAAG,CAACnW,KAAK,CAAC;CACxC,SAAC,CAAC;CACH;CACD;;CAEA;KACA,IAAIoW,YAAY,GAAIC,QAAyB,IAAK;CACjD,MAAA,IAAIja,OAAO,GAAGtF,IAAI,CAACsO,MAAM,CAACiR,QAAQ,EAAC;CAACxc,QAAAA,KAAK,EAAC8B;CAAK,OAAC,CAAC;CACjD,MAAA,IAAIS,OAAO,EAAE;CACZsY,QAAAA,aAAa,GAAG,IAAI;SACpBlK,gBAAgB,CAAC8L,YAAY,CAACla,OAAO,EAAEoO,gBAAgB,CAACnO,UAAU,CAAC;CACpE;CACA,MAAA,OAAOD,OAAO;MACd;;CAGD;KACA,IAAItF,IAAI,CAACqC,OAAO,EAAE;OACjBid,YAAY,CAAC,SAAS,CAAC;;CAExB;CACA,KAAC,MAAK,IAAI,CAACtf,IAAI,CAACM,QAAQ,CAAC+N,UAAU,CAACnM,IAAI,CAAClC,IAAI,EAAC6E,KAAK,CAAC,EAAE;OACrDya,YAAY,CAAC,aAAa,CAAC;;CAE5B;MACC,MAAK,IAAI5B,OAAO,CAACpO,KAAK,CAAC5P,MAAM,KAAK,CAAC,EAAE;OACrC4f,YAAY,CAAC,YAAY,CAAC;CAE3B;;CAIA;CACAjC,IAAAA,iBAAiB,GAAGrd,IAAI,CAACyf,SAAS,CAAC5a,KAAK,CAAC;CACzC,IAAA,IAAIwY,iBAAiB,EAAE;CACtBvR,MAAAA,MAAM,GAAGwT,YAAY,CAAC,eAAe,CAAC;CACvC;;CAGA;KACAtf,IAAI,CAACiS,UAAU,GAAGyL,OAAO,CAACpO,KAAK,CAAC5P,MAAM,GAAG,CAAC,IAAI2d,iBAAiB;CAC/D,IAAA,IAAIO,aAAa,EAAE;CAElB,MAAA,IAAIF,OAAO,CAACpO,KAAK,CAAC5P,MAAM,GAAG,CAAC,EAAE;CAE7B,QAAA,IAAI,CAACie,aAAa,IAAI3d,IAAI,CAACM,QAAQ,CAACmN,IAAI,KAAK,QAAQ,IAAIzN,IAAI,CAACsP,KAAK,CAAC,CAAC,CAAC,IAAI3P,SAAS,EAAE;WACpFge,aAAa,GAAG3d,IAAI,CAACwe,SAAS,CAACxe,IAAI,CAACsP,KAAK,CAAC,CAAC,CAAC,CAAC;CAC9C;CAEA,QAAA,IAAI,CAACoE,gBAAgB,CAACjM,QAAQ,CAACkW,aAAa,CAAC,EAAG;WAE/C,IAAI+B,YAAY,GAAG,CAAC;WACpB,IAAI5T,MAAM,IAAI,CAAC9L,IAAI,CAACM,QAAQ,CAACiM,aAAa,EAAE;CAC3CmT,YAAAA,YAAY,GAAG,CAAC;CACjB;WACA/B,aAAa,GAAG3d,IAAI,CAAC2f,UAAU,EAAE,CAACD,YAAY,CAAgB;CAC/D;QAEA,MAAK,IAAI5T,MAAM,EAAE;CACjB6R,QAAAA,aAAa,GAAG7R,MAAM;CACvB;CAEA,MAAA,IAAImR,eAAe,IAAI,CAACjd,IAAI,CAACsR,MAAM,EAAE;SACpCtR,IAAI,CAAC+Y,IAAI,EAAE;CACX/Y,QAAAA,IAAI,CAACsb,cAAc,CAACqC,aAAa,EAAC,MAAM,CAAC;CAC1C;CACA3d,MAAAA,IAAI,CAACkZ,eAAe,CAACyE,aAAa,CAAC;CAEpC,KAAC,MAAI;OACJ3d,IAAI,CAACma,iBAAiB,EAAE;CACxB,MAAA,IAAI8C,eAAe,IAAIjd,IAAI,CAACsR,MAAM,EAAE;CACnCtR,QAAAA,IAAI,CAAC2W,KAAK,CAAC,KAAK,CAAC,CAAC;CACnB;CACD;CACD;;CAEA;CACD;CACA;CACA;CACCgJ,EAAAA,UAAUA,GAAW;CACpB,IAAA,OAAO,IAAI,CAACjM,gBAAgB,CAAClJ,gBAAgB,CAAC,mBAAmB,CAAC;CACnE;;CAIA;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACCwF,EAAAA,SAASA,CAAE1G,IAAc,EAAEsW,YAAY,GAAG,KAAK,EAAgB;KAC9D,MAAM5f,IAAI,GAAG,IAAI;;CAEjB;CACA;CACA,IAAA,IAAIe,KAAK,CAACC,OAAO,CAACsI,IAAI,CAAC,EAAE;CACxBtJ,MAAAA,IAAI,CAACkX,UAAU,CAAE5N,IAAI,EAAEsW,YAAY,CAAC;CACpC,MAAA,OAAO,KAAK;CACb;CAEA,IAAA,MAAM/e,GAAG,GAAGY,QAAQ,CAAC6H,IAAI,CAACtJ,IAAI,CAACM,QAAQ,CAAC0M,UAAU,CAAC,CAAC;CACpD,IAAA,IAAInM,GAAG,KAAK,IAAI,IAAIb,IAAI,CAACkB,OAAO,CAACC,cAAc,CAACN,GAAG,CAAC,EAAE;CACrD,MAAA,OAAO,KAAK;CACb;KAEAyI,IAAI,CAACoG,MAAM,GAAKpG,IAAI,CAACoG,MAAM,IAAI,EAAE1P,IAAI,CAACqR,KAAK;KAC3C/H,IAAI,CAACqV,GAAG,GAAK3e,IAAI,CAACgT,OAAO,GAAG,OAAO,GAAG1J,IAAI,CAACoG,MAAM;CACjD1P,IAAAA,IAAI,CAACkB,OAAO,CAACL,GAAG,CAAC,GAAGyI,IAAI;KACxBtJ,IAAI,CAAC8X,SAAS,GAAI,IAAI;CAEtB,IAAA,IAAI8H,YAAY,EAAE;CACjB5f,MAAAA,IAAI,CAACsS,WAAW,CAACzR,GAAG,CAAC,GAAG+e,YAAY;OACpC5f,IAAI,CAACF,OAAO,CAAC,YAAY,EAAEe,GAAG,EAAEyI,IAAI,CAAC;CACtC;CAEA,IAAA,OAAOzI,GAAG;CACX;;CAEA;CACD;CACA;CACA;CACCqW,EAAAA,UAAUA,CAAE5N,IAAgB,EAAEsW,YAAY,GAAG,KAAK,EAAO;CACxDpe,IAAAA,OAAO,CAAE8H,IAAI,EAAGuW,GAAa,IAAK;CACjC,MAAA,IAAI,CAAC7P,SAAS,CAAC6P,GAAG,EAAED,YAAY,CAAC;CAClC,KAAC,CAAC;CACH;;CAEA;CACD;CACA;GACCE,cAAcA,CAAExW,IAAc,EAAgB;CAC7C,IAAA,OAAO,IAAI,CAAC0G,SAAS,CAAC1G,IAAI,CAAC;CAC5B;;CAEA;CACD;CACA;CACA;CACA;GACC6N,mBAAmBA,CAAC7N,IAAc,EAAE;CACnC,IAAA,IAAIzI,GAAG,GAAGY,QAAQ,CAAC6H,IAAI,CAAC,IAAI,CAAChJ,QAAQ,CAAC8M,kBAAkB,CAAC,CAAC;CAE1D,IAAA,IAAKvM,GAAG,KAAK,IAAI,EAAG,OAAO,KAAK;KAEhCyI,IAAI,CAACoG,MAAM,GAAGpG,IAAI,CAACoG,MAAM,IAAI,EAAE,IAAI,CAAC2B,KAAK;CACzC,IAAA,IAAI,CAAC5F,SAAS,CAAC5K,GAAG,CAAC,GAAGyI,IAAI;CAC1B,IAAA,OAAOzI,GAAG;CACX;;CAEA;CACD;CACA;CACA;CACA;CACCkf,EAAAA,cAAcA,CAAC5b,EAAS,EAAEmF,IAAc,EAAE;CACzC,IAAA,IAAI0W,SAAS;KACb1W,IAAI,CAAC,IAAI,CAAChJ,QAAQ,CAAC8M,kBAAkB,CAAC,GAAGjJ,EAAE;KAE3C,IAAI6b,SAAS,GAAG,IAAI,CAAC7I,mBAAmB,CAAC7N,IAAI,CAAC,EAAE;OAC/C,IAAI,CAACxJ,OAAO,CAAC,cAAc,EAAEkgB,SAAS,EAAE1W,IAAI,CAAC;CAC9C;CACD;;CAEA;CACD;CACA;CACA;GACC2W,iBAAiBA,CAAC9b,EAAS,EAAE;KAC5B,IAAI,IAAI,CAACsH,SAAS,CAACtK,cAAc,CAACgD,EAAE,CAAC,EAAE;CACtC,MAAA,OAAO,IAAI,CAACsH,SAAS,CAACtH,EAAE,CAAC;OACzB,IAAI,CAAC+b,UAAU,EAAE;CACjB,MAAA,IAAI,CAACpgB,OAAO,CAAC,iBAAiB,EAAEqE,EAAE,CAAC;CACpC;CACD;;CAEA;CACD;CACA;CACCgc,EAAAA,iBAAiBA,GAAG;CACnB,IAAA,IAAI,CAAC1U,SAAS,GAAG,EAAE;KACnB,IAAI,CAACyU,UAAU,EAAE;CACjB,IAAA,IAAI,CAACpgB,OAAO,CAAC,gBAAgB,CAAC;CAC/B;;CAEA;CACD;CACA;CACA;CACA;CACA;CACCsgB,EAAAA,YAAYA,CAAC1e,KAAY,EAAE4H,IAAc,EAAE;KAC1C,MAAMtJ,IAAI,GAAG,IAAI;CACjB,IAAA,IAAIqgB,QAAQ;CACZ,IAAA,IAAIC,UAAU;CAEd,IAAA,MAAMC,SAAS,GAAI9e,QAAQ,CAACC,KAAK,CAAC;CAClC,IAAA,MAAM8e,SAAS,GAAI/e,QAAQ,CAAC6H,IAAI,CAACtJ,IAAI,CAACM,QAAQ,CAAC0M,UAAU,CAAC,CAAC;;CAE3D;KACA,IAAIuT,SAAS,KAAK,IAAI,EAAG;CAEzB,IAAA,MAAME,QAAQ,GAAIzgB,IAAI,CAACkB,OAAO,CAACqf,SAAS,CAAC;KAEzC,IAAIE,QAAQ,IAAI9gB,SAAS,EAAG;KAC5B,IAAI,OAAO6gB,SAAS,KAAK,QAAQ,EAAG,MAAM,IAAIjf,KAAK,CAAC,kCAAkC,CAAC;CAGvF,IAAA,MAAM4N,MAAM,GAAInP,IAAI,CAACwe,SAAS,CAAC+B,SAAS,CAAC;CACzC,IAAA,MAAMhJ,IAAI,GAAKvX,IAAI,CAAC0gB,OAAO,CAACH,SAAS,CAAC;KAGtCjX,IAAI,CAACoG,MAAM,GAAGpG,IAAI,CAACoG,MAAM,IAAI+Q,QAAQ,CAAC/Q,MAAM;CAC5C,IAAA,OAAO1P,IAAI,CAACkB,OAAO,CAACqf,SAAS,CAAC;;CAE9B;CACA;CACAvgB,IAAAA,IAAI,CAAC2gB,YAAY,CAACH,SAAS,CAAC;CAE5BxgB,IAAAA,IAAI,CAACkB,OAAO,CAACsf,SAAS,CAAC,GAAGlX,IAAI;;CAE9B;CACA,IAAA,IAAI6F,MAAM,EAAE;OACX,IAAInP,IAAI,CAAC0T,gBAAgB,CAACjM,QAAQ,CAAC0H,MAAM,CAAC,EAAE;SAE3C,MAAMyR,UAAU,GAAG5gB,IAAI,CAACyT,OAAO,CAAC,QAAQ,EAAEnK,IAAI,CAAC;CAC/CV,QAAAA,WAAW,CAACuG,MAAM,EAAEyR,UAAU,CAAC;CAE/B,QAAA,IAAI5gB,IAAI,CAACoS,YAAY,KAAKjD,MAAM,EAAE;CACjCnP,UAAAA,IAAI,CAACkZ,eAAe,CAAC0H,UAAU,CAAC;CACjC;CACD;OACAzR,MAAM,CAAClI,MAAM,EAAE;CAChB;;CAEA;CACA,IAAA,IAAIsQ,IAAI,EAAE;OACT+I,UAAU,GAAGtgB,IAAI,CAACsP,KAAK,CAACzP,OAAO,CAAC0gB,SAAS,CAAC;CAC1C,MAAA,IAAID,UAAU,KAAK,CAAC,CAAC,EAAE;SACtBtgB,IAAI,CAACsP,KAAK,CAAC1P,MAAM,CAAC0gB,UAAU,EAAE,CAAC,EAAEE,SAAS,CAAC;CAC5C;OAEAH,QAAQ,GAAGrgB,IAAI,CAACyT,OAAO,CAAC,MAAM,EAAEnK,IAAI,CAAC;CAErC,MAAA,IAAIiO,IAAI,CAACzQ,SAAS,CAACW,QAAQ,CAAC,QAAQ,CAAC,EAAGnB,UAAU,CAAC+Z,QAAQ,EAAC,QAAQ,CAAC;CAErEzX,MAAAA,WAAW,CAAE2O,IAAI,EAAE8I,QAAQ,CAAC;CAC7B;;CAEA;KACArgB,IAAI,CAAC8X,SAAS,GAAG,IAAI;CACtB;;CAEA;CACD;CACA;CACA;CACC+I,EAAAA,YAAYA,CAACnf,KAAY,EAAE6Y,MAAe,EAAO;KAChD,MAAMva,IAAI,GAAG,IAAI;CACjB0B,IAAAA,KAAK,GAAGC,QAAQ,CAACD,KAAK,CAAC;CAEvB1B,IAAAA,IAAI,CAAC2gB,YAAY,CAACjf,KAAK,CAAC;CAExB,IAAA,OAAO1B,IAAI,CAACsS,WAAW,CAAC5Q,KAAK,CAAC;CAC9B,IAAA,OAAO1B,IAAI,CAACkB,OAAO,CAACQ,KAAK,CAAC;KAC1B1B,IAAI,CAAC8X,SAAS,GAAG,IAAI;CACrB9X,IAAAA,IAAI,CAACF,OAAO,CAAC,eAAe,EAAE4B,KAAK,CAAC;CACpC1B,IAAAA,IAAI,CAAC8gB,UAAU,CAACpf,KAAK,EAAE6Y,MAAM,CAAC;CAC/B;;CAEA;CACD;CACA;GACCwG,YAAYA,CAAC3Z,MAAsB,EAAG;CAErC,IAAA,MAAM4Z,WAAW,GAAG,CAAC5Z,MAAM,IAAI,IAAI,CAAC6Z,WAAW,EAAE1M,IAAI,CAAC,IAAI,CAAC;CAE3D,IAAA,IAAI,CAAC9R,cAAc,GAAI,EAAE;CACzB,IAAA,IAAI,CAAC6P,WAAW,GAAI,EAAE;KACtB,IAAI,CAAC4N,UAAU,EAAE;KAEjB,MAAM5P,QAAmB,GAAG,EAAE;KAC9B9O,OAAO,CAAC,IAAI,CAACN,OAAO,EAAC,CAACiO,MAAgB,EAACtO,GAAU,KAAG;CACnD,MAAA,IAAImgB,WAAW,CAAC7R,MAAM,EAACtO,GAAa,CAAC,EAAE;CACtCyP,QAAAA,QAAQ,CAACzP,GAAG,CAAC,GAAGsO,MAAM;CACvB;CACD,KAAC,CAAC;KAEF,IAAI,CAACjO,OAAO,GAAG,IAAI,CAACiS,MAAM,CAAC7D,KAAK,GAAGgB,QAAQ;KAC3C,IAAI,CAACwH,SAAS,GAAG,IAAI;CACrB,IAAA,IAAI,CAAChY,OAAO,CAAC,cAAc,CAAC;CAC7B;;CAEA;CACD;CACA;CACA;CACA;CACCmhB,EAAAA,WAAWA,CAAC9R,MAAgB,EAACzN,KAAY,EAAC;KACzC,IAAI,IAAI,CAAC4N,KAAK,CAACzP,OAAO,CAAC6B,KAAK,CAAC,IAAI,CAAC,EAAE;CACnC,MAAA,OAAO,IAAI;CACZ;CACA,IAAA,OAAO,KAAK;CACb;;CAEA;CACD;CACA;CACA;CACA;CACC8c,EAAAA,SAASA,CAAC9c,KAA0C,EAAEoK,MAAc,GAAC,KAAK,EAAmB;CAE5F,IAAA,MAAMkR,MAAM,GAAGvb,QAAQ,CAACC,KAAK,CAAC;CAC9B,IAAA,IAAIsb,MAAM,KAAK,IAAI,EAAG,OAAO,IAAI;CAEjC,IAAA,MAAM7N,MAAM,GAAG,IAAI,CAACjO,OAAO,CAAC8b,MAAM,CAAC;KACnC,IAAI7N,MAAM,IAAIxP,SAAS,EAAE;OAExB,IAAIwP,MAAM,CAAC+R,IAAI,EAAE;SAChB,OAAO/R,MAAM,CAAC+R,IAAI;CACnB;CAEA,MAAA,IAAIpV,MAAM,EAAE;CACX,QAAA,OAAO,IAAI,CAAC2H,OAAO,CAAC,QAAQ,EAAEtE,MAAM,CAAC;CACtC;CACD;CAEA,IAAA,OAAO,IAAI;CACZ;;CAEA;CACD;CACA;CACA;CACA;GACC8J,WAAWA,CAAE9J,MAAuB,EAAErH,SAAgB,EAAElF,IAAW,GAAG,QAAQ,EAAqB;KAClG,IAAI5C,IAAI,GAAG,IAAI;OAAEmhB,GAAG;KAEpB,IAAI,CAAChS,MAAM,EAAE;CACZ,MAAA,OAAO,IAAI;CACZ;KAEA,IAAIvM,IAAI,IAAI,MAAM,EAAE;CACnBue,MAAAA,GAAG,GAAKnhB,IAAI,CAACic,eAAe,EAAE;CAC/B,KAAC,MAAI;OACJkF,GAAG,GAAKnhB,IAAI,CAAC0T,gBAAgB,CAAClJ,gBAAgB,CAAC,mBAAmB,CAAC;CACpE;CAEA,IAAA,KAAK,IAAInC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8Y,GAAG,CAACzhB,MAAM,EAAE2I,CAAC,EAAE,EAAE;CACpC,MAAA,IAAI8Y,GAAG,CAAC9Y,CAAC,CAAC,IAAI8G,MAAM,EAAE;CACrB,QAAA;CACD;OAEA,IAAIrH,SAAS,GAAG,CAAC,EAAE;CAClB,QAAA,OAAOqZ,GAAG,CAAC9Y,CAAC,GAAC,CAAC,CAAC;CAChB;CAEA,MAAA,OAAO8Y,GAAG,CAAC9Y,CAAC,GAAC,CAAC,CAAC;CAChB;CACA,IAAA,OAAO,IAAI;CACZ;;CAGA;CACD;CACA;CACA;CACA;GACCqY,OAAOA,CAACnJ,IAAwB,EAAe;CAE9C,IAAA,IAAI,OAAOA,IAAI,IAAI,QAAQ,EAAE;CAC5B,MAAA,OAAOA,IAAI;CACZ;CAEA,IAAA,IAAI7V,KAAK,GAAGD,QAAQ,CAAC8V,IAAI,CAAC;CAC1B,IAAA,OAAO7V,KAAK,KAAK,IAAI,GAClB,IAAI,CAAC6R,OAAO,CAAC/N,aAAa,CAAC,CAAA,aAAA,EAAgBjB,UAAU,CAAC7C,KAAK,CAAC,CAAI,EAAA,CAAA,CAAC,GACjE,IAAI;CACR;;CAEA;CACD;CACA;CACA;CACA;CACC+Y,EAAAA,QAAQA,CAAE1J,MAAsB,EAAEwJ,MAAe,EAAO;KACvD,IAAIva,IAAI,GAAG,IAAI;CAEf,IAAA,IAAIsP,KAAK,GAAGvO,KAAK,CAACC,OAAO,CAAC+P,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;CACrDzB,IAAAA,KAAK,GAAGA,KAAK,CAAClI,MAAM,CAACga,CAAC,IAAIphB,IAAI,CAACsP,KAAK,CAACzP,OAAO,CAACuhB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC;KACvD,MAAMC,SAAS,GAAG/R,KAAK,CAACA,KAAK,CAAC5P,MAAM,GAAG,CAAC,CAAC;CACzC4P,IAAAA,KAAK,CAACxQ,OAAO,CAACyY,IAAI,IAAI;CACrBvX,MAAAA,IAAI,CAACshB,SAAS,GAAI/J,IAAI,KAAK8J,SAAU;CACrCrhB,MAAAA,IAAI,CAACsY,OAAO,CAACf,IAAI,EAAEgD,MAAM,CAAC;CAC3B,KAAC,CAAC;CACH;;CAEA;CACD;CACA;CACA;CACA;CACCjC,EAAAA,OAAOA,CAAE5W,KAAY,EAAE6Y,MAAe,EAAO;KAC5C,IAAI5b,MAAM,GAAG4b,MAAM,GAAG,EAAE,GAAG,CAAC,QAAQ,EAAC,gBAAgB,CAAC;CAEtD7X,IAAAA,eAAe,CAAC,IAAI,EAAE/D,MAAM,EAAE,MAAM;OACnC,IAAI4Y,IAAI,EAAEgK,OAAO;OACjB,MAAMvhB,IAAI,GAAG,IAAI;CAChB,MAAA,MAAM2T,SAAS,GAAG3T,IAAI,CAACM,QAAQ,CAACmN,IAAI;CACrC,MAAA,MAAMuP,MAAM,GAAGvb,QAAQ,CAACC,KAAK,CAAC;CAE9B,MAAA,IAAIsb,MAAM,IAAIhd,IAAI,CAACsP,KAAK,CAACzP,OAAO,CAACmd,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;SAEhD,IAAIrJ,SAAS,KAAK,QAAQ,EAAE;WAC3B3T,IAAI,CAAC2W,KAAK,EAAE;CACb;SAEA,IAAIhD,SAAS,KAAK,QAAQ,IAAI,CAAC3T,IAAI,CAACM,QAAQ,CAACgM,UAAU,EAAE;CACxD,UAAA;CACD;CACD;CAEA,MAAA,IAAI0Q,MAAM,KAAK,IAAI,IAAI,CAAChd,IAAI,CAACkB,OAAO,CAACC,cAAc,CAAC6b,MAAM,CAAC,EAAE;OAC7D,IAAIrJ,SAAS,KAAK,QAAQ,EAAE3T,IAAI,CAACwa,KAAK,CAACD,MAAM,CAAC;OAC9C,IAAI5G,SAAS,KAAK,OAAO,IAAI3T,IAAI,CAACwhB,MAAM,EAAE,EAAE;CAE5CjK,MAAAA,IAAI,GAAGvX,IAAI,CAACyT,OAAO,CAAC,MAAM,EAAEzT,IAAI,CAACkB,OAAO,CAAC8b,MAAM,CAAC,CAAC;OAEjD,IAAIhd,IAAI,CAACuT,OAAO,CAAC9L,QAAQ,CAAC8P,IAAI,CAAC,EAAE;CAAE;CAClCA,QAAAA,IAAI,GAAGA,IAAI,CAAC1N,SAAS,CAAC,IAAI,CAAgB;CAC3C;CAEA0X,MAAAA,OAAO,GAAGvhB,IAAI,CAACwhB,MAAM,EAAE;CACvBxhB,MAAAA,IAAI,CAACsP,KAAK,CAAC1P,MAAM,CAACI,IAAI,CAACmS,QAAQ,EAAE,CAAC,EAAE6K,MAAM,CAAC;CAC3Chd,MAAAA,IAAI,CAACyhB,aAAa,CAAClK,IAAI,CAAC;OAExB,IAAIvX,IAAI,CAAC8R,OAAO,EAAE;CAEjB;SACA,IAAI,CAAC9R,IAAI,CAACshB,SAAS,IAAIthB,IAAI,CAACM,QAAQ,CAAC+L,YAAY,EAAE;CAClD,UAAA,IAAI8C,MAAM,GAAGnP,IAAI,CAACwe,SAAS,CAACxB,MAAM,CAAC;WACnC,IAAIhE,IAAI,GAAGhZ,IAAI,CAACiZ,WAAW,CAAC9J,MAAM,EAAE,CAAC,CAAC;CACtC,UAAA,IAAI6J,IAAI,EAAE;CACThZ,YAAAA,IAAI,CAACkZ,eAAe,CAACF,IAAI,CAAC;CAC3B;CACD;;CAEA;CACA;SACA,IAAI,CAAChZ,IAAI,CAACshB,SAAS,IAAI,CAACthB,IAAI,CAACM,QAAQ,CAAC0Z,gBAAgB,EAAE;WACvDha,IAAI,CAACyZ,cAAc,CAACzZ,IAAI,CAAC4R,SAAS,IAAI+B,SAAS,KAAK,QAAQ,CAAC;CAC9D;;CAEA;CACA,QAAA,IAAI3T,IAAI,CAACM,QAAQ,CAAC0Z,gBAAgB,IAAI,KAAK,IAAIha,IAAI,CAACwhB,MAAM,EAAE,EAAE;WAC7DxhB,IAAI,CAAC2W,KAAK,EAAE;CACb,SAAC,MAAM,IAAI,CAAC3W,IAAI,CAACshB,SAAS,EAAE;WAC3BthB,IAAI,CAACkW,gBAAgB,EAAE;CACxB;SAEAlW,IAAI,CAACF,OAAO,CAAC,UAAU,EAAEkd,MAAM,EAAEzF,IAAI,CAAC;CAEtC,QAAA,IAAI,CAACvX,IAAI,CAACshB,SAAS,EAAE;WACpBthB,IAAI,CAACyW,mBAAmB,CAAC;CAAC8D,YAAAA,MAAM,EAAEA;CAAM,WAAC,CAAC;CAC3C;CACD;CAEA,MAAA,IAAI,CAACva,IAAI,CAACshB,SAAS,IAAK,CAACC,OAAO,IAAIvhB,IAAI,CAACwhB,MAAM,EAAG,EAAE;SACnDxhB,IAAI,CAACgW,UAAU,EAAE;SACjBhW,IAAI,CAACwW,YAAY,EAAE;CACpB;CAED,KAAC,CAAC;CACH;;CAEA;CACD;CACA;CACA;CACA;CACCsK,EAAAA,UAAUA,CAAEvJ,IAAwB,GAAC,IAAI,EAAEgD,MAAe,EAAE;KAC3D,MAAMva,IAAI,GAAI,IAAI;CAClBuX,IAAAA,IAAI,GAAKvX,IAAI,CAAC0gB,OAAO,CAACnJ,IAAI,CAAC;KAE3B,IAAI,CAACA,IAAI,EAAG;KAEZ,IAAIlP,CAAC,EAAC+S,GAAG;CACT,IAAA,MAAM1Z,KAAK,GAAG6V,IAAI,CAAC3H,OAAO,CAAClO,KAAK;CAChC2G,IAAAA,CAAC,GAAGH,SAAS,CAACqP,IAAI,CAAC;KAEnBA,IAAI,CAACtQ,MAAM,EAAE;KACb,IAAIsQ,IAAI,CAACzQ,SAAS,CAACW,QAAQ,CAAC,QAAQ,CAAC,EAAE;OACtC2T,GAAG,GAAGpb,IAAI,CAACqS,WAAW,CAACxS,OAAO,CAAC0X,IAAI,CAAC;OACpCvX,IAAI,CAACqS,WAAW,CAACzS,MAAM,CAACwb,GAAG,EAAE,CAAC,CAAC;CAC/BpU,MAAAA,aAAa,CAACuQ,IAAI,EAAC,QAAQ,CAAC;CAC7B;KAEAvX,IAAI,CAACsP,KAAK,CAAC1P,MAAM,CAACyI,CAAC,EAAE,CAAC,CAAC;KACvBrI,IAAI,CAAC8X,SAAS,GAAG,IAAI;CACrB,IAAA,IAAI,CAAC9X,IAAI,CAACM,QAAQ,CAACsL,OAAO,IAAI5L,IAAI,CAACsS,WAAW,CAACnR,cAAc,CAACO,KAAK,CAAC,EAAE;CACrE1B,MAAAA,IAAI,CAAC6gB,YAAY,CAACnf,KAAK,EAAE6Y,MAAM,CAAC;CACjC;CAEA,IAAA,IAAIlS,CAAC,GAAGrI,IAAI,CAACmS,QAAQ,EAAE;OACtBnS,IAAI,CAAC8Z,QAAQ,CAAC9Z,IAAI,CAACmS,QAAQ,GAAG,CAAC,CAAC;CACjC;KAEAnS,IAAI,CAACyW,mBAAmB,CAAC;CAAC8D,MAAAA,MAAM,EAAEA;CAAM,KAAC,CAAC;KAC1Cva,IAAI,CAACwW,YAAY,EAAE;KACnBxW,IAAI,CAACkW,gBAAgB,EAAE;KACvBlW,IAAI,CAACF,OAAO,CAAC,aAAa,EAAE4B,KAAK,EAAE6V,IAAI,CAAC;CAEzC;;CAEA;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;GACCgB,UAAUA,CAAExV,KAAiB,GAAC,IAAI,EAAEnE,QAA0B,GAAGA,MAAI,EAAE,EAAU;CAEhF;CACA,IAAA,IAAIa,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;CAC3Bd,MAAAA,QAAQ,GAAGa,SAAS,CAAC,CAAC,CAAC;CACxB;CACA,IAAA,IAAI,OAAOb,QAAQ,IAAI,UAAU,EAAE;CAClCA,MAAAA,QAAQ,GAAGA,MAAM,EAAE;CACpB;KAEA,IAAIoB,IAAI,GAAI,IAAI;CAChB,IAAA,IAAI0hB,KAAK,GAAG1hB,IAAI,CAACmS,QAAQ;CACzB,IAAA,IAAIwP,MAAM;CACV5e,IAAAA,KAAK,GAAGA,KAAK,IAAI/C,IAAI,CAACkY,UAAU,EAAE;CAElC,IAAA,IAAI,CAAClY,IAAI,CAACyf,SAAS,CAAC1c,KAAK,CAAC,EAAE;CAC3BnE,MAAAA,QAAQ,EAAE;CACV,MAAA,OAAO,KAAK;CACb;KAEAoB,IAAI,CAAC4hB,IAAI,EAAE;KAEX,IAAIC,OAAO,GAAG,KAAK;KACnB,IAAI/V,MAAM,GAAIxC,IAAuB,IAAK;OACzCtJ,IAAI,CAAC8hB,MAAM,EAAE;OAEb,IAAI,CAACxY,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE,OAAO1K,QAAQ,EAAE;CACxD,MAAA,IAAI8C,KAAK,GAAGD,QAAQ,CAAC6H,IAAI,CAACtJ,IAAI,CAACM,QAAQ,CAAC0M,UAAU,CAAC,CAAC;CACpD,MAAA,IAAI,OAAOtL,KAAK,KAAK,QAAQ,EAAE;SAC9B,OAAO9C,QAAQ,EAAE;CAClB;OAEAoB,IAAI,CAACoa,eAAe,EAAE;CACtBpa,MAAAA,IAAI,CAACgQ,SAAS,CAAC1G,IAAI,EAAC,IAAI,CAAC;CACzBtJ,MAAAA,IAAI,CAAC8Z,QAAQ,CAAC4H,KAAK,CAAC;CACpB1hB,MAAAA,IAAI,CAACsY,OAAO,CAAC5W,KAAK,CAAC;OACnB9C,QAAQ,CAAC0K,IAAI,CAAC;CACduY,MAAAA,OAAO,GAAG,IAAI;MACd;KAED,IAAI,OAAO7hB,IAAI,CAACM,QAAQ,CAACwL,MAAM,KAAK,UAAU,EAAE;CAC/C6V,MAAAA,MAAM,GAAG3hB,IAAI,CAACM,QAAQ,CAACwL,MAAM,CAAC5J,IAAI,CAAC,IAAI,EAAEa,KAAK,EAAE+I,MAAM,CAAC;CACxD,KAAC,MAAI;CACJ6V,MAAAA,MAAM,GAAG;CACR,QAAA,CAAC3hB,IAAI,CAACM,QAAQ,CAAC2M,UAAU,GAAGlK,KAAK;CACjC,QAAA,CAAC/C,IAAI,CAACM,QAAQ,CAAC0M,UAAU,GAAGjK;QAC5B;CACF;KAEA,IAAI,CAAC8e,OAAO,EAAE;OACb/V,MAAM,CAAC6V,MAAM,CAAC;CACf;CAEA,IAAA,OAAO,IAAI;CACZ;;CAEA;CACD;CACA;CACCjL,EAAAA,YAAYA,GAAG;KACd,IAAI1W,IAAI,GAAG,IAAI;KACfA,IAAI,CAAC8X,SAAS,GAAG,IAAI;KAErB,IAAI9X,IAAI,CAAC8R,OAAO,EAAE;CACjB9R,MAAAA,IAAI,CAACya,QAAQ,CAACza,IAAI,CAACsP,KAAK,CAAC;CAC1B;KAEAtP,IAAI,CAACyW,mBAAmB,EAAE;KAC1BzW,IAAI,CAACwW,YAAY,EAAE;CACpB;;CAEA;CACD;CACA;CACA;CACCA,EAAAA,YAAYA,GAAG;KACd,MAAMxW,IAAI,GAAO,IAAI;KAErBA,IAAI,CAAC+hB,oBAAoB,EAAE;CAE3B,IAAA,MAAMP,MAAM,GAAGxhB,IAAI,CAACwhB,MAAM,EAAE;CAC5B,IAAA,MAAM7P,QAAQ,GAAG3R,IAAI,CAAC2R,QAAQ;CAE9B3R,IAAAA,IAAI,CAACwH,OAAO,CAACV,SAAS,CAACoV,MAAM,CAAC,KAAK,EAAClc,IAAI,CAAC+S,GAAG,CAAC;CAG7C,IAAA,MAAMiP,cAAc,GAAGhiB,IAAI,CAACwH,OAAO,CAACV,SAAS;KAE7Ckb,cAAc,CAAC9F,MAAM,CAAC,OAAO,EAAElc,IAAI,CAAC4R,SAAS,CAAC;KAC9CoQ,cAAc,CAAC9F,MAAM,CAAC,UAAU,EAAElc,IAAI,CAACuR,UAAU,CAAC;KAClDyQ,cAAc,CAAC9F,MAAM,CAAC,UAAU,EAAElc,IAAI,CAACwR,UAAU,CAAC;KAClDwQ,cAAc,CAAC9F,MAAM,CAAC,UAAU,EAAElc,IAAI,CAACiT,UAAU,CAAC;KAClD+O,cAAc,CAAC9F,MAAM,CAAC,SAAS,EAAE,CAAClc,IAAI,CAAC0R,OAAO,CAAC;CAC/CsQ,IAAAA,cAAc,CAAC9F,MAAM,CAAC,QAAQ,EAAEvK,QAAQ,CAAC;CACzCqQ,IAAAA,cAAc,CAAC9F,MAAM,CAAC,MAAM,EAAEsF,MAAM,CAAC;CACrCQ,IAAAA,cAAc,CAAC9F,MAAM,CAAC,cAAc,EAAElc,IAAI,CAAC4R,SAAS,IAAI,CAAC5R,IAAI,CAAC6R,aAAa,CAAC;KAC5EmQ,cAAc,CAAC9F,MAAM,CAAC,iBAAiB,EAAElc,IAAI,CAACsR,MAAM,CAAC;KACrD0Q,cAAc,CAAC9F,MAAM,CAAC,aAAa,EAAEnU,aAAa,CAAC/H,IAAI,CAACkB,OAAO,CAAE,CAAC;CAClE8gB,IAAAA,cAAc,CAAC9F,MAAM,CAAC,WAAW,EAAElc,IAAI,CAACsP,KAAK,CAAC5P,MAAM,GAAG,CAAC,CAAC;CAE1D;;CAGA;CACD;CACA;CACA;CACA;CACA;CACA;CACA;CACCqiB,EAAAA,oBAAoBA,GAAG;KACtB,IAAI/hB,IAAI,GAAG,IAAI;CAEf,IAAA,IAAI,CAACA,IAAI,CAAC+C,KAAK,CAACkf,QAAQ,EAAE;CACzB,MAAA;CACD;KAEAjiB,IAAI,CAAC0R,OAAO,GAAG1R,IAAI,CAAC+C,KAAK,CAACkf,QAAQ,CAACC,KAAK;CACxCliB,IAAAA,IAAI,CAACyR,SAAS,GAAG,CAACzR,IAAI,CAAC0R,OAAO;CAC/B;;CAEA;CACD;CACA;CACA;CACA;CACA;CACC8P,EAAAA,MAAMA,GAAG;CACR,IAAA,OAAO,IAAI,CAAClhB,QAAQ,CAAC8L,QAAQ,KAAK,IAAI,IAAI,IAAI,CAACkD,KAAK,CAAC5P,MAAM,IAAI,IAAI,CAACY,QAAQ,CAAC8L,QAAQ;CACtF;;CAEA;CACD;CACA;CACA;CACA;CACCqK,EAAAA,mBAAmBA,CAAE0L,IAAiB,GAAG,EAAE,EAAE;KAC5C,MAAMniB,IAAI,GAAG,IAAI;KACjB,IAAImP,MAAM,EAAEiF,KAAK;KAEjB,MAAMgO,YAAY,GAAGpiB,IAAI,CAAC+C,KAAK,CAACyC,aAAa,CAAC,kBAAkB,CAAsB;KAEtF,IAAIxF,IAAI,CAAC8S,aAAa,EAAE;OAEvB,MAAMxC,QAA4B,GAAI,EAAE;OACxC,MAAM+R,YAAmB,GAAMriB,IAAI,CAAC+C,KAAK,CAACyH,gBAAgB,CAAC,gBAAgB,CAAC,CAAC9K,MAAM;CAEnF,MAAA,SAAS4iB,WAAWA,CAAC/D,SAAgC,EAAE7c,KAAY,EAAE0S,KAAY,EAAmB;SAEnG,IAAI,CAACmK,SAAS,EAAE;CACfA,UAAAA,SAAS,GAAG3Z,MAAM,CAAC,iBAAiB,GAAGhD,WAAW,CAACF,KAAK,CAAC,GAAG,IAAI,GAAGE,WAAW,CAACwS,KAAK,CAAC,GAAG,WAAW,CAAsB;CAC1H;;CAEA;CACA;SACA,IAAImK,SAAS,IAAI6D,YAAY,EAAE;CAC9BpiB,UAAAA,IAAI,CAAC+C,KAAK,CAACyB,MAAM,CAAC+Z,SAAS,CAAC;CAC7B;CAEAjO,QAAAA,QAAQ,CAAChR,IAAI,CAACif,SAAS,CAAC;;CAExB;CACA;CACA,QAAA,IAAIA,SAAS,IAAI6D,YAAY,IAAIC,YAAY,GAAG,CAAC,EAAE;WAClD9D,SAAS,CAACjO,QAAQ,GAAG,IAAI;CAC1B;CAEA,QAAA,OAAOiO,SAAS;CACjB;;CAEA;OACAve,IAAI,CAAC+C,KAAK,CAACyH,gBAAgB,CAAC,gBAAgB,CAAC,CAAC1L,OAAO,CAAEyf,SAAiB,IAAK;SACxDA,SAAS,CAAEjO,QAAQ,GAAG,KAAK;CAChD,OAAC,CAAC;;CAGF;CACA,MAAA,IAAItQ,IAAI,CAACsP,KAAK,CAAC5P,MAAM,IAAI,CAAC,IAAIM,IAAI,CAACM,QAAQ,CAACmN,IAAI,IAAI,QAAQ,EAAE;CAE7D6U,QAAAA,WAAW,CAACF,YAAY,EAAE,EAAE,EAAE,EAAE,CAAC;;CAElC;CACA,OAAC,MAAI;CAEJpiB,QAAAA,IAAI,CAACsP,KAAK,CAACxQ,OAAO,CAAE4C,KAAK,IAAG;CAC3ByN,UAAAA,MAAM,GAAKnP,IAAI,CAACkB,OAAO,CAACQ,KAAK,CAAE;WAC/B0S,KAAK,GAAKjF,MAAM,CAACnP,IAAI,CAACM,QAAQ,CAAC2M,UAAU,CAAC,IAAI,EAAE;WAEhD,IAAIqD,QAAQ,CAACmO,QAAQ,CAACtP,MAAM,CAACkB,OAAO,CAAC,EAAE;CACtC,YAAA,MAAMkS,SAAS,GAAGviB,IAAI,CAAC+C,KAAK,CAACyC,aAAa,CAAC,CAAA,cAAA,EAAiBjB,UAAU,CAAC7C,KAAK,CAAC,kBAAkB,CAAsB;CACrH4gB,YAAAA,WAAW,CAACC,SAAS,EAAE7gB,KAAK,EAAE0S,KAAK,CAAC;CACrC,WAAC,MAAI;CACJjF,YAAAA,MAAM,CAACkB,OAAO,GAAGiS,WAAW,CAACnT,MAAM,CAACkB,OAAO,EAAE3O,KAAK,EAAE0S,KAAK,CAAC;CAC3D;CACD,SAAC,CAAC;CAEH;CAED,KAAC,MAAM;OACNpU,IAAI,CAAC+C,KAAK,CAACrB,KAAK,GAAG1B,IAAI,CAACsa,QAAQ,EAAY;CAC7C;KAEA,IAAIta,IAAI,CAAC8R,OAAO,EAAE;CACjB,MAAA,IAAI,CAACqQ,IAAI,CAAC5H,MAAM,EAAE;SACjBva,IAAI,CAACF,OAAO,CAAC,QAAQ,EAAEE,IAAI,CAACsa,QAAQ,EAAG,CAAC;CACzC;CACD;CACD;;CAEA;CACD;CACA;CACA;CACCvB,EAAAA,IAAIA,GAAG;KACN,IAAI/Y,IAAI,GAAG,IAAI;KAEf,IAAIA,IAAI,CAAC2R,QAAQ,IAAI3R,IAAI,CAACsR,MAAM,IAAKtR,IAAI,CAACM,QAAQ,CAACmN,IAAI,KAAK,OAAO,IAAIzN,IAAI,CAACwhB,MAAM,EAAG,EAAE;KACvFxhB,IAAI,CAACsR,MAAM,GAAG,IAAI;CAClB/I,IAAAA,OAAO,CAACvI,IAAI,CAAC6T,UAAU,EAAC;CAAC,MAAA,eAAe,EAAE;CAAM,KAAC,CAAC;KAClD7T,IAAI,CAACwW,YAAY,EAAE;CACnBvQ,IAAAA,QAAQ,CAACjG,IAAI,CAACwT,QAAQ,EAAC;CAACgP,MAAAA,UAAU,EAAE,QAAQ;CAAEC,MAAAA,OAAO,EAAE;CAAO,KAAC,CAAC;KAChEziB,IAAI,CAACkW,gBAAgB,EAAE;CACvBjQ,IAAAA,QAAQ,CAACjG,IAAI,CAACwT,QAAQ,EAAC;CAACgP,MAAAA,UAAU,EAAE,SAAS;CAAEC,MAAAA,OAAO,EAAE;CAAO,KAAC,CAAC;KACjEziB,IAAI,CAACsU,KAAK,EAAE;KACZtU,IAAI,CAACF,OAAO,CAAC,eAAe,EAAEE,IAAI,CAACwT,QAAQ,CAAC;CAC7C;;CAEA;CACD;CACA;CACCmD,EAAAA,KAAKA,CAACyD,eAAe,GAAC,IAAI,EAAE;KAC3B,IAAIpa,IAAI,GAAG,IAAI;CACf,IAAA,IAAIF,OAAO,GAAGE,IAAI,CAACsR,MAAM;CAEzB,IAAA,IAAI8I,eAAe,EAAE;CAEpB;OACApa,IAAI,CAACoa,eAAe,EAAE;CAEtB,MAAA,IAAIpa,IAAI,CAACM,QAAQ,CAACmN,IAAI,KAAK,QAAQ,IAAIzN,IAAI,CAACsP,KAAK,CAAC5P,MAAM,EAAE;SACzDM,IAAI,CAACgW,UAAU,EAAE;CAClB;CACD;KAEAhW,IAAI,CAACsR,MAAM,GAAG,KAAK;CACnB/I,IAAAA,OAAO,CAACvI,IAAI,CAAC6T,UAAU,EAAC;CAAC,MAAA,eAAe,EAAE;CAAO,KAAC,CAAC;CACnD5N,IAAAA,QAAQ,CAACjG,IAAI,CAACwT,QAAQ,EAAC;CAACiP,MAAAA,OAAO,EAAE;CAAM,KAAC,CAAC;CACzC,IAAA,IAAIziB,IAAI,CAACM,QAAQ,CAAC+L,YAAY,EAAE;OAC/BrM,IAAI,CAACma,iBAAiB,EAAE;CACzB;KACAna,IAAI,CAACwW,YAAY,EAAE;KAEnB,IAAI1W,OAAO,EAAEE,IAAI,CAACF,OAAO,CAAC,gBAAgB,EAAEE,IAAI,CAACwT,QAAQ,CAAC;CAC3D;;CAEA;CACD;CACA;CACA;CACA;CACC0C,EAAAA,gBAAgBA,GAAE;CAEjB,IAAA,IAAI,IAAI,CAAC5V,QAAQ,CAAC0N,cAAc,KAAK,MAAM,EAAE;CAC5C,MAAA;CACD;CAEA,IAAA,IAAI0U,OAAO,GAAK,IAAI,CAACnP,OAAO;CAC5B,IAAA,IAAIoP,IAAI,GAAKD,OAAO,CAAC5G,qBAAqB,EAAE;CAC5C,IAAA,IAAIC,GAAG,GAAM2G,OAAO,CAAC9G,YAAY,GAAG+G,IAAI,CAAC5G,GAAG,GAAI/Z,MAAM,CAAC4gB,OAAO;KAC9D,IAAIC,IAAI,GAAKF,IAAI,CAACE,IAAI,GAAG7gB,MAAM,CAAC8gB,OAAO;CAGvC7c,IAAAA,QAAQ,CAAC,IAAI,CAACuN,QAAQ,EAAC;CACtBkB,MAAAA,KAAK,EAAGiO,IAAI,CAACjO,KAAK,GAAG,IAAI;OACzBqH,GAAG,EAAKA,GAAG,GAAG,IAAI;OAClB8G,IAAI,EAAIA,IAAI,GAAG;CAChB,KAAC,CAAC;CAEH;;CAEA;CACD;CACA;CACA;CACA;GACCrI,KAAKA,CAACD,MAAe,EAAE;KACtB,IAAIva,IAAI,GAAG,IAAI;CAEf,IAAA,IAAI,CAACA,IAAI,CAACsP,KAAK,CAAC5P,MAAM,EAAE;CAExB,IAAA,IAAI4P,KAAK,GAAGtP,IAAI,CAACic,eAAe,EAAE;CAClCza,IAAAA,OAAO,CAAC8N,KAAK,EAAEiI,IAAY,IAAG;CAC7BvX,MAAAA,IAAI,CAAC8gB,UAAU,CAACvJ,IAAI,EAAC,IAAI,CAAC;CAC3B,KAAC,CAAC;KAEFvX,IAAI,CAACgW,UAAU,EAAE;CACjB,IAAA,IAAI,CAACuE,MAAM,EAAGva,IAAI,CAACyW,mBAAmB,EAAE;CACxCzW,IAAAA,IAAI,CAACF,OAAO,CAAC,OAAO,CAAC;CACtB;;CAEA;CACD;CACA;CACA;CACA;GACC2hB,aAAaA,CAACvd,EAAc,EAAE;KAC7B,MAAMlE,IAAI,GAAI,IAAI;CAClB,IAAA,MAAM0hB,KAAK,GAAI1hB,IAAI,CAACmS,QAAQ;CAC5B,IAAA,MAAM3O,MAAM,GAAGxD,IAAI,CAACuT,OAAO;CAE3B/P,IAAAA,MAAM,CAACgc,YAAY,CAACtb,EAAE,EAAEV,MAAM,CAACkN,QAAQ,CAACgR,KAAK,CAAC,IAAI,IAAI,CAAC;CACvD1hB,IAAAA,IAAI,CAAC8Z,QAAQ,CAAC4H,KAAK,GAAG,CAAC,CAAC;CACzB;;CAEA;CACD;CACA;CACA;GACCnI,eAAeA,CAACvE,CAAe,EAAU;CACxC,IAAA,IAAIlN,SAAS,EAAEib,SAAS,EAAErB,KAAK,EAAEsB,IAAI;KACrC,IAAIhjB,IAAI,GAAG,IAAI;CAEf8H,IAAAA,SAAS,GAAIkN,CAAC,IAAIA,CAAC,CAAC2D,OAAO,KAAKE,aAAuB,GAAI,CAAC,CAAC,GAAG,CAAC;CACjEkK,IAAAA,SAAS,GAAGjgB,YAAY,CAAC9C,IAAI,CAAC4T,aAAa,CAAC;;CAG5C;KACA,MAAMqP,QAAkB,GAAG,EAAE;CAE7B,IAAA,IAAIjjB,IAAI,CAACqS,WAAW,CAAC3S,MAAM,EAAE;OAE5BsjB,IAAI,GAAGpb,OAAO,CAAC5H,IAAI,CAACqS,WAAW,EAAEvK,SAAS,CAAC;CAC3C4Z,MAAAA,KAAK,GAAGxZ,SAAS,CAAC8a,IAAI,CAAC;OAEvB,IAAIlb,SAAS,GAAG,CAAC,EAAE;CAAE4Z,QAAAA,KAAK,EAAE;CAAE;CAE9BlgB,MAAAA,OAAO,CAACxB,IAAI,CAACqS,WAAW,EAAGkF,IAAY,IAAK0L,QAAQ,CAAC3jB,IAAI,CAACiY,IAAI,CAAE,CAAC;MAEjE,MAAM,IAAI,CAACvX,IAAI,CAAC4R,SAAS,IAAI5R,IAAI,CAACM,QAAQ,CAACmN,IAAI,KAAK,QAAQ,KAAKzN,IAAI,CAACsP,KAAK,CAAC5P,MAAM,EAAE;CACpF,MAAA,MAAM4P,KAAK,GAAGtP,IAAI,CAACic,eAAe,EAAE;CACpC,MAAA,IAAIiH,OAAO;CACX,MAAA,IAAIpb,SAAS,GAAG,CAAC,IAAIib,SAAS,CAAC/f,KAAK,KAAK,CAAC,IAAI+f,SAAS,CAACrjB,MAAM,KAAK,CAAC,EAAE;SACrEwjB,OAAO,GAAG5T,KAAK,CAACtP,IAAI,CAACmS,QAAQ,GAAG,CAAC,CAAC;CAEnC,OAAC,MAAK,IAAIrK,SAAS,GAAG,CAAC,IAAIib,SAAS,CAAC/f,KAAK,KAAKhD,IAAI,CAACkY,UAAU,EAAE,CAACxY,MAAM,EAAE;CACxEwjB,QAAAA,OAAO,GAAG5T,KAAK,CAACtP,IAAI,CAACmS,QAAQ,CAAC;CAC/B;OAEA,IAAI+Q,OAAO,KAAKvjB,SAAS,EAAE;CAC1BsjB,QAAAA,QAAQ,CAAC3jB,IAAI,CAAE4jB,OAAQ,CAAC;CACzB;CACD;KAEA,IAAI,CAACljB,IAAI,CAACmjB,YAAY,CAACF,QAAQ,EAACjO,CAAC,CAAC,EAAE;CACnC,MAAA,OAAO,KAAK;CACb;CAEA7R,IAAAA,cAAc,CAAC6R,CAAC,EAAC,IAAI,CAAC;;CAEtB;CACA,IAAA,IAAI,OAAO0M,KAAK,KAAK,WAAW,EAAE;CACjC1hB,MAAAA,IAAI,CAAC8Z,QAAQ,CAAC4H,KAAK,CAAC;CACrB;KAEA,OAAOuB,QAAQ,CAACvjB,MAAM,EAAE;OACvBM,IAAI,CAAC8gB,UAAU,CAACmC,QAAQ,CAACG,GAAG,EAAE,CAAC;CAChC;KAEApjB,IAAI,CAACgW,UAAU,EAAE;KACjBhW,IAAI,CAACkW,gBAAgB,EAAE;CACvBlW,IAAAA,IAAI,CAACyZ,cAAc,CAAC,KAAK,CAAC;CAE1B,IAAA,OAAO,IAAI;CACZ;;CAEA;CACD;CACA;CACC0J,EAAAA,YAAYA,CAAC7T,KAAe,EAAClM,GAA4B,EAAC;CAEzD,IAAA,MAAM2N,MAAM,GAAGzB,KAAK,CAAC1I,GAAG,CAAC2Q,IAAI,IAAIA,IAAI,CAAC3H,OAAO,CAAClO,KAAK,CAAC;;CAEpD;KACA,IAAI,CAACqP,MAAM,CAACrR,MAAM,IAAK,OAAO,IAAI,CAACY,QAAQ,CAAC+iB,QAAQ,KAAK,UAAU,IAAI,IAAI,CAAC/iB,QAAQ,CAAC+iB,QAAQ,CAACtS,MAAM,EAAC3N,GAAG,CAAC,KAAK,KAAM,EAAE;CACrH,MAAA,OAAO,KAAK;CACb;CAEA,IAAA,OAAO,IAAI;CACZ;;CAEA;CACD;CACA;CACA;CACA;CACA;CACA;CACCkW,EAAAA,gBAAgBA,CAACxR,SAAgB,EAAEkN,CAA2B,EAAE;CAC/D,IAAA,IAAImG,WAAW;OAAEmI,QAAQ;CAAEtjB,MAAAA,IAAI,GAAG,IAAI;CAEtC,IAAA,IAAIA,IAAI,CAAC+S,GAAG,EAAEjL,SAAS,IAAI,CAAC,CAAC;CAC7B,IAAA,IAAI9H,IAAI,CAACkY,UAAU,EAAE,CAACxY,MAAM,EAAG;;CAG/B;CACA,IAAA,IAAIgE,SAAS,CAACmV,YAAsB,EAAC7D,CAAC,CAAC,IAAItR,SAAS,CAAC,UAAU,EAACsR,CAAC,CAAC,EAAE;CAEnEmG,MAAAA,WAAW,GAAKnb,IAAI,CAACgb,aAAa,CAAClT,SAAS,CAAC;CAC7C,MAAA,IAAIqT,WAAW,EAAE;SAEhB,IAAI,CAACA,WAAW,CAACrU,SAAS,CAACW,QAAQ,CAAC,QAAQ,CAAC,EAAE;CAC9C6b,UAAAA,QAAQ,GAAKnI,WAAW;CACzB,SAAC,MAAI;WACJmI,QAAQ,GAAKtjB,IAAI,CAACiZ,WAAW,CAACkC,WAAW,EAACrT,SAAS,EAAC,MAAM,CAAC;CAC5D;;CAED;CACA,OAAC,MAAK,IAAIA,SAAS,GAAG,CAAC,EAAE;CACxBwb,QAAAA,QAAQ,GAAKtjB,IAAI,CAAC4T,aAAa,CAAC2P,kBAAkB;CACnD,OAAC,MAAI;CACJD,QAAAA,QAAQ,GAAKtjB,IAAI,CAAC4T,aAAa,CAACtL,sBAAsB;CACvD;CAGA,MAAA,IAAIgb,QAAQ,EAAE;SACb,IAAIA,QAAQ,CAACxc,SAAS,CAACW,QAAQ,CAAC,QAAQ,CAAC,EAAE;CAC1CzH,UAAAA,IAAI,CAACkb,gBAAgB,CAACC,WAAW,CAAC;CACnC;CACAnb,QAAAA,IAAI,CAACib,kBAAkB,CAACqI,QAAQ,CAAC,CAAC;CACnC;;CAED;CACA,KAAC,MAAI;CACJtjB,MAAAA,IAAI,CAACwjB,SAAS,CAAC1b,SAAS,CAAC;CAC1B;CACD;GAEA0b,SAASA,CAAC1b,SAAgB,EAAC;;CAE3B;CACD;CACA;CACA;GACCkT,aAAaA,CAAClT,SAAiB,EAAC;KAE/B,IAAIqT,WAAW,GAAG,IAAI,CAAC5H,OAAO,CAAC/N,aAAa,CAAC,cAAc,CAAC;CAC5D,IAAA,IAAI2V,WAAW,EAAE;CAChB,MAAA,OAAOA,WAAW;CACnB;KAGA,IAAIyB,MAAM,GAAG,IAAI,CAACrJ,OAAO,CAAC/I,gBAAgB,CAAC,SAAS,CAAC;CACrD,IAAA,IAAIoS,MAAM,EAAE;CACX,MAAA,OAAOhV,OAAO,CAACgV,MAAM,EAAC9U,SAAS,CAAC;CACjC;CACD;;CAGA;CACD;CACA;CACA;CACA;CACA;CACA;CACA;GACCgS,QAAQA,CAAC2J,OAAc,EAAE;CACxB,IAAA,IAAI,CAACtR,QAAQ,GAAG,IAAI,CAAC7C,KAAK,CAAC5P,MAAM;CAClC;;CAEA;CACD;CACA;CACA;CACCuc,EAAAA,eAAeA,GAAY;CAC1B,IAAA,OAAOlb,KAAK,CAACqJ,IAAI,CAAE,IAAI,CAACmJ,OAAO,CAAC/I,gBAAgB,CAAC,gBAAgB,CAAE,CAAC;CACrE;;CAEA;CACD;CACA;CACA;CACCoX,EAAAA,IAAIA,GAAG;CACN,IAAA,IAAI,CAAC8B,SAAS,CAAC,IAAI,CAAC;CACrB;;CAEA;CACD;CACA;CACC5B,EAAAA,MAAMA,GAAG;CACR,IAAA,IAAI,CAAC4B,SAAS,CAAC,KAAK,CAAC;CACtB;;CAEA;CACD;CACA;GACCA,SAASA,CAAE9B,IAAY,GAAG,IAAI,CAACpQ,UAAU,IAAI,IAAI,CAACD,UAAU,EAAE;KAC7D,IAAI,CAACI,QAAQ,GAAGiQ,IAAI;KACpB,IAAI,CAACpL,YAAY,EAAE;CACpB;;CAEA;CACD;CACA;CACA;CACCI,EAAAA,OAAOA,GAAG;CACT,IAAA,IAAI,CAAC+M,WAAW,CAAC,IAAI,CAAC;KACtB,IAAI,CAAChN,KAAK,EAAE;CACb;;CAEA;CACD;CACA;CACA;CACCI,EAAAA,MAAMA,GAAG;CACR,IAAA,IAAI,CAAC4M,WAAW,CAAC,KAAK,CAAC;CACxB;GAEAA,WAAWA,CAACvT,QAAgB,EAAC;CAC5B,IAAA,IAAI,CAACyD,UAAU,CAAChB,QAAQ,GAAIzC,QAAQ,GAAG,CAAC,CAAC,GAAG,IAAI,CAACyC,QAAQ;KACzD,IAAI,CAACtB,UAAU,GAAOnB,QAAQ;CAC9B,IAAA,IAAI,CAACrN,KAAK,CAACqN,QAAQ,GAAMA,QAAQ;CACjC,IAAA,IAAI,CAACwD,aAAa,CAACxD,QAAQ,GAAIA,QAAQ;KACvC,IAAI,CAACsT,SAAS,EAAE;CACjB;GAEA5M,WAAWA,CAACtF,UAAkB,EAAC;KAC9B,IAAI,CAACA,UAAU,GAAOA,UAAU;CAChC,IAAA,IAAI,CAACzO,KAAK,CAAC8T,QAAQ,GAAMrF,UAAU;CACnC,IAAA,IAAI,CAACoC,aAAa,CAACiD,QAAQ,GAAIrF,UAAU;KACzC,IAAI,CAACkS,SAAS,EAAE;CACjB;;CAEA;CACD;CACA;CACA;CACA;CACCE,EAAAA,OAAOA,GAAG;KACT,IAAI5jB,IAAI,GAAG,IAAI;CACf,IAAA,IAAIqW,cAAc,GAAGrW,IAAI,CAACqW,cAAc;CAExCrW,IAAAA,IAAI,CAACF,OAAO,CAAC,SAAS,CAAC;KACvBE,IAAI,CAACT,GAAG,EAAE;CACVS,IAAAA,IAAI,CAACwH,OAAO,CAACP,MAAM,EAAE;CACrBjH,IAAAA,IAAI,CAACwT,QAAQ,CAACvM,MAAM,EAAE;CAEtBjH,IAAAA,IAAI,CAAC+C,KAAK,CAACqC,SAAS,GAAGiR,cAAc,CAACjR,SAAS;CAC/CpF,IAAAA,IAAI,CAAC+C,KAAK,CAAC8P,QAAQ,GAAGwD,cAAc,CAACxD,QAAQ;KAE7C7L,aAAa,CAAChH,IAAI,CAAC+C,KAAK,EAAC,aAAa,EAAC,sBAAsB,CAAC;KAE9D/C,IAAI,CAACmW,QAAQ,EAAE;CAEf,IAAA,OAAOnW,IAAI,CAAC+C,KAAK,CAAC0P,SAAS;CAC5B;;CAEA;CACD;CACA;CACA;CACA;CACCnE,EAAAA,MAAMA,CAAEuV,YAA6B,EAAEva,IAAS,EAAmB;KAClE,IAAInF,EAAE,EAAEiZ,IAAI;KACZ,MAAMpd,IAAI,GAAG,IAAI;KAEjB,IAAI,OAAO,IAAI,CAACM,QAAQ,CAACgO,MAAM,CAACuV,YAAY,CAAC,KAAK,UAAU,EAAE;CAC7D,MAAA,OAAO,IAAI;CACZ;;CAEA;CACAzG,IAAAA,IAAI,GAAGpd,IAAI,CAACM,QAAQ,CAACgO,MAAM,CAACuV,YAAY,CAAC,CAAC3hB,IAAI,CAAC,IAAI,EAAEoH,IAAI,EAAE1H,WAAW,CAAC;KAEvE,IAAI,CAACwb,IAAI,EAAE;CACV,MAAA,OAAO,IAAI;CACZ;CAEAA,IAAAA,IAAI,GAAGxY,MAAM,CAAEwY,IAAK,CAAC;;CAErB;CACA,IAAA,IAAIyG,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,eAAe,EAAE;OAElE,IAAIva,IAAI,CAACtJ,IAAI,CAACM,QAAQ,CAAC4M,aAAa,CAAC,EAAE;SACtC3E,OAAO,CAAC6U,IAAI,EAAC;CAAC,UAAA,eAAe,EAAC;CAAM,SAAC,CAAC;CACvC,OAAC,MAAI;SACJ7U,OAAO,CAAC6U,IAAI,EAAC;CAAC,UAAA,iBAAiB,EAAE;CAAE,SAAC,CAAC;CACtC;CAED,KAAC,MAAK,IAAIyG,YAAY,KAAK,UAAU,EAAE;OACtC1f,EAAE,GAAGmF,IAAI,CAAC2G,KAAK,CAACjQ,IAAI,CAACM,QAAQ,CAAC8M,kBAAkB,CAAC;OACjD7E,OAAO,CAAC6U,IAAI,EAAC;CAAC,QAAA,YAAY,EAAEjZ;CAAE,OAAC,CAAC;OAChC,IAAGmF,IAAI,CAAC2G,KAAK,CAACjQ,IAAI,CAACM,QAAQ,CAAC4M,aAAa,CAAC,EAAE;SAC3C3E,OAAO,CAAC6U,IAAI,EAAC;CAAC,UAAA,eAAe,EAAE;CAAE,SAAC,CAAC;CACpC;CACD;CAEA,IAAA,IAAIyG,YAAY,KAAK,QAAQ,IAAIA,YAAY,KAAK,MAAM,EAAE;CACzD,MAAA,MAAMniB,KAAK,GAAGC,QAAQ,CAAC2H,IAAI,CAACtJ,IAAI,CAACM,QAAQ,CAAC0M,UAAU,CAAC,CAAC;OACtDzE,OAAO,CAAC6U,IAAI,EAAC;CAAC,QAAA,YAAY,EAAE1b;CAAM,OAAC,CAAC;;CAGpC;OACA,IAAImiB,YAAY,KAAK,MAAM,EAAE;SAC5Bvd,UAAU,CAAC8W,IAAI,EAACpd,IAAI,CAACM,QAAQ,CAACwN,SAAS,CAAC;SACxCvF,OAAO,CAAC6U,IAAI,EAAC;CAAC,UAAA,cAAc,EAAC;CAAE,SAAC,CAAC;CAClC,OAAC,MAAI;SACJ9W,UAAU,CAAC8W,IAAI,EAACpd,IAAI,CAACM,QAAQ,CAACyN,WAAW,CAAC;SAC1CxF,OAAO,CAAC6U,IAAI,EAAC;CACZlJ,UAAAA,IAAI,EAAC,QAAQ;WACb/P,EAAE,EAACmF,IAAI,CAACqV;CACT,SAAC,CAAC;;CAEF;SACArV,IAAI,CAAC4X,IAAI,GAAG9D,IAAI;CAChBpd,QAAAA,IAAI,CAACkB,OAAO,CAACQ,KAAK,CAAC,GAAG4H,IAAI;CAC3B;CAGD;CAEA,IAAA,OAAO8T,IAAI;CAEZ;;CAGA;CACD;CACA;CACA;CACC3J,EAAAA,OAAOA,CAAEoQ,YAA6B,EAAEva,IAAS,EAAc;KAC9D,MAAM8T,IAAI,GAAG,IAAI,CAAC9O,MAAM,CAACuV,YAAY,EAAEva,IAAI,CAAC;KAE5C,IAAI8T,IAAI,IAAI,IAAI,EAAE;CACjB,MAAA,MAAM,sBAAsB;CAC7B;CACA,IAAA,OAAOA,IAAI;CACZ;;CAGA;CACD;CACA;CACA;CACA;CACA;CACC8C,EAAAA,UAAUA,GAAO;CAEhB1e,IAAAA,OAAO,CAAC,IAAI,CAACN,OAAO,EAAGiO,MAAgB,IAAG;OACzC,IAAIA,MAAM,CAAC+R,IAAI,EAAE;CAChB/R,QAAAA,MAAM,CAAC+R,IAAI,CAACja,MAAM,EAAE;SACpB,OAAOkI,MAAM,CAAC+R,IAAI;CACnB;CACD,KAAC,CAAC;CAEH;;CAEA;CACD;CACA;CACA;GACCP,YAAYA,CAACjf,KAAY,EAAC;CAEzB,IAAA,MAAM6c,SAAS,GAAK,IAAI,CAACC,SAAS,CAAC9c,KAAK,CAAC;CACzC,IAAA,IAAI6c,SAAS,EAAGA,SAAS,CAACtX,MAAM,EAAE;CAEnC;;CAEA;CACD;CACA;CACA;CACA;GACCwY,SAASA,CAAE1c,KAAY,EAAW;KACjC,OAAO,IAAI,CAACzC,QAAQ,CAACwL,MAAM,IAAK/I,KAAK,CAACrD,MAAM,GAAG,CAAE,IAAK,IAAI,CAACY,QAAQ,CAAC0L,YAAY,CAAsB9J,IAAI,CAAC,IAAI,EAAEa,KAAK,CAAC;CACxH;;CAGA;CACD;CACA;CACA;CACA;CACA;CACA;CACC+gB,EAAAA,IAAIA,CAAEC,IAAW,EAAEC,MAAa,EAAEC,MAAU,EAAE;KAC7C,IAAIjkB,IAAI,GAAG,IAAI;CACf,IAAA,IAAIkkB,WAAW,GAAGlkB,IAAI,CAACgkB,MAAM,CAAC;CAG9BhkB,IAAAA,IAAI,CAACgkB,MAAM,CAAC,GAAG,YAAU;OACxB,IAAIpH,MAAM,EAAEuH,UAAU;OAEtB,IAAIJ,IAAI,KAAK,OAAO,EAAE;SACrBnH,MAAM,GAAGsH,WAAW,CAACjkB,KAAK,CAACD,IAAI,EAAEP,SAAS,CAAC;CAC5C;OAEA0kB,UAAU,GAAGF,MAAM,CAAChkB,KAAK,CAACD,IAAI,EAAEP,SAAU,CAAC;OAE3C,IAAIskB,IAAI,KAAK,SAAS,EAAE;CACvB,QAAA,OAAOI,UAAU;CAClB;OAEA,IAAIJ,IAAI,KAAK,QAAQ,EAAE;SACtBnH,MAAM,GAAGsH,WAAW,CAACjkB,KAAK,CAACD,IAAI,EAAEP,SAAS,CAAC;CAC5C;CAEA,MAAA,OAAOmd,MAAM;MACb;CAEF;CAED;;CC7tFA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAMe,uBAAyB,IAAA;GACvC,IAAI5c,IAAI,GAAG,IAAI;;CAEf;CACD;CACA;CACA;CACA;CACA;CACA;CACA;GACCA,IAAI,CAAC8jB,IAAI,CAAC,SAAS,EAAC,UAAU,EAAEL,OAAc,IAAK;CAElD,IAAA,IAAIzjB,IAAI,CAACM,QAAQ,CAACmN,IAAI,KAAK,QAAQ,IAAI,CAACzN,IAAI,CAACuT,OAAO,CAAC9L,QAAQ,CAACzH,IAAI,CAAC4T,aAAa,CAAC,EAAG;CACnF6P,MAAAA,OAAO,GAAGzjB,IAAI,CAACsP,KAAK,CAAC5P,MAAM;CAC5B,KAAC,MAAM;CACN+jB,MAAAA,OAAO,GAAGnhB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACwb,GAAG,CAAC9d,IAAI,CAACsP,KAAK,CAAC5P,MAAM,EAAE+jB,OAAO,CAAC,CAAC;OAE3D,IAAIA,OAAO,IAAIzjB,IAAI,CAACmS,QAAQ,IAAI,CAACnS,IAAI,CAACshB,SAAS,EAAE;SAEhDthB,IAAI,CAACic,eAAe,EAAE,CAACnd,OAAO,CAAC,CAAC8R,KAAK,EAACsM,CAAC,KAAK;WAC3C,IAAIA,CAAC,GAAGuG,OAAO,EAAE;aAChBzjB,IAAI,CAAC4T,aAAa,CAAC0C,qBAAqB,CAAC,aAAa,EAAE1F,KAAM,CAAC;CAChE,WAAC,MAAM;CACN5Q,YAAAA,IAAI,CAACuT,OAAO,CAACzJ,WAAW,CAAE8G,KAAM,CAAC;CAClC;CACD,SAAC,CAAC;CACH;CACD;KAEA5Q,IAAI,CAACmS,QAAQ,GAAGsR,OAAO;CACxB,GAAC,CAAC;GAEFzjB,IAAI,CAAC8jB,IAAI,CAAC,SAAS,EAAC,WAAW,EAAEhc,SAAgB,IAAK;CAErD,IAAA,IAAI,CAAC9H,IAAI,CAAC4R,SAAS,EAAG;;CAEtB;CACA,IAAA,MAAMuJ,WAAW,GAAInb,IAAI,CAACgb,aAAa,CAAClT,SAAS,CAAC;CAClD,IAAA,IAAIqT,WAAW,EAAE;CAChB,MAAA,MAAMC,GAAG,GAAGlT,SAAS,CAACiT,WAAW,CAAC;CAClCnb,MAAAA,IAAI,CAAC8Z,QAAQ,CAAChS,SAAS,GAAG,CAAC,GAAGsT,GAAG,GAAG,CAAC,GAAEA,GAAG,CAAC;OAC3Cpb,IAAI,CAAC6Z,aAAa,EAAE;CACpB7S,MAAAA,aAAa,CAACmU,WAAW,EAAgB,aAAa,CAAC;;CAExD;CACA,KAAC,MAAI;OACJnb,IAAI,CAAC8Z,QAAQ,CAAC9Z,IAAI,CAACmS,QAAQ,GAAGrK,SAAS,CAAC;CAEzC;CAED,GAAC,CAAC;CAEH;;CCxEA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAQe,uBAAyB,IAAA;GACvC,MAAM9H,IAAI,GAAG,IAAI;CAEjBA,EAAAA,IAAI,CAACM,QAAQ,CAAC4L,UAAU,GAAG,IAAI,CAAC;;CAEhClM,EAAAA,IAAI,CAAC8jB,IAAI,CAAC,QAAQ,EAAC,OAAO,EAAC,MAAI;CAC9B9jB,IAAAA,IAAI,CAAC6T,UAAU,GAAI7T,IAAI,CAACuT,OAAO;CAE/BjN,IAAAA,UAAU,CAAEtG,IAAI,CAAC4T,aAAa,EAAE,gBAAgB,CAAC;CAEhD,IAAA,MAAMwQ,GAAG,GAAGxf,MAAM,CAAC,mCAAmC,CAAC;CACxDwf,IAAAA,GAAG,CAAC5f,MAAM,CAACxE,IAAI,CAAC4T,aAAa,CAAC;CAC9B5T,IAAAA,IAAI,CAACwT,QAAQ,CAACgM,YAAY,CAAC4E,GAAG,EAAEpkB,IAAI,CAACwT,QAAQ,CAACjO,UAAU,CAAC;;CAEzD;CACA,IAAA,MAAM4I,WAAW,GAAGvJ,MAAM,CAAC,mDAAmD,CAAqB;KACnGuJ,WAAW,CAACA,WAAW,GAAGnO,IAAI,CAACM,QAAQ,CAAC6N,WAAW,IAAG,EAAE;CACxDnO,IAAAA,IAAI,CAACuT,OAAO,CAAC/O,MAAM,CAAC2J,WAAW,CAAC;CAEjC,GAAC,CAAC;CAGFnO,EAAAA,IAAI,CAACb,EAAE,CAAC,YAAY,EAAC,MAAI;CAExB;KACAa,IAAI,CAAC4T,aAAa,CAACnQ,gBAAgB,CAAC,SAAS,EAAEL,GAAiB,IAAI;CACpE;OACC,QAAQA,GAAG,CAACuV,OAAO;SAClB,KAAKE,OAAiB;WACrB,IAAI7Y,IAAI,CAACsR,MAAM,EAAE;CAChBnO,YAAAA,cAAc,CAACC,GAAG,EAAC,IAAI,CAAC;aACxBpD,IAAI,CAAC2W,KAAK,EAAE;CACb;WACA3W,IAAI,CAAC+X,gBAAgB,EAAE;CACxB,UAAA;SACA,KAAKc,OAAiB;CACrB7Y,UAAAA,IAAI,CAAC6T,UAAU,CAAChB,QAAQ,GAAG,CAAC,CAAC;CAC9B,UAAA;CACD;OACA,OAAO7S,IAAI,CAACuV,SAAS,CAACrT,IAAI,CAAClC,IAAI,EAACoD,GAAG,CAAC;CACrC,KAAC,CAAC;CAEFpD,IAAAA,IAAI,CAACb,EAAE,CAAC,MAAM,EAAC,MAAI;CAClBa,MAAAA,IAAI,CAAC6T,UAAU,CAAChB,QAAQ,GAAG7S,IAAI,CAACuR,UAAU,GAAG,CAAC,CAAC,GAAGvR,IAAI,CAAC6S,QAAQ;CAChE,KAAC,CAAC;;CAGF;CACA7S,IAAAA,IAAI,CAACb,EAAE,CAAC,eAAe,EAAC,MAAK;CAC5Ba,MAAAA,IAAI,CAAC4T,aAAa,CAACU,KAAK,EAAE;CAC3B,KAAC,CAAC;;CAEF;CACA,IAAA,MAAM+P,WAAW,GAAGrkB,IAAI,CAAC0V,MAAM;KAC/B1V,IAAI,CAAC8jB,IAAI,CAAC,SAAS,EAAC,QAAQ,EAAE1gB,GAAe,IAAG;OAC/C,IAAIA,GAAG,IAAIA,GAAG,CAACkhB,aAAa,IAAItkB,IAAI,CAAC4T,aAAa,EAAG;CACrD,MAAA,OAAOyQ,WAAW,CAACniB,IAAI,CAAClC,IAAI,CAAC;CAC9B,KAAC,CAAC;CAEFuD,IAAAA,QAAQ,CAACvD,IAAI,CAAC4T,aAAa,EAAC,MAAM,EAAE,MAAM5T,IAAI,CAAC0V,MAAM,EAAG,CAAC;;CAEzD;CACA1V,IAAAA,IAAI,CAAC8jB,IAAI,CAAC,QAAQ,EAAC,OAAO,EAAC,MAAK;CAE/B,MAAA,IAAI,CAAC9jB,IAAI,CAACsR,MAAM,EAAG;CACnBtR,MAAAA,IAAI,CAAC6T,UAAU,CAACS,KAAK,CAAC;CAACiQ,QAAAA,aAAa,EAAE;CAAI,OAAC,CAAC;CAC7C,KAAC,CAAC;CAEH,GAAC,CAAC;CAEH;;CC3FA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAIe,4BAAyB,IAAA;GACvC,IAAIvkB,IAAI,GAAG,IAAI;CACf,EAAA,IAAIwkB,oBAAoB,GAAGxkB,IAAI,CAACuZ,eAAe;GAE/C,IAAI,CAACuK,IAAI,CAAC,SAAS,EAAC,iBAAiB,EAAE1gB,GAAiB,IAAK;CAE5D,IAAA,IAAIpD,IAAI,CAACqS,WAAW,CAAC3S,MAAM,EAAE;CAC5B,MAAA,OAAO8kB,oBAAoB,CAACtiB,IAAI,CAAClC,IAAI,EAAEoD,GAAG,CAAC;CAC5C;CAEA,IAAA,OAAO,KAAK;CACb,GAAC,CAAC;CAEH;;CC7BA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAQe,sBAAA,EAAyBkP,WAAqB,EAAE;CAE9D,EAAA,MAAMpR,OAAO,GAAGiF,MAAM,CAACC,MAAM,CAAC;CAC5BgO,IAAAA,KAAK,EAAO,SAAS;CACrBqQ,IAAAA,KAAK,EAAO,QAAQ;CACpBjb,IAAAA,SAAS,EAAG,QAAQ;CACpBhF,IAAAA,MAAM,EAAM;IACZ,EAAE8N,WAAW,CAAC;;CAGhB;GACA,IAAItS,IAAI,GAAK,IAAI;;CAEjB;CACA,EAAA,IAAI,CAACkB,OAAO,CAACsD,MAAM,EAAE;CACpB,IAAA;CACD;GAEA,IAAI4Y,IAAI,GAAG,sCAAsC,GAAGlc,OAAO,CAACsI,SAAS,GAAG,yBAAyB,GAAG5H,WAAW,CAACV,OAAO,CAACujB,KAAK,CAAC,GAAG,IAAI,GAAGvjB,OAAO,CAACkT,KAAK,GAAG,MAAM;CAE9JpU,EAAAA,IAAI,CAAC8jB,IAAI,CAAC,OAAO,EAAC,gBAAgB,EAAC,MAAM;KAExC,IAAIY,gBAAgB,GAAG1kB,IAAI,CAACM,QAAQ,CAACgO,MAAM,CAACiJ,IAAI;KAEhDvX,IAAI,CAACM,QAAQ,CAACgO,MAAM,CAACiJ,IAAI,GAAG,CAACjO,IAAc,EAAEgO,MAAyB,KAAK;CAE1E,MAAA,IAAIC,IAAI,GAAG3S,MAAM,CAAC8f,gBAAgB,CAACxiB,IAAI,CAAClC,IAAI,EAAEsJ,IAAI,EAAEgO,MAAM,CAAC,CAAY;CAEvE,MAAA,IAAIqN,YAAY,GAAG/f,MAAM,CAACwY,IAAI,CAAC;CAC/B7F,MAAAA,IAAI,CAACzN,WAAW,CAAC6a,YAAY,CAAC;CAE9BphB,MAAAA,QAAQ,CAACohB,YAAY,EAAC,WAAW,EAAEvhB,GAAG,IAAK;CAC1CD,QAAAA,cAAc,CAACC,GAAG,EAAC,IAAI,CAAC;CACzB,OAAC,CAAC;CAEFG,MAAAA,QAAQ,CAACohB,YAAY,EAAC,OAAO,EAAEvhB,GAAG,IAAK;SAEtC,IAAIpD,IAAI,CAAC2R,QAAQ,EAAG;;CAEpB;CACAxO,QAAAA,cAAc,CAACC,GAAG,EAAC,IAAI,CAAC;SAExB,IAAIpD,IAAI,CAAC2R,QAAQ,EAAG;SACpB,IAAI,CAAC3R,IAAI,CAACmjB,YAAY,CAAC,CAAC5L,IAAI,CAAC,EAACnU,GAAiB,CAAC,EAAG;CAEnDpD,QAAAA,IAAI,CAAC8gB,UAAU,CAACvJ,IAAI,CAAC;CACrBvX,QAAAA,IAAI,CAACyZ,cAAc,CAAC,KAAK,CAAC;SAC1BzZ,IAAI,CAACgW,UAAU,EAAE;CAClB,OAAC,CAAC;CAEF,MAAA,OAAOuB,IAAI;MACX;CAEF,GAAC,CAAC;CAGH;;CC7EA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAQe,6BAAA,EAAyBjF,WAA0B,EAAE;GACnE,MAAMtS,IAAI,GAAG,IAAI;CAEjB,EAAA,MAAMkB,OAAO,GAAGiF,MAAM,CAACC,MAAM,CAAC;KAC7Bwe,IAAI,EAAGzV,MAAgB,IAAK;CAC3B,MAAA,OAAOA,MAAM,CAACnP,IAAI,CAACM,QAAQ,CAAC2M,UAAU,CAAC;CACxC;IACA,EAACqF,WAAW,CAAC;CAEdtS,EAAAA,IAAI,CAACb,EAAE,CAAC,aAAa,EAAC,UAASuC,KAAY,EAAC;CAC3C,IAAA,IAAI,CAAC1B,IAAI,CAAC4R,SAAS,EAAE;CACpB,MAAA;CACD;KAEA,IAAI5R,IAAI,CAAC4T,aAAa,CAAClS,KAAK,CAAC2D,IAAI,EAAE,KAAK,EAAE,EAAE;CAC3C,MAAA,IAAI8J,MAAM,GAAGnP,IAAI,CAACkB,OAAO,CAACQ,KAAK,CAAC;CAChC,MAAA,IAAIyN,MAAM,EAAE;CACXnP,QAAAA,IAAI,CAACoa,eAAe,CAAClZ,OAAO,CAAC0jB,IAAI,CAAC1iB,IAAI,CAAClC,IAAI,EAAEmP,MAAM,CAAC,CAAC;CACtD;CACD;CACD,GAAC,CAAC;CAEH;;CCnCA+B,SAAS,CAACzQ,MAAM,CAAC,gBAAgB,EAAEokB,cAAc,CAAC;CAClD3T,SAAS,CAACzQ,MAAM,CAAC,gBAAgB,EAAEqkB,cAAc,CAAC;CAClD5T,SAAS,CAACzQ,MAAM,CAAC,qBAAqB,EAAEskB,mBAAmB,CAAC;CAC5D7T,SAAS,CAACzQ,MAAM,CAAC,eAAe,EAAEukB,aAAa,CAAC;CAChD9T,SAAS,CAACzQ,MAAM,CAAC,sBAAsB,EAAEwkB,oBAAoB,CAAC;;;;;;;;;;;", "x_google_ignoreList": [2, 3, 4, 5, 6]}