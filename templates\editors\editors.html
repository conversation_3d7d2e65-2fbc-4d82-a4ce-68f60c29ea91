{% extends "basic.html" %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          Редакторы
        </h2>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{{ url_for('editor_edit', editor_id=0) }}" class="btn btn-primary d-none d-sm-inline-block">
            <i class="ti ti-plus"></i>
            Добавить редактора
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="row row-deck row-cards">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Фильтры</h3>
          </div>
          <div class="card-body">
            <form method="GET" action="{{ url_for('editors') }}">
              <div class="row">
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">Имя</label>
                    <input type="text" class="form-control" name="name" value="{{ search_name }}" placeholder="Поиск по имени">
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">Специализация</label>
                    <input type="text" class="form-control" name="specialization" value="{{ search_specialization }}" placeholder="Поиск по специализации">
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div>
                      <button type="submit" class="btn btn-primary">Найти</button>
                      <a href="{{ url_for('editors') }}" class="btn btn-secondary">Сбросить</a>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Список редакторов ({{ total_editors }})</h3>
          </div>
          <div class="table-responsive">
            <table class="table table-vcenter card-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Имя</th>
                  <th>Email</th>
                  <th>Специализация</th>
                  <th>Статистика</th>
                  <th>Действия</th>
                </tr>
              </thead>
              <tbody>
                {% for editor in editors %}
                <tr>
                  <td>{{ editor.id }}</td>
                  <td>
                    <div class="d-flex py-1 align-items-center">
                      <span class="avatar me-2">{{ editor.name[0] if editor.name else 'E' }}</span>
                      <div class="flex-fill">
                        <div class="font-weight-medium">{{ editor.name }} {{ editor.second_name or '' }}</div>
                        <div class="text-secondary">{{ editor.father_name or '' }}</div>
                      </div>
                    </div>
                  </td>
                  <td>{{ editor.email or '-' }}</td>
                  <td>
                    <span class="badge bg-blue-lt">{{ editor.editor_specialization or 'Не указана' }}</span>
                  </td>
                  <td>
                    {% set stats = editor_stats.get(editor.id, {'total': 0, 'pending': 0, 'reviewed': 0, 'rejected': 0}) %}
                    <div class="text-secondary">
                      <small>
                        Всего: {{ stats.total }}<br>
                        Ожидает: <span class="text-warning">{{ stats.pending }}</span><br>
                        Проверено: <span class="text-success">{{ stats.reviewed }}</span><br>
                        Отклонено: <span class="text-danger">{{ stats.rejected }}</span>
                      </small>
                    </div>
                  </td>
                  <td>
                    <div class="btn-list flex-nowrap">
                      <a href="{{ url_for('editor_edit', editor_id=editor.id) }}" class="btn btn-white btn-sm">
                        Редактировать
                      </a>
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          
          {% if total_pages > 1 %}
          <div class="card-footer d-flex align-items-center">
            <p class="m-0 text-secondary">Показано {{ editors|length }} из {{ total_editors }} записей</p>
            <ul class="pagination m-0 ms-auto">
              {% if page > 1 %}
              <li class="page-item">
                <a class="page-link" href="{{ url_for('editors', page=page-1, name=search_name, specialization=search_specialization) }}">
                  <i class="ti ti-chevron-left"></i>
                  Назад
                </a>
              </li>
              {% endif %}
              
              {% for p in range(1, total_pages + 1) %}
                {% if p == page %}
                <li class="page-item active">
                  <a class="page-link" href="#">{{ p }}</a>
                </li>
                {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                <li class="page-item">
                  <a class="page-link" href="{{ url_for('editors', page=p, name=search_name, specialization=search_specialization) }}">{{ p }}</a>
                </li>
                {% elif p == 4 and page > 6 %}
                <li class="page-item disabled">
                  <span class="page-link">…</span>
                </li>
                {% elif p == total_pages - 3 and page < total_pages - 5 %}
                <li class="page-item disabled">
                  <span class="page-link">…</span>
                </li>
                {% endif %}
              {% endfor %}
              
              {% if page < total_pages %}
              <li class="page-item">
                <a class="page-link" href="{{ url_for('editors', page=page+1, name=search_name, specialization=search_specialization) }}">
                  Вперед
                  <i class="ti ti-chevron-right"></i>
                </a>
              </li>
              {% endif %}
            </ul>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
