{% extends "basic.html" %}

{% block content %}
<div class="page-header d-print-none">
  <div class="row align-items-center">
    <div class="col">
      <h2 class="page-title">Редакторы</h2>
    </div>
    <div class="col-auto ms-auto">
      <div class="btn-list">
        <a href="{{ url_for('editor_edit', editor_id=0) }}" class="btn btn-primary d-none d-sm-inline-block">
          <i class="ti ti-plus"></i>
          Добавить редактора
        </a>
      </div>
    </div>
  </div>
</div>

<form method="GET" action="{{ url_for('editors') }}">
<div class="row mb-3">
  <div class="col-md-4">
    <div class="form-label">Имя</div>
    <input type="text" class="form-control" placeholder="Введите имя..." name="name" value="{{ search_name|default('') }}">
  </div>
  <div class="col-md-4">
    <div class="form-label">Специализация</div>
    <input type="text" class="form-control" placeholder="Введите специализацию..." name="specialization" value="{{ search_specialization|default('') }}">
  </div>
  <div class="col-md-4">
    <div class="form-label">&nbsp;</div>
    <div class="d-flex justify-content-end">
      <button class="btn btn-ghost-danger me-2" type="button" onclick="window.location='{{ url_for('editors') }}';">
        <i class="ti ti-x pe-2"></i>
        Очистить
      </button>
      <button class="btn btn-primary" type="submit">
        <i class="ti ti-search pe-2"></i>
        Поиск
      </button>
    </div>
  </div>
</div>
</form>

<div class="card mt-4">
  <div class="table-responsive">
    <table class="table card-table table-hover">
      <thead>
        <tr>
          <th>Имя</th>
          <th>Email</th>
          <th>Специализация</th>
          <th>Статистика</th>
          <th class="w-1"></th>
        </tr>
      </thead>
      <tbody>
        {% for editor in editors %}
        <tr>
          <td class="align-middle">
            <div class="d-flex py-1 align-items-center">
              <span class="avatar me-2">{{ editor.name[0] if editor.name else 'E' }}</span>
              <div class="flex-fill">
                <div class="font-weight-medium">{{ editor.name }} {{ editor.second_name or '' }}</div>
                <div class="text-secondary">{{ editor.father_name or '' }}</div>
              </div>
            </div>
          </td>
          <td class="align-middle">{{ editor.email or '-' }}</td>
          <td class="align-middle">
            {% if editor.editor_specialization %}
              <span class="badge bg-blue-lt">{{ editor.editor_specialization }}</span>
            {% else %}
              <span class="text-secondary">Не указана</span>
            {% endif %}
          </td>
          <td class="align-middle">
            {% set stats = editor_stats.get(editor.id, {'total': 0, 'pending': 0, 'reviewed': 0, 'rejected': 0}) %}
            <div class="text-secondary">
              <small>
                Всего: {{ stats.total }}<br>
                Ожидает: <span class="text-warning">{{ stats.pending }}</span><br>
                Проверено: <span class="text-success">{{ stats.reviewed }}</span><br>
                Отклонено: <span class="text-danger">{{ stats.rejected }}</span>
              </small>
            </div>
          </td>
          <td class="align-middle">
            <a href="{{ url_for('editor_edit', editor_id=editor.id) }}" class="btn btn-icon btn-outline-primary bg-primary-lt btn-sm px-2 py-1">
              Просмотр
            </a>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  <div class="card-footer d-flex align-items-center">
    <p class="m-0 text-secondary">Показано <span>{{ editors|length }}</span> из <span>{{ total_editors }}</span> записей</p>
    <ul class="pagination m-0 ms-auto">
      <li class="page-item {% if page <= 1 %}disabled{% endif %}">
        <a class="page-link" href="{{ url_for('editors', page=page-1, name=search_name, specialization=search_specialization) }}" tabindex="-1" aria-disabled="true">
          <i class="ti ti-chevron-left"></i>
        </a>
      </li>
      {% for p in range(1, total_pages+1) %}
        <li class="page-item {% if p == page %}active{% endif %}"><a class="page-link" href="{{ url_for('editors', page=p, name=search_name, specialization=search_specialization) }}">{{ p }}</a></li>
      {% endfor %}
      <li class="page-item {% if page >= total_pages %}disabled{% endif %}">
        <a class="page-link" href="{{ url_for('editors', page=page+1, name=search_name, specialization=search_specialization) }}">
          <i class="ti ti-chevron-right"></i>
        </a>
      </li>
    </ul>
  </div>
</div>

{% endblock %}
