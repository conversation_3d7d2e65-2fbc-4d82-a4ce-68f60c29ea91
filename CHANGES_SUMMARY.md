# Сводка изменений: Система редакторов

## Файлы базы данных

### `migration.sql` (новый файл)
- Создание таблицы `editor_assignments` для управления назначениями
- Создание таблицы `editor_notifications` для уведомлений
- Добавление поля `editor_specialization` в таблицу `users`
- Добавление поля `editor_review_status` в таблицу `submissions`
- Создание индексов для производительности
- Вставка тестовых редакторов

## Изменения в backend (run.py)

### Новые декораторы доступа
- `is_editor_allowed` - доступ только для редакторов
- `is_admin_or_editor` - доступ для админов и редакторов

### Новые вспомогательные функции
- `get_current_user()` - получение текущего пользователя
- `create_editor_notification()` - создание уведомлений
- `get_editors()` - получение списка редакторов
- `parse_date()` - парсинг дат

### Новые маршруты

#### Управление редакторами (только админ)
- `GET /fmadmin/editors` - список редакторов
- `GET/POST /fmadmin/editors/<id>` - создание/редактирование редактора

#### Назначения (админ + редакторы)
- `GET /fmadmin/editor-assignments` - список назначений
- `GET/POST /fmadmin/submissions/<id>/assign-editors` - назначение редакторов
- `GET/POST /fmadmin/editor-assignments/<id>/review` - проверка статьи

## Изменения в frontend

### Обновленные шаблоны

#### `templates/basic.html`
- Добавлен пункт меню "Редакторы"
- Добавлен пункт меню "Назначения редакторам"
- Исправлены жестко заданные пути на `url_for()`

#### `templates/submissions/list.html`
- Добавлена колонка "Статус проверки"
- Добавлена кнопка "Назначить редакторов" для подходящих статей
- Отображение статуса редакторской проверки

### Новые шаблоны

#### `templates/editors/editors.html`
- Список всех редакторов с фильтрами
- Статистика по назначениям для каждого редактора
- Пагинация и поиск

#### `templates/editors/edit.html`
- Форма создания/редактирования редактора
- Поля для специализации и контактной информации
- Статистические карточки для существующих редакторов

#### `templates/editors/assignments.html`
- Список назначений с фильтрами по статусу и редактору
- Разные представления для админов и редакторов
- Кнопки действий в зависимости от статуса

#### `templates/editors/assign.html`
- Форма назначения редакторов на статью
- Выбор нескольких редакторов с чекбоксами
- Информация о статье и уже назначенных редакторах

#### `templates/editors/review.html`
- Форма проверки статьи редактором
- Отображение только анонимной информации
- Возможность оставить комментарий и прикрепить файл
- Выбор решения (одобрить/отклонить)

## Ключевые особенности реализации

### Безопасность
- Редакторы видят только анонимную информацию о статьях
- Строгое разделение прав доступа через декораторы
- Проверка принадлежности назначений редактору

### Функциональность
- Поддержка множественных назначений на одну статью
- Автоматическое обновление статусов статей
- Система уведомлений для редакторов
- Загрузка файлов с замечаниями

### Интеграция
- Полная совместимость с существующей системой
- Использование существующих компонентов UI
- Сохранение архитектуры приложения

### UX/UI
- Интуитивные интерфейсы для всех ролей
- Четкое разделение функций админа и редактора
- Информативные статусы и индикаторы прогресса

## Файлы для выполнения миграции

1. Выполните `migration.sql` в базе данных
2. Перезапустите приложение
3. Создайте редакторов через интерфейс админа
4. Начните назначать статьи на проверку

## Тестирование

Для тестирования системы:
1. Создайте пользователя с ролью 'editor'
2. Создайте подачу статьи с анонимным файлом
3. Назначьте редактора через интерфейс админа
4. Войдите как редактор и проверьте статью
5. Проверьте обновление статусов в админ-панели
