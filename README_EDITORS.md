# Система редакторов для научного журнала

## Описание

Данная реализация добавляет в систему управления научным журналом полнофункциональную систему редакторов с возможностью назначения статей на проверку, комментирования и прикрепления файлов.

## Новые возможности

### 1. Роль редактора
- Новая роль пользователя `editor` с ограниченными правами доступа
- Специализация редактора (область научных интересов)
- Статистика работы редактора

### 2. Система назначений
- Администратор может назначать редакторов для проверки статей
- Поддержка назначения нескольких редакторов на одну статью
- Автоматические уведомления редакторам о новых назначениях
- Отслеживание статуса проверки

### 3. Процесс проверки
- Редактор видит только название статьи и анонимный файл
- Возможность оставить комментарий
- Прикрепление файла с замечаниями
- Решение: одобрить или отклонить статью

## Установка

### 1. Выполните миграцию базы данных

```sql
-- Выполните содержимое файла migration.sql в вашей PostgreSQL базе данных
psql -U postgres -d journal -f migration.sql
```

### 2. Перезапустите приложение

```bash
python run.py
```

### 3. Авторизация

Система теперь требует авторизации для доступа к административной панели:

- Перейдите на `/login` для входа в систему
- Используйте email и пароль пользователя с ролью `admin` или `editor`
- Тестовые редакторы создаются автоматически при миграции:
  - `<EMAIL>` / пароль из базы данных
  - `<EMAIL>` / пароль из базы данных

**Важно:** Только пользователи с ролями `admin` и `editor` имеют доступ к системе.

## Структура базы данных

### Новые таблицы

#### `editor_assignments`
Таблица для управления назначениями статей редакторам:
- `submission_id` - ID статьи
- `editor_id` - ID редактора
- `assigned_by` - кто назначил (админ)
- `status` - статус проверки (pending, reviewed, rejected)
- `editor_comment` - комментарий редактора
- `editor_file` - прикрепленный файл
- `assigned_at`, `reviewed_at` - временные метки

#### `editor_notifications`
Уведомления для редакторов:
- `editor_id` - ID редактора
- `assignment_id` - ID назначения
- `message` - текст уведомления
- `is_read` - прочитано ли уведомление

### Изменения в существующих таблицах

#### `users`
- `editor_specialization` - специализация редактора

#### `submissions`
- `editor_review_status` - статус редакторской проверки

## Использование

### Для администратора

1. **Создание редакторов**
   - Перейдите в "Пользователи" → "Редакторы"
   - Нажмите "Добавить редактора"
   - Заполните информацию и укажите специализацию

2. **Назначение редакторов**
   - В списке подач найдите статью со статусом "submitted" или "in_process"
   - Убедитесь, что у статьи есть анонимный файл
   - Нажмите "Назначить редакторов"
   - Выберите одного или нескольких редакторов

3. **Отслеживание проверок**
   - Перейдите в "Назначения редакторам"
   - Просматривайте статус всех назначений
   - Читайте комментарии и скачивайте файлы от редакторов

### Для редактора

1. **Просмотр назначений**
   - Войдите в систему с ролью "editor"
   - Перейдите в "Назначения редакторам"
   - Видите только свои назначения

2. **Проверка статьи**
   - Нажмите "Проверить" для статьи со статусом "Ожидает проверки"
   - Скачайте анонимный файл статьи
   - Изучите название и аннотацию
   - Оставьте комментарий
   - При необходимости прикрепите файл с замечаниями
   - Выберите решение: одобрить или отклонить

## Безопасность и анонимность

- Редакторы видят только название статьи, аннотацию и анонимный файл
- Информация об авторе скрыта от редакторов
- Редакторы не могут видеть назначения других редакторов
- Все действия логируются с временными метками

## API маршруты

### Для администраторов
- `GET /fmadmin/editors` - список редакторов
- `GET/POST /fmadmin/editors/<id>` - редактирование редактора
- `GET/POST /fmadmin/submissions/<id>/assign-editors` - назначение редакторов

### Для редакторов и администраторов
- `GET /fmadmin/editor-assignments` - список назначений
- `GET/POST /fmadmin/editor-assignments/<id>/review` - проверка статьи

## Статусы проверки

### Статусы назначений (`editor_assignments.status`)
- `pending` - ожидает проверки
- `reviewed` - проверено (одобрено)
- `rejected` - отклонено

### Статусы статей (`submissions.editor_review_status`)
- `not_assigned` - редакторы не назначены
- `assigned` - редакторы назначены
- `in_review` - проверка в процессе
- `reviewed` - все редакторы завершили проверку
- `approved` - статья одобрена
- `rejected` - статья отклонена

## Уведомления

Система автоматически создает уведомления для редакторов при:
- Назначении новой статьи на проверку
- Изменении статуса назначения

## Файлы и загрузки

Редакторы могут прикреплять файлы с замечаниями в форматах:
- PDF
- DOC/DOCX
- TXT

Файлы сохраняются в `static/uploads/editor_reviews/`

## Интеграция с существующей системой

Система редакторов полностью интегрирована с существующим функционалом:
- Использует существующую систему пользователей
- Работает с текущими подачами статей
- Совместима с системой ролей
- Использует существующие шаблоны и стили
