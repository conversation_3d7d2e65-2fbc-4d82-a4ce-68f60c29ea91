{% extends 'basic.html' %}

{% block content %}

<div class="page-header d-print-none">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="page-title">Подача статьи #{{ submission.id }}</h2>
            <a href="{{ url_for('submissions') }}" class="">
                <i class="ti ti-arrow-left"></i>
                Назад к списку
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Основная информация</h3>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>ID:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ submission.id }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Статус:</strong>
                    </div>
                    <div class="col-sm-9">
                        {% if submission.status == 'draft' %}
                            <span class="badge bg-secondary-lt">Черновик</span>
                        {% elif submission.status == 'published' %}
                            <span class="badge bg-success-lt">Опубликовано</span>
                        {% elif submission.status == 'in_process' %}
                            <span class="badge bg-warning-lt">В процессе</span>
                        {% elif submission.status == 'rejected' %}
                            <span class="badge bg-danger-lt">Отклонено</span>
                        {% else %}
                            <span class="badge bg-light">{{ submission.status or 'Не указан' }}</span>
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Название:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ submission.title or 'Не указано' }}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Аннотация:</strong>
                    </div>
                    <div class="col-sm-9">
                        {% if submission.abstract %}
                            <div class="text-pre-wrap">{{ submission.abstract }}</div>
                        {% else %}
                            <span class="text-muted">Не указана</span>
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Ключевые слова:</strong>
                    </div>
                    <div class="col-sm-9">
                        {% if submission.keywords %}
                            {% for keyword in submission.keywords %}
                                <span class="badge bg-light me-1">{{ keyword }}</span>
                            {% endfor %}
                        {% else %}
                            <span class="text-muted">Не указаны</span>
                        {% endif %}
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-sm-3">
                        <strong>Количество слов:</strong>
                    </div>
                    <div class="col-sm-9">
                        {{ submission.word_count or 'Не указано' }}
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">Ответ администрации</h3>
            </div>
            <div class="card-body">
                <form id="adminResponseForm" method="post" action="/fmadmin/submissions/edit">
                    <input type="hidden" name="submission_id" value="{{ submission.id }}">

                    <div class="mb-3">
                        <label class="form-label">Статус</label>
                        <select class="form-select" name="status" required>
                            <option value="draft" {% if submission.status == 'draft' %}selected{% endif %}>Черновик</option>
                            <option value="in_process" {% if submission.status == 'in_process' %}selected{% endif %}>В процессе</option>
                            <option value="published" {% if submission.status == 'published' %}selected{% endif %}>Опубликовано</option>
                            <option value="rejected" {% if submission.status == 'rejected' %}selected{% endif %}>Отклонено</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Заметки администрации</label>
                        <textarea class="form-control" name="notes" rows="4" placeholder="Заметки к подаче">{{ submission.notes or '' }}</textarea>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">Сохранить изменения</button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">Дополнительные свойства</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if submission.is_special %}checked{% endif %} disabled>
                            <label class="form-check-label">Специальная статья</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if submission.is_dataset %}checked{% endif %} disabled>
                            <label class="form-check-label">Набор данных</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if submission.check_copyright %}checked{% endif %} disabled>
                            <label class="form-check-label">Проверка авторских прав</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if submission.check_ethical %}checked{% endif %} disabled>
                            <label class="form-check-label">Этическая проверка</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if submission.check_consent %}checked{% endif %} disabled>
                            <label class="form-check-label">Согласие получено</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if submission.check_acknowledgements %}checked{% endif %} disabled>
                            <label class="form-check-label">Благодарности</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if submission.is_used_previous %}checked{% endif %} disabled>
                            <label class="form-check-label">Использованы предыдущие работы</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if submission.is_corresponding_author %}checked{% endif %} disabled>
                            <label class="form-check-label">Корреспондирующий автор</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" {% if submission.is_competing_interests %}checked{% endif %} disabled>
                            <label class="form-check-label">Конкурирующие интересы</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">Даты</h3>
            </div>
            <div class="card-body">
                {% if submission.created_date %}
                    <p><strong>Создано:</strong> {{ submission.created_date | timestamp_to_date }}</p>
                {% endif %}
                {% if submission.updated_at %}
                    <p><strong>Обновлено:</strong> {{ submission.updated_at | timestamp_to_date }}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Автор подачи</h3>
            </div>
            <div class="card-body">
                {% if user %}
                    <p><strong>Имя:</strong> {{ user.name }}</p>
                    <p><strong>Email:</strong> {{ user.email or 'Не указан' }}</p>
                    <p><strong>ID:</strong> {{ user.id }}</p>
                {% else %}
                    <p class="text-muted">Пользователь не найден (ID: {{ submission.user_id }})</p>
                {% endif %}
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">Авторы статьи</h3>
            </div>
            <div class="card-body">
                {% if main_author %}
                    <div class="mb-3">
                        <strong>Основной автор:</strong>
                        <div class="ms-3">
                            <p class="mb-1">{{ main_author.name }}</p>
                            {% if main_author.orcid %}
                                <p class="mb-0 text-muted small">ORCID: {{ main_author.orcid }}</p>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}

                {% if sub_authors %}
                    <div>
                        <strong>Соавторы:</strong>
                        <div class="ms-3">
                            {% for author in sub_authors %}
                                <div class="mb-2">
                                    <p class="mb-1">{{ author.name }}</p>
                                    {% if author.orcid %}
                                        <p class="mb-0 text-muted small">ORCID: {{ author.orcid }}</p>
                                    {% endif %}
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                {% endif %}

                {% if not main_author and not sub_authors %}
                    <p class="text-muted">Авторы не указаны</p>
                {% endif %}
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h3 class="card-title">Файлы</h3>
            </div>
            <div class="card-body">
                {% if submission.file_authors %}
                    <div class="mb-2">
                        <a href="{{ submission.file_authors }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="ti ti-file"></i> Файл с авторами
                        </a>
                    </div>
                {% endif %}

                {% if submission.file_anonymized %}
                    <div class="mb-2">
                        <a href="{{ submission.file_anonymized }}" target="_blank" class="btn btn-sm btn-outline-primary">
                            <i class="ti ti-file"></i> Анонимизированный файл
                        </a>
                    </div>
                {% endif %}

                {% if not submission.file_authors and not submission.file_anonymized %}
                    <p class="text-muted">Файлы не загружены</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Обработка отправки формы
    const form = document.getElementById('adminResponseForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('/fmadmin/submissions/edit', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Ошибка при сохранении: ' + (data.error || 'Неизвестная ошибка'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Ошибка при отправке запроса');
            });
        });
    }
});
</script>
{% endblock %} 