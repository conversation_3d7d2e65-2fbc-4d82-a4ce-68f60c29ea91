{% extends 'basic.html' %}

{% block content %}

<div class="page-header d-print-none">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="page-title">Документы</h2>
        </div>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <div class="card-title">Фильтры</div>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Статус верификации</label>
                <select class="form-select" name="status">
                    <option value="">Все статусы</option>
                    <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Ожидает</option>
                    <option value="verified" {% if status_filter == 'verified' %}selected{% endif %}>Проверено</option>
                    <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Отклонено</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Название работы</label>
                <input type="text" class="form-control" name="title" value="{{ search_title }}" placeholder="Поиск по названию">
            </div>
            <div class="col-md-3">
                <label class="form-label">Пользователь ID</label>
                <input type="number" class="form-control" name="user" value="{{ user_filter }}" placeholder="ID пользователя">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">Применить</button>
                <a href="{{ url_for('submission_documents') }}" class="btn btn-secondary ms-2">Сбросить</a>
            </div>
        </form>
    </div>
</div>

<div class="card mt-4">
  <div class="table-responsive">
    <table class="table card-table table-hover">
      <thead>
        <tr>
          <th>ID</th>
          <th>Пользователь</th>
          <th>Название работы</th>
          <th>Статус верификации</th>
          <th>Файл</th>
          <th>Дата создания</th>
          <th>Действия</th>
        </tr>
      </thead>
      <tbody>
        {% for doc in docs_list %}
        <tr>
          <td class="align-middle">{{ doc.id }}</td>
          <td class="align-middle">
            {% if users_map.get(doc.user_id) %}
              {{ users_map[doc.user_id].name }}
            {% else %}
              ID: {{ doc.user_id or 'Не указан' }}
            {% endif %}
          </td>
          <td class="align-middle">{{ doc.work_title or 'Без названия' }}</td>
          <td class="align-middle">
            {% if doc.verification_status == 'pending' %}
              <span class="badge bg-warning-lt">Ожидает</span>
            {% elif doc.verification_status == 'verified' %}
              <span class="badge bg-success-lt">Проверено</span>
            {% elif doc.verification_status == 'rejected' %}
              <span class="badge bg-danger-lt">Отклонено</span>
            {% else %}
              <span class="badge bg-light">{{ doc.verification_status or 'Не указан' }}</span>
            {% endif %}
          </td>
          <td class="align-middle">
            {% if doc.file_path %}
              <a href="{{ doc.file_path }}" target="_blank" class="btn btn-xs btn-outline-primary" title="Скачать файл">
                <i class="ti ti-download"></i>
              </a>
            {% else %}
              <span class="text-muted">Нет файла</span>
            {% endif %}
          </td>
          <td class="align-middle">
            {% if doc.created_at %}
              {{ doc.created_at | timestamp_to_date }}
            {% else %}
              Не указана
            {% endif %}
          </td>
          <td class="align-middle">
            <button class="btn btn-sm btn-outline-secondary" 
                    data-bs-toggle="modal" 
                    data-bs-target="#editDocModal"
                    data-doc-id="{{ doc.id }}"
                    data-doc-status="{{ doc.verification_status or '' }}"
                    data-doc-title="{{ doc.work_title or '' }}">
              <i class="ti ti-edit"></i> Редактировать
            </button>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  <div class="card-footer d-flex align-items-center">
    {% set start_idx = (page - 1) * 25 + 1 %}
    {% set end_idx = (page - 1) * 25 + docs_list|length %}
    <p class="m-0 text-secondary">Показано <span>{{ start_idx }}-{{ end_idx }}</span> из <span>{{ total_docs }}</span> записей</p>
    <ul class="pagination m-0 ms-auto">
      <li class="page-item {% if page <= 1 %}disabled{% endif %}">
        <a class="page-link" href="{{ url_for('submission_documents', page=page-1, status=status_filter, title=search_title, user=user_filter) }}" tabindex="-1" aria-disabled="true">
          <i class="ti ti-chevron-left"></i>
        </a>
      </li>
      {% for p in range(1, total_pages+1) %}
        <li class="page-item {% if p == page %}active{% endif %}"><a class="page-link" href="{{ url_for('submission_documents', page=p, status=status_filter, title=search_title, user=user_filter) }}">{{ p }}</a></li>
      {% endfor %}
      <li class="page-item {% if page >= total_pages %}disabled{% endif %}">
        <a class="page-link" href="{{ url_for('submission_documents', page=page+1, status=status_filter, title=search_title, user=user_filter) }}">
          <i class="ti ti-chevron-right"></i>
        </a>
      </li>
    </ul>
  </div>
</div>

<!-- Модальное окно для редактирования документа -->
<div class="modal modal-blur fade" id="editDocModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Редактирование документа</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editDocForm" method="post" action="{{ url_for('document_edit') }}">
                <div class="modal-body">
                    <input type="hidden" id="doc_id" name="doc_id">

                    <div class="mb-3">
                        <label class="form-label">Название работы</label>
                        <input type="text" class="form-control" id="doc_title" name="work_title" readonly>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Статус верификации</label>
                        <select class="form-select" id="doc_status" name="verification_status" required>
                            <option value="pending">Ожидает</option>
                            <option value="verified">Проверено</option>
                            <option value="rejected">Отклонено</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="#" class="btn btn-link link-secondary" data-bs-dismiss="modal">Отмена</a>
                    <button type="submit" class="btn btn-primary ms-auto">Сохранить изменения</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Обработка показа модального окна
    const modal = document.getElementById('editDocModal');
    if (modal) {
        modal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            
            const docId = button.getAttribute('data-doc-id');
            const docStatus = button.getAttribute('data-doc-status');
            const docTitle = button.getAttribute('data-doc-title');
            
            // Заполняем форму
            document.getElementById('doc_id').value = docId;
            document.getElementById('doc_status').value = docStatus;
            document.getElementById('doc_title').value = docTitle;
        });
    }

    // Обработка отправки формы
    const form = document.getElementById('editDocForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('{{ url_for('document_edit') }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Ошибка при сохранении: ' + (data.error || 'Неизвестная ошибка'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Ошибка при отправке запроса');
            });
        });
    }
});
</script>
{% endblock %} 