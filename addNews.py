from connector import PostgreSQLConnector
import time

# Подключение к базе
conn = PostgreSQLConnector(database='journal', user='postgres', password='1', host='localhost', port='5432')

now = int(time.time())

# Добавляем 10 новостей
types = [('news', 'Новость'), ('announcement', 'Объявление')]
for t, label in types:
    for i in range(10):
        conn.news.add(
            type=t,
            title=f'{label} {i+1} (en)',
            title_ru=f'{label} {i+1}',
            title_uz=f'{label} {i+1} (uz)',
            content=f'{label} {i+1} (en)',
            content_ru=f'Это содержимое {label.lower()} номер {i+1}.',
            content_uz=f'Bu {label.lower()} {i+1}-raqam uchun matn.',
            status='published' if i % 2 == 0 else 'draft',
            created_at=now - i * 86400,
            published_at=now - i * 86400 if i % 2 == 0 else None,
            author_id=None,
            cover_image=None
        ).exec()

print('10 новостей и 10 объявлений добавлено!') 