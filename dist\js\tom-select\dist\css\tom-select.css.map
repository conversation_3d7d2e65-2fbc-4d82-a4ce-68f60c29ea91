{"version": 3, "sources": ["tom-select.css"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;EAaE;AACF;EACE,yBAAyB;EACzB,gBAAgB;EAChB,WAAW;EACX,gBAAgB;EAChB,kBAAkB;EAClB,UAAU;EACV,sBAAsB;EACtB,gBAAgB;EAChB,kBAAkB;EAClB,aAAa;EACb,eAAe;AACjB;AACA;EACE,0DAA0D;AAC5D;AACA;EACE,sBAAsB;AACxB;AACA;EACE,0BAA0B;AAC5B;AACA;EACE,gBAAgB;AAClB;AACA;EACE,wBAAwB;EACxB,qBAAqB;AACvB;AACA;EACE,eAAe;EACf,mBAAmB;EACnB,gBAAgB;EAChB,mBAAmB;EACnB,cAAc;EACd,uBAAuB;AACzB;AACA;EACE,mBAAmB;EACnB,cAAc;EACd,uBAAuB;AACzB;AACA;EACE,+BAA+B;EAC/B,iBAAiB;EACjB,qBAAqB;AACvB;AACA;EACE,cAAc;EACd,eAAe;EACf,gCAAgC;EAChC,qBAAqB;EACrB,wBAAwB;EACxB,2BAA2B;EAC3B,0BAA0B;EAC1B,oBAAoB;EACpB,yBAAyB;EACzB,yBAAyB;EACzB,2BAA2B;EAC3B,+BAA+B;EAC/B,oCAA4B;KAA5B,iCAA4B;MAA5B,gCAA4B;UAA5B,4BAA4B;EAC5B,2BAA2B;AAC7B;AACA;EACE,aAAa;AACf;AACA;EACE,wBAAwB;AAC1B;AACA;EACE,wBAAwB;AAC1B;AACA;EACE,iBAAiB;AACnB;AACA;EACE,UAAU;EACV,WAAW;AACb;AACA;EACE,+BAA+B;AACjC;AACA;EACE,YAAY;EACZ,yBAAyB;AAC3B;AACA;EACE,UAAU;EACV,kBAAkB;EAClB,cAAc;AAChB;;AAEA;EACE,kBAAkB;EAClB,SAAS;EACT,OAAO;EACP,WAAW;EACX,WAAW;EACX,yBAAyB;EACzB,gBAAgB;EAChB,mBAAmB;EACnB,kBAAkB;EAClB,sBAAsB;EACtB,wCAAwC;EACxC,0BAA0B;AAC5B;AACA;EACE,eAAe;EACf,gBAAgB;AAClB;AACA;EACE,oCAAoC;EACpC,kBAAkB;AACpB;AACA;;;;EAIE,gBAAgB;AAClB;AACA;EACE,eAAe;EACf,YAAY;AACd;AACA;EACE,UAAU;EACV,eAAe;AACjB;AACA;EACE,kBAAkB;AACpB;AACA;EACE,cAAc;EACd,gBAAgB;EAChB,eAAe;AACjB;AACA;EACE,yBAAyB;EACzB,cAAc;AAChB;AACA;EACE,cAAc;AAChB;AACA;EACE,4BAA4B;AAC9B;AACA;EACE,qBAAqB;EACrB,WAAW;EACX,YAAY;EACZ,eAAe;AACjB;AACA;EACE,YAAY;EACZ,cAAc;EACd,WAAW;EACX,YAAY;EACZ,WAAW;EACX,kBAAkB;EAClB,yBAAyB;EACzB,qDAAqD;EACrD,6CAA6C;AAC/C;AACA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,yBAAyB;EAC3B;AACF;;AAEA;EACE,qBAAqB;EACrB,iBAAiB;EACjB,uBAAuB;AACzB;;AAEA;EACE,6BAA6B;AAC/B;AACA;EACE,6BAA6B;AAC/B;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,mBAAmB;AACrB;;AAEA,yCAAyC;AACzC;EACE,yBAAyB;AAC3B;AACA;EACE,UAAU;EACV,kBAAkB;EAClB,QAAQ;EACR,2BAA2B;EAC3B,sBAAsB;EACtB,0BAA0B;EAC1B,kCAAkC;EAClC,wBAAwB;EACxB,eAAe;AACjB;AACA;EACE,mCAAmC;AACrC;AACA;EACE,UAAU;AACZ;;AAEA;EACE,kBAAkB;EAClB,iBAAiB;EACjB,gCAAgC;EAChC,yCAAyC;EACzC,0BAA0B;AAC5B;AACA;EACE,kBAAkB;EAClB,UAAU;EACV,QAAQ;EACR,cAAc;EACd,YAAY;EACZ,iBAAiB;EACjB,iBAAiB;EACjB,0BAA0B;AAC5B;AACA;EACE,YAAY;AACd;;AAEA;EACE,gBAAgB;EAChB,yBAAyB;AAC3B;AACA;EACE,yBAAyB;EACzB,qBAAqB;EACrB,cAAc;EACd,gBAAgB;EAChB,gBAAgB;EAChB,WAAW;EACX,uBAAuB;AACzB;AACA;EACE,yBAAyB;EACzB,2BAA2B;EAC3B,WAAW;AACb;AACA;EACE,wBAAwB;AAC1B;;AAEA;EACE,YAAY;AACd;AACA;EACE,UAAU;EACV,cAAc;AAChB;AACA;EACE,kBAAkB;AACpB;AAFA;EACE,kBAAkB;AACpB;;AAEA;EACE,aAAa;AACf;AACA;EACE,+BAA+B;EAC/B,kBAAkB;EAClB,YAAY;EACZ,aAAa;EACb,YAAY;AACd;AACA;EACE,oBAAoB;AACtB;AACA;EACE,aAAa;AACf;AACA;EACE,kBAAkB;AACpB;;AAEA;EACE,oBAAoB;EACpB,mBAAmB;AACrB;AACA;EACE,cAAc;EACd,qBAAqB;EACrB,sBAAsB;EACtB,qBAAqB;EACrB,cAAc;EACd,0BAA0B;EAC1B,sBAAsB;AACxB;AACA;EACE,+BAA+B;AACjC;AACA;EACE,gBAAgB;AAClB;AACA;EACE,kBAAkB;EAClB,QAAQ;EACR,MAAM;EACN,eAAe;AACjB;;AAEA;EACE,2BAA2B;AAC7B;AACA;EACE,8BAA8B;EAC9B,gBAAgB;AAClB;AACA;EACE,0BAA0B;AAC5B;AACA;EACE,wBAAwB;AAC1B;;AAEA;EACE,0BAA0B;AAC5B;AACA;EACE,+BAA+B;EAC/B,iBAAiB;AACnB;AACA;EACE,2BAA2B;AAC7B;AACA;EACE,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;EACzB,kBAAkB;EAClB,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,+FAA+F;AACjG;;AAEA;EACE,8FAA8F;AAChG;;AAEA;EACE,kBAAkB;AACpB;;AAEA;;;EAGE,cAAc;EACd,oBAAoB;EACpB,eAAe;EACf,iBAAiB;AACnB;;AAEA;;EAEE,gBAAgB;EAChB,YAAY;AACd;;AAEA;EACE,oBAAoB;EACpB,8BAA8B;EAC9B,wCAAgC;UAAhC,gCAAgC;EAChC,2BAA2B;EAC3B,qBAAqB;EACrB,6BAA6B;EAC7B,qBAAqB;EACrB,8BAA8B;AAChC", "file": "tom-select.css", "sourcesContent": ["/**\n * tom-select.css (v//@@version)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n.ts-control {\n  border: 1px solid #d0d0d0;\n  padding: 8px 8px;\n  width: 100%;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n  box-sizing: border-box;\n  box-shadow: none;\n  border-radius: 3px;\n  display: flex;\n  flex-wrap: wrap;\n}\n.ts-wrapper.multi.has-items .ts-control {\n  padding: calc(8px - 2px - 0) 8px calc(8px - 2px - 3px - 0);\n}\n.full .ts-control {\n  background-color: #fff;\n}\n.disabled .ts-control, .disabled .ts-control * {\n  cursor: default !important;\n}\n.focus .ts-control {\n  box-shadow: none;\n}\n.ts-control > * {\n  vertical-align: baseline;\n  display: inline-block;\n}\n.ts-wrapper.multi .ts-control > div {\n  cursor: pointer;\n  margin: 0 3px 3px 0;\n  padding: 2px 6px;\n  background: #f2f2f2;\n  color: #303030;\n  border: 0 solid #d0d0d0;\n}\n.ts-wrapper.multi .ts-control > div.active {\n  background: #e8e8e8;\n  color: #303030;\n  border: 0 solid #cacaca;\n}\n.ts-wrapper.multi.disabled .ts-control > div, .ts-wrapper.multi.disabled .ts-control > div.active {\n  color: rgb(124.5, 124.5, 124.5);\n  background: white;\n  border: 0 solid white;\n}\n.ts-control > input {\n  flex: 1 1 auto;\n  min-width: 7rem;\n  display: inline-block !important;\n  padding: 0 !important;\n  min-height: 0 !important;\n  max-height: none !important;\n  max-width: 100% !important;\n  margin: 0 !important;\n  text-indent: 0 !important;\n  border: 0 none !important;\n  background: none !important;\n  line-height: inherit !important;\n  user-select: auto !important;\n  box-shadow: none !important;\n}\n.ts-control > input::-ms-clear {\n  display: none;\n}\n.ts-control > input:focus {\n  outline: none !important;\n}\n.has-items .ts-control > input {\n  margin: 0 4px !important;\n}\n.ts-control.rtl {\n  text-align: right;\n}\n.ts-control.rtl.single .ts-control:after {\n  left: 15px;\n  right: auto;\n}\n.ts-control.rtl .ts-control > input {\n  margin: 0 4px 0 -2px !important;\n}\n.disabled .ts-control {\n  opacity: 0.5;\n  background-color: #fafafa;\n}\n.input-hidden .ts-control > input {\n  opacity: 0;\n  position: absolute;\n  left: -10000px;\n}\n\n.ts-dropdown {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  width: 100%;\n  z-index: 10;\n  border: 1px solid #d0d0d0;\n  background: #fff;\n  margin: 0.25rem 0 0;\n  border-top: 0 none;\n  box-sizing: border-box;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border-radius: 0 0 3px 3px;\n}\n.ts-dropdown [data-selectable] {\n  cursor: pointer;\n  overflow: hidden;\n}\n.ts-dropdown [data-selectable] .highlight {\n  background: rgba(125, 168, 208, 0.2);\n  border-radius: 1px;\n}\n.ts-dropdown .option,\n.ts-dropdown .optgroup-header,\n.ts-dropdown .no-results,\n.ts-dropdown .create {\n  padding: 5px 8px;\n}\n.ts-dropdown .option, .ts-dropdown [data-disabled], .ts-dropdown [data-disabled] [data-selectable].option {\n  cursor: inherit;\n  opacity: 0.5;\n}\n.ts-dropdown [data-selectable].option {\n  opacity: 1;\n  cursor: pointer;\n}\n.ts-dropdown .optgroup:first-child .optgroup-header {\n  border-top: 0 none;\n}\n.ts-dropdown .optgroup-header {\n  color: #303030;\n  background: #fff;\n  cursor: default;\n}\n.ts-dropdown .active {\n  background-color: #f5fafd;\n  color: #495c68;\n}\n.ts-dropdown .active.create {\n  color: #495c68;\n}\n.ts-dropdown .create {\n  color: rgba(48, 48, 48, 0.5);\n}\n.ts-dropdown .spinner {\n  display: inline-block;\n  width: 30px;\n  height: 30px;\n  margin: 5px 8px;\n}\n.ts-dropdown .spinner::after {\n  content: \" \";\n  display: block;\n  width: 24px;\n  height: 24px;\n  margin: 3px;\n  border-radius: 50%;\n  border: 5px solid #d0d0d0;\n  border-color: #d0d0d0 transparent #d0d0d0 transparent;\n  animation: lds-dual-ring 1.2s linear infinite;\n}\n@keyframes lds-dual-ring {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.ts-dropdown-content {\n  overflow: hidden auto;\n  max-height: 200px;\n  scroll-behavior: smooth;\n}\n\n.ts-wrapper.plugin-drag_drop .ts-dragging {\n  color: transparent !important;\n}\n.ts-wrapper.plugin-drag_drop .ts-dragging > * {\n  visibility: hidden !important;\n}\n\n.plugin-checkbox_options:not(.rtl) .option input {\n  margin-right: 0.5rem;\n}\n\n.plugin-checkbox_options.rtl .option input {\n  margin-left: 0.5rem;\n}\n\n/* stylelint-disable function-name-case */\n.plugin-clear_button {\n  --ts-pr-clear-button: 1em;\n}\n.plugin-clear_button .clear-button {\n  opacity: 0;\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  right: calc(8px - 6px);\n  margin-right: 0 !important;\n  background: transparent !important;\n  transition: opacity 0.5s;\n  cursor: pointer;\n}\n.plugin-clear_button.form-select .clear-button, .plugin-clear_button.single .clear-button {\n  right: max(var(--ts-pr-caret), 8px);\n}\n.plugin-clear_button.focus.has-items .clear-button, .plugin-clear_button:not(.disabled):hover.has-items .clear-button {\n  opacity: 1;\n}\n\n.ts-wrapper .dropdown-header {\n  position: relative;\n  padding: 10px 8px;\n  border-bottom: 1px solid #d0d0d0;\n  background: color-mix(#fff, #d0d0d0, 85%);\n  border-radius: 3px 3px 0 0;\n}\n.ts-wrapper .dropdown-header-close {\n  position: absolute;\n  right: 8px;\n  top: 50%;\n  color: #303030;\n  opacity: 0.4;\n  margin-top: -12px;\n  line-height: 20px;\n  font-size: 20px !important;\n}\n.ts-wrapper .dropdown-header-close:hover {\n  color: black;\n}\n\n.plugin-dropdown_input.focus.dropdown-active .ts-control {\n  box-shadow: none;\n  border: 1px solid #d0d0d0;\n}\n.plugin-dropdown_input .dropdown-input {\n  border: 1px solid #d0d0d0;\n  border-width: 0 0 1px;\n  display: block;\n  padding: 8px 8px;\n  box-shadow: none;\n  width: 100%;\n  background: transparent;\n}\n.plugin-dropdown_input .items-placeholder {\n  border: 0 none !important;\n  box-shadow: none !important;\n  width: 100%;\n}\n.plugin-dropdown_input.has-items .items-placeholder, .plugin-dropdown_input.dropdown-active .items-placeholder {\n  display: none !important;\n}\n\n.ts-wrapper.plugin-input_autogrow.has-items .ts-control > input {\n  min-width: 0;\n}\n.ts-wrapper.plugin-input_autogrow.has-items.focus .ts-control > input {\n  flex: none;\n  min-width: 4px;\n}\n.ts-wrapper.plugin-input_autogrow.has-items.focus .ts-control > input::placeholder {\n  color: transparent;\n}\n\n.ts-dropdown.plugin-optgroup_columns .ts-dropdown-content {\n  display: flex;\n}\n.ts-dropdown.plugin-optgroup_columns .optgroup {\n  border-right: 1px solid #f2f2f2;\n  border-top: 0 none;\n  flex-grow: 1;\n  flex-basis: 0;\n  min-width: 0;\n}\n.ts-dropdown.plugin-optgroup_columns .optgroup:last-child {\n  border-right: 0 none;\n}\n.ts-dropdown.plugin-optgroup_columns .optgroup::before {\n  display: none;\n}\n.ts-dropdown.plugin-optgroup_columns .optgroup-header {\n  border-top: 0 none;\n}\n\n.ts-wrapper.plugin-remove_button .item {\n  display: inline-flex;\n  align-items: center;\n}\n.ts-wrapper.plugin-remove_button .item .remove {\n  color: inherit;\n  text-decoration: none;\n  vertical-align: middle;\n  display: inline-block;\n  padding: 0 6px;\n  border-radius: 0 2px 2px 0;\n  box-sizing: border-box;\n}\n.ts-wrapper.plugin-remove_button .item .remove:hover {\n  background: rgba(0, 0, 0, 0.05);\n}\n.ts-wrapper.plugin-remove_button.disabled .item .remove:hover {\n  background: none;\n}\n.ts-wrapper.plugin-remove_button .remove-single {\n  position: absolute;\n  right: 0;\n  top: 0;\n  font-size: 23px;\n}\n\n.ts-wrapper.plugin-remove_button:not(.rtl) .item {\n  padding-right: 0 !important;\n}\n.ts-wrapper.plugin-remove_button:not(.rtl) .item .remove {\n  border-left: 1px solid #d0d0d0;\n  margin-left: 6px;\n}\n.ts-wrapper.plugin-remove_button:not(.rtl) .item.active .remove {\n  border-left-color: #cacaca;\n}\n.ts-wrapper.plugin-remove_button:not(.rtl).disabled .item .remove {\n  border-left-color: white;\n}\n\n.ts-wrapper.plugin-remove_button.rtl .item {\n  padding-left: 0 !important;\n}\n.ts-wrapper.plugin-remove_button.rtl .item .remove {\n  border-right: 1px solid #d0d0d0;\n  margin-right: 6px;\n}\n.ts-wrapper.plugin-remove_button.rtl .item.active .remove {\n  border-right-color: #cacaca;\n}\n.ts-wrapper.plugin-remove_button.rtl.disabled .item .remove {\n  border-right-color: white;\n}\n\n:root {\n  --ts-pr-clear-button: 0px;\n  --ts-pr-caret: 0px;\n  --ts-pr-min: .75rem;\n}\n\n.ts-wrapper.single .ts-control, .ts-wrapper.single .ts-control input {\n  cursor: pointer;\n}\n\n.ts-control:not(.rtl) {\n  padding-right: max(var(--ts-pr-min), var(--ts-pr-clear-button) + var(--ts-pr-caret)) !important;\n}\n\n.ts-control.rtl {\n  padding-left: max(var(--ts-pr-min), var(--ts-pr-clear-button) + var(--ts-pr-caret)) !important;\n}\n\n.ts-wrapper {\n  position: relative;\n}\n\n.ts-dropdown,\n.ts-control,\n.ts-control input {\n  color: #303030;\n  font-family: inherit;\n  font-size: 13px;\n  line-height: 18px;\n}\n\n.ts-control,\n.ts-wrapper.single.input-active .ts-control {\n  background: #fff;\n  cursor: text;\n}\n\n.ts-hidden-accessible {\n  border: 0 !important;\n  clip: rect(0 0 0 0) !important;\n  clip-path: inset(50%) !important;\n  overflow: hidden !important;\n  padding: 0 !important;\n  position: absolute !important;\n  width: 1px !important;\n  white-space: nowrap !important;\n}"]}