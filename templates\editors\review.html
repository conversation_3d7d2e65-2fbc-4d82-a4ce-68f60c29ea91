{% extends "basic.html" %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          {% if assignment.status == 'pending' %}
            Проверка статьи
          {% else %}
            Просмотр проверки
          {% endif %}
        </h2>
        <div class="page-subtitle">
          Статья: {{ submission.title }}
        </div>
      </div>
      <div class="col-auto ms-auto d-print-none">
        <div class="btn-list">
          <a href="{{ url_for('editor_assignments') }}" class="btn btn-secondary">
            <i class="ti ti-arrow-left"></i>
            Назад к назначениям
          </a>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="row row-deck row-cards">
      <div class="col-md-8">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Информация о статье</h3>
          </div>
          <div class="card-body">
            <div class="datagrid">
              <div class="datagrid-item">
                <div class="datagrid-title">Название</div>
                <div class="datagrid-content">{{ submission.title }}</div>
              </div>
              {% if submission.abstract %}
              <div class="datagrid-item">
                <div class="datagrid-title">Аннотация</div>
                <div class="datagrid-content">{{ submission.abstract }}</div>
              </div>
              {% endif %}
              {% if submission.keywords %}
              <div class="datagrid-item">
                <div class="datagrid-title">Ключевые слова</div>
                <div class="datagrid-content">
                  {% for keyword in submission.keywords %}
                  <span class="badge bg-blue-lt me-1">{{ keyword }}</span>
                  {% endfor %}
                </div>
              </div>
              {% endif %}
              {% if submission.file_anonymized %}
              <div class="datagrid-item">
                <div class="datagrid-title">Анонимный файл</div>
                <div class="datagrid-content">
                  <a href="{{ submission.file_anonymized }}" class="btn btn-primary" target="_blank">
                    <i class="ti ti-download"></i>
                    Скачать файл для проверки
                  </a>
                </div>
              </div>
              {% endif %}
            </div>
          </div>
        </div>
        
        {% if assignment.status == 'pending' %}
        <form method="POST" action="{{ url_for('review_assignment', assignment_id=assignment.id) }}" enctype="multipart/form-data">
          <div class="card mt-3">
            <div class="card-header">
              <h3 class="card-title">Ваша проверка</h3>
            </div>
            <div class="card-body">
              <div class="mb-3">
                <label class="form-label">Решение</label>
                <div class="form-selectgroup">
                  <label class="form-selectgroup-item">
                    <input type="radio" name="status" value="reviewed" class="form-selectgroup-input" checked>
                    <span class="form-selectgroup-label">
                      <i class="ti ti-check text-success me-1"></i>
                      Одобрить
                    </span>
                  </label>
                  <label class="form-selectgroup-item">
                    <input type="radio" name="status" value="rejected" class="form-selectgroup-input">
                    <span class="form-selectgroup-label">
                      <i class="ti ti-x text-danger me-1"></i>
                      Отклонить
                    </span>
                  </label>
                </div>
              </div>
              
              <div class="mb-3">
                <label class="form-label">Комментарий</label>
                <textarea class="form-control" name="editor_comment" rows="6" placeholder="Ваши замечания и рекомендации по статье...">{{ assignment.editor_comment or '' }}</textarea>
                <small class="form-hint">Опишите ваше мнение о статье, замечания и рекомендации для автора</small>
              </div>
              
              <div class="mb-3">
                <label class="form-label">Прикрепить файл</label>
                <input type="file" class="form-control" name="editor_file" accept=".pdf,.doc,.docx,.txt">
                <small class="form-hint">Вы можете прикрепить файл с подробными замечаниями (PDF, DOC, DOCX, TXT)</small>
                {% if assignment.editor_file %}
                <div class="mt-2">
                  <span class="text-success">
                    <i class="ti ti-file"></i>
                    Текущий файл: <a href="{{ assignment.editor_file }}" target="_blank">Скачать</a>
                  </span>
                </div>
                {% endif %}
              </div>
            </div>
            
            <div class="card-footer text-end">
              <div class="d-flex">
                <a href="{{ url_for('editor_assignments') }}" class="btn btn-link">Отмена</a>
                <button type="submit" class="btn btn-primary ms-auto">
                  <i class="ti ti-check"></i>
                  Сохранить проверку
                </button>
              </div>
            </div>
          </div>
        </form>
        {% else %}
        <div class="card mt-3">
          <div class="card-header">
            <h3 class="card-title">Результат проверки</h3>
          </div>
          <div class="card-body">
            <div class="datagrid">
              <div class="datagrid-item">
                <div class="datagrid-title">Решение</div>
                <div class="datagrid-content">
                  {% if assignment.status == 'reviewed' %}
                  <span class="badge bg-success-lt">Одобрено</span>
                  {% elif assignment.status == 'rejected' %}
                  <span class="badge bg-danger-lt">Отклонено</span>
                  {% else %}
                  <span class="badge bg-secondary-lt">{{ assignment.status }}</span>
                  {% endif %}
                </div>
              </div>
              {% if assignment.reviewed_at %}
              <div class="datagrid-item">
                <div class="datagrid-title">Дата проверки</div>
                <div class="datagrid-content">{{ moment(assignment.reviewed_at).format('DD.MM.YYYY HH:mm') }}</div>
              </div>
              {% endif %}
              {% if assignment.editor_comment %}
              <div class="datagrid-item">
                <div class="datagrid-title">Комментарий</div>
                <div class="datagrid-content">{{ assignment.editor_comment }}</div>
              </div>
              {% endif %}
              {% if assignment.editor_file %}
              <div class="datagrid-item">
                <div class="datagrid-title">Прикрепленный файл</div>
                <div class="datagrid-content">
                  <a href="{{ assignment.editor_file }}" class="btn btn-outline-primary btn-sm" target="_blank">
                    <i class="ti ti-download"></i>
                    Скачать файл
                  </a>
                </div>
              </div>
              {% endif %}
            </div>
          </div>
        </div>
        {% endif %}
      </div>
      
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Детали назначения</h3>
          </div>
          <div class="card-body">
            <div class="datagrid">
              <div class="datagrid-item">
                <div class="datagrid-title">ID назначения</div>
                <div class="datagrid-content">{{ assignment.id }}</div>
              </div>
              <div class="datagrid-item">
                <div class="datagrid-title">Дата назначения</div>
                <div class="datagrid-content">{{ assignment.assigned_at | timestamp_to_date }}</div>
              </div>
              <div class="datagrid-item">
                <div class="datagrid-title">Статус</div>
                <div class="datagrid-content">
                  {% if assignment.status == 'pending' %}
                  <span class="badge bg-warning-lt">Ожидает проверки</span>
                  {% elif assignment.status == 'reviewed' %}
                  <span class="badge bg-success-lt">Проверено</span>
                  {% elif assignment.status == 'rejected' %}
                  <span class="badge bg-danger-lt">Отклонено</span>
                  {% else %}
                  <span class="badge bg-secondary-lt">{{ assignment.status }}</span>
                  {% endif %}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {% if author %}
        <div class="card mt-3">
          <div class="card-header">
            <h3 class="card-title">Информация об авторе</h3>
          </div>
          <div class="card-body">
            <div class="datagrid">
              <div class="datagrid-item">
                <div class="datagrid-title">Имя</div>
                <div class="datagrid-content">{{ author.name }}</div>
              </div>
              {% if author.organization %}
              <div class="datagrid-item">
                <div class="datagrid-title">Организация</div>
                <div class="datagrid-content">{{ author.organization }}</div>
              </div>
              {% endif %}
              {% if author.orcid %}
              <div class="datagrid-item">
                <div class="datagrid-title">ORCID</div>
                <div class="datagrid-content">
                  <a href="https://orcid.org/{{ author.orcid }}" target="_blank">{{ author.orcid }}</a>
                </div>
              </div>
              {% endif %}
            </div>
          </div>
        </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<script>
function moment(timestamp) {
  if (!timestamp) return { format: () => '-' };
  const date = new Date(timestamp * 1000);
  return {
    format: (format) => {
      if (format === 'DD.MM.YYYY HH:mm') {
        return date.toLocaleString('ru-RU', {
          day: '2-digit',
          month: '2-digit', 
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
      return date.toLocaleDateString('ru-RU');
    }
  };
}
</script>
{% endblock %}
