{% extends 'basic.html' %}


{% block content %}

<div class="page-header d-print-none mb-4">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="page-title">{% if author.id == 0 %}Создание автора{% else %}Редактирование автора{% endif %}</h2>
            <a href="/users/authors" class="">
                <i class="ti ti-arrow-left"></i>
                Назад
            </a>
        </div>
        <div class="col-auto ms-auto">
            <div class="btn-list">
                <button id="save-author-btn-top" type="button" class="btn btn-primary d-none d-sm-inline-block">
                    <i class="ti ti-check icon icon-1"></i>
                    Сохранить
                </button>
            </div>
        </div>
    </div>
</div>

<form id="main-author-form" method="post" enctype="multipart/form-data">
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">ID</label>
                    <input type="text" class="form-control" value="{{ author.id }}" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">Имя</label>
                    <input type="text" class="form-control" name="name" value="{{ author.name or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Организация</label>
                    <input type="text" class="form-control" name="organization" value="{{ author.organization or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Email</label>
                    <input type="email" class="form-control" name="email" value="{{ author.email or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Должность</label>
                    <input type="text" class="form-control" name="position" value="{{ author.position or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Страна</label>
                    <input type="text" class="form-control" name="address_country" value="{{ author.address_country or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Город</label>
                    <input type="text" class="form-control" name="address_city" value="{{ author.address_city or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Улица</label>
                    <input type="text" class="form-control" name="address_street" value="{{ author.address_street or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Почтовый индекс</label>
                    <input type="text" class="form-control" name="address_zip" value="{{ author.address_zip or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Телефон</label>
                    <input type="text" class="form-control" name="phone" value="{{ author.phone or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">ORCID</label>
                    <input type="text" class="form-control" name="orcid" value="{{ author.orcid or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Отдел</label>
                    <input type="text" class="form-control" name="department" value="{{ author.department or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Дата создания</label>
                    <input type="datetime-local" class="form-control" name="created_at" value="{{ author.created_at | date_to_form_full }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Дата обновления</label>
                    <input type="datetime-local" class="form-control" name="updated_at" value="{{ author.updated_at | date_to_form_full }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Пользователь</label>
                    <select class="form-select" name="user_id">
                        <option value="">Не выбран</option>
                        {% for user in users %}
                        <option value="{{ user.id }}" {% if author.user_id == user.id %}selected{% endif %}>{{ user.name }} {{ user.second_name }} ({{ user.email }})</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="d-flex justify-content-end">
                    <button id="save-author-btn" type="submit" class="btn btn-primary">Сохранить</button>
                </div>
            </div>
        </div>
    </div>
</div>
</form>

{% endblock %}

{% block scripts %}
<script>
document.getElementById('save-author-btn-top').addEventListener('click', function() {
    document.getElementById('save-author-btn').click();
});
</script>
{% endblock %}