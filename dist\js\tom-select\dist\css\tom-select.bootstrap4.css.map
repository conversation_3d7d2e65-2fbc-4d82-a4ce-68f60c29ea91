{"version": 3, "sources": ["tom-select.bootstrap4.css"], "names": [], "mappings": "AAAA;;EAEE;AACF;;;;;;;;;;;;;EAaE;AACF;EACE,yBAAyB;EACzB,yBAAyB;EACzB,WAAW;EACX,gBAAgB;EAChB,kBAAkB;EAClB,UAAU;EACV,sBAAsB;EACtB,gBAAgB;EAChB,sBAAsB;EACtB,aAAa;EACb,eAAe;AACjB;AACA;EACE,wEAAwE;AAC1E;AACA;EACE,sBAAsB;AACxB;AACA;EACE,0BAA0B;AAC5B;AACA;EACE,gBAAgB;AAClB;AACA;EACE,wBAAwB;EACxB,qBAAqB;AACvB;AACA;EACE,eAAe;EACf,mBAAmB;EACnB,gBAAgB;EAChB,mBAAmB;EACnB,cAAc;EACd,uBAAuB;AACzB;AACA;EACE,mBAAmB;EACnB,WAAW;EACX,gCAAgC;AAClC;AACA;EACE,+BAA+B;EAC/B,iBAAiB;EACjB,qBAAqB;AACvB;AACA;EACE,cAAc;EACd,eAAe;EACf,gCAAgC;EAChC,qBAAqB;EACrB,wBAAwB;EACxB,2BAA2B;EAC3B,0BAA0B;EAC1B,oBAAoB;EACpB,yBAAyB;EACzB,yBAAyB;EACzB,2BAA2B;EAC3B,+BAA+B;EAC/B,oCAA4B;KAA5B,iCAA4B;MAA5B,gCAA4B;UAA5B,4BAA4B;EAC5B,2BAA2B;AAC7B;AACA;EACE,aAAa;AACf;AACA;EACE,wBAAwB;AAC1B;AACA;EACE,wBAAwB;AAC1B;AACA;EACE,iBAAiB;AACnB;AACA;EACE,yBAAyB;EACzB,WAAW;AACb;AACA;EACE,+BAA+B;AACjC;AACA;EACE,YAAY;EACZ,yBAAyB;AAC3B;AACA;EACE,UAAU;EACV,kBAAkB;EAClB,cAAc;AAChB;;AAEA;EACE,kBAAkB;EAClB,SAAS;EACT,OAAO;EACP,WAAW;EACX,WAAW;EACX,yBAAyB;EACzB,gBAAgB;EAChB,mBAAmB;EACnB,kBAAkB;EAClB,sBAAsB;EACtB,wCAAwC;EACxC,kCAAkC;AACpC;AACA;EACE,eAAe;EACf,gBAAgB;AAClB;AACA;EACE,mCAAmC;EACnC,kBAAkB;AACpB;AACA;;;;EAIE,oBAAoB;AACtB;AACA;EACE,eAAe;EACf,YAAY;AACd;AACA;EACE,UAAU;EACV,eAAe;AACjB;AACA;EACE,kBAAkB;AACpB;AACA;EACE,cAAc;EACd,gBAAgB;EAChB,eAAe;AACjB;AACA;EACE,yBAAyB;EACzB,+CAA+C;AACjD;AACA;EACE,+CAA+C;AACjD;AACA;EACE,4BAA4B;AAC9B;AACA;EACE,qBAAqB;EACrB,WAAW;EACX,YAAY;EACZ,mBAAmB;AACrB;AACA;EACE,YAAY;EACZ,cAAc;EACd,WAAW;EACX,YAAY;EACZ,WAAW;EACX,kBAAkB;EAClB,yBAAyB;EACzB,qDAAqD;EACrD,6CAA6C;AAC/C;AACA;EACE;IACE,uBAAuB;EACzB;EACA;IACE,yBAAyB;EAC3B;AACF;;AAEA;EACE,qBAAqB;EACrB,iBAAiB;EACjB,uBAAuB;AACzB;;AAEA;EACE,6BAA6B;AAC/B;AACA;EACE,6BAA6B;AAC/B;;AAEA;EACE,oBAAoB;AACtB;;AAEA;EACE,mBAAmB;AACrB;;AAEA,yCAAyC;AACzC;EACE,yBAAyB;AAC3B;AACA;EACE,UAAU;EACV,kBAAkB;EAClB,QAAQ;EACR,2BAA2B;EAC3B,0BAA0B;EAC1B,0BAA0B;EAC1B,kCAAkC;EAClC,wBAAwB;EACxB,eAAe;AACjB;AACA;EACE,uCAAuC;AACzC;AACA;EACE,UAAU;AACZ;;AAEA;EACE,kBAAkB;EAClB,oBAAoB;EACpB,gCAAgC;EAChC,yCAAyC;EACzC,kCAAkC;AACpC;AACA;EACE,kBAAkB;EAClB,cAAc;EACd,QAAQ;EACR,cAAc;EACd,YAAY;EACZ,iBAAiB;EACjB,iBAAiB;EACjB,0BAA0B;AAC5B;AACA;EACE,YAAY;AACd;;AAEA;EACE,gBAAgB;EAChB,yBAAyB;EACzB,gDAAgD;AAClD;AACA;EACE,yBAAyB;EACzB,qBAAqB;EACrB,cAAc;EACd,yBAAyB;EACzB,gBAAgB;EAChB,WAAW;EACX,uBAAuB;AACzB;AACA;EACE,kCAAkC;EAClC,UAAU;EACV,gDAAgD;AAClD;AACA;EACE,yBAAyB;EACzB,2BAA2B;EAC3B,WAAW;AACb;AACA;EACE,wBAAwB;AAC1B;;AAEA;EACE,YAAY;AACd;AACA;EACE,UAAU;EACV,cAAc;AAChB;AACA;EACE,kBAAkB;AACpB;AAFA;EACE,kBAAkB;AACpB;;AAEA;EACE,aAAa;AACf;AACA;EACE,+BAA+B;EAC/B,kBAAkB;EAClB,YAAY;EACZ,aAAa;EACb,YAAY;AACd;AACA;EACE,oBAAoB;AACtB;AACA;EACE,aAAa;AACf;AACA;EACE,kBAAkB;AACpB;;AAEA;EACE,oBAAoB;EACpB,mBAAmB;AACrB;AACA;EACE,cAAc;EACd,qBAAqB;EACrB,sBAAsB;EACtB,qBAAqB;EACrB,cAAc;EACd,0BAA0B;EAC1B,sBAAsB;AACxB;AACA;EACE,+BAA+B;AACjC;AACA;EACE,gBAAgB;AAClB;AACA;EACE,kBAAkB;EAClB,QAAQ;EACR,MAAM;EACN,eAAe;AACjB;;AAEA;EACE,2BAA2B;AAC7B;AACA;EACE,8BAA8B;EAC9B,gBAAgB;AAClB;AACA;EACE,mCAAmC;AACrC;AACA;EACE,wBAAwB;AAC1B;;AAEA;EACE,0BAA0B;AAC5B;AACA;EACE,+BAA+B;EAC/B,iBAAiB;AACnB;AACA;EACE,oCAAoC;AACtC;AACA;EACE,yBAAyB;AAC3B;;AAEA;EACE,yBAAyB;EACzB,kBAAkB;EAClB,mBAAmB;AACrB;;AAEA;EACE,eAAe;AACjB;;AAEA;EACE,+FAA+F;AACjG;;AAEA;EACE,8FAA8F;AAChG;;AAEA;EACE,kBAAkB;AACpB;;AAEA;;;EAGE,cAAc;EACd,oBAAoB;EACpB,kBAAkB;EAClB,gBAAgB;AAClB;;AAEA;;EAEE,gBAAgB;EAChB,YAAY;AACd;;AAEA;EACE,oBAAoB;EACpB,8BAA8B;EAC9B,wCAAgC;UAAhC,gCAAgC;EAChC,2BAA2B;EAC3B,qBAAqB;EACrB,6BAA6B;EAC7B,qBAAqB;EACrB,8BAA8B;AAChC;;AAEA;EACE,mBAAmB;AACrB;AACA;EACE,YAAY;EACZ,cAAc;EACd,kBAAkB;EAClB,QAAQ;EACR,gBAAgB;EAChB,QAAQ;EACR,SAAS;EACT,mBAAmB;EACnB,2BAA2B;EAC3B,yDAAyD;AAC3D;AACA;EACE,0BAA0B;AAC5B;AACA;EACE,yBAAyB;AAC3B;AACA;EACE,gBAAgB;EAChB,2BAA2B;EAC3B,yDAAyD;AAC3D;AACA;EACE,YAAY;AACd;;AAEA;;EAEE,qBAAqB;AACvB;;AAEA;;EAEE,YAAY;EACZ,UAAU;EACV,aAAa;EACb,gBAAgB;EAChB,qCAAqC;EACrC,sBAAsB;EACtB,2CAA2C;AAC7C;;AAEA;EACE,mBAAmB;EACnB,gBAAgB;AAClB;AACA;EACE,aAAa;AACf;AACA;EACE,YAAY;EACZ,cAAc;EACd,SAAS;EACT,gBAAgB;EAChB,gBAAgB;EAChB,6BAA6B;EAC7B,qBAAqB;EACrB,sBAAsB;AACxB;AACA;EACE,qBAAqB;AACvB;;AAEA;EACE,cAAc;AAChB;;AAEA;EACE,uCAAuC;EACvC,wEAAwE;EACxE,aAAa;EACb,mBAAmB;AACrB;AACA;EACE;IACE,gBAAgB;EAClB;AACF;AACA;EACE,kCAAkC;EAClC,UAAU;EACV,gDAAgD;AAClD;;AAEA;;EAEE,qBAAqB;AACvB;AACA;;EAEE,+DAA+D;EAC/D,gDAAgD;AAClD;;AAEA;EACE,qBAAqB;AACvB;AACA;EACE,qBAAqB;EACrB,gDAAgD;AAClD;;AAEA;EACE,sCAAsC;EACtC,kBAAkB;EAClB,qBAAqB;EACrB,mBAAmB;AACrB;AACA;EACE,iDAAiD;EACjD,mBAAmB;EACnB,iBAAiB;AACnB;AACA;EACE,qFAAqF;AACvF;AACA;EACE,iCAAiC;EACjC,gCAAgC;AAClC;AACA;EACE,kCAAkC;AACpC;AACA;EACE,oCAAoC;EACpC,qBAAqB;EACrB,kBAAkB;AACpB;;AAEA;EACE,UAAU;EACV,YAAY;EACZ,YAAY;EACZ,gBAAgB;EAChB,gBAAgB;AAClB;;AAEA;EACE,YAAY;AACd;AACA;EACE,yBAAyB;EACzB,4BAA4B;AAC9B;AACA;EACE,0BAA0B;EAC1B,6BAA6B;AAC/B", "file": "tom-select.bootstrap4.css", "sourcesContent": ["/**\n * Tom Select Bootstrap 4\n */\n/**\n * tom-select.css (v//@@version)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n.ts-control {\n  border: 1px solid #ced4da;\n  padding: 0.375rem 0.75rem;\n  width: 100%;\n  overflow: hidden;\n  position: relative;\n  z-index: 1;\n  box-sizing: border-box;\n  box-shadow: none;\n  border-radius: 0.25rem;\n  display: flex;\n  flex-wrap: wrap;\n}\n.ts-wrapper.multi.has-items .ts-control {\n  padding: calc(0.375rem - 1px - 0) 0.75rem calc(0.375rem - 1px - 3px - 0);\n}\n.full .ts-control {\n  background-color: #fff;\n}\n.disabled .ts-control, .disabled .ts-control * {\n  cursor: default !important;\n}\n.focus .ts-control {\n  box-shadow: none;\n}\n.ts-control > * {\n  vertical-align: baseline;\n  display: inline-block;\n}\n.ts-wrapper.multi .ts-control > div {\n  cursor: pointer;\n  margin: 0 3px 3px 0;\n  padding: 1px 5px;\n  background: #efefef;\n  color: #343a40;\n  border: 0 solid #dee2e6;\n}\n.ts-wrapper.multi .ts-control > div.active {\n  background: #007bff;\n  color: #fff;\n  border: 0 solid rgba(0, 0, 0, 0);\n}\n.ts-wrapper.multi.disabled .ts-control > div, .ts-wrapper.multi.disabled .ts-control > div.active {\n  color: rgb(134.5, 134.5, 134.5);\n  background: white;\n  border: 0 solid white;\n}\n.ts-control > input {\n  flex: 1 1 auto;\n  min-width: 7rem;\n  display: inline-block !important;\n  padding: 0 !important;\n  min-height: 0 !important;\n  max-height: none !important;\n  max-width: 100% !important;\n  margin: 0 !important;\n  text-indent: 0 !important;\n  border: 0 none !important;\n  background: none !important;\n  line-height: inherit !important;\n  user-select: auto !important;\n  box-shadow: none !important;\n}\n.ts-control > input::-ms-clear {\n  display: none;\n}\n.ts-control > input:focus {\n  outline: none !important;\n}\n.has-items .ts-control > input {\n  margin: 0 4px !important;\n}\n.ts-control.rtl {\n  text-align: right;\n}\n.ts-control.rtl.single .ts-control:after {\n  left: calc(0.75rem + 5px);\n  right: auto;\n}\n.ts-control.rtl .ts-control > input {\n  margin: 0 4px 0 -2px !important;\n}\n.disabled .ts-control {\n  opacity: 0.5;\n  background-color: #e9ecef;\n}\n.input-hidden .ts-control > input {\n  opacity: 0;\n  position: absolute;\n  left: -10000px;\n}\n\n.ts-dropdown {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  width: 100%;\n  z-index: 10;\n  border: 1px solid #d0d0d0;\n  background: #fff;\n  margin: 0.25rem 0 0;\n  border-top: 0 none;\n  box-sizing: border-box;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\n  border-radius: 0 0 0.25rem 0.25rem;\n}\n.ts-dropdown [data-selectable] {\n  cursor: pointer;\n  overflow: hidden;\n}\n.ts-dropdown [data-selectable] .highlight {\n  background: rgba(255, 237, 40, 0.4);\n  border-radius: 1px;\n}\n.ts-dropdown .option,\n.ts-dropdown .optgroup-header,\n.ts-dropdown .no-results,\n.ts-dropdown .create {\n  padding: 3px 0.75rem;\n}\n.ts-dropdown .option, .ts-dropdown [data-disabled], .ts-dropdown [data-disabled] [data-selectable].option {\n  cursor: inherit;\n  opacity: 0.5;\n}\n.ts-dropdown [data-selectable].option {\n  opacity: 1;\n  cursor: pointer;\n}\n.ts-dropdown .optgroup:first-child .optgroup-header {\n  border-top: 0 none;\n}\n.ts-dropdown .optgroup-header {\n  color: #6c757d;\n  background: #fff;\n  cursor: default;\n}\n.ts-dropdown .active {\n  background-color: #e9ecef;\n  color: rgb(21.6283783784, 24.25, 26.8716216216);\n}\n.ts-dropdown .active.create {\n  color: rgb(21.6283783784, 24.25, 26.8716216216);\n}\n.ts-dropdown .create {\n  color: rgba(52, 58, 64, 0.5);\n}\n.ts-dropdown .spinner {\n  display: inline-block;\n  width: 30px;\n  height: 30px;\n  margin: 3px 0.75rem;\n}\n.ts-dropdown .spinner::after {\n  content: \" \";\n  display: block;\n  width: 24px;\n  height: 24px;\n  margin: 3px;\n  border-radius: 50%;\n  border: 5px solid #d0d0d0;\n  border-color: #d0d0d0 transparent #d0d0d0 transparent;\n  animation: lds-dual-ring 1.2s linear infinite;\n}\n@keyframes lds-dual-ring {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.ts-dropdown-content {\n  overflow: hidden auto;\n  max-height: 200px;\n  scroll-behavior: smooth;\n}\n\n.ts-wrapper.plugin-drag_drop .ts-dragging {\n  color: transparent !important;\n}\n.ts-wrapper.plugin-drag_drop .ts-dragging > * {\n  visibility: hidden !important;\n}\n\n.plugin-checkbox_options:not(.rtl) .option input {\n  margin-right: 0.5rem;\n}\n\n.plugin-checkbox_options.rtl .option input {\n  margin-left: 0.5rem;\n}\n\n/* stylelint-disable function-name-case */\n.plugin-clear_button {\n  --ts-pr-clear-button: 1em;\n}\n.plugin-clear_button .clear-button {\n  opacity: 0;\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  right: calc(0.75rem - 5px);\n  margin-right: 0 !important;\n  background: transparent !important;\n  transition: opacity 0.5s;\n  cursor: pointer;\n}\n.plugin-clear_button.form-select .clear-button, .plugin-clear_button.single .clear-button {\n  right: max(var(--ts-pr-caret), 0.75rem);\n}\n.plugin-clear_button.focus.has-items .clear-button, .plugin-clear_button:not(.disabled):hover.has-items .clear-button {\n  opacity: 1;\n}\n\n.ts-wrapper .dropdown-header {\n  position: relative;\n  padding: 6px 0.75rem;\n  border-bottom: 1px solid #d0d0d0;\n  background: color-mix(#fff, #d0d0d0, 85%);\n  border-radius: 0.25rem 0.25rem 0 0;\n}\n.ts-wrapper .dropdown-header-close {\n  position: absolute;\n  right: 0.75rem;\n  top: 50%;\n  color: #343a40;\n  opacity: 0.4;\n  margin-top: -12px;\n  line-height: 20px;\n  font-size: 20px !important;\n}\n.ts-wrapper .dropdown-header-close:hover {\n  color: black;\n}\n\n.plugin-dropdown_input.focus.dropdown-active .ts-control {\n  box-shadow: none;\n  border: 1px solid #ced4da;\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);\n}\n.plugin-dropdown_input .dropdown-input {\n  border: 1px solid #d0d0d0;\n  border-width: 0 0 1px;\n  display: block;\n  padding: 0.375rem 0.75rem;\n  box-shadow: none;\n  width: 100%;\n  background: transparent;\n}\n.plugin-dropdown_input.focus .ts-dropdown .dropdown-input {\n  border-color: rgb(127.5, 189, 255);\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n.plugin-dropdown_input .items-placeholder {\n  border: 0 none !important;\n  box-shadow: none !important;\n  width: 100%;\n}\n.plugin-dropdown_input.has-items .items-placeholder, .plugin-dropdown_input.dropdown-active .items-placeholder {\n  display: none !important;\n}\n\n.ts-wrapper.plugin-input_autogrow.has-items .ts-control > input {\n  min-width: 0;\n}\n.ts-wrapper.plugin-input_autogrow.has-items.focus .ts-control > input {\n  flex: none;\n  min-width: 4px;\n}\n.ts-wrapper.plugin-input_autogrow.has-items.focus .ts-control > input::placeholder {\n  color: transparent;\n}\n\n.ts-dropdown.plugin-optgroup_columns .ts-dropdown-content {\n  display: flex;\n}\n.ts-dropdown.plugin-optgroup_columns .optgroup {\n  border-right: 1px solid #f2f2f2;\n  border-top: 0 none;\n  flex-grow: 1;\n  flex-basis: 0;\n  min-width: 0;\n}\n.ts-dropdown.plugin-optgroup_columns .optgroup:last-child {\n  border-right: 0 none;\n}\n.ts-dropdown.plugin-optgroup_columns .optgroup::before {\n  display: none;\n}\n.ts-dropdown.plugin-optgroup_columns .optgroup-header {\n  border-top: 0 none;\n}\n\n.ts-wrapper.plugin-remove_button .item {\n  display: inline-flex;\n  align-items: center;\n}\n.ts-wrapper.plugin-remove_button .item .remove {\n  color: inherit;\n  text-decoration: none;\n  vertical-align: middle;\n  display: inline-block;\n  padding: 0 5px;\n  border-radius: 0 2px 2px 0;\n  box-sizing: border-box;\n}\n.ts-wrapper.plugin-remove_button .item .remove:hover {\n  background: rgba(0, 0, 0, 0.05);\n}\n.ts-wrapper.plugin-remove_button.disabled .item .remove:hover {\n  background: none;\n}\n.ts-wrapper.plugin-remove_button .remove-single {\n  position: absolute;\n  right: 0;\n  top: 0;\n  font-size: 23px;\n}\n\n.ts-wrapper.plugin-remove_button:not(.rtl) .item {\n  padding-right: 0 !important;\n}\n.ts-wrapper.plugin-remove_button:not(.rtl) .item .remove {\n  border-left: 1px solid #dee2e6;\n  margin-left: 5px;\n}\n.ts-wrapper.plugin-remove_button:not(.rtl) .item.active .remove {\n  border-left-color: rgba(0, 0, 0, 0);\n}\n.ts-wrapper.plugin-remove_button:not(.rtl).disabled .item .remove {\n  border-left-color: white;\n}\n\n.ts-wrapper.plugin-remove_button.rtl .item {\n  padding-left: 0 !important;\n}\n.ts-wrapper.plugin-remove_button.rtl .item .remove {\n  border-right: 1px solid #dee2e6;\n  margin-right: 5px;\n}\n.ts-wrapper.plugin-remove_button.rtl .item.active .remove {\n  border-right-color: rgba(0, 0, 0, 0);\n}\n.ts-wrapper.plugin-remove_button.rtl.disabled .item .remove {\n  border-right-color: white;\n}\n\n:root {\n  --ts-pr-clear-button: 0px;\n  --ts-pr-caret: 0px;\n  --ts-pr-min: .75rem;\n}\n\n.ts-wrapper.single .ts-control, .ts-wrapper.single .ts-control input {\n  cursor: pointer;\n}\n\n.ts-control:not(.rtl) {\n  padding-right: max(var(--ts-pr-min), var(--ts-pr-clear-button) + var(--ts-pr-caret)) !important;\n}\n\n.ts-control.rtl {\n  padding-left: max(var(--ts-pr-min), var(--ts-pr-clear-button) + var(--ts-pr-caret)) !important;\n}\n\n.ts-wrapper {\n  position: relative;\n}\n\n.ts-dropdown,\n.ts-control,\n.ts-control input {\n  color: #343a40;\n  font-family: inherit;\n  font-size: inherit;\n  line-height: 1.5;\n}\n\n.ts-control,\n.ts-wrapper.single.input-active .ts-control {\n  background: #fff;\n  cursor: text;\n}\n\n.ts-hidden-accessible {\n  border: 0 !important;\n  clip: rect(0 0 0 0) !important;\n  clip-path: inset(50%) !important;\n  overflow: hidden !important;\n  padding: 0 !important;\n  position: absolute !important;\n  width: 1px !important;\n  white-space: nowrap !important;\n}\n\n.ts-wrapper.single .ts-control {\n  --ts-pr-caret: 2rem;\n}\n.ts-wrapper.single .ts-control::after {\n  content: \" \";\n  display: block;\n  position: absolute;\n  top: 50%;\n  margin-top: -3px;\n  width: 0;\n  height: 0;\n  border-style: solid;\n  border-width: 5px 5px 0 5px;\n  border-color: #343a40 transparent transparent transparent;\n}\n.ts-wrapper.single .ts-control:not(.rtl)::after {\n  right: calc(0.75rem + 5px);\n}\n.ts-wrapper.single .ts-control.rtl::after {\n  left: calc(0.75rem + 5px);\n}\n.ts-wrapper.single.dropdown-active .ts-control::after {\n  margin-top: -4px;\n  border-width: 0 5px 5px 5px;\n  border-color: transparent transparent #343a40 transparent;\n}\n.ts-wrapper.single.input-active .ts-control, .ts-wrapper.single.input-active .ts-control input {\n  cursor: text;\n}\n\n.ts-wrapper.form-control,\n.ts-wrapper.form-select {\n  padding: 0 !important;\n}\n\n.ts-dropdown,\n.ts-dropdown.form-control {\n  height: auto;\n  padding: 0;\n  z-index: 1000;\n  background: #fff;\n  border: 1px solid rgba(0, 0, 0, 0.15);\n  border-radius: 0.25rem;\n  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.175);\n}\n\n.ts-dropdown .optgroup-header {\n  font-size: 0.875rem;\n  line-height: 1.5;\n}\n.ts-dropdown .optgroup:first-child::before {\n  display: none;\n}\n.ts-dropdown .optgroup::before {\n  content: \" \";\n  display: block;\n  height: 0;\n  margin: 0.5rem 0;\n  overflow: hidden;\n  border-top: 1px solid #e9ecef;\n  margin-left: -0.75rem;\n  margin-right: -0.75rem;\n}\n.ts-dropdown .create {\n  padding-left: 0.75rem;\n}\n\n.ts-dropdown-content {\n  padding: 5px 0;\n}\n\n.ts-control {\n  min-height: calc(1.5em + 0.75rem + 2px);\n  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n  display: flex;\n  align-items: center;\n}\n@media (prefers-reduced-motion: reduce) {\n  .ts-control {\n    transition: none;\n  }\n}\n.focus .ts-control {\n  border-color: rgb(127.5, 189, 255);\n  outline: 0;\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n}\n\n.is-invalid .ts-control,\n.was-validated .invalid .ts-control {\n  border-color: #dc3545;\n}\n.focus .is-invalid .ts-control,\n.focus .was-validated .invalid .ts-control {\n  border-color: rgb(189.2151898734, 32.7848101266, 47.7721518987);\n  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);\n}\n\n.is-valid .ts-control {\n  border-color: #28a745;\n}\n.focus .is-valid .ts-control {\n  border-color: #28a745;\n  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);\n}\n\n.input-group-sm > .ts-wrapper .ts-control, .ts-wrapper.form-control-sm .ts-control {\n  min-height: calc(1.5em + 0.5rem + 2px);\n  padding: 0 0.75rem;\n  border-radius: 0.2rem;\n  font-size: 0.875rem;\n}\n.input-group-sm > .ts-wrapper.has-items .ts-control, .ts-wrapper.form-control-sm.has-items .ts-control {\n  min-height: calc(1.5em + 0.5rem + 2px) !important;\n  font-size: 0.875rem;\n  padding-bottom: 0;\n}\n.input-group-sm > .ts-wrapper.multi.has-items .ts-control, .ts-wrapper.form-control-sm.multi.has-items .ts-control {\n  padding-top: calc((calc(1.5em + 0.5rem + 2px) - 1.5 * 0.875rem - 4px) / 2) !important;\n}\n.ts-wrapper.multi.has-items .ts-control {\n  padding-left: calc(0.75rem - 5px);\n  --ts-pr-min: calc(0.75rem - 5px);\n}\n.ts-wrapper.multi .ts-control > div {\n  border-radius: calc(0.25rem - 1px);\n}\n.input-group-lg > .ts-wrapper > .ts-control, .ts-wrapper.form-control-lg .ts-control {\n  min-height: calc(1.5em + 1rem + 2px);\n  border-radius: 0.3rem;\n  font-size: 1.25rem;\n}\n\n.form-control.ts-wrapper {\n  padding: 0;\n  height: auto;\n  border: none;\n  background: none;\n  border-radius: 0;\n}\n\n.input-group > .ts-wrapper {\n  flex-grow: 1;\n}\n.input-group > .ts-wrapper:not(:nth-child(2)) > .ts-control {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.input-group > .ts-wrapper:not(:last-child) > .ts-control {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}"]}