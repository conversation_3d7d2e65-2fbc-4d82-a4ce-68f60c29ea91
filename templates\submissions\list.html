{% extends 'basic.html' %}

{% block content %}

<div class="page-header d-print-none">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="page-title">Подача статей</h2>
        </div>
    </div>
</div>

<div class="card mt-4">
    <div class="card-header">
        <div class="card-title">Фильтры</div>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label class="form-label">Статус</label>
                <select class="form-select" name="status">
                    <option value="">Все статусы</option>
                    <option value="draft" {% if status_filter == 'draft' %}selected{% endif %}>Черновик</option>
                    <option value="in_process" {% if status_filter == 'in_process' %}selected{% endif %}>В процессе</option>
                    <option value="published" {% if status_filter == 'published' %}selected{% endif %}>Опубликовано</option>
                    <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Отклонено</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">Пользователь ID</label>
                <input type="number" class="form-control" name="user" value="{{ user_filter }}" placeholder="ID пользователя">
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">Применить</button>
                <a href="{{ url_for('submissions') }}" class="btn btn-secondary ms-2">Сбросить</a>
            </div>
        </form>
    </div>
</div>

<div class="card mt-4">
  <div class="table-responsive">
    <table class="table card-table table-hover">
      <thead>
        <tr>
          <th>ID</th>
          <th>Пользователь</th>
          <th>Название</th>
          <th>Статус</th>
          <th>Основной автор</th>
          <th>Дата создания</th>
          <th>Действия</th>
        </tr>
      </thead>
      <tbody>
        {% for submission in submissions_list %}
        <tr>
          <td class="align-middle">{{ submission.id }}</td>
          <td class="align-middle">
            {% if users_map.get(submission.user_id) %}
              {{ users_map[submission.user_id].name }}
            {% else %}
              ID: {{ submission.user_id or 'Не указан' }}
            {% endif %}
          </td>
          <td class="align-middle">{{ submission.title or 'Без названия' }}</td>
          <td class="align-middle">
            {% if submission.status == 'draft' %}
              <span class="badge bg-secondary-lt">Черновик</span>
            {% elif submission.status == 'published' %}
              <span class="badge bg-success-lt">Опубликовано</span>
            {% elif submission.status == 'in_process' %}
              <span class="badge bg-warning-lt">В процессе</span>
            {% elif submission.status == 'rejected' %}
              <span class="badge bg-danger-lt">Отклонено</span>
            {% else %}
              <span class="badge bg-light">{{ submission.status or 'Не указан' }}</span>
            {% endif %}
          </td>
          <td class="align-middle">
            {% if authors_map.get(submission.main_author_id) %}
              {{ authors_map[submission.main_author_id].name }}
            {% else %}
              ID: {{ submission.main_author_id or 'Не указан' }}
            {% endif %}
          </td>
          <td class="align-middle">
            {% if submission.created_date %}
              {{ submission.created_date | timestamp_to_date }}
            {% else %}
              Не указана
            {% endif %}
          </td>
          <td class="align-middle">
            <a href="{{ url_for('submission_detail', submission_id=submission.id) }}" class="btn btn-sm btn-outline-primary">
              Просмотр
            </a>
            <button class="btn btn-sm btn-outline-secondary ms-1" 
                    data-bs-toggle="modal" 
                    data-bs-target="#editSubmissionModal"
                    data-submission-id="{{ submission.id }}"
                    data-submission-status="{{ submission.status or '' }}"
                    data-submission-notes="{{ submission.notes or '' }}">
              <i class="ti ti-edit"></i>
            </button>
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
  <div class="card-footer d-flex align-items-center">
    {% set start_idx = (page - 1) * 20 + 1 %}
    {% set end_idx = (page - 1) * 20 + submissions_list|length %}
    <p class="m-0 text-secondary">Показано <span>{{ start_idx }}-{{ end_idx }}</span> из <span>{{ total_submissions }}</span> записей</p>
    <ul class="pagination m-0 ms-auto">
      <li class="page-item {% if page <= 1 %}disabled{% endif %}">
        <a class="page-link" href="{{ url_for('submissions', page=page-1, status=status_filter, user=user_filter) }}" tabindex="-1" aria-disabled="true">
          <i class="ti ti-chevron-left"></i>
        </a>
      </li>
      {% for p in range(1, total_pages+1) %}
        <li class="page-item {% if p == page %}active{% endif %}"><a class="page-link" href="{{ url_for('submissions', page=p, status=status_filter, user=user_filter) }}">{{ p }}</a></li>
      {% endfor %}
      <li class="page-item {% if page >= total_pages %}disabled{% endif %}">
        <a class="page-link" href="{{ url_for('submissions', page=page+1, status=status_filter, user=user_filter) }}">
          <i class="ti ti-chevron-right"></i>
        </a>
      </li>
    </ul>
  </div>
</div>

<!-- Модальное окно для редактирования подачи -->
<div class="modal modal-blur fade" id="editSubmissionModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Редактирование подачи</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editSubmissionForm" method="post" action="{{ url_for('submission_edit') }}">
                <div class="modal-body">
                    <input type="hidden" id="submission_id" name="submission_id">

                    <div class="mb-3">
                        <label class="form-label">Статус</label>
                        <select class="form-select" id="submission_status" name="status" required>
                            <option value="draft">Черновик</option>
                            <option value="in_process">В процессе</option>
                            <option value="published">Опубликовано</option>
                            <option value="rejected">Отклонено</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Заметки</label>
                        <textarea class="form-control" id="submission_notes" name="notes" rows="5" placeholder="Заметки к подаче"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="#" class="btn btn-link link-secondary" data-bs-dismiss="modal">Отмена</a>
                    <button type="submit" class="btn btn-primary ms-auto">Сохранить изменения</button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Обработка показа модального окна
    const modal = document.getElementById('editSubmissionModal');
    if (modal) {
        modal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            
            const submissionId = button.getAttribute('data-submission-id');
            const submissionStatus = button.getAttribute('data-submission-status');
            const submissionNotes = button.getAttribute('data-submission-notes');
            
            // Заполняем форму
            document.getElementById('submission_id').value = submissionId;
            document.getElementById('submission_status').value = submissionStatus;
            document.getElementById('submission_notes').value = submissionNotes;
        });
    }

    // Обработка отправки формы
    const form = document.getElementById('editSubmissionForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('{{ url_for('submission_edit') }}', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Ошибка при сохранении: ' + (data.error || 'Неизвестная ошибка'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Ошибка при отправке запроса');
            });
        });
    }
});
</script>
{% endblock %} 