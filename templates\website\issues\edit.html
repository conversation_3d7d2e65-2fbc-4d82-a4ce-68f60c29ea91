{% extends 'basic.html' %}

{% block content %}

<div class="page-header d-print-none mb-4">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="page-title">{% if issue.id == 0 %}Создание выпуска{% else %}Редактирование выпуска{% endif %}</h2>
            <a href="{{ url_for('issues') }}" class="">
                <i class="ti ti-arrow-left"></i>
                Назад
            </a>
        </div>
        <div class="col-auto ms-auto">
            <div class="btn-list">
                <button id="save-issue-btn-top" type="button" class="btn btn-primary d-none d-sm-inline-block">
                    <i class="ti ti-check icon icon-1"></i>
                    Сохранить
                </button>
            </div>
        </div>
    </div>
</div>

<form id="main-issue-form" method="post" enctype="multipart/form-data">
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    <h5 class="card-title">{% if issue.id == 0 %}Создание выпуска{% else %}Редактирование выпуска{% endif %}</h5>
                </div>
                <div class="card-actions">
                    <div class="btn-group" role="group" aria-label="Язык">
                        <button type="button" class="btn btn-outline-primary" data-lang="all">Все</button>
                        <button type="button" class="btn btn-outline-primary" data-lang="ru">RU</button>
                        <button type="button" class="btn btn-outline-primary" data-lang="uz">UZ</button>
                        <button type="button" class="btn btn-outline-primary" data-lang="en">EN</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">ID</label>
                    <input type="text" class="form-control" value="{{ issue.id }}" readonly>
                </div>
                <div class="mb-3 lang-field lang-en">
                    <label class="form-label">Название</label>
                    <input type="text" class="form-control" name="title" value="{{ issue.title or '' }}">
                </div>
                <div class="mb-3 lang-field lang-uz">
                    <label class="form-label">Название (узб)</label>
                    <input type="text" class="form-control" name="title_uz" value="{{ issue.title_uz or '' }}">
                </div>
                <div class="mb-3 lang-field lang-ru">
                    <label class="form-label">Название (рус)</label>
                    <input type="text" class="form-control" name="title_ru" value="{{ issue.title_ru or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Том</label>
                    <input type="text" class="form-control" name="vol_no" value="{{ issue.vol_no or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Номер выпуска</label>
                    <input type="text" class="form-control" name="issue_no" value="{{ issue.issue_no or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Год</label>
                    <input type="text" class="form-control" name="year" value="{{ issue.year or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Категория</label>
                    <input type="text" class="form-control" name="category" value="{{ issue.category or '' }}">
                </div>
                <div class="mb-3 lang-field lang-en">
                    <label class="form-label">Краткое описание</label>
                    <input type="text" class="form-control" name="shortinfo" value="{{ issue.shortinfo or '' }}">
                </div>
                <div class="mb-3 lang-field lang-uz">
                    <label class="form-label">Краткое описание (узб)</label>
                    <input type="text" class="form-control" name="shortinfo_uz" value="{{ issue.shortinfo_uz or '' }}">
                </div>
                <div class="mb-3 lang-field lang-ru">
                    <label class="form-label">Краткое описание (рус)</label>
                    <input type="text" class="form-control" name="shortinfo_ru" value="{{ issue.shortinfo_ru or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Цена</label>
                    <input type="text" class="form-control" name="price" value="{{ issue.price or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Цена (узб)</label>
                    <input type="text" class="form-control" name="price_uz" value="{{ issue.price_uz or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Цена (рус)</label>
                    <input type="text" class="form-control" name="price_ru" value="{{ issue.price_ru or '' }}">
                </div>
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" name="subscription_enable" id="subscription_enable" {% if issue.subscription_enable %}checked{% endif %}>
                    <label class="form-check-label" for="subscription_enable">Подписка включена</label>
                </div>
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" name="is_paid" id="is_paid" {% if issue.is_paid %}checked{% endif %}>
                    <label class="form-check-label" for="is_paid">Платный выпуск</label>
                </div>
                <div class="mb-3">
                    <label class="form-label">Обложка</label>
                    {% if issue.cover_image %}
                        <div class="mb-2"><img src="{{ issue.cover_image }}" alt="cover" style="max-width:200px;"></div>
                    {% endif %}
                    <input type="file" class="form-control" name="cover_image" accept="image/*">
                    <input type="hidden" name="cover_image" value="{{ issue.cover_image }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Дата создания</label>
                    <input type="datetime-local" class="form-control" name="created_at" value="{{ issue.created_at | date_to_form_full }}">
                </div>
                <div class="d-flex justify-content-end">
                    <button id="save-issue-btn" type="submit" class="btn btn-primary">Сохранить</button>
                </div>
            </div>
        </div>
    </div>
</div>
</form>

{% endblock %}

{% block scripts %}
<script>
function setLang(lang) {
    if (lang === 'all') {
        document.querySelectorAll('.lang-field').forEach(e => e.style.display = '');
    } else {
        document.querySelectorAll('.lang-field').forEach(e => e.style.display = 'none');
        document.querySelectorAll('.lang-' + lang).forEach(e => e.style.display = '');
    }
    document.querySelectorAll('.btn-group [data-lang]').forEach(btn => btn.classList.remove('active'));
    document.querySelector('.btn-group [data-lang="' + lang + '"]').classList.add('active');
}
document.querySelectorAll('.btn-group [data-lang]').forEach(btn => {
    btn.addEventListener('click', function() {
        setLang(this.getAttribute('data-lang'));
    });
});
setLang('all');

document.getElementById('save-issue-btn-top').addEventListener('click', function() {
    document.getElementById('save-issue-btn').click();
});
</script>
{% endblock %}
