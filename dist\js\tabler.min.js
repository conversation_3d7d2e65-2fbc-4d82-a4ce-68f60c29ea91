/*!
 * Tabler v1.0.0 (https://tabler.io)
 * Copyright 2018-2025 The Tabler Authors
 * Copyright 2018-2025 codecalm.net Paweł Kuna
 * Licensed under MIT (https://github.com/tabler/tabler/blob/master/LICENSE)
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(require("autosize"),require("imask")):"function"==typeof define&&define.amd?define(["autosize","imask"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).autosize,t.IMask)}(this,(function(t,e){"use strict";const i=document.querySelectorAll('[data-bs-toggle="autosize"]');i.length&&i.forEach((function(e){t&&t(e)}));const n=document.querySelectorAll("[data-countup]");n.length&&n.forEach((function(t){let e={};try{const i=t.getAttribute("data-countup")?JSON.parse(t.getAttribute("data-countup")):{};e=Object.assign({enableScrollSpy:!0},i)}catch(t){}const i=parseInt(t.innerHTML,10),n=new window.countUp.CountUp(t,i,e);n.error||n.start()})),[].slice.call(document.querySelectorAll("[data-mask]")).map((function(t){e&&new e(t,{mask:t.dataset.mask,lazy:"true"===t.dataset["mask-visible"]})}));var s="top",o="bottom",r="right",a="left",l="auto",c=[s,o,r,a],h="start",u="end",d="clippingParents",f="viewport",p="popper",g="reference",m=c.reduce((function(t,e){return t.concat([e+"-"+h,e+"-"+u])}),[]),_=[].concat(c,[l]).reduce((function(t,e){return t.concat([e,e+"-"+h,e+"-"+u])}),[]),b="beforeRead",v="read",y="afterRead",w="beforeMain",A="main",E="afterMain",T="beforeWrite",C="write",O="afterWrite",x=[b,v,y,w,A,E,T,C,O];function k(t){return t?(t.nodeName||"").toLowerCase():null}function S(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function L(t){return t instanceof S(t).Element||t instanceof Element}function $(t){return t instanceof S(t).HTMLElement||t instanceof HTMLElement}function D(t){return"undefined"!=typeof ShadowRoot&&(t instanceof S(t).ShadowRoot||t instanceof ShadowRoot)}const I={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach((function(t){var i=e.styles[t]||{},n=e.attributes[t]||{},s=e.elements[t];$(s)&&k(s)&&(Object.assign(s.style,i),Object.keys(n).forEach((function(t){var e=n[t];!1===e?s.removeAttribute(t):s.setAttribute(t,!0===e?"":e)})))}))},effect:function(t){var e=t.state,i={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,i.popper),e.styles=i,e.elements.arrow&&Object.assign(e.elements.arrow.style,i.arrow),function(){Object.keys(e.elements).forEach((function(t){var n=e.elements[t],s=e.attributes[t]||{},o=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:i[t]).reduce((function(t,e){return t[e]="",t}),{});$(n)&&k(n)&&(Object.assign(n.style,o),Object.keys(s).forEach((function(t){n.removeAttribute(t)})))}))}},requires:["computeStyles"]};function N(t){return t.split("-")[0]}var P=Math.max,j=Math.min,M=Math.round;function F(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map((function(t){return t.brand+"/"+t.version})).join(" "):navigator.userAgent}function H(){return!/^((?!chrome|android).)*safari/i.test(F())}function q(t,e,i){void 0===e&&(e=!1),void 0===i&&(i=!1);var n=t.getBoundingClientRect(),s=1,o=1;e&&$(t)&&(s=t.offsetWidth>0&&M(n.width)/t.offsetWidth||1,o=t.offsetHeight>0&&M(n.height)/t.offsetHeight||1);var r=(L(t)?S(t):window).visualViewport,a=!H()&&i,l=(n.left+(a&&r?r.offsetLeft:0))/s,c=(n.top+(a&&r?r.offsetTop:0))/o,h=n.width/s,u=n.height/o;return{width:h,height:u,top:c,right:l+h,bottom:c+u,left:l,x:l,y:c}}function z(t){var e=q(t),i=t.offsetWidth,n=t.offsetHeight;return Math.abs(e.width-i)<=1&&(i=e.width),Math.abs(e.height-n)<=1&&(n=e.height),{x:t.offsetLeft,y:t.offsetTop,width:i,height:n}}function W(t,e){var i=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(i&&D(i)){var n=e;do{if(n&&t.isSameNode(n))return!0;n=n.parentNode||n.host}while(n)}return!1}function B(t){return S(t).getComputedStyle(t)}function R(t){return["table","td","th"].indexOf(k(t))>=0}function V(t){return((L(t)?t.ownerDocument:t.document)||window.document).documentElement}function K(t){return"html"===k(t)?t:t.assignedSlot||t.parentNode||(D(t)?t.host:null)||V(t)}function U(t){return $(t)&&"fixed"!==B(t).position?t.offsetParent:null}function Q(t){for(var e=S(t),i=U(t);i&&R(i)&&"static"===B(i).position;)i=U(i);return i&&("html"===k(i)||"body"===k(i)&&"static"===B(i).position)?e:i||function(t){var e=/firefox/i.test(F());if(/Trident/i.test(F())&&$(t)&&"fixed"===B(t).position)return null;var i=K(t);for(D(i)&&(i=i.host);$(i)&&["html","body"].indexOf(k(i))<0;){var n=B(i);if("none"!==n.transform||"none"!==n.perspective||"paint"===n.contain||-1!==["transform","perspective"].indexOf(n.willChange)||e&&"filter"===n.willChange||e&&n.filter&&"none"!==n.filter)return i;i=i.parentNode}return null}(t)||e}function X(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function Y(t,e,i){return P(t,j(e,i))}function G(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function J(t,e){return e.reduce((function(e,i){return e[i]=t,e}),{})}const Z={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,i=t.state,n=t.name,l=t.options,h=i.elements.arrow,u=i.modifiersData.popperOffsets,d=N(i.placement),f=X(d),p=[a,r].indexOf(d)>=0?"height":"width";if(h&&u){var g=function(t,e){return G("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:J(t,c))}(l.padding,i),m=z(h),_="y"===f?s:a,b="y"===f?o:r,v=i.rects.reference[p]+i.rects.reference[f]-u[f]-i.rects.popper[p],y=u[f]-i.rects.reference[f],w=Q(h),A=w?"y"===f?w.clientHeight||0:w.clientWidth||0:0,E=v/2-y/2,T=g[_],C=A-m[p]-g[b],O=A/2-m[p]/2+E,x=Y(T,O,C),k=f;i.modifiersData[n]=((e={})[k]=x,e.centerOffset=x-O,e)}},effect:function(t){var e=t.state,i=t.options.element,n=void 0===i?"[data-popper-arrow]":i;null!=n&&("string"!=typeof n||(n=e.elements.popper.querySelector(n)))&&W(e.elements.popper,n)&&(e.elements.arrow=n)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function tt(t){return t.split("-")[1]}var et={top:"auto",right:"auto",bottom:"auto",left:"auto"};function it(t){var e,i=t.popper,n=t.popperRect,l=t.placement,c=t.variation,h=t.offsets,d=t.position,f=t.gpuAcceleration,p=t.adaptive,g=t.roundOffsets,m=t.isFixed,_=h.x,b=void 0===_?0:_,v=h.y,y=void 0===v?0:v,w="function"==typeof g?g({x:b,y:y}):{x:b,y:y};b=w.x,y=w.y;var A=h.hasOwnProperty("x"),E=h.hasOwnProperty("y"),T=a,C=s,O=window;if(p){var x=Q(i),k="clientHeight",L="clientWidth";x===S(i)&&"static"!==B(x=V(i)).position&&"absolute"===d&&(k="scrollHeight",L="scrollWidth"),(l===s||(l===a||l===r)&&c===u)&&(C=o,y-=(m&&x===O&&O.visualViewport?O.visualViewport.height:x[k])-n.height,y*=f?1:-1),l!==a&&(l!==s&&l!==o||c!==u)||(T=r,b-=(m&&x===O&&O.visualViewport?O.visualViewport.width:x[L])-n.width,b*=f?1:-1)}var $,D=Object.assign({position:d},p&&et),I=!0===g?function(t,e){var i=t.x,n=t.y,s=e.devicePixelRatio||1;return{x:M(i*s)/s||0,y:M(n*s)/s||0}}({x:b,y:y},S(i)):{x:b,y:y};return b=I.x,y=I.y,f?Object.assign({},D,(($={})[C]=E?"0":"",$[T]=A?"0":"",$.transform=(O.devicePixelRatio||1)<=1?"translate("+b+"px, "+y+"px)":"translate3d("+b+"px, "+y+"px, 0)",$)):Object.assign({},D,((e={})[C]=E?y+"px":"",e[T]=A?b+"px":"",e.transform="",e))}const nt={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,i=t.options,n=i.gpuAcceleration,s=void 0===n||n,o=i.adaptive,r=void 0===o||o,a=i.roundOffsets,l=void 0===a||a,c={placement:N(e.placement),variation:tt(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:s,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,it(Object.assign({},c,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:r,roundOffsets:l})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,it(Object.assign({},c,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}};var st={passive:!0};const ot={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,i=t.instance,n=t.options,s=n.scroll,o=void 0===s||s,r=n.resize,a=void 0===r||r,l=S(e.elements.popper),c=[].concat(e.scrollParents.reference,e.scrollParents.popper);return o&&c.forEach((function(t){t.addEventListener("scroll",i.update,st)})),a&&l.addEventListener("resize",i.update,st),function(){o&&c.forEach((function(t){t.removeEventListener("scroll",i.update,st)})),a&&l.removeEventListener("resize",i.update,st)}},data:{}};var rt={left:"right",right:"left",bottom:"top",top:"bottom"};function at(t){return t.replace(/left|right|bottom|top/g,(function(t){return rt[t]}))}var lt={start:"end",end:"start"};function ct(t){return t.replace(/start|end/g,(function(t){return lt[t]}))}function ht(t){var e=S(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function ut(t){return q(V(t)).left+ht(t).scrollLeft}function dt(t){var e=B(t),i=e.overflow,n=e.overflowX,s=e.overflowY;return/auto|scroll|overlay|hidden/.test(i+s+n)}function ft(t){return["html","body","#document"].indexOf(k(t))>=0?t.ownerDocument.body:$(t)&&dt(t)?t:ft(K(t))}function pt(t,e){var i;void 0===e&&(e=[]);var n=ft(t),s=n===(null==(i=t.ownerDocument)?void 0:i.body),o=S(n),r=s?[o].concat(o.visualViewport||[],dt(n)?n:[]):n,a=e.concat(r);return s?a:a.concat(pt(K(r)))}function gt(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function mt(t,e,i){return e===f?gt(function(t,e){var i=S(t),n=V(t),s=i.visualViewport,o=n.clientWidth,r=n.clientHeight,a=0,l=0;if(s){o=s.width,r=s.height;var c=H();(c||!c&&"fixed"===e)&&(a=s.offsetLeft,l=s.offsetTop)}return{width:o,height:r,x:a+ut(t),y:l}}(t,i)):L(e)?function(t,e){var i=q(t,!1,"fixed"===e);return i.top=i.top+t.clientTop,i.left=i.left+t.clientLeft,i.bottom=i.top+t.clientHeight,i.right=i.left+t.clientWidth,i.width=t.clientWidth,i.height=t.clientHeight,i.x=i.left,i.y=i.top,i}(e,i):gt(function(t){var e,i=V(t),n=ht(t),s=null==(e=t.ownerDocument)?void 0:e.body,o=P(i.scrollWidth,i.clientWidth,s?s.scrollWidth:0,s?s.clientWidth:0),r=P(i.scrollHeight,i.clientHeight,s?s.scrollHeight:0,s?s.clientHeight:0),a=-n.scrollLeft+ut(t),l=-n.scrollTop;return"rtl"===B(s||i).direction&&(a+=P(i.clientWidth,s?s.clientWidth:0)-o),{width:o,height:r,x:a,y:l}}(V(t)))}function _t(t){var e,i=t.reference,n=t.element,l=t.placement,c=l?N(l):null,d=l?tt(l):null,f=i.x+i.width/2-n.width/2,p=i.y+i.height/2-n.height/2;switch(c){case s:e={x:f,y:i.y-n.height};break;case o:e={x:f,y:i.y+i.height};break;case r:e={x:i.x+i.width,y:p};break;case a:e={x:i.x-n.width,y:p};break;default:e={x:i.x,y:i.y}}var g=c?X(c):null;if(null!=g){var m="y"===g?"height":"width";switch(d){case h:e[g]=e[g]-(i[m]/2-n[m]/2);break;case u:e[g]=e[g]+(i[m]/2-n[m]/2)}}return e}function bt(t,e){void 0===e&&(e={});var i=e,n=i.placement,a=void 0===n?t.placement:n,l=i.strategy,h=void 0===l?t.strategy:l,u=i.boundary,m=void 0===u?d:u,_=i.rootBoundary,b=void 0===_?f:_,v=i.elementContext,y=void 0===v?p:v,w=i.altBoundary,A=void 0!==w&&w,E=i.padding,T=void 0===E?0:E,C=G("number"!=typeof T?T:J(T,c)),O=y===p?g:p,x=t.rects.popper,S=t.elements[A?O:y],D=function(t,e,i,n){var s="clippingParents"===e?function(t){var e=pt(K(t)),i=["absolute","fixed"].indexOf(B(t).position)>=0&&$(t)?Q(t):t;return L(i)?e.filter((function(t){return L(t)&&W(t,i)&&"body"!==k(t)})):[]}(t):[].concat(e),o=[].concat(s,[i]),r=o[0],a=o.reduce((function(e,i){var s=mt(t,i,n);return e.top=P(s.top,e.top),e.right=j(s.right,e.right),e.bottom=j(s.bottom,e.bottom),e.left=P(s.left,e.left),e}),mt(t,r,n));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}(L(S)?S:S.contextElement||V(t.elements.popper),m,b,h),I=q(t.elements.reference),N=_t({reference:I,element:x,placement:a}),M=gt(Object.assign({},x,N)),F=y===p?M:I,H={top:D.top-F.top+C.top,bottom:F.bottom-D.bottom+C.bottom,left:D.left-F.left+C.left,right:F.right-D.right+C.right},z=t.modifiersData.offset;if(y===p&&z){var R=z[a];Object.keys(H).forEach((function(t){var e=[r,o].indexOf(t)>=0?1:-1,i=[s,o].indexOf(t)>=0?"y":"x";H[t]+=R[i]*e}))}return H}function vt(t,e){void 0===e&&(e={});var i=e,n=i.placement,s=i.boundary,o=i.rootBoundary,r=i.padding,a=i.flipVariations,l=i.allowedAutoPlacements,h=void 0===l?_:l,u=tt(n),d=u?a?m:m.filter((function(t){return tt(t)===u})):c,f=d.filter((function(t){return h.indexOf(t)>=0}));0===f.length&&(f=d);var p=f.reduce((function(e,i){return e[i]=bt(t,{placement:i,boundary:s,rootBoundary:o,padding:r})[N(i)],e}),{});return Object.keys(p).sort((function(t,e){return p[t]-p[e]}))}const yt={name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,n=t.name;if(!e.modifiersData[n]._skip){for(var c=i.mainAxis,u=void 0===c||c,d=i.altAxis,f=void 0===d||d,p=i.fallbackPlacements,g=i.padding,m=i.boundary,_=i.rootBoundary,b=i.altBoundary,v=i.flipVariations,y=void 0===v||v,w=i.allowedAutoPlacements,A=e.options.placement,E=N(A),T=p||(E!==A&&y?function(t){if(N(t)===l)return[];var e=at(t);return[ct(t),e,ct(e)]}(A):[at(A)]),C=[A].concat(T).reduce((function(t,i){return t.concat(N(i)===l?vt(e,{placement:i,boundary:m,rootBoundary:_,padding:g,flipVariations:y,allowedAutoPlacements:w}):i)}),[]),O=e.rects.reference,x=e.rects.popper,k=new Map,S=!0,L=C[0],$=0;$<C.length;$++){var D=C[$],I=N(D),P=tt(D)===h,j=[s,o].indexOf(I)>=0,M=j?"width":"height",F=bt(e,{placement:D,boundary:m,rootBoundary:_,altBoundary:b,padding:g}),H=j?P?r:a:P?o:s;O[M]>x[M]&&(H=at(H));var q=at(H),z=[];if(u&&z.push(F[I]<=0),f&&z.push(F[H]<=0,F[q]<=0),z.every((function(t){return t}))){L=D,S=!1;break}k.set(D,z)}if(S)for(var W=function(t){var e=C.find((function(e){var i=k.get(e);if(i)return i.slice(0,t).every((function(t){return t}))}));if(e)return L=e,"break"},B=y?3:1;B>0&&"break"!==W(B);B--);e.placement!==L&&(e.modifiersData[n]._skip=!0,e.placement=L,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function wt(t,e,i){return void 0===i&&(i={x:0,y:0}),{top:t.top-e.height-i.y,right:t.right-e.width+i.x,bottom:t.bottom-e.height+i.y,left:t.left-e.width-i.x}}function At(t){return[s,r,o,a].some((function(e){return t[e]>=0}))}const Et={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,i=t.name,n=e.rects.reference,s=e.rects.popper,o=e.modifiersData.preventOverflow,r=bt(e,{elementContext:"reference"}),a=bt(e,{altBoundary:!0}),l=wt(r,n),c=wt(a,s,o),h=At(l),u=At(c);e.modifiersData[i]={referenceClippingOffsets:l,popperEscapeOffsets:c,isReferenceHidden:h,hasPopperEscaped:u},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":h,"data-popper-escaped":u})}},Tt={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,i=t.options,n=t.name,o=i.offset,l=void 0===o?[0,0]:o,c=_.reduce((function(t,i){return t[i]=function(t,e,i){var n=N(t),o=[a,s].indexOf(n)>=0?-1:1,l="function"==typeof i?i(Object.assign({},e,{placement:t})):i,c=l[0],h=l[1];return c=c||0,h=(h||0)*o,[a,r].indexOf(n)>=0?{x:h,y:c}:{x:c,y:h}}(i,e.rects,l),t}),{}),h=c[e.placement],u=h.x,d=h.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=u,e.modifiersData.popperOffsets.y+=d),e.modifiersData[n]=c}},Ct={name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,i=t.name;e.modifiersData[i]=_t({reference:e.rects.reference,element:e.rects.popper,placement:e.placement})},data:{}},Ot={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,i=t.options,n=t.name,l=i.mainAxis,c=void 0===l||l,u=i.altAxis,d=void 0!==u&&u,f=i.boundary,p=i.rootBoundary,g=i.altBoundary,m=i.padding,_=i.tether,b=void 0===_||_,v=i.tetherOffset,y=void 0===v?0:v,w=bt(e,{boundary:f,rootBoundary:p,padding:m,altBoundary:g}),A=N(e.placement),E=tt(e.placement),T=!E,C=X(A),O="x"===C?"y":"x",x=e.modifiersData.popperOffsets,k=e.rects.reference,S=e.rects.popper,L="function"==typeof y?y(Object.assign({},e.rects,{placement:e.placement})):y,$="number"==typeof L?{mainAxis:L,altAxis:L}:Object.assign({mainAxis:0,altAxis:0},L),D=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,I={x:0,y:0};if(x){if(c){var M,F="y"===C?s:a,H="y"===C?o:r,q="y"===C?"height":"width",W=x[C],B=W+w[F],R=W-w[H],V=b?-S[q]/2:0,K=E===h?k[q]:S[q],U=E===h?-S[q]:-k[q],G=e.elements.arrow,J=b&&G?z(G):{width:0,height:0},Z=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},et=Z[F],it=Z[H],nt=Y(0,k[q],J[q]),st=T?k[q]/2-V-nt-et-$.mainAxis:K-nt-et-$.mainAxis,ot=T?-k[q]/2+V+nt+it+$.mainAxis:U+nt+it+$.mainAxis,rt=e.elements.arrow&&Q(e.elements.arrow),at=rt?"y"===C?rt.clientTop||0:rt.clientLeft||0:0,lt=null!=(M=null==D?void 0:D[C])?M:0,ct=W+ot-lt,ht=Y(b?j(B,W+st-lt-at):B,W,b?P(R,ct):R);x[C]=ht,I[C]=ht-W}if(d){var ut,dt="x"===C?s:a,ft="x"===C?o:r,pt=x[O],gt="y"===O?"height":"width",mt=pt+w[dt],_t=pt-w[ft],vt=-1!==[s,a].indexOf(A),yt=null!=(ut=null==D?void 0:D[O])?ut:0,wt=vt?mt:pt-k[gt]-S[gt]-yt+$.altAxis,At=vt?pt+k[gt]+S[gt]-yt-$.altAxis:_t,Et=b&&vt?function(t,e,i){var n=Y(t,e,i);return n>i?i:n}(wt,pt,At):Y(b?wt:mt,pt,b?At:_t);x[O]=Et,I[O]=Et-pt}e.modifiersData[n]=I}},requiresIfExists:["offset"]};function xt(t,e,i){void 0===i&&(i=!1);var n,s,o=$(e),r=$(e)&&function(t){var e=t.getBoundingClientRect(),i=M(e.width)/t.offsetWidth||1,n=M(e.height)/t.offsetHeight||1;return 1!==i||1!==n}(e),a=V(e),l=q(t,r,i),c={scrollLeft:0,scrollTop:0},h={x:0,y:0};return(o||!o&&!i)&&(("body"!==k(e)||dt(a))&&(c=(n=e)!==S(n)&&$(n)?{scrollLeft:(s=n).scrollLeft,scrollTop:s.scrollTop}:ht(n)),$(e)?((h=q(e,!0)).x+=e.clientLeft,h.y+=e.clientTop):a&&(h.x=ut(a))),{x:l.left+c.scrollLeft-h.x,y:l.top+c.scrollTop-h.y,width:l.width,height:l.height}}function kt(t){var e=new Map,i=new Set,n=[];function s(t){i.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!i.has(t)){var n=e.get(t);n&&s(n)}})),n.push(t)}return t.forEach((function(t){e.set(t.name,t)})),t.forEach((function(t){i.has(t.name)||s(t)})),n}var St={placement:"bottom",modifiers:[],strategy:"absolute"};function Lt(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return!e.some((function(t){return!(t&&"function"==typeof t.getBoundingClientRect)}))}function $t(t){void 0===t&&(t={});var e=t,i=e.defaultModifiers,n=void 0===i?[]:i,s=e.defaultOptions,o=void 0===s?St:s;return function(t,e,i){void 0===i&&(i=o);var s,r,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},St,o),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},l=[],c=!1,h={state:a,setOptions:function(i){var s="function"==typeof i?i(a.options):i;u(),a.options=Object.assign({},o,a.options,s),a.scrollParents={reference:L(t)?pt(t):t.contextElement?pt(t.contextElement):[],popper:pt(e)};var r,c,d=function(t){var e=kt(t);return x.reduce((function(t,i){return t.concat(e.filter((function(t){return t.phase===i})))}),[])}((r=[].concat(n,a.options.modifiers),c=r.reduce((function(t,e){var i=t[e.name];return t[e.name]=i?Object.assign({},i,e,{options:Object.assign({},i.options,e.options),data:Object.assign({},i.data,e.data)}):e,t}),{}),Object.keys(c).map((function(t){return c[t]}))));return a.orderedModifiers=d.filter((function(t){return t.enabled})),a.orderedModifiers.forEach((function(t){var e=t.name,i=t.options,n=void 0===i?{}:i,s=t.effect;if("function"==typeof s){var o=s({state:a,name:e,instance:h,options:n});l.push(o||function(){})}})),h.update()},forceUpdate:function(){if(!c){var t=a.elements,e=t.reference,i=t.popper;if(Lt(e,i)){a.rects={reference:xt(e,Q(i),"fixed"===a.options.strategy),popper:z(i)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(t){return a.modifiersData[t.name]=Object.assign({},t.data)}));for(var n=0;n<a.orderedModifiers.length;n++)if(!0!==a.reset){var s=a.orderedModifiers[n],o=s.fn,r=s.options,l=void 0===r?{}:r,u=s.name;"function"==typeof o&&(a=o({state:a,options:l,name:u,instance:h})||a)}else a.reset=!1,n=-1}}},update:(s=function(){return new Promise((function(t){h.forceUpdate(),t(a)}))},function(){return r||(r=new Promise((function(t){Promise.resolve().then((function(){r=void 0,t(s())}))}))),r}),destroy:function(){u(),c=!0}};if(!Lt(t,e))return h;function u(){l.forEach((function(t){return t()})),l=[]}return h.setOptions(i).then((function(t){!c&&i.onFirstUpdate&&i.onFirstUpdate(t)})),h}}var Dt=$t(),It=$t({defaultModifiers:[ot,Ct,nt,I]}),Nt=$t({defaultModifiers:[ot,Ct,nt,I,Tt,yt,Ot,Z,Et]});const Pt=Object.freeze(Object.defineProperty({__proto__:null,afterMain:E,afterRead:y,afterWrite:O,applyStyles:I,arrow:Z,auto:l,basePlacements:c,beforeMain:w,beforeRead:b,beforeWrite:T,bottom:o,clippingParents:d,computeStyles:nt,createPopper:Nt,createPopperBase:Dt,createPopperLite:It,detectOverflow:bt,end:u,eventListeners:ot,flip:yt,hide:Et,left:a,main:A,modifierPhases:x,offset:Tt,placements:_,popper:p,popperGenerator:$t,popperOffsets:Ct,preventOverflow:Ot,read:v,reference:g,right:r,start:h,top:s,variationPlacements:m,viewport:f,write:C},Symbol.toStringTag,{value:"Module"})),jt=new Map,Mt={set(t,e,i){jt.has(t)||jt.set(t,new Map);const n=jt.get(t);n.has(e)||0===n.size?n.set(e,i):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(n.keys())[0]}.`)},get:(t,e)=>jt.has(t)&&jt.get(t).get(e)||null,remove(t,e){if(!jt.has(t))return;const i=jt.get(t);i.delete(e),0===i.size&&jt.delete(t)}},Ft="transitionend",Ht=t=>(t&&window.CSS&&window.CSS.escape&&(t=t.replace(/#([^\s"#']+)/g,((t,e)=>`#${CSS.escape(e)}`))),t),qt=t=>{t.dispatchEvent(new Event(Ft))},zt=t=>!(!t||"object"!=typeof t)&&(void 0!==t.jquery&&(t=t[0]),void 0!==t.nodeType),Wt=t=>zt(t)?t.jquery?t[0]:t:"string"==typeof t&&t.length>0?document.querySelector(Ht(t)):null,Bt=t=>{if(!zt(t)||0===t.getClientRects().length)return!1;const e="visible"===getComputedStyle(t).getPropertyValue("visibility"),i=t.closest("details:not([open])");if(!i)return e;if(i!==t){const e=t.closest("summary");if(e&&e.parentNode!==i)return!1;if(null===e)return!1}return e},Rt=t=>!t||t.nodeType!==Node.ELEMENT_NODE||!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled")),Vt=t=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?Vt(t.parentNode):null},Kt=()=>{},Ut=t=>{t.offsetHeight},Qt=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,Xt=[],Yt=()=>"rtl"===document.documentElement.dir,Gt=t=>{var e;e=()=>{const e=Qt();if(e){const i=t.NAME,n=e.fn[i];e.fn[i]=t.jQueryInterface,e.fn[i].Constructor=t,e.fn[i].noConflict=()=>(e.fn[i]=n,t.jQueryInterface)}},"loading"===document.readyState?(Xt.length||document.addEventListener("DOMContentLoaded",(()=>{for(const t of Xt)t()})),Xt.push(e)):e()},Jt=(t,e=[],i=t)=>"function"==typeof t?t(...e):i,Zt=(t,e,i=!0)=>{if(!i)return void Jt(t);const n=(t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:i}=window.getComputedStyle(t);const n=Number.parseFloat(e),s=Number.parseFloat(i);return n||s?(e=e.split(",")[0],i=i.split(",")[0],1e3*(Number.parseFloat(e)+Number.parseFloat(i))):0})(e)+5;let s=!1;const o=({target:i})=>{i===e&&(s=!0,e.removeEventListener(Ft,o),Jt(t))};e.addEventListener(Ft,o),setTimeout((()=>{s||qt(e)}),n)},te=(t,e,i,n)=>{const s=t.length;let o=t.indexOf(e);return-1===o?!i&&n?t[s-1]:t[0]:(o+=i?1:-1,n&&(o=(o+s)%s),t[Math.max(0,Math.min(o,s-1))])},ee=/[^.]*(?=\..*)\.|.*/,ie=/\..*/,ne=/::\d+$/,se={};
/*!
	  * Bootstrap v5.3.3 (https://getbootstrap.com/)
	  * Copyright 2011-2024 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
	  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
	  */let oe=1;const re={mouseenter:"mouseover",mouseleave:"mouseout"},ae=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function le(t,e){return e&&`${e}::${oe++}`||t.uidEvent||oe++}function ce(t){const e=le(t);return t.uidEvent=e,se[e]=se[e]||{},se[e]}function he(t,e,i=null){return Object.values(t).find((t=>t.callable===e&&t.delegationSelector===i))}function ue(t,e,i){const n="string"==typeof e,s=n?i:e||i;let o=ge(t);return ae.has(o)||(o=t),[n,s,o]}function de(t,e,i,n,s){if("string"!=typeof e||!t)return;let[o,r,a]=ue(e,i,n);if(e in re){const t=t=>function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return t.call(this,e)};r=t(r)}const l=ce(t),c=l[a]||(l[a]={}),h=he(c,r,o?i:null);if(h)return void(h.oneOff=h.oneOff&&s);const u=le(r,e.replace(ee,"")),d=o?function(t,e,i){return function n(s){const o=t.querySelectorAll(e);for(let{target:r}=s;r&&r!==this;r=r.parentNode)for(const a of o)if(a===r)return _e(s,{delegateTarget:r}),n.oneOff&&me.off(t,s.type,e,i),i.apply(r,[s])}}(t,i,r):function(t,e){return function i(n){return _e(n,{delegateTarget:t}),i.oneOff&&me.off(t,n.type,e),e.apply(t,[n])}}(t,r);d.delegationSelector=o?i:null,d.callable=r,d.oneOff=s,d.uidEvent=u,c[u]=d,t.addEventListener(a,d,o)}function fe(t,e,i,n,s){const o=he(e[i],n,s);o&&(t.removeEventListener(i,o,Boolean(s)),delete e[i][o.uidEvent])}function pe(t,e,i,n){const s=e[i]||{};for(const[o,r]of Object.entries(s))o.includes(n)&&fe(t,e,i,r.callable,r.delegationSelector)}function ge(t){return t=t.replace(ie,""),re[t]||t}const me={on(t,e,i,n){de(t,e,i,n,!1)},one(t,e,i,n){de(t,e,i,n,!0)},off(t,e,i,n){if("string"!=typeof e||!t)return;const[s,o,r]=ue(e,i,n),a=r!==e,l=ce(t),c=l[r]||{},h=e.startsWith(".");if(void 0===o){if(h)for(const i of Object.keys(l))pe(t,l,i,e.slice(1));for(const[i,n]of Object.entries(c)){const s=i.replace(ne,"");a&&!e.includes(s)||fe(t,l,r,n.callable,n.delegationSelector)}}else{if(!Object.keys(c).length)return;fe(t,l,r,o,s?i:null)}},trigger(t,e,i){if("string"!=typeof e||!t)return null;const n=Qt();let s=null,o=!0,r=!0,a=!1;e!==ge(e)&&n&&(s=n.Event(e,i),n(t).trigger(s),o=!s.isPropagationStopped(),r=!s.isImmediatePropagationStopped(),a=s.isDefaultPrevented());const l=_e(new Event(e,{bubbles:o,cancelable:!0}),i);return a&&l.preventDefault(),r&&t.dispatchEvent(l),l.defaultPrevented&&s&&s.preventDefault(),l}};function _e(t,e={}){for(const[i,n]of Object.entries(e))try{t[i]=n}catch(e){Object.defineProperty(t,i,{configurable:!0,get:()=>n})}return t}function be(t){if("true"===t)return!0;if("false"===t)return!1;if(t===Number(t).toString())return Number(t);if(""===t||"null"===t)return null;if("string"!=typeof t)return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function ve(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const ye={setDataAttribute(t,e,i){t.setAttribute(`data-bs-${ve(e)}`,i)},removeDataAttribute(t,e){t.removeAttribute(`data-bs-${ve(e)}`)},getDataAttributes(t){if(!t)return{};const e={},i=Object.keys(t.dataset).filter((t=>t.startsWith("bs")&&!t.startsWith("bsConfig")));for(const n of i){let i=n.replace(/^bs/,"");i=i.charAt(0).toLowerCase()+i.slice(1,i.length),e[i]=be(t.dataset[n])}return e},getDataAttribute:(t,e)=>be(t.getAttribute(`data-bs-${ve(e)}`))};class we{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const i=zt(e)?ye.getDataAttribute(e,"config"):{};return{...this.constructor.Default,..."object"==typeof i?i:{},...zt(e)?ye.getDataAttributes(e):{},..."object"==typeof t?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[n,s]of Object.entries(e)){const e=t[n],o=zt(e)?"element":null==(i=e)?`${i}`:Object.prototype.toString.call(i).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(s).test(o))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${n}" provided type "${o}" but expected type "${s}".`)}var i}}class Ae extends we{constructor(t,e){super(),(t=Wt(t))&&(this._element=t,this._config=this._getConfig(e),Mt.set(this._element,this.constructor.DATA_KEY,this))}dispose(){Mt.remove(this._element,this.constructor.DATA_KEY),me.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,i=!0){Zt(t,e,i)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return Mt.get(Wt(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return"5.3.3"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const Ee=t=>{let e=t.getAttribute("data-bs-target");if(!e||"#"===e){let i=t.getAttribute("href");if(!i||!i.includes("#")&&!i.startsWith("."))return null;i.includes("#")&&!i.startsWith("#")&&(i=`#${i.split("#")[1]}`),e=i&&"#"!==i?i.trim():null}return e?e.split(",").map((t=>Ht(t))).join(","):null},Te={find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const i=[];let n=t.parentNode.closest(e);for(;n;)i.push(n),n=n.parentNode.closest(e);return i},prev(t,e){let i=t.previousElementSibling;for(;i;){if(i.matches(e))return[i];i=i.previousElementSibling}return[]},next(t,e){let i=t.nextElementSibling;for(;i;){if(i.matches(e))return[i];i=i.nextElementSibling}return[]},focusableChildren(t){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((t=>`${t}:not([tabindex^="-"])`)).join(",");return this.find(e,t).filter((t=>!Rt(t)&&Bt(t)))},getSelectorFromElement(t){const e=Ee(t);return e&&Te.findOne(e)?e:null},getElementFromSelector(t){const e=Ee(t);return e?Te.findOne(e):null},getMultipleElementsFromSelector(t){const e=Ee(t);return e?Te.find(e):[]}},Ce=(t,e="hide")=>{const i=`click.dismiss${t.EVENT_KEY}`,n=t.NAME;me.on(document,i,`[data-bs-dismiss="${n}"]`,(function(i){if(["A","AREA"].includes(this.tagName)&&i.preventDefault(),Rt(this))return;const s=Te.getElementFromSelector(this)||this.closest(`.${n}`);t.getOrCreateInstance(s)[e]()}))},Oe=".bs.alert",xe=`close${Oe}`,ke=`closed${Oe}`;class Se extends Ae{static get NAME(){return"alert"}close(){if(me.trigger(this._element,xe).defaultPrevented)return;this._element.classList.remove("show");const t=this._element.classList.contains("fade");this._queueCallback((()=>this._destroyElement()),this._element,t)}_destroyElement(){this._element.remove(),me.trigger(this._element,ke),this.dispose()}static jQueryInterface(t){return this.each((function(){const e=Se.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}Ce(Se,"close"),Gt(Se);const Le='[data-bs-toggle="button"]';class $e extends Ae{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(t){return this.each((function(){const e=$e.getOrCreateInstance(this);"toggle"===t&&e[t]()}))}}me.on(document,"click.bs.button.data-api",Le,(t=>{t.preventDefault();const e=t.target.closest(Le);$e.getOrCreateInstance(e).toggle()})),Gt($e);const De=".bs.swipe",Ie=`touchstart${De}`,Ne=`touchmove${De}`,Pe=`touchend${De}`,je=`pointerdown${De}`,Me=`pointerup${De}`,Fe={endCallback:null,leftCallback:null,rightCallback:null},He={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class qe extends we{constructor(t,e){super(),this._element=t,t&&qe.isSupported()&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return Fe}static get DefaultType(){return He}static get NAME(){return"swipe"}dispose(){me.off(this._element,De)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),Jt(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=40)return;const e=t/this._deltaX;this._deltaX=0,e&&Jt(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(me.on(this._element,je,(t=>this._start(t))),me.on(this._element,Me,(t=>this._end(t))),this._element.classList.add("pointer-event")):(me.on(this._element,Ie,(t=>this._start(t))),me.on(this._element,Ne,(t=>this._move(t))),me.on(this._element,Pe,(t=>this._end(t))))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&("pen"===t.pointerType||"touch"===t.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const ze=".bs.carousel",We=".data-api",Be="ArrowLeft",Re="ArrowRight",Ve="next",Ke="prev",Ue="left",Qe="right",Xe=`slide${ze}`,Ye=`slid${ze}`,Ge=`keydown${ze}`,Je=`mouseenter${ze}`,Ze=`mouseleave${ze}`,ti=`dragstart${ze}`,ei=`load${ze}${We}`,ii=`click${ze}${We}`,ni="carousel",si="active",oi=".active",ri=".carousel-item",ai=oi+ri,li={[Be]:Qe,[Re]:Ue},ci={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},hi={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class ui extends Ae{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=Te.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===ni&&this.cycle()}static get Default(){return ci}static get DefaultType(){return hi}static get NAME(){return"carousel"}next(){this._slide(Ve)}nextWhenVisible(){!document.hidden&&Bt(this._element)&&this.next()}prev(){this._slide(Ke)}pause(){this._isSliding&&qt(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?me.one(this._element,Ye,(()=>this.cycle())):this.cycle())}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding)return void me.one(this._element,Ye,(()=>this.to(t)));const i=this._getItemIndex(this._getActive());if(i===t)return;const n=t>i?Ve:Ke;this._slide(n,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&me.on(this._element,Ge,(t=>this._keydown(t))),"hover"===this._config.pause&&(me.on(this._element,Je,(()=>this.pause())),me.on(this._element,Ze,(()=>this._maybeEnableCycle()))),this._config.touch&&qe.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of Te.find(".carousel-item img",this._element))me.on(t,ti,(t=>t.preventDefault()));const t={leftCallback:()=>this._slide(this._directionToOrder(Ue)),rightCallback:()=>this._slide(this._directionToOrder(Qe)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new qe(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=li[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=Te.findOne(oi,this._indicatorsElement);e.classList.remove(si),e.removeAttribute("aria-current");const i=Te.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);i&&(i.classList.add(si),i.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const i=this._getActive(),n=t===Ve,s=e||te(this._getItems(),i,n,this._config.wrap);if(s===i)return;const o=this._getItemIndex(s),r=e=>me.trigger(this._element,e,{relatedTarget:s,direction:this._orderToDirection(t),from:this._getItemIndex(i),to:o});if(r(Xe).defaultPrevented)return;if(!i||!s)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(o),this._activeElement=s;const l=n?"carousel-item-start":"carousel-item-end",c=n?"carousel-item-next":"carousel-item-prev";s.classList.add(c),Ut(s),i.classList.add(l),s.classList.add(l),this._queueCallback((()=>{s.classList.remove(l,c),s.classList.add(si),i.classList.remove(si,c,l),this._isSliding=!1,r(Ye)}),i,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return Te.findOne(ai,this._element)}_getItems(){return Te.find(ri,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return Yt()?t===Ue?Ke:Ve:t===Ue?Ve:Ke}_orderToDirection(t){return Yt()?t===Ke?Ue:Qe:t===Ke?Qe:Ue}static jQueryInterface(t){return this.each((function(){const e=ui.getOrCreateInstance(this,t);if("number"!=typeof t){if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}else e.to(t)}))}}me.on(document,ii,"[data-bs-slide], [data-bs-slide-to]",(function(t){const e=Te.getElementFromSelector(this);if(!e||!e.classList.contains(ni))return;t.preventDefault();const i=ui.getOrCreateInstance(e),n=this.getAttribute("data-bs-slide-to");return n?(i.to(n),void i._maybeEnableCycle()):"next"===ye.getDataAttribute(this,"slide")?(i.next(),void i._maybeEnableCycle()):(i.prev(),void i._maybeEnableCycle())})),me.on(window,ei,(()=>{const t=Te.find('[data-bs-ride="carousel"]');for(const e of t)ui.getOrCreateInstance(e)})),Gt(ui);const di=".bs.collapse",fi=`show${di}`,pi=`shown${di}`,gi=`hide${di}`,mi=`hidden${di}`,_i=`click${di}.data-api`,bi="show",vi="collapse",yi="collapsing",wi=`:scope .${vi} .${vi}`,Ai='[data-bs-toggle="collapse"]',Ei={parent:null,toggle:!0},Ti={parent:"(null|element)",toggle:"boolean"};class Ci extends Ae{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const i=Te.find(Ai);for(const t of i){const e=Te.getSelectorFromElement(t),i=Te.find(e).filter((t=>t===this._element));null!==e&&i.length&&this._triggerArray.push(t)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return Ei}static get DefaultType(){return Ti}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter((t=>t!==this._element)).map((t=>Ci.getOrCreateInstance(t,{toggle:!1})))),t.length&&t[0]._isTransitioning)return;if(me.trigger(this._element,fi).defaultPrevented)return;for(const e of t)e.hide();const e=this._getDimension();this._element.classList.remove(vi),this._element.classList.add(yi),this._element.style[e]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const i=`scroll${e[0].toUpperCase()+e.slice(1)}`;this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(yi),this._element.classList.add(vi,bi),this._element.style[e]="",me.trigger(this._element,pi)}),this._element,!0),this._element.style[e]=`${this._element[i]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(me.trigger(this._element,gi).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,Ut(this._element),this._element.classList.add(yi),this._element.classList.remove(vi,bi);for(const t of this._triggerArray){const e=Te.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0,this._element.style[t]="",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(yi),this._element.classList.add(vi),me.trigger(this._element,mi)}),this._element,!0)}_isShown(t=this._element){return t.classList.contains(bi)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=Wt(t.parent),t}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(Ai);for(const e of t){const t=Te.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(t){const e=Te.find(wi,this._config.parent);return Te.find(t,this._config.parent).filter((t=>!e.includes(t)))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const i of t)i.classList.toggle("collapsed",!e),i.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return"string"==typeof t&&/show|hide/.test(t)&&(e.toggle=!1),this.each((function(){const i=Ci.getOrCreateInstance(this,e);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t]()}}))}}me.on(document,_i,Ai,(function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();for(const t of Te.getMultipleElementsFromSelector(this))Ci.getOrCreateInstance(t,{toggle:!1}).toggle()})),Gt(Ci);const Oi="dropdown",xi=".bs.dropdown",ki=".data-api",Si="ArrowUp",Li="ArrowDown",$i=`hide${xi}`,Di=`hidden${xi}`,Ii=`show${xi}`,Ni=`shown${xi}`,Pi=`click${xi}${ki}`,ji=`keydown${xi}${ki}`,Mi=`keyup${xi}${ki}`,Fi="show",Hi='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',qi=`${Hi}.${Fi}`,zi=".dropdown-menu",Wi=Yt()?"top-end":"top-start",Bi=Yt()?"top-start":"top-end",Ri=Yt()?"bottom-end":"bottom-start",Vi=Yt()?"bottom-start":"bottom-end",Ki=Yt()?"left-start":"right-start",Ui=Yt()?"right-start":"left-start",Qi={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Xi={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Yi extends Ae{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=Te.next(this._element,zi)[0]||Te.prev(this._element,zi)[0]||Te.findOne(zi,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Qi}static get DefaultType(){return Xi}static get NAME(){return Oi}toggle(){return this._isShown()?this.hide():this.show()}show(){if(Rt(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!me.trigger(this._element,Ii,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const t of[].concat(...document.body.children))me.on(t,"mouseover",Kt);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(Fi),this._element.classList.add(Fi),me.trigger(this._element,Ni,t)}}hide(){if(Rt(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!me.trigger(this._element,$i,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))me.off(t,"mouseover",Kt);this._popper&&this._popper.destroy(),this._menu.classList.remove(Fi),this._element.classList.remove(Fi),this._element.setAttribute("aria-expanded","false"),ye.removeDataAttribute(this._menu,"popper"),me.trigger(this._element,Di,t)}}_getConfig(t){if("object"==typeof(t=super._getConfig(t)).reference&&!zt(t.reference)&&"function"!=typeof t.reference.getBoundingClientRect)throw new TypeError(`${Oi.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(void 0===Pt)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let t=this._element;"parent"===this._config.reference?t=this._parent:zt(this._config.reference)?t=Wt(this._config.reference):"object"==typeof this._config.reference&&(t=this._config.reference);const e=this._getPopperConfig();this._popper=Nt(t,this._menu,e)}_isShown(){return this._menu.classList.contains(Fi)}_getPlacement(){const t=this._parent;if(t.classList.contains("dropend"))return Ki;if(t.classList.contains("dropstart"))return Ui;if(t.classList.contains("dropup-center"))return"top";if(t.classList.contains("dropdown-center"))return"bottom";const e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return t.classList.contains("dropup")?e?Bi:Wi:e?Vi:Ri}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(ye.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...Jt(this._config.popperConfig,[t])}}_selectMenuItem({key:t,target:e}){const i=Te.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter((t=>Bt(t)));i.length&&te(i,e,t===Li,!i.includes(e)).focus()}static jQueryInterface(t){return this.each((function(){const e=Yi.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}static clearMenus(t){if(2===t.button||"keyup"===t.type&&"Tab"!==t.key)return;const e=Te.find(qi);for(const i of e){const e=Yi.getInstance(i);if(!e||!1===e._config.autoClose)continue;const n=t.composedPath(),s=n.includes(e._menu);if(n.includes(e._element)||"inside"===e._config.autoClose&&!s||"outside"===e._config.autoClose&&s)continue;if(e._menu.contains(t.target)&&("keyup"===t.type&&"Tab"===t.key||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const o={relatedTarget:e._element};"click"===t.type&&(o.clickEvent=t),e._completeHide(o)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),i="Escape"===t.key,n=[Si,Li].includes(t.key);if(!n&&!i)return;if(e&&!i)return;t.preventDefault();const s=this.matches(Hi)?this:Te.prev(this,Hi)[0]||Te.next(this,Hi)[0]||Te.findOne(Hi,t.delegateTarget.parentNode),o=Yi.getOrCreateInstance(s);if(n)return t.stopPropagation(),o.show(),void o._selectMenuItem(t);o._isShown()&&(t.stopPropagation(),o.hide(),s.focus())}}me.on(document,ji,Hi,Yi.dataApiKeydownHandler),me.on(document,ji,zi,Yi.dataApiKeydownHandler),me.on(document,Pi,Yi.clearMenus),me.on(document,Mi,Yi.clearMenus),me.on(document,Pi,Hi,(function(t){t.preventDefault(),Yi.getOrCreateInstance(this).toggle()})),Gt(Yi);const Gi="backdrop",Ji="show",Zi=`mousedown.bs.${Gi}`,tn={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},en={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class nn extends we{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return tn}static get DefaultType(){return en}static get NAME(){return Gi}show(t){if(!this._config.isVisible)return void Jt(t);this._append();const e=this._getElement();this._config.isAnimated&&Ut(e),e.classList.add(Ji),this._emulateAnimation((()=>{Jt(t)}))}hide(t){this._config.isVisible?(this._getElement().classList.remove(Ji),this._emulateAnimation((()=>{this.dispose(),Jt(t)}))):Jt(t)}dispose(){this._isAppended&&(me.off(this._element,Zi),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add("fade"),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=Wt(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),me.on(t,Zi,(()=>{Jt(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(t){Zt(t,this._getElement(),this._config.isAnimated)}}const sn=".bs.focustrap",on=`focusin${sn}`,rn=`keydown.tab${sn}`,an="backward",ln={autofocus:!0,trapElement:null},cn={autofocus:"boolean",trapElement:"element"};class hn extends we{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return ln}static get DefaultType(){return cn}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),me.off(document,sn),me.on(document,on,(t=>this._handleFocusin(t))),me.on(document,rn,(t=>this._handleKeydown(t))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,me.off(document,sn))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const i=Te.focusableChildren(e);0===i.length?e.focus():this._lastTabNavDirection===an?i[i.length-1].focus():i[0].focus()}_handleKeydown(t){"Tab"===t.key&&(this._lastTabNavDirection=t.shiftKey?an:"forward")}}const un=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",dn=".sticky-top",fn="padding-right",pn="margin-right";class gn{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,fn,(e=>e+t)),this._setElementAttributes(un,fn,(e=>e+t)),this._setElementAttributes(dn,pn,(e=>e-t))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,fn),this._resetElementAttributes(un,fn),this._resetElementAttributes(dn,pn)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,i){const n=this.getWidth();this._applyManipulationCallback(t,(t=>{if(t!==this._element&&window.innerWidth>t.clientWidth+n)return;this._saveInitialAttribute(t,e);const s=window.getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,`${i(Number.parseFloat(s))}px`)}))}_saveInitialAttribute(t,e){const i=t.style.getPropertyValue(e);i&&ye.setDataAttribute(t,e,i)}_resetElementAttributes(t,e){this._applyManipulationCallback(t,(t=>{const i=ye.getDataAttribute(t,e);null!==i?(ye.removeDataAttribute(t,e),t.style.setProperty(e,i)):t.style.removeProperty(e)}))}_applyManipulationCallback(t,e){if(zt(t))e(t);else for(const i of Te.find(t,this._element))e(i)}}const mn=".bs.modal",_n=`hide${mn}`,bn=`hidePrevented${mn}`,vn=`hidden${mn}`,yn=`show${mn}`,wn=`shown${mn}`,An=`resize${mn}`,En=`click.dismiss${mn}`,Tn=`mousedown.dismiss${mn}`,Cn=`keydown.dismiss${mn}`,On=`click${mn}.data-api`,xn="modal-open",kn="show",Sn="modal-static",Ln={backdrop:!0,focus:!0,keyboard:!0},$n={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class Dn extends Ae{constructor(t,e){super(t,e),this._dialog=Te.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new gn,this._addEventListeners()}static get Default(){return Ln}static get DefaultType(){return $n}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||this._isTransitioning||me.trigger(this._element,yn,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(xn),this._adjustDialog(),this._backdrop.show((()=>this._showElement(t))))}hide(){this._isShown&&!this._isTransitioning&&(me.trigger(this._element,_n).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(kn),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated())))}dispose(){me.off(window,mn),me.off(this._dialog,mn),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new nn({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new hn({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=Te.findOne(".modal-body",this._dialog);e&&(e.scrollTop=0),Ut(this._element),this._element.classList.add(kn),this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,me.trigger(this._element,wn,{relatedTarget:t})}),this._dialog,this._isAnimated())}_addEventListeners(){me.on(this._element,Cn,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),me.on(window,An,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),me.on(this._element,Tn,(t=>{me.one(this._element,En,(e=>{this._element===t.target&&this._element===e.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(xn),this._resetAdjustments(),this._scrollBar.reset(),me.trigger(this._element,vn)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(me.trigger(this._element,bn).defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;"hidden"===e||this._element.classList.contains(Sn)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(Sn),this._queueCallback((()=>{this._element.classList.remove(Sn),this._queueCallback((()=>{this._element.style.overflowY=e}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),i=e>0;if(i&&!t){const t=Yt()?"paddingLeft":"paddingRight";this._element.style[t]=`${e}px`}if(!i&&t){const t=Yt()?"paddingRight":"paddingLeft";this._element.style[t]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each((function(){const i=Dn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===i[t])throw new TypeError(`No method named "${t}"`);i[t](e)}}))}}me.on(document,On,'[data-bs-toggle="modal"]',(function(t){const e=Te.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),me.one(e,yn,(t=>{t.defaultPrevented||me.one(e,vn,(()=>{Bt(this)&&this.focus()}))}));const i=Te.findOne(".modal.show");i&&Dn.getInstance(i).hide(),Dn.getOrCreateInstance(e).toggle(this)})),Ce(Dn),Gt(Dn);const In=".bs.offcanvas",Nn=".data-api",Pn=`load${In}${Nn}`,jn="show",Mn="showing",Fn="hiding",Hn=".offcanvas.show",qn=`show${In}`,zn=`shown${In}`,Wn=`hide${In}`,Bn=`hidePrevented${In}`,Rn=`hidden${In}`,Vn=`resize${In}`,Kn=`click${In}${Nn}`,Un=`keydown.dismiss${In}`,Qn={backdrop:!0,keyboard:!0,scroll:!1},Xn={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Yn extends Ae{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return Qn}static get DefaultType(){return Xn}static get NAME(){return"offcanvas"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){this._isShown||me.trigger(this._element,qn,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._backdrop.show(),this._config.scroll||(new gn).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(Mn),this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(jn),this._element.classList.remove(Mn),me.trigger(this._element,zn,{relatedTarget:t})}),this._element,!0))}hide(){this._isShown&&(me.trigger(this._element,Wn).defaultPrevented||(this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(Fn),this._backdrop.hide(),this._queueCallback((()=>{this._element.classList.remove(jn,Fn),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new gn).reset(),me.trigger(this._element,Rn)}),this._element,!0)))}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=Boolean(this._config.backdrop);return new nn({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{"static"!==this._config.backdrop?this.hide():me.trigger(this._element,Bn)}:null})}_initializeFocusTrap(){return new hn({trapElement:this._element})}_addEventListeners(){me.on(this._element,Un,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():me.trigger(this._element,Bn))}))}static jQueryInterface(t){return this.each((function(){const e=Yn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}me.on(document,Kn,'[data-bs-toggle="offcanvas"]',(function(t){const e=Te.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),Rt(this))return;me.one(e,Rn,(()=>{Bt(this)&&this.focus()}));const i=Te.findOne(Hn);i&&i!==e&&Yn.getInstance(i).hide(),Yn.getOrCreateInstance(e).toggle(this)})),me.on(window,Pn,(()=>{for(const t of Te.find(Hn))Yn.getOrCreateInstance(t).show()})),me.on(window,Vn,(()=>{for(const t of Te.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(t).position&&Yn.getOrCreateInstance(t).hide()})),Ce(Yn),Gt(Yn);const Gn={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],dd:[],div:[],dl:[],dt:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Jn=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Zn=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,ts=(t,e)=>{const i=t.nodeName.toLowerCase();return e.includes(i)?!Jn.has(i)||Boolean(Zn.test(t.nodeValue)):e.filter((t=>t instanceof RegExp)).some((t=>t.test(i)))},es={allowList:Gn,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},is={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},ns={entry:"(string|element|function|null)",selector:"(string|element)"};class ss extends we{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return es}static get DefaultType(){return is}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map((t=>this._resolvePossibleFunction(t))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[e,i]of Object.entries(this._config.content))this._setContent(t,i,e);const e=t.children[0],i=this._resolvePossibleFunction(this._config.extraClass);return i&&e.classList.add(...i.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,i]of Object.entries(t))super._typeCheckConfig({selector:e,entry:i},ns)}_setContent(t,e,i){const n=Te.findOne(i,t);n&&((e=this._resolvePossibleFunction(e))?zt(e)?this._putElementInTemplate(Wt(e),n):this._config.html?n.innerHTML=this._maybeSanitize(e):n.textContent=e:n.remove())}_maybeSanitize(t){return this._config.sanitize?function(t,e,i){if(!t.length)return t;if(i&&"function"==typeof i)return i(t);const n=(new window.DOMParser).parseFromString(t,"text/html"),s=[].concat(...n.body.querySelectorAll("*"));for(const t of s){const i=t.nodeName.toLowerCase();if(!Object.keys(e).includes(i)){t.remove();continue}const n=[].concat(...t.attributes),s=[].concat(e["*"]||[],e[i]||[]);for(const e of n)ts(e,s)||t.removeAttribute(e.nodeName)}return n.body.innerHTML}(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return Jt(t,[this])}_putElementInTemplate(t,e){if(this._config.html)return e.innerHTML="",void e.append(t);e.textContent=t.textContent}}const os=new Set(["sanitize","allowList","sanitizeFn"]),rs="fade",as="show",ls=".tooltip-inner",cs=".modal",hs="hide.bs.modal",us="hover",ds="focus",fs={AUTO:"auto",TOP:"top",RIGHT:Yt()?"left":"right",BOTTOM:"bottom",LEFT:Yt()?"right":"left"},ps={allowList:Gn,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},gs={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class ms extends Ae{constructor(t,e){if(void 0===Pt)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(t,e),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return ps}static get DefaultType(){return gs}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),me.off(this._element.closest(cs),hs,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const t=me.trigger(this._element,this.constructor.eventName("show")),e=(Vt(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();const i=this._getTipElement();this._element.setAttribute("aria-describedby",i.getAttribute("id"));const{container:n}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(n.append(i),me.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(i),i.classList.add(as),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))me.on(t,"mouseover",Kt);this._queueCallback((()=>{me.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(this._isShown()&&!me.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented){if(this._getTipElement().classList.remove(as),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))me.off(t,"mouseover",Kt);this._activeTrigger.click=!1,this._activeTrigger[ds]=!1,this._activeTrigger[us]=!1,this._isHovered=null,this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),me.trigger(this._element,this.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(rs,as),e.classList.add(`bs-${this.constructor.NAME}-auto`);const i=(t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t})(this.constructor.NAME).toString();return e.setAttribute("id",i),this._isAnimated()&&e.classList.add(rs),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new ss({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{[ls]:this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(rs)}_isShown(){return this.tip&&this.tip.classList.contains(as)}_createPopper(t){const e=Jt(this._config.placement,[this,t,this._element]),i=fs[e.toUpperCase()];return Nt(this._element,t,this._getPopperConfig(i))}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_resolvePossibleFunction(t){return Jt(t,[this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:t=>{this._getTipElement().setAttribute("data-popper-placement",t.state.placement)}}]};return{...e,...Jt(this._config.popperConfig,[e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if("click"===e)me.on(this._element,this.constructor.eventName("click"),this._config.selector,(t=>{this._initializeOnDelegatedTarget(t).toggle()}));else if("manual"!==e){const t=e===us?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),i=e===us?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");me.on(this._element,t,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusin"===t.type?ds:us]=!0,e._enter()})),me.on(this._element,i,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusout"===t.type?ds:us]=e._element.contains(t.relatedTarget),e._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},me.on(this._element.closest(cs),hs,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=ye.getDataAttributes(this._element);for(const t of Object.keys(e))os.has(t)&&delete e[t];return t={...e,..."object"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:Wt(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,i]of Object.entries(this._config))this.constructor.Default[e]!==i&&(t[e]=i);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each((function(){const e=ms.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}Gt(ms);const _s=".popover-header",bs=".popover-body",vs={...ms.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},ys={...ms.DefaultType,content:"(null|string|element|function)"};class ws extends ms{static get Default(){return vs}static get DefaultType(){return ys}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{[_s]:this._getTitle(),[bs]:this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each((function(){const e=ws.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}Gt(ws);const As=".bs.scrollspy",Es=`activate${As}`,Ts=`click${As}`,Cs=`load${As}.data-api`,Os="active",xs="[href]",ks=".nav-link",Ss=`${ks}, .nav-item > ${ks}, .list-group-item`,Ls={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},$s={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class Ds extends Ae{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return Ls}static get DefaultType(){return $s}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=Wt(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,"string"==typeof t.threshold&&(t.threshold=t.threshold.split(",").map((t=>Number.parseFloat(t)))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(me.off(this._config.target,Ts),me.on(this._config.target,Ts,xs,(t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const i=this._rootElement||window,n=e.offsetTop-this._element.offsetTop;if(i.scrollTo)return void i.scrollTo({top:n,behavior:"smooth"});i.scrollTop=n}})))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((t=>this._observerCallback(t)),t)}_observerCallback(t){const e=t=>this._targetLinks.get(`#${t.target.id}`),i=t=>{this._previousScrollData.visibleEntryTop=t.target.offsetTop,this._process(e(t))},n=(this._rootElement||document.documentElement).scrollTop,s=n>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=n;for(const o of t){if(!o.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(o));continue}const t=o.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(s&&t){if(i(o),!n)return}else s||t||i(o)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=Te.find(xs,this._config.target);for(const e of t){if(!e.hash||Rt(e))continue;const t=Te.findOne(decodeURI(e.hash),this._element);Bt(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(Os),this._activateParents(t),me.trigger(this._element,Es,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains("dropdown-item"))Te.findOne(".dropdown-toggle",t.closest(".dropdown")).classList.add(Os);else for(const e of Te.parents(t,".nav, .list-group"))for(const t of Te.prev(e,Ss))t.classList.add(Os)}_clearActiveClass(t){t.classList.remove(Os);const e=Te.find(`${xs}.${Os}`,t);for(const t of e)t.classList.remove(Os)}static jQueryInterface(t){return this.each((function(){const e=Ds.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}me.on(window,Cs,(()=>{for(const t of Te.find('[data-bs-spy="scroll"]'))Ds.getOrCreateInstance(t)})),Gt(Ds);const Is=".bs.tab",Ns=`hide${Is}`,Ps=`hidden${Is}`,js=`show${Is}`,Ms=`shown${Is}`,Fs=`click${Is}`,Hs=`keydown${Is}`,qs=`load${Is}`,zs="ArrowLeft",Ws="ArrowRight",Bs="ArrowUp",Rs="ArrowDown",Vs="Home",Ks="End",Us="active",Qs="fade",Xs="show",Ys=".dropdown-toggle",Gs=`:not(${Ys})`,Js='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Zs=`.nav-link${Gs}, .list-group-item${Gs}, [role="tab"]${Gs}, ${Js}`,to=`.${Us}[data-bs-toggle="tab"], .${Us}[data-bs-toggle="pill"], .${Us}[data-bs-toggle="list"]`;class eo extends Ae{constructor(t){super(t),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),me.on(this._element,Hs,(t=>this._keydown(t))))}static get NAME(){return"tab"}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),i=e?me.trigger(e,Ns,{relatedTarget:t}):null;me.trigger(t,js,{relatedTarget:e}).defaultPrevented||i&&i.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){t&&(t.classList.add(Us),this._activate(Te.getElementFromSelector(t)),this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),me.trigger(t,Ms,{relatedTarget:e})):t.classList.add(Xs)}),t,t.classList.contains(Qs)))}_deactivate(t,e){t&&(t.classList.remove(Us),t.blur(),this._deactivate(Te.getElementFromSelector(t)),this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),me.trigger(t,Ps,{relatedTarget:e})):t.classList.remove(Xs)}),t,t.classList.contains(Qs)))}_keydown(t){if(![zs,Ws,Bs,Rs,Vs,Ks].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter((t=>!Rt(t)));let i;if([Vs,Ks].includes(t.key))i=e[t.key===Vs?0:e.length-1];else{const n=[Ws,Rs].includes(t.key);i=te(e,t.target,n,!0)}i&&(i.focus({preventScroll:!0}),eo.getOrCreateInstance(i).show())}_getChildren(){return Te.find(Zs,this._parent)}_getActiveElem(){return this._getChildren().find((t=>this._elemIsActive(t)))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const t of e)this._setInitialAttributesOnChild(t)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),i=this._getOuterElement(t);t.setAttribute("aria-selected",e),i!==t&&this._setAttributeIfNotExists(i,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=Te.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const i=this._getOuterElement(t);if(!i.classList.contains("dropdown"))return;const n=(t,n)=>{const s=Te.findOne(t,i);s&&s.classList.toggle(n,e)};n(Ys,Us),n(".dropdown-menu",Xs),i.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,i){t.hasAttribute(e)||t.setAttribute(e,i)}_elemIsActive(t){return t.classList.contains(Us)}_getInnerElement(t){return t.matches(Zs)?t:Te.findOne(Zs,t)}_getOuterElement(t){return t.closest(".nav-item, .list-group-item")||t}static jQueryInterface(t){return this.each((function(){const e=eo.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}me.on(document,Fs,Js,(function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),Rt(this)||eo.getOrCreateInstance(this).show()})),me.on(window,qs,(()=>{for(const t of Te.find(to))eo.getOrCreateInstance(t)})),Gt(eo);const io=".bs.toast",no=`mouseover${io}`,so=`mouseout${io}`,oo=`focusin${io}`,ro=`focusout${io}`,ao=`hide${io}`,lo=`hidden${io}`,co=`show${io}`,ho=`shown${io}`,uo="hide",fo="show",po="showing",go={animation:"boolean",autohide:"boolean",delay:"number"},mo={animation:!0,autohide:!0,delay:5e3};class _o extends Ae{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return mo}static get DefaultType(){return go}static get NAME(){return"toast"}show(){me.trigger(this._element,co).defaultPrevented||(this._clearTimeout(),this._config.animation&&this._element.classList.add("fade"),this._element.classList.remove(uo),Ut(this._element),this._element.classList.add(fo,po),this._queueCallback((()=>{this._element.classList.remove(po),me.trigger(this._element,ho),this._maybeScheduleHide()}),this._element,this._config.animation))}hide(){this.isShown()&&(me.trigger(this._element,ao).defaultPrevented||(this._element.classList.add(po),this._queueCallback((()=>{this._element.classList.add(uo),this._element.classList.remove(po,fo),me.trigger(this._element,lo)}),this._element,this._config.animation)))}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(fo),super.dispose()}isShown(){return this._element.classList.contains(fo)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}if(e)return void this._clearTimeout();const i=t.relatedTarget;this._element===i||this._element.contains(i)||this._maybeScheduleHide()}_setListeners(){me.on(this._element,no,(t=>this._onInteraction(t,!0))),me.on(this._element,so,(t=>this._onInteraction(t,!1))),me.on(this._element,oo,(t=>this._onInteraction(t,!0))),me.on(this._element,ro,(t=>this._onInteraction(t,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each((function(){const e=_o.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}Ce(_o),Gt(_o);const bo=Object.freeze(Object.defineProperty({__proto__:null,Alert:Se,Button:$e,Carousel:ui,Collapse:Ci,Dropdown:Yi,Modal:Dn,Offcanvas:Yn,Popover:ws,ScrollSpy:Ds,Tab:eo,Toast:_o,Tooltip:ms},Symbol.toStringTag,{value:"Module"}));[].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]')).map((function(t){let e={boundary:"viewport"===t.getAttribute("data-bs-boundary")?document.querySelector(".btn"):"clippingParents"};return new Yi(t,e)})),[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map((function(t){let e={delay:{show:50,hide:50},html:"true"===t.getAttribute("data-bs-html")??!1,placement:t.getAttribute("data-bs-placement")??"auto"};return new ms(t,e)})),[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]')).map((function(t){let e={delay:{show:50,hide:50},html:"true"===t.getAttribute("data-bs-html")??!1,placement:t.getAttribute("data-bs-placement")??"auto"};return new ws(t,e)})),[].slice.call(document.querySelectorAll('[data-bs-toggle="switch-icon"]')).map((function(t){t.addEventListener("click",(e=>{e.stopPropagation(),t.classList.toggle("active")}))})),(()=>{const t=window.location.hash;t&&[].slice.call(document.querySelectorAll('[data-bs-toggle="tab"]')).filter((e=>e.hash===t)).map((t=>{new eo(t).show()}))})(),[].slice.call(document.querySelectorAll('[data-bs-toggle="toast"]')).map((function(t){if(!t.hasAttribute("data-bs-target"))return;const e=new _o(t.getAttribute("data-bs-target"));t.addEventListener("click",(()=>{e.show()}))}));const vo="tblr-",yo=(t,e)=>{const i=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return i?`rgba(${parseInt(i[1],16)}, ${parseInt(i[2],16)}, ${parseInt(i[3],16)}, ${e})`:null},wo=Object.freeze(Object.defineProperty({__proto__:null,getColor:(t,e=1)=>{const i=getComputedStyle(document.body).getPropertyValue(`--${vo}${t}`).trim();return 1!==e?yo(i,e):i},hexToRgba:yo,prefix:vo},Symbol.toStringTag,{value:"Module"}));globalThis.bootstrap=bo,globalThis.tabler=wo}));
//# sourceMappingURL=tabler.min.js.map