{% extends 'basic.html' %}

{% block content %}

<div class="page-header d-print-none mb-4">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="page-title">{% if article.id == 0 %}Создание статьи{% else %}Редактирование статьи{% endif %}</h2>
            <a href="{{ url_for('articles') }}" class="">
                <i class="ti ti-arrow-left"></i>
                Назад
            </a>
        </div>
        <div class="col-auto ms-auto">
            <div class="btn-list">
                <button id="save-article-btn-top" type="button" class="btn btn-primary d-none d-sm-inline-block">
                    <i class="ti ti-check icon icon-1"></i>
                    Сохранить
                </button>
            </div>
        </div>
    </div>
</div>

<form id="main-article-form" method="post" enctype="multipart/form-data">
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    <h5 class="card-title">{% if article.id == 0 %}Создание статьи{% else %}Редактирование статьи{% endif %}</h5>
                </div>
                <div class="card-actions">
                    <div class="btn-group" role="group" aria-label="Язык">
                        <button type="button" class="btn btn-outline-primary" data-lang="all">Все</button>
                        <button type="button" class="btn btn-outline-primary" data-lang="ru">RU</button>
                        <button type="button" class="btn btn-outline-primary" data-lang="uz">UZ</button>
                        <button type="button" class="btn btn-outline-primary" data-lang="en">EN</button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">ID</label>
                    <input type="text" class="form-control" value="{{ article.id }}" readonly>
                </div>
                <div class="mb-3 lang-field lang-en">
                    <label class="form-label">Название</label>
                    <input type="text" class="form-control" name="title" value="{{ article.title or '' }}">
                </div>
                <div class="mb-3 lang-field lang-uz">
                    <label class="form-label">Название (узб)</label>
                    <input type="text" class="form-control" name="title_uz" value="{{ article.title_uz or '' }}">
                </div>
                <div class="mb-3 lang-field lang-ru">
                    <label class="form-label">Название (рус)</label>
                    <input type="text" class="form-control" name="title_ru" value="{{ article.title_ru or '' }}">
                </div>
                <div class="mb-3 lang-field lang-en">
                    <label class="form-label">Аннотация</label>
                    <textarea class="form-control" name="abstract" rows="4">{{ article.abstract or '' }}</textarea>
                </div>
                <div class="mb-3 lang-field lang-uz">
                    <label class="form-label">Аннотация (узб)</label>
                    <textarea class="form-control" name="abstract_uz" rows="4">{{ article.abstract_uz or '' }}</textarea>
                </div>
                <div class="mb-3 lang-field lang-ru">
                    <label class="form-label">Аннотация (рус)</label>
                    <textarea class="form-control" name="abstract_ru" rows="4">{{ article.abstract_ru or '' }}</textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Ключевые слова</label>
                    <input type="text" class="form-control" name="keywords" value="{{ article.keywords|join(', ') if article.keywords else '' }}" placeholder="Разделите запятыми">
                    <div class="form-text">Введите ключевые слова через запятую</div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Выпуск</label>
                    <select class="form-select" name="issue_id">
                        <option value="">Не выбран</option>
                        {% for issue in issues %}
                        <option value="{{ issue.id }}" {% if article.issue_id == issue.id %}selected{% endif %}>{{ issue.vol_no }}-{{ issue.issue_no }}{% if issue.year %} ({{ issue.year }}){% endif %}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Основной автор</label>
                    <select class="form-select" name="main_author_id" id="main_author_id_select">
                        <option value="">Не выбран</option>
                        {% for author in authors %}
                        <option value="{{ author.id }}" {% if article.main_author_id == author.id %}selected{% endif %}>{{ author.name }}{% if author.orcid %} ({{ author.orcid }}){% endif %}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Соавторы</label>
                    <select class="form-select" name="subauthor_ids" multiple id="select-tags">
                        {% for author in authors %}
                        <option value="{{ author.id }}" {% if author.id in article.subauthor_ids %}selected{% endif %}>{{ author.name }}{% if author.orcid %} ({{ author.orcid }}){% endif %}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">DOI</label>
                    <input type="text" class="form-control" name="doi" value="{{ article.doi or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">DOI ссылка</label>
                    <input type="text" class="form-control" name="doi_link" value="{{ article.doi_link or '' }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Дополнительная информация</label>
                    <textarea class="form-control" name="additional" rows="3">{{ article.additional or '' }}</textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Дата отправки</label>
                    <input type="datetime-local" class="form-control" name="date_sent" value="{{ article.date_sent | date_to_form_full }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Дата принятия</label>
                    <input type="datetime-local" class="form-control" name="date_accept" value="{{ article.date_accept | date_to_form_full }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Дата публикации</label>
                    <input type="datetime-local" class="form-control" name="date_publish" value="{{ article.date_publish | date_to_form_full }}">
                </div>
                <div class="mb-3">
                    <label class="form-label">Комментарии</label>
                    <textarea class="form-control" name="comments" rows="3">{{ article.comments or '' }}</textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">ID файлов</label>
                    <input type="text" class="form-control" name="file_ids" value="{{ article.file_ids|join(', ') if article.file_ids else '' }}" placeholder="Разделите запятыми">
                    <div class="form-text">Введите ID файлов через запятую</div>
                </div>
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" name="subscription_enable" id="subscription_enable" {% if article.subscription_enable %}checked{% endif %}>
                    <label class="form-check-label" for="subscription_enable">Подписка включена</label>
                </div>
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" name="is_paid" id="is_paid" {% if article.is_paid %}checked{% endif %}>
                    <label class="form-check-label" for="is_paid">Платная статья</label>
                </div>
                <div id="price-fields">
                    <div class="mb-3">
                        <label class="form-label">Цена</label>
                        <input type="text" class="form-control" name="price" value="{{ article.price or '' }}">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Цена (узб)</label>
                        <input type="text" class="form-control" name="price_uz" value="{{ article.price_uz or '' }}">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Цена (рус)</label>
                        <input type="text" class="form-control" name="price_ru" value="{{ article.price_ru or '' }}">
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Дата создания</label>
                    <input type="datetime-local" class="form-control" name="created_at" value="{{ article.created_at | date_to_form_full }}">
                </div>
                <div class="d-flex justify-content-end">
                    <button id="save-article-btn" type="submit" class="btn btn-primary">Сохранить</button>
                </div>
            </div>
        </div>
    </div>
</div>
</form>

{% endblock %}

{% block scripts %}
<script>
function setLang(lang) {
    if (lang === 'all') {
        document.querySelectorAll('.lang-field').forEach(e => e.style.display = '');
    } else {
        document.querySelectorAll('.lang-field').forEach(e => e.style.display = 'none');
        document.querySelectorAll('.lang-' + lang).forEach(e => e.style.display = '');
    }
    document.querySelectorAll('.btn-group [data-lang]').forEach(btn => btn.classList.remove('active'));
    document.querySelector('.btn-group [data-lang="' + lang + '"]').classList.add('active');
}
document.querySelectorAll('.btn-group [data-lang]').forEach(btn => {
    btn.addEventListener('click', function() {
        setLang(this.getAttribute('data-lang'));
    });
});
setLang('all');
document.getElementById('save-article-btn-top').addEventListener('click', function() {
    document.getElementById('save-article-btn').click();
});
document.addEventListener("DOMContentLoaded", function () {
    var el;
    window.TomSelect &&
      new TomSelect((el = document.getElementById("select-tags")), {
        copyClassesToDropdown: false,
        dropdownParent: "body",
        controlInput: "<input>",
        render: {
          item: function (data, escape) {
            if (data.customProperties) {
              return '<div><span class="dropdown-item-indicator">' + data.customProperties + "</span>" + escape(data.text) + "</div>";
            }
            return "<div>" + escape(data.text) + "</div>";
          },
          option: function (data, escape) {
            if (data.customProperties) {
              return '<div><span class="dropdown-item-indicator">' + data.customProperties + "</span>" + escape(data.text) + "</div>";
            }
            return "<div>" + escape(data.text) + "</div>";
          },
        },
      });
    // Скрывать выбранного главного автора из соавторов
    var mainAuthorSelect = document.getElementById('main_author_id_select');
    var subAuthorsSelect = document.getElementById('select-tags');
    function updateSubAuthors() {
        var mainId = mainAuthorSelect.value;
        Array.from(subAuthorsSelect.options).forEach(function(opt) {
            opt.style.display = (opt.value && opt.value === mainId) ? 'none' : '';
        });
        // Если выбранный главный автор был выбран как соавтор — снять выделение
        if (mainId) {
            var selected = Array.from(subAuthorsSelect.selectedOptions).map(o => o.value);
            if (selected.includes(mainId)) {
                subAuthorsSelect.querySelector('option[value="' + mainId + '"]').selected = false;
                if (window.TomSelect && subAuthorsSelect.tomselect) subAuthorsSelect.tomselect.removeItem(mainId);
            }
        }
    }
    mainAuthorSelect.addEventListener('change', updateSubAuthors);
    updateSubAuthors();
    // Скрывать/показывать поля цены в зависимости от чекбокса "Платная статья"
    function togglePriceFields() {
        var isPaid = document.getElementById('is_paid').checked;
        document.getElementById('price-fields').style.display = isPaid ? '' : 'none';
    }
    document.getElementById('is_paid').addEventListener('change', togglePriceFields);
    togglePriceFields();
});
</script>
{% endblock %}
