{% extends 'basic.html' %}

{% block content %}
<div class="page-header d-print-none mb-4">
    <div class="row align-items-center">
        <div class="col">
            <h2 class="page-title">{% if announcement_id == 0 %}Создание объявления{% else %}Редактирование объявления{% endif %}</h2>
            <a href="{{ url_for('announcements') }}" class="">
                <i class="ti ti-arrow-left"></i>
                Назад
            </a>
        </div>
    </div>
</div>

<div class="card mt-4">
  <div class="card-header">
    <div class="card-title">
      <h5 class="card-title">{% if announcement_id == 0 %}Создание объявления{% else %}Редактирование объявления{% endif %}</h5>
    </div>
    <div class="card-actions">
      <div class="btn-group" role="group" aria-label="Язык">
        <button type="button" class="btn btn-outline-primary" data-lang="all">Все</button>
        <button type="button" class="btn btn-outline-primary" data-lang="ru">RU</button>
        <button type="button" class="btn btn-outline-primary" data-lang="uz">UZ</button>
        <button type="button" class="btn btn-outline-primary" data-lang="en">EN</button>
        </div>
    </div>
</div>
    <div class="card-body">
    <form method="post" enctype="multipart/form-data" action="{{ url_for('announcement_edit', announcement_id=announcement_id) }}">
      <div class="mb-3 lang-field lang-en">
        <label for="title_en" class="form-label">Название (en)</label>
        <input type="text" class="form-control" id="title_en" name="title_en" value="{{ announcement.title }}" required>
      </div>
      <div class="mb-3 lang-field lang-ru">
        <label for="title_ru" class="form-label">Название (ru)</label>
        <input type="text" class="form-control" id="title_ru" name="title_ru" value="{{ announcement.title_ru }}" required>
      </div>
      <div class="mb-3 lang-field lang-uz">
        <label for="title_uz" class="form-label">Название (uz)</label>
        <input type="text" class="form-control" id="title_uz" name="title_uz" value="{{ announcement.title_uz }}" required>
                </div>
      <div class="mb-3 lang-field lang-en">
        <label for="content_en" class="form-label">Контент (en)</label>
        <textarea class="form-control" id="content_en" name="content_en" rows="10">{{ announcement.content }}</textarea>
                </div>
      <div class="mb-3 lang-field lang-ru">
        <label for="content_ru" class="form-label">Контент (ru)</label>
        <textarea class="form-control" id="content_ru" name="content_ru" rows="10">{{ announcement.content_ru }}</textarea>
                </div>
      <div class="mb-3 lang-field lang-uz">
        <label for="content_uz" class="form-label">Контент (uz)</label>
        <textarea class="form-control" id="content_uz" name="content_uz" rows="10">{{ announcement.content_uz }}</textarea>
            </div>
            <div class="mb-3">
        <label for="status" class="form-label">Статус</label>
        <select class="form-select" id="status" name="status" required>
                    <option value="draft" {% if announcement.status == 'draft' %}selected{% endif %}>Черновик</option>
                    <option value="published" {% if announcement.status == 'published' %}selected{% endif %}>Опубликовано</option>
                    <option value="archived" {% if announcement.status == 'archived' %}selected{% endif %}>Архив</option>
                </select>
            </div>
            <div class="mb-3">
        <label for="published_at" class="form-label">Дата публикации</label>
        <input type="date" class="form-control" id="published_at" name="published_at" value="{{ announcement.published_at|date_to_form }}" required>
            </div>
            <div class="mb-3">
        <label for="cover_image" class="form-label">Обложка (загрузить)</label>
        <input type="file" class="form-control" id="cover_image" name="cover_image" accept="image/*">
                {% if announcement.cover_image %}
        <div class="mt-2">
          <img src="{{ announcement.cover_image }}" alt="Обложка" style="max-width: 200px; max-height: 200px; border: 1px solid #ccc;"/>
        </div>
                {% endif %}
            </div>
      <div class="mt-3">
        <button type="submit" class="btn btn-primary">Сохранить</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
  ClassicEditor.create(document.getElementById('content_en'));
  ClassicEditor.create(document.getElementById('content_ru'));
  ClassicEditor.create(document.getElementById('content_uz'));
  function setLang(lang) {
    document.querySelectorAll('.lang-field').forEach(e => e.style.display = 'none');
    if (lang === 'all') {
      document.querySelectorAll('.lang-field').forEach(e => e.style.display = '');
    } else {
      document.querySelectorAll('.lang-' + lang).forEach(e => e.style.display = '');
    }
    document.querySelectorAll('.btn-group [data-lang]').forEach(btn => btn.classList.remove('active'));
    document.querySelector('.btn-group [data-lang="' + lang + '"]').classList.add('active');
  }
  document.querySelectorAll('.btn-group [data-lang]').forEach(btn => {
    btn.addEventListener('click', function() {
      setLang(this.getAttribute('data-lang'));
    });
  });
  setLang('all');
</script>
{% endblock %} 