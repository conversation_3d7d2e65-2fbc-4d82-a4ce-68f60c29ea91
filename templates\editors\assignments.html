{% extends "basic.html" %}

{% block content %}
<div class="page-header d-print-none">
  <div class="container-xl">
    <div class="row g-2 align-items-center">
      <div class="col">
        <h2 class="page-title">
          Назначения редакторам
        </h2>
      </div>
    </div>
  </div>
</div>

<div class="page-body">
  <div class="container-xl">
    <div class="row row-deck row-cards">
      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Фильтры</h3>
          </div>
          <div class="card-body">
            <form method="GET" action="{{ url_for('editor_assignments') }}">
              <div class="row">
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">Статус</label>
                    <select class="form-select" name="status">
                      <option value="">Все статусы</option>
                      <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Ожидает проверки</option>
                      <option value="reviewed" {% if status_filter == 'reviewed' %}selected{% endif %}>Проверено</option>
                      <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Отклонено</option>
                    </select>
                  </div>
                </div>
                {% if current_user.rolename == 'admin' %}
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">Редактор</label>
                    <select class="form-select" name="editor">
                      <option value="">Все редакторы</option>
                      {% for editor in editors %}
                      <option value="{{ editor.id }}" {% if editor_filter == editor.id|string %}selected{% endif %}>
                        {{ editor.name }} {{ editor.second_name or '' }}
                      </option>
                      {% endfor %}
                    </select>
                  </div>
                </div>
                {% endif %}
                <div class="col-md-4">
                  <div class="mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div>
                      <button type="submit" class="btn btn-primary">Найти</button>
                      <a href="{{ url_for('editor_assignments') }}" class="btn btn-secondary">Сбросить</a>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>

      <div class="col-12">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">Список назначений ({{ total_assignments }})</h3>
          </div>
          <div class="table-responsive">
            <table class="table table-vcenter card-table">
              <thead>
                <tr>
                  <th>ID</th>
                  <th>Статья</th>
                  <th>Редактор</th>
                  <th>Назначено</th>
                  <th>Статус</th>
                  <th>Проверено</th>
                  <th>Действия</th>
                </tr>
              </thead>
              <tbody>
                {% for assignment in assignments %}
                {% set submission = submissions_map.get(assignment.submission_id) %}
                {% set editor = editors_map.get(assignment.editor_id) %}
                {% set assigned_by_user = users_map.get(assignment.assigned_by) %}
                <tr>
                  <td>{{ assignment.id }}</td>
                  <td>
                    <div class="d-flex flex-column">
                      <div class="font-weight-medium">{{ submission.title if submission else 'Статья не найдена' }}</div>
                      {% if submission %}
                      <div class="text-secondary small">ID: {{ submission.id }}</div>
                      {% endif %}
                    </div>
                  </td>
                  <td>
                    {% if editor %}
                    <div class="d-flex py-1 align-items-center">
                      <span class="avatar me-2">{{ editor.name[0] if editor.name else 'E' }}</span>
                      <div class="flex-fill">
                        <div class="font-weight-medium">{{ editor.name }} {{ editor.second_name or '' }}</div>
                        <div class="text-secondary small">{{ editor.editor_specialization or 'Специализация не указана' }}</div>
                      </div>
                    </div>
                    {% else %}
                    <span class="text-secondary">Редактор не найден</span>
                    {% endif %}
                  </td>
                  <td>
                    <div class="d-flex flex-column">
                      <div class="text-secondary small">
                        {{ assignment.assigned_at | timestamp_to_date if assignment.assigned_at else '-' }}
                      </div>
                      {% if assigned_by_user %}
                      <div class="text-secondary small">
                        {{ assigned_by_user.name }} {{ assigned_by_user.second_name or '' }}
                      </div>
                      {% endif %}
                    </div>
                  </td>
                  <td>
                    {% if assignment.status == 'pending' %}
                    <span class="badge bg-warning-lt">Ожидает проверки</span>
                    {% elif assignment.status == 'reviewed' %}
                    <span class="badge bg-success-lt">Проверено</span>
                    {% elif assignment.status == 'rejected' %}
                    <span class="badge bg-danger-lt">Отклонено</span>
                    {% else %}
                    <span class="badge bg-secondary-lt">{{ assignment.status }}</span>
                    {% endif %}
                  </td>
                  <td>
                    {% if assignment.reviewed_at %}
                    <div class="text-secondary small">
                      {{assignment.reviewed_at | timestamp_to_date }}
                    </div>
                    {% else %}
                    <span class="text-secondary">-</span>
                    {% endif %}
                  </td>
                  <td>
                    <div class="btn-list flex-nowrap">
                      {% if current_user.rolename == 'editor' and assignment.editor_id == current_user.id and assignment.status == 'pending' %}
                      <a href="{{ url_for('review_assignment', assignment_id=assignment.id) }}" class="btn btn-primary btn-sm">
                        Проверить
                      </a>
                      {% elif assignment.status != 'pending' %}
                      <a href="{{ url_for('review_assignment', assignment_id=assignment.id) }}" class="btn btn-white btn-sm">
                        Просмотр
                      </a>
                      {% endif %}
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
          
          {% if total_pages > 1 %}
          <div class="card-footer d-flex align-items-center">
            <p class="m-0 text-secondary">Показано {{ assignments|length }} из {{ total_assignments }} записей</p>
            <ul class="pagination m-0 ms-auto">
              {% if page > 1 %}
              <li class="page-item">
                <a class="page-link" href="{{ url_for('editor_assignments', page=page-1, status=status_filter, editor=editor_filter) }}">
                  <i class="ti ti-chevron-left"></i>
                  Назад
                </a>
              </li>
              {% endif %}
              
              {% for p in range(1, total_pages + 1) %}
                {% if p == page %}
                <li class="page-item active">
                  <a class="page-link" href="#">{{ p }}</a>
                </li>
                {% elif p <= 3 or p >= total_pages - 2 or (p >= page - 1 and p <= page + 1) %}
                <li class="page-item">
                  <a class="page-link" href="{{ url_for('editor_assignments', page=p, status=status_filter, editor=editor_filter) }}">{{ p }}</a>
                </li>
                {% elif p == 4 and page > 6 %}
                <li class="page-item disabled">
                  <span class="page-link">…</span>
                </li>
                {% elif p == total_pages - 3 and page < total_pages - 5 %}
                <li class="page-item disabled">
                  <span class="page-link">…</span>
                </li>
                {% endif %}
              {% endfor %}
              
              {% if page < total_pages %}
              <li class="page-item">
                <a class="page-link" href="{{ url_for('editor_assignments', page=page+1, status=status_filter, editor=editor_filter) }}">
                  Вперед
                  <i class="ti ti-chevron-right"></i>
                </a>
              </li>
              {% endif %}
            </ul>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<script>
// Добавляем moment.js для форматирования дат
function moment(timestamp) {
  if (!timestamp) return { format: () => '-' };
  const date = new Date(timestamp * 1000);
  return {
    format: (format) => {
      if (format === 'DD.MM.YYYY HH:mm') {
        return date.toLocaleString('ru-RU', {
          day: '2-digit',
          month: '2-digit', 
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        });
      }
      return date.toLocaleDateString('ru-RU');
    }
  };
}
</script>
{% endblock %}
