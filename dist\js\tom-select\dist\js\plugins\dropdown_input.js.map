{"version": 3, "file": "dropdown_input.js", "sources": ["../../../src/constants.ts", "../../../src/utils.ts", "../../../src/vanilla.ts", "../../../src/plugins/dropdown_input/plugin.ts"], "sourcesContent": ["export const KEY_A\t\t\t\t= 65;\nexport const KEY_RETURN\t\t\t= 13;\nexport const KEY_ESC\t\t\t= 27;\nexport const KEY_LEFT\t\t\t= 37;\nexport const KEY_UP\t\t\t\t= 38;\nexport const KEY_RIGHT\t\t\t= 39;\nexport const KEY_DOWN\t\t\t= 40;\nexport const KEY_BACKSPACE\t\t= 8;\nexport const KEY_DELETE\t\t\t= 46;\nexport const KEY_TAB\t\t\t= 9;\n\nexport const IS_MAC      \t\t= typeof navigator === 'undefined' ? false : /Mac/.test(navigator.userAgent);\nexport const KEY_SHORTCUT\t\t= IS_MAC ? 'metaKey' : 'ctrlKey'; // ctrl key or apple key for ma\n", "\nimport type TomSelect from './tom-select.ts';\nimport { TomLoadCallback } from './types/index.ts';\n\n\n/**\n * Converts a scalar to its best string representation\n * for hash keys and HTML attribute values.\n *\n * Transformations:\n *   'str'     -> 'str'\n *   null      -> ''\n *   undefined -> ''\n *   true      -> '1'\n *   false     -> '0'\n *   0         -> '0'\n *   1         -> '1'\n *\n */\nexport const hash_key = (value:undefined|null|boolean|string|number):string|null => {\n\tif (typeof value === 'undefined' || value === null) return null;\n\treturn get_hash(value);\n};\n\nexport const get_hash = (value:boolean|string|number):string => {\n\tif (typeof value === 'boolean') return value ? '1' : '0';\n\treturn value + '';\n};\n\n/**\n * Escapes a string for use within HTML.\n *\n */\nexport const escape_html = (str:string):string => {\n\treturn (str + '')\n\t\t.replace(/&/g, '&amp;')\n\t\t.replace(/</g, '&lt;')\n\t\t.replace(/>/g, '&gt;')\n\t\t.replace(/\"/g, '&quot;');\n};\n\n\n/**\n * use setTimeout if timeout > 0 \n */\nexport const timeout = (fn:()=>void,timeout:number): number | null => {\n\tif( timeout > 0 ){\n\t\treturn window.setTimeout(fn,timeout);\n\t}\n\n\tfn.call(null);\n\treturn null;\n}\n\n/**\n * Debounce the user provided load function\n *\n */\nexport const loadDebounce = (fn:(value:string,callback:TomLoadCallback) => void,delay:number) => {\n\tvar timeout: null|ReturnType<typeof setTimeout>;\n\treturn function(this:TomSelect, value:string,callback:TomLoadCallback) {\n\t\tvar self = this;\n\n\t\tif( timeout ){\n\t\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\t\tclearTimeout(timeout);\n\t\t}\n\t\ttimeout = setTimeout(function() {\n\t\t\ttimeout = null;\n\t\t\tself.loadedSearches[value] = true;\n\t\t\tfn.call(self, value, callback);\n\n\t\t}, delay);\n\t};\n};\n\n\n/**\n * Debounce all fired events types listed in `types`\n * while executing the provided `fn`.\n *\n */\nexport const debounce_events = ( self:TomSelect, types:string[], fn:() => void ) => {\n\tvar type:string;\n\tvar trigger = self.trigger;\n\tvar event_args:{ [key: string]: any } = {};\n\n\t// override trigger method\n\tself.trigger = function(){\n\t\tvar type = arguments[0];\n\t\tif (types.indexOf(type) !== -1) {\n\t\t\tevent_args[type] = arguments;\n\t\t} else {\n\t\t\treturn trigger.apply(self, arguments);\n\t\t}\n\t};\n\n\t// invoke provided function\n\tfn.apply(self, []);\n\tself.trigger = trigger;\n\n\t// trigger queued events\n\tfor( type of types ){\n\t\tif( type in event_args ){\n\t\t\ttrigger.apply(self, event_args[type]);\n\t\t}\n\t}\n};\n\n\n/**\n * Determines the current selection within a text input control.\n * Returns an object containing:\n *   - start\n *   - length\n *\n * Note: \"selectionStart, selectionEnd ... apply only to inputs of types text, search, URL, tel and password\"\n * \t- https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange\n */\nexport const getSelection = (input:HTMLInputElement):{ start: number; length: number } => {\n\treturn {\n\t\tstart\t: input.selectionStart || 0,\n\t\tlength\t: (input.selectionEnd||0) - (input.selectionStart||0),\n\t};\n};\n\n\n/**\n * Prevent default\n *\n */\nexport const preventDefault = (evt?:Event, stop:boolean=false):void => {\n\tif( evt ){\n\t\tevt.preventDefault();\n\t\tif( stop ){\n\t\t\tevt.stopPropagation();\n\t\t}\n\t}\n}\n\n\n/**\n * Add event helper\n *\n */\nexport const addEvent = (target:EventTarget, type:string, callback:EventListenerOrEventListenerObject, options?:object):void => {\n\ttarget.addEventListener(type,callback,options);\n};\n\n\n/**\n * Return true if the requested key is down\n * Will return false if more than one control character is pressed ( when [ctrl+shift+a] != [ctrl+a] )\n * The current evt may not always set ( eg calling advanceSelection() )\n *\n */\nexport const isKeyDown = ( key_name:keyof (KeyboardEvent|MouseEvent), evt?:KeyboardEvent|MouseEvent ) => {\n\n\tif( !evt ){\n\t\treturn false;\n\t}\n\n\tif( !evt[key_name] ){\n\t\treturn false;\n\t}\n\n\tvar count = (evt.altKey?1:0) + (evt.ctrlKey?1:0) + (evt.shiftKey?1:0) + (evt.metaKey?1:0);\n\n\tif( count === 1 ){\n\t\treturn true;\n\t}\n\n\treturn false;\n};\n\n\n/**\n * Get the id of an element\n * If the id attribute is not set, set the attribute with the given id\n *\n */\nexport const getId = (el:Element,id:string) => {\n\tconst existing_id = el.getAttribute('id');\n\tif( existing_id ){\n\t\treturn existing_id;\n\t}\n\n\tel.setAttribute('id',id);\n\treturn id;\n};\n\n\n/**\n * Returns a string with backslashes added before characters that need to be escaped.\n */\nexport const addSlashes = (str:string):string => {\n\treturn str.replace(/[\\\\\"']/g, '\\\\$&');\n};\n\n/**\n *\n */\nexport const append = ( parent:Element|DocumentFragment, node: string|Node|null|undefined ):void =>{\n\tif( node ) parent.append(node);\n};\n\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\nexport const iterate = (object:[]|{[key:string]:any}, callback:(value:any,key:any)=>any) => {\n\n\tif ( Array.isArray(object)) {\n\t\tobject.forEach(callback);\n\n\t}else{\n\n\t\tfor (var key in object) {\n\t\t\tif (object.hasOwnProperty(key)) {\n\t\t\t\tcallback(object[key], key);\n\t\t\t}\n\t\t}\n\t}\n};\n", "\nimport { iterate } from './utils.ts';\n\n/**\n * Return a dom element from either a dom query string, jQuery object, a dom element or html string\n * https://stackoverflow.com/questions/494143/creating-a-new-dom-element-from-an-html-string-using-built-in-dom-methods-or-pro/35385518#35385518\n *\n * param query should be {}\n */\nexport const getDom = ( query:any ):HTMLElement => {\n\n\tif( query.jquery ){\n\t\treturn query[0];\n\t}\n\n\tif( query instanceof HTMLElement ){\n\t\treturn query;\n\t}\n\n\tif( isHtmlString(query) ){\n\t\tvar tpl = document.createElement('template');\n\t\ttpl.innerHTML = query.trim(); // Never return a text node of whitespace as the result\n\t\treturn tpl.content.firstChild as HTMLElement;\n\t}\n\n\treturn document.querySelector(query);\n};\n\nexport const isHtmlString = (arg:any): boolean => {\n\tif( typeof arg === 'string' && arg.indexOf('<') > -1 ){\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nexport const escapeQuery = (query:string):string => {\n\treturn query.replace(/['\"\\\\]/g, '\\\\$&');\n}\n\n/**\n * Dispatch an event\n *\n */\nexport const triggerEvent = ( dom_el:HTMLElement, event_name:string ):void => {\n\tvar event = document.createEvent('HTMLEvents');\n\tevent.initEvent(event_name, true, false);\n\tdom_el.dispatchEvent(event)\n};\n\n/**\n * Apply CSS rules to a dom element\n *\n */\nexport const applyCSS = ( dom_el:HTMLElement, css:{ [key: string]: string|number }):void => {\n\tObject.assign(dom_el.style, css);\n}\n\n\n/**\n * Add css classes\n *\n */\nexport const addClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n\tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map( cls => {\n\t\t\tel.classList.add( cls );\n\t\t});\n\t});\n}\n\n/**\n * Remove css classes\n *\n */\n export const removeClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n \tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map(cls => {\n\t \t\tel.classList.remove( cls );\n\t\t});\n \t});\n }\n\n\n/**\n * Return arguments\n *\n */\nexport const classesArray = (args:string[]|string[][]):string[] => {\n\tvar classes:string[] = [];\n\titerate( args, (_classes) =>{\n\t\tif( typeof _classes === 'string' ){\n\t\t\t_classes = _classes.trim().split(/[\\t\\n\\f\\r\\s]/);\n\t\t}\n\t\tif( Array.isArray(_classes) ){\n\t\t\tclasses = classes.concat(_classes);\n\t\t}\n\t});\n\n\treturn classes.filter(Boolean);\n}\n\n\n/**\n * Create an array from arg if it's not already an array\n *\n */\nexport const castAsArray = (arg:any):Array<any> => {\n\tif( !Array.isArray(arg) ){\n \t\targ = [arg];\n \t}\n\treturn arg;\n}\n\n\n/**\n * Get the closest node to the evt.target matching the selector\n * Stops at wrapper\n *\n */\nexport const parentMatch = ( target:null|HTMLElement, selector:string, wrapper?:HTMLElement ):HTMLElement|void => {\n\n\tif( wrapper && !wrapper.contains(target) ){\n\t\treturn;\n\t}\n\n\twhile( target && target.matches ){\n\n\t\tif( target.matches(selector) ){\n\t\t\treturn target;\n\t\t}\n\n\t\ttarget = target.parentNode as HTMLElement;\n\t}\n}\n\n\n/**\n * Get the first or last item from an array\n *\n * > 0 - right (last)\n * <= 0 - left (first)\n *\n */\nexport const getTail = ( list:Array<any>|NodeList, direction:number=0 ):any => {\n\n\tif( direction > 0 ){\n\t\treturn list[list.length-1];\n\t}\n\n\treturn list[0];\n}\n\n/**\n * Return true if an object is empty\n *\n */\nexport const isEmptyObject = (obj:object):boolean => {\n\treturn (Object.keys(obj).length === 0);\n}\n\n\n/**\n * Get the index of an element amongst sibling nodes of the same type\n *\n */\nexport const nodeIndex = ( el:null|Element, amongst?:string ):number => {\n\tif (!el) return -1;\n\n\tamongst = amongst || el.nodeName;\n\n\tvar i = 0;\n\twhile( el = el.previousElementSibling ){\n\n\t\tif( el.matches(amongst) ){\n\t\t\ti++;\n\t\t}\n\t}\n\treturn i;\n}\n\n\n/**\n * Set attributes of an element\n *\n */\nexport const setAttr = (el:Element,attrs:{ [key: string]: null|string|number }) => {\n\titerate( attrs,(val,attr) => {\n\t\tif( val == null ){\n\t\t\tel.removeAttribute(attr as string);\n\t\t}else{\n\t\t\tel.setAttribute(attr as string, ''+val);\n\t\t}\n\t});\n}\n\n\n/**\n * Replace a node\n */\nexport const replaceNode = ( existing:Node, replacement:Node ) => {\n\tif( existing.parentNode ) existing.parentNode.replaceChild(replacement, existing);\n}\n", "/**\n * Plugin: \"dropdown_input\" (<PERSON>)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport type TomSelect from '../../tom-select.ts';\nimport * as constants from '../../constants.ts';\nimport { getDom, addClasses } from '../../vanilla.ts';\nimport { addEvent, preventDefault } from '../../utils.ts';\n\n\nexport default function(this:TomSelect) {\n\tconst self = this;\n\n\tself.settings.shouldOpen = true; // make sure the input is shown even if there are no options to display in the dropdown\n\n\tself.hook('before','setup',()=>{\n\t\tself.focus_node\t\t= self.control;\n\n\t\taddClasses( self.control_input, 'dropdown-input');\n\n\t \tconst div = getDom('<div class=\"dropdown-input-wrap\">');\n\t\tdiv.append(self.control_input);\n\t\tself.dropdown.insertBefore(div, self.dropdown.firstChild);\n\n\t\t// set a placeholder in the select control\n\t\tconst placeholder = getDom('<input class=\"items-placeholder\" tabindex=\"-1\" />') as HTMLInputElement;\n\t\tplaceholder.placeholder = self.settings.placeholder ||'';\n\t\tself.control.append(placeholder);\n\n\t});\n\n\n\tself.on('initialize',()=>{\n\n\t\t// set tabIndex on control to -1, otherwise [shift+tab] will put focus right back on control_input\n\t\tself.control_input.addEventListener('keydown',(evt:KeyboardEvent) =>{\n\t\t//addEvent(self.control_input,'keydown' as const,(evt:KeyboardEvent) =>{\n\t\t\tswitch( evt.keyCode ){\n\t\t\t\tcase constants.KEY_ESC:\n\t\t\t\t\tif (self.isOpen) {\n\t\t\t\t\t\tpreventDefault(evt,true);\n\t\t\t\t\t\tself.close();\n\t\t\t\t\t}\n\t\t\t\t\tself.clearActiveItems();\n\t\t\t\treturn;\n\t\t\t\tcase constants.KEY_TAB:\n\t\t\t\t\tself.focus_node.tabIndex = -1;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\treturn self.onKeyDown.call(self,evt);\n\t\t});\n\n\t\tself.on('blur',()=>{\n\t\t\tself.focus_node.tabIndex = self.isDisabled ? -1 : self.tabIndex;\n\t\t});\n\n\n\t\t// give the control_input focus when the dropdown is open\n\t\tself.on('dropdown_open',() =>{\n\t\t\tself.control_input.focus();\n\t\t});\n\n\t\t// prevent onBlur from closing when focus is on the control_input\n\t\tconst orig_onBlur = self.onBlur;\n\t\tself.hook('instead','onBlur',(evt?:FocusEvent)=>{\n\t\t\tif( evt && evt.relatedTarget == self.control_input ) return;\n\t\t\treturn orig_onBlur.call(self);\n\t\t});\n\n\t\taddEvent(self.control_input,'blur', () => self.onBlur() );\n\n\t\t// return focus to control to allow further keyboard input\n\t\tself.hook('before','close',() =>{\n\n\t\t\tif( !self.isOpen ) return;\n\t\t\tself.focus_node.focus({preventScroll: true});\n\t\t});\n\n\t});\n\n};\n"], "names": ["KEY_ESC", "KEY_TAB", "preventDefault", "evt", "stop", "stopPropagation", "addEvent", "target", "type", "callback", "options", "addEventListener", "iterate", "object", "Array", "isArray", "for<PERSON>ach", "key", "hasOwnProperty", "getDom", "query", "j<PERSON>y", "HTMLElement", "isHtmlString", "tpl", "document", "createElement", "innerHTML", "trim", "content", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "arg", "indexOf", "addClasses", "elmts", "classes", "norm_classes", "classesArray", "cast<PERSON><PERSON><PERSON><PERSON>", "map", "el", "cls", "classList", "add", "args", "_classes", "split", "concat", "filter", "Boolean", "self", "settings", "shouldOpen", "hook", "focus_node", "control", "control_input", "div", "append", "dropdown", "insertBefore", "placeholder", "on", "keyCode", "constants", "isOpen", "close", "clearActiveItems", "tabIndex", "onKeyDown", "call", "isDisabled", "focus", "orig_onBlur", "onBlur", "relatedTarget", "preventScroll"], "mappings": ";;;;;;;;;;;CAEO,MAAMA,OAAO,GAAK,EAAE;CAOpB,MAAMC,OAAO,GAAK,CAAC;CAGkC;;CCP5D;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CA6GA;CACA;CACA;CACA;CACO,MAAMC,cAAc,GAAGA,CAACC,GAAU,EAAEC,IAAY,GAAC,KAAK,KAAU;CACtE,EAAA,IAAID,GAAG,EAAE;KACRA,GAAG,CAACD,cAAc,EAAE;CACpB,IAAA,IAAIE,IAAI,EAAE;OACTD,GAAG,CAACE,eAAe,EAAE;CACtB;CACD;CACD,CAAC;;CAGD;CACA;CACA;CACA;CACO,MAAMC,QAAQ,GAAGA,CAACC,MAAkB,EAAEC,IAAW,EAAEC,QAA2C,EAAEC,OAAe,KAAU;GAC/HH,MAAM,CAACI,gBAAgB,CAACH,IAAI,EAACC,QAAQ,EAACC,OAAO,CAAC;CAC/C,CAAC;;CA2DD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,MAAME,OAAO,GAAGA,CAACC,MAA4B,EAAEJ,QAAiC,KAAK;CAE3F,EAAA,IAAKK,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;CAC3BA,IAAAA,MAAM,CAACG,OAAO,CAACP,QAAQ,CAAC;CAEzB,GAAC,MAAI;CAEJ,IAAA,KAAK,IAAIQ,GAAG,IAAIJ,MAAM,EAAE;CACvB,MAAA,IAAIA,MAAM,CAACK,cAAc,CAACD,GAAG,CAAC,EAAE;CAC/BR,QAAAA,QAAQ,CAACI,MAAM,CAACI,GAAG,CAAC,EAAEA,GAAG,CAAC;CAC3B;CACD;CACD;CACD,CAAC;;CClOD;CACA;CACA;CACA;CACA;CACA;CACO,MAAME,MAAM,GAAKC,KAAS,IAAkB;GAElD,IAAIA,KAAK,CAACC,MAAM,EAAE;KACjB,OAAOD,KAAK,CAAC,CAAC,CAAC;CAChB;GAEA,IAAIA,KAAK,YAAYE,WAAW,EAAE;CACjC,IAAA,OAAOF,KAAK;CACb;CAEA,EAAA,IAAIG,YAAY,CAACH,KAAK,CAAC,EAAE;CACxB,IAAA,IAAII,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;KAC5CF,GAAG,CAACG,SAAS,GAAGP,KAAK,CAACQ,IAAI,EAAE,CAAC;CAC7B,IAAA,OAAOJ,GAAG,CAACK,OAAO,CAACC,UAAU;CAC9B;CAEA,EAAA,OAAOL,QAAQ,CAACM,aAAa,CAACX,KAAK,CAAC;CACrC,CAAC;CAEM,MAAMG,YAAY,GAAIS,GAAO,IAAc;CACjD,EAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;CACrD,IAAA,OAAO,IAAI;CACZ;CACA,EAAA,OAAO,KAAK;CACb,CAAC;;CAyBD;CACA;CACA;CACA;CACO,MAAMC,UAAU,GAAGA,CAAEC,KAA+B,EAAE,GAAGC,OAA2B,KAAM;CAEhG,EAAA,IAAIC,YAAY,GAAIC,YAAY,CAACF,OAAO,CAAC;CACzCD,EAAAA,KAAK,GAAMI,WAAW,CAACJ,KAAK,CAAC;CAE7BA,EAAAA,KAAK,CAACK,GAAG,CAAEC,EAAE,IAAI;CAChBJ,IAAAA,YAAY,CAACG,GAAG,CAAEE,GAAG,IAAI;CACxBD,MAAAA,EAAE,CAACE,SAAS,CAACC,GAAG,CAAEF,GAAI,CAAC;CACxB,KAAC,CAAC;CACH,GAAC,CAAC;CACH,CAAC;;CAmBD;CACA;CACA;CACA;CACO,MAAMJ,YAAY,GAAIO,IAAwB,IAAc;GAClE,IAAIT,OAAgB,GAAG,EAAE;CACzBxB,EAAAA,OAAO,CAAEiC,IAAI,EAAGC,QAAQ,IAAI;CAC3B,IAAA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;OACjCA,QAAQ,GAAGA,QAAQ,CAAClB,IAAI,EAAE,CAACmB,KAAK,CAAC,cAAc,CAAC;CACjD;CACA,IAAA,IAAIjC,KAAK,CAACC,OAAO,CAAC+B,QAAQ,CAAC,EAAE;CAC5BV,MAAAA,OAAO,GAAGA,OAAO,CAACY,MAAM,CAACF,QAAQ,CAAC;CACnC;CACD,GAAC,CAAC;CAEF,EAAA,OAAOV,OAAO,CAACa,MAAM,CAACC,OAAO,CAAC;CAC/B,CAAC;;CAGD;CACA;CACA;CACA;CACO,MAAMX,WAAW,GAAIP,GAAO,IAAgB;CAClD,EAAA,IAAI,CAAClB,KAAK,CAACC,OAAO,CAACiB,GAAG,CAAC,EAAE;KACvBA,GAAG,GAAG,CAACA,GAAG,CAAC;CACZ;CACD,EAAA,OAAOA,GAAG;CACX,CAAC;;CCvHD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAQe,eAAyB,IAAA;GACvC,MAAMmB,IAAI,GAAG,IAAI;CAEjBA,EAAAA,IAAI,CAACC,QAAQ,CAACC,UAAU,GAAG,IAAI,CAAC;;CAEhCF,EAAAA,IAAI,CAACG,IAAI,CAAC,QAAQ,EAAC,OAAO,EAAC,MAAI;CAC9BH,IAAAA,IAAI,CAACI,UAAU,GAAIJ,IAAI,CAACK,OAAO;CAE/BtB,IAAAA,UAAU,CAAEiB,IAAI,CAACM,aAAa,EAAE,gBAAgB,CAAC;CAEhD,IAAA,MAAMC,GAAG,GAAGvC,MAAM,CAAC,mCAAmC,CAAC;CACxDuC,IAAAA,GAAG,CAACC,MAAM,CAACR,IAAI,CAACM,aAAa,CAAC;CAC9BN,IAAAA,IAAI,CAACS,QAAQ,CAACC,YAAY,CAACH,GAAG,EAAEP,IAAI,CAACS,QAAQ,CAAC9B,UAAU,CAAC;;CAEzD;CACA,IAAA,MAAMgC,WAAW,GAAG3C,MAAM,CAAC,mDAAmD,CAAqB;KACnG2C,WAAW,CAACA,WAAW,GAAGX,IAAI,CAACC,QAAQ,CAACU,WAAW,IAAG,EAAE;CACxDX,IAAAA,IAAI,CAACK,OAAO,CAACG,MAAM,CAACG,WAAW,CAAC;CAEjC,GAAC,CAAC;CAGFX,EAAAA,IAAI,CAACY,EAAE,CAAC,YAAY,EAAC,MAAI;CAExB;KACAZ,IAAI,CAACM,aAAa,CAAC9C,gBAAgB,CAAC,SAAS,EAAER,GAAiB,IAAI;CACpE;OACC,QAAQA,GAAG,CAAC6D,OAAO;SAClB,KAAKC,OAAiB;WACrB,IAAId,IAAI,CAACe,MAAM,EAAE;CAChBhE,YAAAA,cAAc,CAACC,GAAG,EAAC,IAAI,CAAC;aACxBgD,IAAI,CAACgB,KAAK,EAAE;CACb;WACAhB,IAAI,CAACiB,gBAAgB,EAAE;CACxB,UAAA;SACA,KAAKH,OAAiB;CACrBd,UAAAA,IAAI,CAACI,UAAU,CAACc,QAAQ,GAAG,CAAC,CAAC;CAC9B,UAAA;CACD;OACA,OAAOlB,IAAI,CAACmB,SAAS,CAACC,IAAI,CAACpB,IAAI,EAAChD,GAAG,CAAC;CACrC,KAAC,CAAC;CAEFgD,IAAAA,IAAI,CAACY,EAAE,CAAC,MAAM,EAAC,MAAI;CAClBZ,MAAAA,IAAI,CAACI,UAAU,CAACc,QAAQ,GAAGlB,IAAI,CAACqB,UAAU,GAAG,CAAC,CAAC,GAAGrB,IAAI,CAACkB,QAAQ;CAChE,KAAC,CAAC;;CAGF;CACAlB,IAAAA,IAAI,CAACY,EAAE,CAAC,eAAe,EAAC,MAAK;CAC5BZ,MAAAA,IAAI,CAACM,aAAa,CAACgB,KAAK,EAAE;CAC3B,KAAC,CAAC;;CAEF;CACA,IAAA,MAAMC,WAAW,GAAGvB,IAAI,CAACwB,MAAM;KAC/BxB,IAAI,CAACG,IAAI,CAAC,SAAS,EAAC,QAAQ,EAAEnD,GAAe,IAAG;OAC/C,IAAIA,GAAG,IAAIA,GAAG,CAACyE,aAAa,IAAIzB,IAAI,CAACM,aAAa,EAAG;CACrD,MAAA,OAAOiB,WAAW,CAACH,IAAI,CAACpB,IAAI,CAAC;CAC9B,KAAC,CAAC;CAEF7C,IAAAA,QAAQ,CAAC6C,IAAI,CAACM,aAAa,EAAC,MAAM,EAAE,MAAMN,IAAI,CAACwB,MAAM,EAAG,CAAC;;CAEzD;CACAxB,IAAAA,IAAI,CAACG,IAAI,CAAC,QAAQ,EAAC,OAAO,EAAC,MAAK;CAE/B,MAAA,IAAI,CAACH,IAAI,CAACe,MAAM,EAAG;CACnBf,MAAAA,IAAI,CAACI,UAAU,CAACkB,KAAK,CAAC;CAACI,QAAAA,aAAa,EAAE;CAAI,OAAC,CAAC;CAC7C,KAAC,CAAC;CAEH,GAAC,CAAC;CAEH;;;;;;;;"}