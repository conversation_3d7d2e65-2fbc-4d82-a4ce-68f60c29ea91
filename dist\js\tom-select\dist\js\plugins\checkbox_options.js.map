{"version": 3, "file": "checkbox_options.js", "sources": ["../../../src/utils.ts", "../../../src/vanilla.ts", "../../../src/plugins/checkbox_options/plugin.ts"], "sourcesContent": ["\nimport type TomSelect from './tom-select.ts';\nimport { TomLoadCallback } from './types/index.ts';\n\n\n/**\n * Converts a scalar to its best string representation\n * for hash keys and HTML attribute values.\n *\n * Transformations:\n *   'str'     -> 'str'\n *   null      -> ''\n *   undefined -> ''\n *   true      -> '1'\n *   false     -> '0'\n *   0         -> '0'\n *   1         -> '1'\n *\n */\nexport const hash_key = (value:undefined|null|boolean|string|number):string|null => {\n\tif (typeof value === 'undefined' || value === null) return null;\n\treturn get_hash(value);\n};\n\nexport const get_hash = (value:boolean|string|number):string => {\n\tif (typeof value === 'boolean') return value ? '1' : '0';\n\treturn value + '';\n};\n\n/**\n * Escapes a string for use within HTML.\n *\n */\nexport const escape_html = (str:string):string => {\n\treturn (str + '')\n\t\t.replace(/&/g, '&amp;')\n\t\t.replace(/</g, '&lt;')\n\t\t.replace(/>/g, '&gt;')\n\t\t.replace(/\"/g, '&quot;');\n};\n\n\n/**\n * use setTimeout if timeout > 0 \n */\nexport const timeout = (fn:()=>void,timeout:number): number | null => {\n\tif( timeout > 0 ){\n\t\treturn window.setTimeout(fn,timeout);\n\t}\n\n\tfn.call(null);\n\treturn null;\n}\n\n/**\n * Debounce the user provided load function\n *\n */\nexport const loadDebounce = (fn:(value:string,callback:TomLoadCallback) => void,delay:number) => {\n\tvar timeout: null|ReturnType<typeof setTimeout>;\n\treturn function(this:TomSelect, value:string,callback:TomLoadCallback) {\n\t\tvar self = this;\n\n\t\tif( timeout ){\n\t\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\t\tclearTimeout(timeout);\n\t\t}\n\t\ttimeout = setTimeout(function() {\n\t\t\ttimeout = null;\n\t\t\tself.loadedSearches[value] = true;\n\t\t\tfn.call(self, value, callback);\n\n\t\t}, delay);\n\t};\n};\n\n\n/**\n * Debounce all fired events types listed in `types`\n * while executing the provided `fn`.\n *\n */\nexport const debounce_events = ( self:TomSelect, types:string[], fn:() => void ) => {\n\tvar type:string;\n\tvar trigger = self.trigger;\n\tvar event_args:{ [key: string]: any } = {};\n\n\t// override trigger method\n\tself.trigger = function(){\n\t\tvar type = arguments[0];\n\t\tif (types.indexOf(type) !== -1) {\n\t\t\tevent_args[type] = arguments;\n\t\t} else {\n\t\t\treturn trigger.apply(self, arguments);\n\t\t}\n\t};\n\n\t// invoke provided function\n\tfn.apply(self, []);\n\tself.trigger = trigger;\n\n\t// trigger queued events\n\tfor( type of types ){\n\t\tif( type in event_args ){\n\t\t\ttrigger.apply(self, event_args[type]);\n\t\t}\n\t}\n};\n\n\n/**\n * Determines the current selection within a text input control.\n * Returns an object containing:\n *   - start\n *   - length\n *\n * Note: \"selectionStart, selectionEnd ... apply only to inputs of types text, search, URL, tel and password\"\n * \t- https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange\n */\nexport const getSelection = (input:HTMLInputElement):{ start: number; length: number } => {\n\treturn {\n\t\tstart\t: input.selectionStart || 0,\n\t\tlength\t: (input.selectionEnd||0) - (input.selectionStart||0),\n\t};\n};\n\n\n/**\n * Prevent default\n *\n */\nexport const preventDefault = (evt?:Event, stop:boolean=false):void => {\n\tif( evt ){\n\t\tevt.preventDefault();\n\t\tif( stop ){\n\t\t\tevt.stopPropagation();\n\t\t}\n\t}\n}\n\n\n/**\n * Add event helper\n *\n */\nexport const addEvent = (target:EventTarget, type:string, callback:EventListenerOrEventListenerObject, options?:object):void => {\n\ttarget.addEventListener(type,callback,options);\n};\n\n\n/**\n * Return true if the requested key is down\n * Will return false if more than one control character is pressed ( when [ctrl+shift+a] != [ctrl+a] )\n * The current evt may not always set ( eg calling advanceSelection() )\n *\n */\nexport const isKeyDown = ( key_name:keyof (KeyboardEvent|MouseEvent), evt?:KeyboardEvent|MouseEvent ) => {\n\n\tif( !evt ){\n\t\treturn false;\n\t}\n\n\tif( !evt[key_name] ){\n\t\treturn false;\n\t}\n\n\tvar count = (evt.altKey?1:0) + (evt.ctrlKey?1:0) + (evt.shiftKey?1:0) + (evt.metaKey?1:0);\n\n\tif( count === 1 ){\n\t\treturn true;\n\t}\n\n\treturn false;\n};\n\n\n/**\n * Get the id of an element\n * If the id attribute is not set, set the attribute with the given id\n *\n */\nexport const getId = (el:Element,id:string) => {\n\tconst existing_id = el.getAttribute('id');\n\tif( existing_id ){\n\t\treturn existing_id;\n\t}\n\n\tel.setAttribute('id',id);\n\treturn id;\n};\n\n\n/**\n * Returns a string with backslashes added before characters that need to be escaped.\n */\nexport const addSlashes = (str:string):string => {\n\treturn str.replace(/[\\\\\"']/g, '\\\\$&');\n};\n\n/**\n *\n */\nexport const append = ( parent:Element|DocumentFragment, node: string|Node|null|undefined ):void =>{\n\tif( node ) parent.append(node);\n};\n\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\nexport const iterate = (object:[]|{[key:string]:any}, callback:(value:any,key:any)=>any) => {\n\n\tif ( Array.isArray(object)) {\n\t\tobject.forEach(callback);\n\n\t}else{\n\n\t\tfor (var key in object) {\n\t\t\tif (object.hasOwnProperty(key)) {\n\t\t\t\tcallback(object[key], key);\n\t\t\t}\n\t\t}\n\t}\n};\n", "\nimport { iterate } from './utils.ts';\n\n/**\n * Return a dom element from either a dom query string, jQuery object, a dom element or html string\n * https://stackoverflow.com/questions/494143/creating-a-new-dom-element-from-an-html-string-using-built-in-dom-methods-or-pro/35385518#35385518\n *\n * param query should be {}\n */\nexport const getDom = ( query:any ):HTMLElement => {\n\n\tif( query.jquery ){\n\t\treturn query[0];\n\t}\n\n\tif( query instanceof HTMLElement ){\n\t\treturn query;\n\t}\n\n\tif( isHtmlString(query) ){\n\t\tvar tpl = document.createElement('template');\n\t\ttpl.innerHTML = query.trim(); // Never return a text node of whitespace as the result\n\t\treturn tpl.content.firstChild as HTMLElement;\n\t}\n\n\treturn document.querySelector(query);\n};\n\nexport const isHtmlString = (arg:any): boolean => {\n\tif( typeof arg === 'string' && arg.indexOf('<') > -1 ){\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nexport const escapeQuery = (query:string):string => {\n\treturn query.replace(/['\"\\\\]/g, '\\\\$&');\n}\n\n/**\n * Dispatch an event\n *\n */\nexport const triggerEvent = ( dom_el:HTMLElement, event_name:string ):void => {\n\tvar event = document.createEvent('HTMLEvents');\n\tevent.initEvent(event_name, true, false);\n\tdom_el.dispatchEvent(event)\n};\n\n/**\n * Apply CSS rules to a dom element\n *\n */\nexport const applyCSS = ( dom_el:HTMLElement, css:{ [key: string]: string|number }):void => {\n\tObject.assign(dom_el.style, css);\n}\n\n\n/**\n * Add css classes\n *\n */\nexport const addClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n\tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map( cls => {\n\t\t\tel.classList.add( cls );\n\t\t});\n\t});\n}\n\n/**\n * Remove css classes\n *\n */\n export const removeClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n \tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map(cls => {\n\t \t\tel.classList.remove( cls );\n\t\t});\n \t});\n }\n\n\n/**\n * Return arguments\n *\n */\nexport const classesArray = (args:string[]|string[][]):string[] => {\n\tvar classes:string[] = [];\n\titerate( args, (_classes) =>{\n\t\tif( typeof _classes === 'string' ){\n\t\t\t_classes = _classes.trim().split(/[\\t\\n\\f\\r\\s]/);\n\t\t}\n\t\tif( Array.isArray(_classes) ){\n\t\t\tclasses = classes.concat(_classes);\n\t\t}\n\t});\n\n\treturn classes.filter(Boolean);\n}\n\n\n/**\n * Create an array from arg if it's not already an array\n *\n */\nexport const castAsArray = (arg:any):Array<any> => {\n\tif( !Array.isArray(arg) ){\n \t\targ = [arg];\n \t}\n\treturn arg;\n}\n\n\n/**\n * Get the closest node to the evt.target matching the selector\n * Stops at wrapper\n *\n */\nexport const parentMatch = ( target:null|HTMLElement, selector:string, wrapper?:HTMLElement ):HTMLElement|void => {\n\n\tif( wrapper && !wrapper.contains(target) ){\n\t\treturn;\n\t}\n\n\twhile( target && target.matches ){\n\n\t\tif( target.matches(selector) ){\n\t\t\treturn target;\n\t\t}\n\n\t\ttarget = target.parentNode as HTMLElement;\n\t}\n}\n\n\n/**\n * Get the first or last item from an array\n *\n * > 0 - right (last)\n * <= 0 - left (first)\n *\n */\nexport const getTail = ( list:Array<any>|NodeList, direction:number=0 ):any => {\n\n\tif( direction > 0 ){\n\t\treturn list[list.length-1];\n\t}\n\n\treturn list[0];\n}\n\n/**\n * Return true if an object is empty\n *\n */\nexport const isEmptyObject = (obj:object):boolean => {\n\treturn (Object.keys(obj).length === 0);\n}\n\n\n/**\n * Get the index of an element amongst sibling nodes of the same type\n *\n */\nexport const nodeIndex = ( el:null|Element, amongst?:string ):number => {\n\tif (!el) return -1;\n\n\tamongst = amongst || el.nodeName;\n\n\tvar i = 0;\n\twhile( el = el.previousElementSibling ){\n\n\t\tif( el.matches(amongst) ){\n\t\t\ti++;\n\t\t}\n\t}\n\treturn i;\n}\n\n\n/**\n * Set attributes of an element\n *\n */\nexport const setAttr = (el:Element,attrs:{ [key: string]: null|string|number }) => {\n\titerate( attrs,(val,attr) => {\n\t\tif( val == null ){\n\t\t\tel.removeAttribute(attr as string);\n\t\t}else{\n\t\t\tel.setAttribute(attr as string, ''+val);\n\t\t}\n\t});\n}\n\n\n/**\n * Replace a node\n */\nexport const replaceNode = ( existing:Node, replacement:Node ) => {\n\tif( existing.parentNode ) existing.parentNode.replaceChild(replacement, existing);\n}\n", "/**\n * Plugin: \"checkbox_options\" (Tom <PERSON>)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport type TomSelect from '../../tom-select.ts';\nimport { TomTemplate } from '../../types/index.ts';\nimport { preventDefault, hash_key } from '../../utils.ts';\nimport { getDom } from '../../vanilla.ts';\nimport { CBOptions } from './types.ts';\n\n\nexport default function(this:TomSelect, userOptions:CBOptions) {\n\tvar self = this;\n\tvar orig_onOptionSelect = self.onOptionSelect;\n\n\tself.settings.hideSelected = false;\n\n\tconst cbOptions : CBOptions = Object.assign({\n\t\t// so that the user may add different ones as well\n\t\tclassName             : \"tomselect-checkbox\",\n\n\t\t// the following default to the historic plugin's values\n\t\tcheckedClassNames     : undefined,\n\t\tuncheckedClassNames   : undefined,\n\t}, userOptions);\n\n\n\tvar UpdateChecked = function(checkbox:HTMLInputElement, toCheck : boolean) {\n\t\tif( toCheck ){\n\t\t\tcheckbox.checked = true;\n\t\t\tif (cbOptions.uncheckedClassNames) {\n\t\t\t\tcheckbox.classList.remove(...cbOptions.uncheckedClassNames);\n\t\t\t}\n\t\t\tif (cbOptions.checkedClassNames) {\n\t\t\t\tcheckbox.classList.add(...cbOptions.checkedClassNames);\n\t\t\t}\n\t\t}else{\n\t\t\tcheckbox.checked = false;\n\t\t\tif (cbOptions.checkedClassNames) {\n\t\t\t\tcheckbox.classList.remove(...cbOptions.checkedClassNames);\n\t\t\t}\n\t\t\tif (cbOptions.uncheckedClassNames) {\n\t\t\t\tcheckbox.classList.add(...cbOptions.uncheckedClassNames);\n\t\t\t}\n\t\t}\n\t}\n\n\t// update the checkbox for an option\n\tvar UpdateCheckbox = function(option:HTMLElement){\n\t\tsetTimeout(()=>{\n\t\t\tvar checkbox = option.querySelector('input.' + cbOptions.className);\n\t\t\tif( checkbox instanceof HTMLInputElement ){\n\t\t\t\tUpdateChecked(checkbox, option.classList.contains('selected'));\n\t\t\t}\n\t\t},1);\n\t};\n\n\t// add checkbox to option template\n\tself.hook('after','setupTemplates',() => {\n\n\t\tvar orig_render_option = self.settings.render.option;\n\n\t\tself.settings.render.option = ((data, escape_html) => {\n\t\t\tvar rendered = getDom(orig_render_option.call(self, data, escape_html));\n\t\t\tvar checkbox = document.createElement('input');\n\t\t\tif (cbOptions.className) {\n\t\t\t\tcheckbox.classList.add(cbOptions.className);\n\t\t\t}\n\t\t\tcheckbox.addEventListener('click',function(evt){\n\t\t\t\tpreventDefault(evt);\n\t\t\t});\n\n\t\t\tcheckbox.type = 'checkbox';\n\t\t\tconst hashed = hash_key(data[self.settings.valueField]);\n\n\t\t\tUpdateChecked(checkbox, !!(hashed && self.items.indexOf(hashed) > -1) );\n\n\t\t\trendered.prepend(checkbox);\n\t\t\treturn rendered;\n\t\t}) satisfies TomTemplate;\n\t});\n\n\t// uncheck when item removed\n\tself.on('item_remove',(value:string) => {\n\t\tvar option = self.getOption(value);\n\n\t\tif( option ){ // if dropdown hasn't been opened yet, the option won't exist\n\t\t\toption.classList.remove('selected'); // selected class won't be removed yet\n\t\t\tUpdateCheckbox(option);\n\t\t}\n\t});\n\n\t// check when item added\n\tself.on('item_add',(value:string) => {\n\t\tvar option = self.getOption(value);\n\n\t\tif( option ){ // if dropdown hasn't been opened yet, the option won't exist\n\t\t\tUpdateCheckbox(option);\n\t\t}\n\t});\n\n\n\t// remove items when selected option is clicked\n\tself.hook('instead','onOptionSelect',( evt:KeyboardEvent, option:HTMLElement )=>{\n\n\t\tif( option.classList.contains('selected') ){\n\t\t\toption.classList.remove('selected')\n\t\t\tself.removeItem(option.dataset.value);\n\t\t\tself.refreshOptions();\n\t\t\tpreventDefault(evt,true);\n\t\t\treturn;\n        }\n\n\t\torig_onOptionSelect.call(self, evt, option);\n\n\t\tUpdateCheckbox(option);\n\t});\n\n};\n"], "names": ["hash_key", "value", "get_hash", "preventDefault", "evt", "stop", "stopPropagation", "getDom", "query", "j<PERSON>y", "HTMLElement", "isHtmlString", "tpl", "document", "createElement", "innerHTML", "trim", "content", "<PERSON><PERSON><PERSON><PERSON>", "querySelector", "arg", "indexOf", "userOptions", "self", "orig_onOptionSelect", "onOptionSelect", "settings", "hideSelected", "cbOptions", "Object", "assign", "className", "checkedClassNames", "undefined", "uncheckedClassNames", "UpdateChecked", "checkbox", "to<PERSON><PERSON><PERSON>", "checked", "classList", "remove", "add", "UpdateCheckbox", "option", "setTimeout", "HTMLInputElement", "contains", "hook", "orig_render_option", "render", "data", "escape_html", "rendered", "call", "addEventListener", "type", "hashed", "valueField", "items", "prepend", "on", "getOption", "removeItem", "dataset", "refreshOptions"], "mappings": ";;;;;;;;;;;CAKA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,MAAMA,QAAQ,GAAIC,KAA0C,IAAiB;GACnF,IAAI,OAAOA,KAAK,KAAK,WAAW,IAAIA,KAAK,KAAK,IAAI,EAAE,OAAO,IAAI;GAC/D,OAAOC,QAAQ,CAACD,KAAK,CAAC;CACvB,CAAC;CAEM,MAAMC,QAAQ,GAAID,KAA2B,IAAY;GAC/D,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE,OAAOA,KAAK,GAAG,GAAG,GAAG,GAAG;GACxD,OAAOA,KAAK,GAAG,EAAE;CAClB,CAAC;;CAoGD;CACA;CACA;CACA;CACO,MAAME,cAAc,GAAGA,CAACC,GAAU,EAAEC,IAAY,GAAC,KAAK,KAAU;CACtE,EAAA,IAAID,GAAG,EAAE;KACRA,GAAG,CAACD,cAAc,EAAE;CACpB,IAAA,IAAIE,IAAI,EAAE;OACTD,GAAG,CAACE,eAAe,EAAE;CACtB;CACD;CACD,CAAC;;CCvID;CACA;CACA;CACA;CACA;CACA;CACO,MAAMC,MAAM,GAAKC,KAAS,IAAkB;GAElD,IAAIA,KAAK,CAACC,MAAM,EAAE;KACjB,OAAOD,KAAK,CAAC,CAAC,CAAC;CAChB;GAEA,IAAIA,KAAK,YAAYE,WAAW,EAAE;CACjC,IAAA,OAAOF,KAAK;CACb;CAEA,EAAA,IAAIG,YAAY,CAACH,KAAK,CAAC,EAAE;CACxB,IAAA,IAAII,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;KAC5CF,GAAG,CAACG,SAAS,GAAGP,KAAK,CAACQ,IAAI,EAAE,CAAC;CAC7B,IAAA,OAAOJ,GAAG,CAACK,OAAO,CAACC,UAAU;CAC9B;CAEA,EAAA,OAAOL,QAAQ,CAACM,aAAa,CAACX,KAAK,CAAC;CACrC,CAAC;CAEM,MAAMG,YAAY,GAAIS,GAAO,IAAc;CACjD,EAAA,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;CACrD,IAAA,OAAO,IAAI;CACZ;CACA,EAAA,OAAO,KAAK;CACb,CAAC;;CCjCD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CASe,eAAA,EAAyBC,WAAqB,EAAE;GAC9D,IAAIC,IAAI,GAAG,IAAI;CACf,EAAA,IAAIC,mBAAmB,GAAGD,IAAI,CAACE,cAAc;CAE7CF,EAAAA,IAAI,CAACG,QAAQ,CAACC,YAAY,GAAG,KAAK;CAElC,EAAA,MAAMC,SAAqB,GAAGC,MAAM,CAACC,MAAM,CAAC;CAC3C;CACAC,IAAAA,SAAS,EAAe,oBAAoB;CAE5C;CACAC,IAAAA,iBAAiB,EAAOC,SAAS;CACjCC,IAAAA,mBAAmB,EAAKD;IACxB,EAAEX,WAAW,CAAC;GAGf,IAAIa,aAAa,GAAG,SAAhBA,aAAaA,CAAYC,QAAyB,EAAEC,OAAiB,EAAE;CAC1E,IAAA,IAAIA,OAAO,EAAE;OACZD,QAAQ,CAACE,OAAO,GAAG,IAAI;OACvB,IAAIV,SAAS,CAACM,mBAAmB,EAAE;SAClCE,QAAQ,CAACG,SAAS,CAACC,MAAM,CAAC,GAAGZ,SAAS,CAACM,mBAAmB,CAAC;CAC5D;OACA,IAAIN,SAAS,CAACI,iBAAiB,EAAE;SAChCI,QAAQ,CAACG,SAAS,CAACE,GAAG,CAAC,GAAGb,SAAS,CAACI,iBAAiB,CAAC;CACvD;CACD,KAAC,MAAI;OACJI,QAAQ,CAACE,OAAO,GAAG,KAAK;OACxB,IAAIV,SAAS,CAACI,iBAAiB,EAAE;SAChCI,QAAQ,CAACG,SAAS,CAACC,MAAM,CAAC,GAAGZ,SAAS,CAACI,iBAAiB,CAAC;CAC1D;OACA,IAAIJ,SAAS,CAACM,mBAAmB,EAAE;SAClCE,QAAQ,CAACG,SAAS,CAACE,GAAG,CAAC,GAAGb,SAAS,CAACM,mBAAmB,CAAC;CACzD;CACD;IACA;;CAED;CACA,EAAA,IAAIQ,cAAc,GAAG,SAAjBA,cAAcA,CAAYC,MAAkB,EAAC;CAChDC,IAAAA,UAAU,CAAC,MAAI;OACd,IAAIR,QAAQ,GAAGO,MAAM,CAACxB,aAAa,CAAC,QAAQ,GAAGS,SAAS,CAACG,SAAS,CAAC;OACnE,IAAIK,QAAQ,YAAYS,gBAAgB,EAAE;SACzCV,aAAa,CAACC,QAAQ,EAAEO,MAAM,CAACJ,SAAS,CAACO,QAAQ,CAAC,UAAU,CAAC,CAAC;CAC/D;MACA,EAAC,CAAC,CAAC;IACJ;;CAED;CACAvB,EAAAA,IAAI,CAACwB,IAAI,CAAC,OAAO,EAAC,gBAAgB,EAAC,MAAM;KAExC,IAAIC,kBAAkB,GAAGzB,IAAI,CAACG,QAAQ,CAACuB,MAAM,CAACN,MAAM;KAEpDpB,IAAI,CAACG,QAAQ,CAACuB,MAAM,CAACN,MAAM,GAAI,CAACO,IAAI,EAAEC,WAAW,KAAK;CACrD,MAAA,IAAIC,QAAQ,GAAG7C,MAAM,CAACyC,kBAAkB,CAACK,IAAI,CAAC9B,IAAI,EAAE2B,IAAI,EAAEC,WAAW,CAAC,CAAC;CACvE,MAAA,IAAIf,QAAQ,GAAGvB,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;OAC9C,IAAIc,SAAS,CAACG,SAAS,EAAE;SACxBK,QAAQ,CAACG,SAAS,CAACE,GAAG,CAACb,SAAS,CAACG,SAAS,CAAC;CAC5C;CACAK,MAAAA,QAAQ,CAACkB,gBAAgB,CAAC,OAAO,EAAC,UAASlD,GAAG,EAAC;SAC9CD,cAAc,CAACC,GAAG,CAAC;CACpB,OAAC,CAAC;OAEFgC,QAAQ,CAACmB,IAAI,GAAG,UAAU;CAC1B,MAAA,MAAMC,MAAM,GAAGxD,QAAQ,CAACkD,IAAI,CAAC3B,IAAI,CAACG,QAAQ,CAAC+B,UAAU,CAAC,CAAC;CAEvDtB,MAAAA,aAAa,CAACC,QAAQ,EAAE,CAAC,EAAEoB,MAAM,IAAIjC,IAAI,CAACmC,KAAK,CAACrC,OAAO,CAACmC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAE,CAAC;CAEvEJ,MAAAA,QAAQ,CAACO,OAAO,CAACvB,QAAQ,CAAC;CAC1B,MAAA,OAAOgB,QAAQ;MACQ;CACzB,GAAC,CAAC;;CAEF;CACA7B,EAAAA,IAAI,CAACqC,EAAE,CAAC,aAAa,EAAE3D,KAAY,IAAK;CACvC,IAAA,IAAI0C,MAAM,GAAGpB,IAAI,CAACsC,SAAS,CAAC5D,KAAK,CAAC;CAElC,IAAA,IAAI0C,MAAM,EAAE;CAAE;OACbA,MAAM,CAACJ,SAAS,CAACC,MAAM,CAAC,UAAU,CAAC,CAAC;OACpCE,cAAc,CAACC,MAAM,CAAC;CACvB;CACD,GAAC,CAAC;;CAEF;CACApB,EAAAA,IAAI,CAACqC,EAAE,CAAC,UAAU,EAAE3D,KAAY,IAAK;CACpC,IAAA,IAAI0C,MAAM,GAAGpB,IAAI,CAACsC,SAAS,CAAC5D,KAAK,CAAC;CAElC,IAAA,IAAI0C,MAAM,EAAE;CAAE;OACbD,cAAc,CAACC,MAAM,CAAC;CACvB;CACD,GAAC,CAAC;;CAGF;GACApB,IAAI,CAACwB,IAAI,CAAC,SAAS,EAAC,gBAAgB,EAAC,CAAE3C,GAAiB,EAAEuC,MAAkB,KAAI;KAE/E,IAAIA,MAAM,CAACJ,SAAS,CAACO,QAAQ,CAAC,UAAU,CAAC,EAAE;CAC1CH,MAAAA,MAAM,CAACJ,SAAS,CAACC,MAAM,CAAC,UAAU,CAAC;OACnCjB,IAAI,CAACuC,UAAU,CAACnB,MAAM,CAACoB,OAAO,CAAC9D,KAAK,CAAC;OACrCsB,IAAI,CAACyC,cAAc,EAAE;CACrB7D,MAAAA,cAAc,CAACC,GAAG,EAAC,IAAI,CAAC;CACxB,MAAA;CACK;KAENoB,mBAAmB,CAAC6B,IAAI,CAAC9B,IAAI,EAAEnB,GAAG,EAAEuC,MAAM,CAAC;KAE3CD,cAAc,CAACC,MAAM,CAAC;CACvB,GAAC,CAAC;CAEH;;;;;;;;"}