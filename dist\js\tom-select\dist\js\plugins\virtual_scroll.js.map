{"version": 3, "file": "virtual_scroll.js", "sources": ["../../../src/utils.ts", "../../../src/vanilla.ts", "../../../src/plugins/virtual_scroll/plugin.ts"], "sourcesContent": ["\nimport type TomSelect from './tom-select.ts';\nimport { TomLoadCallback } from './types/index.ts';\n\n\n/**\n * Converts a scalar to its best string representation\n * for hash keys and HTML attribute values.\n *\n * Transformations:\n *   'str'     -> 'str'\n *   null      -> ''\n *   undefined -> ''\n *   true      -> '1'\n *   false     -> '0'\n *   0         -> '0'\n *   1         -> '1'\n *\n */\nexport const hash_key = (value:undefined|null|boolean|string|number):string|null => {\n\tif (typeof value === 'undefined' || value === null) return null;\n\treturn get_hash(value);\n};\n\nexport const get_hash = (value:boolean|string|number):string => {\n\tif (typeof value === 'boolean') return value ? '1' : '0';\n\treturn value + '';\n};\n\n/**\n * Escapes a string for use within HTML.\n *\n */\nexport const escape_html = (str:string):string => {\n\treturn (str + '')\n\t\t.replace(/&/g, '&amp;')\n\t\t.replace(/</g, '&lt;')\n\t\t.replace(/>/g, '&gt;')\n\t\t.replace(/\"/g, '&quot;');\n};\n\n\n/**\n * use setTimeout if timeout > 0 \n */\nexport const timeout = (fn:()=>void,timeout:number): number | null => {\n\tif( timeout > 0 ){\n\t\treturn window.setTimeout(fn,timeout);\n\t}\n\n\tfn.call(null);\n\treturn null;\n}\n\n/**\n * Debounce the user provided load function\n *\n */\nexport const loadDebounce = (fn:(value:string,callback:TomLoadCallback) => void,delay:number) => {\n\tvar timeout: null|ReturnType<typeof setTimeout>;\n\treturn function(this:TomSelect, value:string,callback:TomLoadCallback) {\n\t\tvar self = this;\n\n\t\tif( timeout ){\n\t\t\tself.loading = Math.max(self.loading - 1, 0);\n\t\t\tclearTimeout(timeout);\n\t\t}\n\t\ttimeout = setTimeout(function() {\n\t\t\ttimeout = null;\n\t\t\tself.loadedSearches[value] = true;\n\t\t\tfn.call(self, value, callback);\n\n\t\t}, delay);\n\t};\n};\n\n\n/**\n * Debounce all fired events types listed in `types`\n * while executing the provided `fn`.\n *\n */\nexport const debounce_events = ( self:TomSelect, types:string[], fn:() => void ) => {\n\tvar type:string;\n\tvar trigger = self.trigger;\n\tvar event_args:{ [key: string]: any } = {};\n\n\t// override trigger method\n\tself.trigger = function(){\n\t\tvar type = arguments[0];\n\t\tif (types.indexOf(type) !== -1) {\n\t\t\tevent_args[type] = arguments;\n\t\t} else {\n\t\t\treturn trigger.apply(self, arguments);\n\t\t}\n\t};\n\n\t// invoke provided function\n\tfn.apply(self, []);\n\tself.trigger = trigger;\n\n\t// trigger queued events\n\tfor( type of types ){\n\t\tif( type in event_args ){\n\t\t\ttrigger.apply(self, event_args[type]);\n\t\t}\n\t}\n};\n\n\n/**\n * Determines the current selection within a text input control.\n * Returns an object containing:\n *   - start\n *   - length\n *\n * Note: \"selectionStart, selectionEnd ... apply only to inputs of types text, search, URL, tel and password\"\n * \t- https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange\n */\nexport const getSelection = (input:HTMLInputElement):{ start: number; length: number } => {\n\treturn {\n\t\tstart\t: input.selectionStart || 0,\n\t\tlength\t: (input.selectionEnd||0) - (input.selectionStart||0),\n\t};\n};\n\n\n/**\n * Prevent default\n *\n */\nexport const preventDefault = (evt?:Event, stop:boolean=false):void => {\n\tif( evt ){\n\t\tevt.preventDefault();\n\t\tif( stop ){\n\t\t\tevt.stopPropagation();\n\t\t}\n\t}\n}\n\n\n/**\n * Add event helper\n *\n */\nexport const addEvent = (target:EventTarget, type:string, callback:EventListenerOrEventListenerObject, options?:object):void => {\n\ttarget.addEventListener(type,callback,options);\n};\n\n\n/**\n * Return true if the requested key is down\n * Will return false if more than one control character is pressed ( when [ctrl+shift+a] != [ctrl+a] )\n * The current evt may not always set ( eg calling advanceSelection() )\n *\n */\nexport const isKeyDown = ( key_name:keyof (KeyboardEvent|MouseEvent), evt?:KeyboardEvent|MouseEvent ) => {\n\n\tif( !evt ){\n\t\treturn false;\n\t}\n\n\tif( !evt[key_name] ){\n\t\treturn false;\n\t}\n\n\tvar count = (evt.altKey?1:0) + (evt.ctrlKey?1:0) + (evt.shiftKey?1:0) + (evt.metaKey?1:0);\n\n\tif( count === 1 ){\n\t\treturn true;\n\t}\n\n\treturn false;\n};\n\n\n/**\n * Get the id of an element\n * If the id attribute is not set, set the attribute with the given id\n *\n */\nexport const getId = (el:Element,id:string) => {\n\tconst existing_id = el.getAttribute('id');\n\tif( existing_id ){\n\t\treturn existing_id;\n\t}\n\n\tel.setAttribute('id',id);\n\treturn id;\n};\n\n\n/**\n * Returns a string with backslashes added before characters that need to be escaped.\n */\nexport const addSlashes = (str:string):string => {\n\treturn str.replace(/[\\\\\"']/g, '\\\\$&');\n};\n\n/**\n *\n */\nexport const append = ( parent:Element|DocumentFragment, node: string|Node|null|undefined ):void =>{\n\tif( node ) parent.append(node);\n};\n\n/**\n * Iterates over arrays and hashes.\n *\n * ```\n * iterate(this.items, function(item, id) {\n *    // invoked for each item\n * });\n * ```\n *\n */\nexport const iterate = (object:[]|{[key:string]:any}, callback:(value:any,key:any)=>any) => {\n\n\tif ( Array.isArray(object)) {\n\t\tobject.forEach(callback);\n\n\t}else{\n\n\t\tfor (var key in object) {\n\t\t\tif (object.hasOwnProperty(key)) {\n\t\t\t\tcallback(object[key], key);\n\t\t\t}\n\t\t}\n\t}\n};\n", "\nimport { iterate } from './utils.ts';\n\n/**\n * Return a dom element from either a dom query string, jQuery object, a dom element or html string\n * https://stackoverflow.com/questions/494143/creating-a-new-dom-element-from-an-html-string-using-built-in-dom-methods-or-pro/35385518#35385518\n *\n * param query should be {}\n */\nexport const getDom = ( query:any ):HTMLElement => {\n\n\tif( query.jquery ){\n\t\treturn query[0];\n\t}\n\n\tif( query instanceof HTMLElement ){\n\t\treturn query;\n\t}\n\n\tif( isHtmlString(query) ){\n\t\tvar tpl = document.createElement('template');\n\t\ttpl.innerHTML = query.trim(); // Never return a text node of whitespace as the result\n\t\treturn tpl.content.firstChild as HTMLElement;\n\t}\n\n\treturn document.querySelector(query);\n};\n\nexport const isHtmlString = (arg:any): boolean => {\n\tif( typeof arg === 'string' && arg.indexOf('<') > -1 ){\n\t\treturn true;\n\t}\n\treturn false;\n}\n\nexport const escapeQuery = (query:string):string => {\n\treturn query.replace(/['\"\\\\]/g, '\\\\$&');\n}\n\n/**\n * Dispatch an event\n *\n */\nexport const triggerEvent = ( dom_el:HTMLElement, event_name:string ):void => {\n\tvar event = document.createEvent('HTMLEvents');\n\tevent.initEvent(event_name, true, false);\n\tdom_el.dispatchEvent(event)\n};\n\n/**\n * Apply CSS rules to a dom element\n *\n */\nexport const applyCSS = ( dom_el:HTMLElement, css:{ [key: string]: string|number }):void => {\n\tObject.assign(dom_el.style, css);\n}\n\n\n/**\n * Add css classes\n *\n */\nexport const addClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n\tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map( cls => {\n\t\t\tel.classList.add( cls );\n\t\t});\n\t});\n}\n\n/**\n * Remove css classes\n *\n */\n export const removeClasses = ( elmts:HTMLElement|HTMLElement[], ...classes:string[]|string[][] ) => {\n\n \tvar norm_classes \t= classesArray(classes);\n\telmts\t\t\t\t= castAsArray(elmts);\n\n\telmts.map( el => {\n\t\tnorm_classes.map(cls => {\n\t \t\tel.classList.remove( cls );\n\t\t});\n \t});\n }\n\n\n/**\n * Return arguments\n *\n */\nexport const classesArray = (args:string[]|string[][]):string[] => {\n\tvar classes:string[] = [];\n\titerate( args, (_classes) =>{\n\t\tif( typeof _classes === 'string' ){\n\t\t\t_classes = _classes.trim().split(/[\\t\\n\\f\\r\\s]/);\n\t\t}\n\t\tif( Array.isArray(_classes) ){\n\t\t\tclasses = classes.concat(_classes);\n\t\t}\n\t});\n\n\treturn classes.filter(Boolean);\n}\n\n\n/**\n * Create an array from arg if it's not already an array\n *\n */\nexport const castAsArray = (arg:any):Array<any> => {\n\tif( !Array.isArray(arg) ){\n \t\targ = [arg];\n \t}\n\treturn arg;\n}\n\n\n/**\n * Get the closest node to the evt.target matching the selector\n * Stops at wrapper\n *\n */\nexport const parentMatch = ( target:null|HTMLElement, selector:string, wrapper?:HTMLElement ):HTMLElement|void => {\n\n\tif( wrapper && !wrapper.contains(target) ){\n\t\treturn;\n\t}\n\n\twhile( target && target.matches ){\n\n\t\tif( target.matches(selector) ){\n\t\t\treturn target;\n\t\t}\n\n\t\ttarget = target.parentNode as HTMLElement;\n\t}\n}\n\n\n/**\n * Get the first or last item from an array\n *\n * > 0 - right (last)\n * <= 0 - left (first)\n *\n */\nexport const getTail = ( list:Array<any>|NodeList, direction:number=0 ):any => {\n\n\tif( direction > 0 ){\n\t\treturn list[list.length-1];\n\t}\n\n\treturn list[0];\n}\n\n/**\n * Return true if an object is empty\n *\n */\nexport const isEmptyObject = (obj:object):boolean => {\n\treturn (Object.keys(obj).length === 0);\n}\n\n\n/**\n * Get the index of an element amongst sibling nodes of the same type\n *\n */\nexport const nodeIndex = ( el:null|Element, amongst?:string ):number => {\n\tif (!el) return -1;\n\n\tamongst = amongst || el.nodeName;\n\n\tvar i = 0;\n\twhile( el = el.previousElementSibling ){\n\n\t\tif( el.matches(amongst) ){\n\t\t\ti++;\n\t\t}\n\t}\n\treturn i;\n}\n\n\n/**\n * Set attributes of an element\n *\n */\nexport const setAttr = (el:Element,attrs:{ [key: string]: null|string|number }) => {\n\titerate( attrs,(val,attr) => {\n\t\tif( val == null ){\n\t\t\tel.removeAttribute(attr as string);\n\t\t}else{\n\t\t\tel.setAttribute(attr as string, ''+val);\n\t\t}\n\t});\n}\n\n\n/**\n * Replace a node\n */\nexport const replaceNode = ( existing:Node, replacement:Node ) => {\n\tif( existing.parentNode ) existing.parentNode.replaceChild(replacement, existing);\n}\n", "/**\n * Plugin: \"restore_on_backspace\" (Tom Select)\n * Copyright (c) contributors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\"); you may not use this\n * file except in compliance with the License. You may obtain a copy of the License at:\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF\n * ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n *\n */\n\nimport type TomSelect from '../../tom-select.ts';\nimport { TomOption } from '../../types/index.ts';\nimport { addClasses } from '../../vanilla.ts';\n\nexport default function(this:TomSelect) {\n\tconst self\t\t\t\t\t\t\t= this;\n\tconst orig_canLoad\t\t\t\t\t= self.canLoad;\n\tconst orig_clearActiveOption\t\t= self.clearActiveOption;\n\tconst orig_loadCallback\t\t\t\t= self.loadCallback;\n\n\tvar pagination:{[key:string]:any}\t= {};\n\tvar dropdown_content:HTMLElement;\n\tvar loading_more\t\t\t\t\t= false;\n\tvar load_more_opt:HTMLElement;\n\tvar default_values: string[]\t\t= [];\n\n\tif( !self.settings.shouldLoadMore ){\n\n\t\t// return true if additional results should be loaded\n\t\tself.settings.shouldLoadMore = ():boolean=>{\n\n\t\t\tconst scroll_percent = dropdown_content.clientHeight / (dropdown_content.scrollHeight - dropdown_content.scrollTop);\n\t\t\tif( scroll_percent > 0.9 ){\n\t\t\t\treturn true;\n\t\t\t}\n\n\t\t\tif( self.activeOption ){\n\t\t\t\tvar selectable\t= self.selectable();\n\t\t\t\tvar index\t\t= Array.from(selectable).indexOf(self.activeOption);\n\t\t\t\tif( index >= (selectable.length-2) ){\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn false;\n\t\t}\n\t}\n\n\n\tif( !self.settings.firstUrl ){\n\t\tthrow 'virtual_scroll plugin requires a firstUrl() method';\n\t}\n\n\n\t// in order for virtual scrolling to work,\n\t// options need to be ordered the same way they're returned from the remote data source\n\tself.settings.sortField\t\t\t= [{field:'$order'},{field:'$score'}];\n\n\n\t// can we load more results for given query?\n\tconst canLoadMore = (query:string):boolean => {\n\n\t\tif( typeof self.settings.maxOptions === 'number' && dropdown_content.children.length >= self.settings.maxOptions ){\n\t\t\treturn false;\n\t\t}\n\n\t\tif( (query in pagination) && pagination[query] ){\n\t\t\treturn true;\n\t\t}\n\n\t\treturn false;\n\t};\n\n\tconst clearFilter = (option:TomOption, value:string):boolean => {\n\t\tif( self.items.indexOf(value) >= 0 || default_values.indexOf(value) >= 0 ){\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t};\n\n\n\t// set the next url that will be\n\tself.setNextUrl = (value:string,next_url:any):void => {\n\t\tpagination[value] = next_url;\n\t};\n\n\t// getUrl() to be used in settings.load()\n\tself.getUrl = (query:string):any =>{\n\n\t\tif( query in pagination ){\n\t\t\tconst next_url = pagination[query];\n\t\t\tpagination[query] = false;\n\t\t\treturn next_url;\n\t\t}\n\n\t\t// if the user goes back to a previous query\n\t\t// we need to load the first page again\n\t\tself.clearPagination();\n\n\t\treturn self.settings.firstUrl.call(self,query);\n\t};\n\n\t// clear pagination\n\tself.clearPagination = ():void =>{\n\t\tpagination = {};\n\t};\n\n\t// don't clear the active option (and cause unwanted dropdown scroll)\n\t// while loading more results\n\tself.hook('instead','clearActiveOption',()=>{\n\n\t\tif( loading_more ){\n\t\t\treturn;\n\t\t}\n\n\t\treturn orig_clearActiveOption.call(self);\n\t});\n\n\t// override the canLoad method\n\tself.hook('instead','canLoad',(query:string)=>{\n\n\t\t// first time the query has been seen\n\t\tif( !(query in pagination) ){\n\t\t\treturn orig_canLoad.call(self,query);\n\t\t}\n\n\t\treturn canLoadMore(query);\n\t});\n\n\n\t// wrap the load\n\tself.hook('instead','loadCallback',( options:TomOption[], optgroups:TomOption[])=>{\n\n\t\tif( !loading_more ){\n\t\t\tself.clearOptions(clearFilter);\n\t\t}else if( load_more_opt ){\n\t\t\tconst first_option = options[0];\n\t\t\tif( first_option !== undefined ){\n\t\t\t\tload_more_opt.dataset.value\t\t= first_option[self.settings.valueField];\n\t\t\t}\n\t\t}\n\n\t\torig_loadCallback.call( self, options, optgroups);\n\n\t\tloading_more = false;\n\t});\n\n\n\t// add templates to dropdown\n\t//\tloading_more if we have another url in the queue\n\t//\tno_more_results if we don't have another url in the queue\n\tself.hook('after','refreshOptions',()=>{\n\n\t\tconst query\t\t= self.lastValue;\n\t\tvar option;\n\n\t\tif( canLoadMore(query) ){\n\n\t\t\toption = self.render('loading_more',{query:query});\n\t\t\tif( option ){\n\t\t\t\toption.setAttribute('data-selectable',''); // so that navigating dropdown with [down] keypresses can navigate to this node\n\t\t\t\tload_more_opt = option;\n\t\t\t}\n\n\t\t}else if( (query in pagination) && !dropdown_content.querySelector('.no-results') ){\n\t\t\toption = self.render('no_more_results',{query:query});\n\t\t}\n\n\t\tif( option ){\n\t\t\taddClasses(option,self.settings.optionClass);\n\t\t\tdropdown_content.append( option );\n\t\t}\n\n\t});\n\n\n\t// add scroll listener and default templates\n\tself.on('initialize',()=>{\n\t\tdefault_values = Object.keys(self.options);\n\t\tdropdown_content = self.dropdown_content;\n\n\t\t// default templates\n\t\tself.settings.render = Object.assign({}, {\n\t\t\tloading_more:() => {\n\t\t\t\treturn `<div class=\"loading-more-results\">Loading more results ... </div>`;\n\t\t\t},\n\t\t\tno_more_results:() =>{\n\t\t\t\treturn `<div class=\"no-more-results\">No more results</div>`;\n\t\t\t}\n\t\t},self.settings.render);\n\n\n\t\t// watch dropdown content scroll position\n\t\tdropdown_content.addEventListener('scroll',()=>{\n\n\t\t\tif( !self.settings.shouldLoadMore.call(self) ){\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// !important: this will get checked again in load() but we still need to check here otherwise loading_more will be set to true\n\t\t\tif( !canLoadMore(self.lastValue) ){\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// don't call load() too much\n\t\t\tif( loading_more ) return;\n\n\n\t\t\tloading_more = true;\n\t\t\tself.load.call(self,self.lastValue);\n\t\t});\n\t});\n\n};\n"], "names": ["iterate", "object", "callback", "Array", "isArray", "for<PERSON>ach", "key", "hasOwnProperty", "addClasses", "elmts", "classes", "norm_classes", "classesArray", "cast<PERSON><PERSON><PERSON><PERSON>", "map", "el", "cls", "classList", "add", "args", "_classes", "trim", "split", "concat", "filter", "Boolean", "arg", "self", "orig_canLoad", "canLoad", "orig_clearActiveOption", "clearActiveOption", "orig_loadCallback", "loadCallback", "pagination", "dropdown_content", "loading_more", "load_more_opt", "default_values", "settings", "shouldLoadMore", "scroll_percent", "clientHeight", "scrollHeight", "scrollTop", "activeOption", "selectable", "index", "from", "indexOf", "length", "firstUrl", "sortField", "field", "canLoadMore", "query", "maxOptions", "children", "clearFilter", "option", "value", "items", "setNextUrl", "next_url", "getUrl", "clearPagination", "call", "hook", "options", "optgroups", "clearOptions", "first_option", "undefined", "dataset", "valueField", "lastValue", "render", "setAttribute", "querySelector", "optionClass", "append", "on", "Object", "keys", "assign", "no_more_results", "addEventListener", "load"], "mappings": ";;;;;;;;;;;CAKA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CA4LA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACO,MAAMA,OAAO,GAAGA,CAACC,MAA4B,EAAEC,QAAiC,KAAK;CAE3F,EAAA,IAAKC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;CAC3BA,IAAAA,MAAM,CAACI,OAAO,CAACH,QAAQ,CAAC;CAEzB,GAAC,MAAI;CAEJ,IAAA,KAAK,IAAII,GAAG,IAAIL,MAAM,EAAE;CACvB,MAAA,IAAIA,MAAM,CAACM,cAAc,CAACD,GAAG,CAAC,EAAE;CAC/BJ,QAAAA,QAAQ,CAACD,MAAM,CAACK,GAAG,CAAC,EAAEA,GAAG,CAAC;CAC3B;CACD;CACD;CACD,CAAC;;CC3KD;CACA;CACA;CACA;CACO,MAAME,UAAU,GAAGA,CAAEC,KAA+B,EAAE,GAAGC,OAA2B,KAAM;CAEhG,EAAA,IAAIC,YAAY,GAAIC,YAAY,CAACF,OAAO,CAAC;CACzCD,EAAAA,KAAK,GAAMI,WAAW,CAACJ,KAAK,CAAC;CAE7BA,EAAAA,KAAK,CAACK,GAAG,CAAEC,EAAE,IAAI;CAChBJ,IAAAA,YAAY,CAACG,GAAG,CAAEE,GAAG,IAAI;CACxBD,MAAAA,EAAE,CAACE,SAAS,CAACC,GAAG,CAAEF,GAAI,CAAC;CACxB,KAAC,CAAC;CACH,GAAC,CAAC;CACH,CAAC;;CAmBD;CACA;CACA;CACA;CACO,MAAMJ,YAAY,GAAIO,IAAwB,IAAc;GAClE,IAAIT,OAAgB,GAAG,EAAE;CACzBV,EAAAA,OAAO,CAAEmB,IAAI,EAAGC,QAAQ,IAAI;CAC3B,IAAA,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;OACjCA,QAAQ,GAAGA,QAAQ,CAACC,IAAI,EAAE,CAACC,KAAK,CAAC,cAAc,CAAC;CACjD;CACA,IAAA,IAAInB,KAAK,CAACC,OAAO,CAACgB,QAAQ,CAAC,EAAE;CAC5BV,MAAAA,OAAO,GAAGA,OAAO,CAACa,MAAM,CAACH,QAAQ,CAAC;CACnC;CACD,GAAC,CAAC;CAEF,EAAA,OAAOV,OAAO,CAACc,MAAM,CAACC,OAAO,CAAC;CAC/B,CAAC;;CAGD;CACA;CACA;CACA;CACO,MAAMZ,WAAW,GAAIa,GAAO,IAAgB;CAClD,EAAA,IAAI,CAACvB,KAAK,CAACC,OAAO,CAACsB,GAAG,CAAC,EAAE;KACvBA,GAAG,GAAG,CAACA,GAAG,CAAC;CACZ;CACD,EAAA,OAAOA,GAAG;CACX,CAAC;;CCvHD;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA;;CAMe,eAAyB,IAAA;GACvC,MAAMC,IAAI,GAAS,IAAI;CACvB,EAAA,MAAMC,YAAY,GAAOD,IAAI,CAACE,OAAO;CACrC,EAAA,MAAMC,sBAAsB,GAAIH,IAAI,CAACI,iBAAiB;CACtD,EAAA,MAAMC,iBAAiB,GAAML,IAAI,CAACM,YAAY;GAE9C,IAAIC,UAA6B,GAAG,EAAE;CACtC,EAAA,IAAIC,gBAA4B;GAChC,IAAIC,YAAY,GAAO,KAAK;CAC5B,EAAA,IAAIC,aAAyB;GAC7B,IAAIC,cAAwB,GAAI,EAAE;CAElC,EAAA,IAAI,CAACX,IAAI,CAACY,QAAQ,CAACC,cAAc,EAAE;CAElC;CACAb,IAAAA,IAAI,CAACY,QAAQ,CAACC,cAAc,GAAG,MAAY;CAE1C,MAAA,MAAMC,cAAc,GAAGN,gBAAgB,CAACO,YAAY,IAAIP,gBAAgB,CAACQ,YAAY,GAAGR,gBAAgB,CAACS,SAAS,CAAC;OACnH,IAAIH,cAAc,GAAG,GAAG,EAAE;CACzB,QAAA,OAAO,IAAI;CACZ;OAEA,IAAId,IAAI,CAACkB,YAAY,EAAE;CACtB,QAAA,IAAIC,UAAU,GAAGnB,IAAI,CAACmB,UAAU,EAAE;CAClC,QAAA,IAAIC,KAAK,GAAI5C,KAAK,CAAC6C,IAAI,CAACF,UAAU,CAAC,CAACG,OAAO,CAACtB,IAAI,CAACkB,YAAY,CAAC;CAC9D,QAAA,IAAIE,KAAK,IAAKD,UAAU,CAACI,MAAM,GAAC,CAAE,EAAE;CACnC,UAAA,OAAO,IAAI;CACZ;CACD;CAEA,MAAA,OAAO,KAAK;MACZ;CACF;CAGA,EAAA,IAAI,CAACvB,IAAI,CAACY,QAAQ,CAACY,QAAQ,EAAE;CAC5B,IAAA,MAAM,oDAAoD;CAC3D;;CAGA;CACA;CACAxB,EAAAA,IAAI,CAACY,QAAQ,CAACa,SAAS,GAAK,CAAC;CAACC,IAAAA,KAAK,EAAC;CAAQ,GAAC,EAAC;CAACA,IAAAA,KAAK,EAAC;CAAQ,GAAC,CAAC;;CAG/D;GACA,MAAMC,WAAW,GAAIC,KAAY,IAAa;KAE7C,IAAI,OAAO5B,IAAI,CAACY,QAAQ,CAACiB,UAAU,KAAK,QAAQ,IAAIrB,gBAAgB,CAACsB,QAAQ,CAACP,MAAM,IAAIvB,IAAI,CAACY,QAAQ,CAACiB,UAAU,EAAE;CACjH,MAAA,OAAO,KAAK;CACb;KAEA,IAAKD,KAAK,IAAIrB,UAAU,IAAKA,UAAU,CAACqB,KAAK,CAAC,EAAE;CAC/C,MAAA,OAAO,IAAI;CACZ;CAEA,IAAA,OAAO,KAAK;IACZ;CAED,EAAA,MAAMG,WAAW,GAAGA,CAACC,MAAgB,EAAEC,KAAY,KAAa;CAC/D,IAAA,IAAIjC,IAAI,CAACkC,KAAK,CAACZ,OAAO,CAACW,KAAK,CAAC,IAAI,CAAC,IAAItB,cAAc,CAACW,OAAO,CAACW,KAAK,CAAC,IAAI,CAAC,EAAE;CACzE,MAAA,OAAO,IAAI;CACZ;CACA,IAAA,OAAO,KAAK;IACZ;;CAGD;CACAjC,EAAAA,IAAI,CAACmC,UAAU,GAAG,CAACF,KAAY,EAACG,QAAY,KAAU;CACrD7B,IAAAA,UAAU,CAAC0B,KAAK,CAAC,GAAGG,QAAQ;IAC5B;;CAED;CACApC,EAAAA,IAAI,CAACqC,MAAM,GAAIT,KAAY,IAAQ;KAElC,IAAIA,KAAK,IAAIrB,UAAU,EAAE;CACxB,MAAA,MAAM6B,QAAQ,GAAG7B,UAAU,CAACqB,KAAK,CAAC;CAClCrB,MAAAA,UAAU,CAACqB,KAAK,CAAC,GAAG,KAAK;CACzB,MAAA,OAAOQ,QAAQ;CAChB;;CAEA;CACA;KACApC,IAAI,CAACsC,eAAe,EAAE;KAEtB,OAAOtC,IAAI,CAACY,QAAQ,CAACY,QAAQ,CAACe,IAAI,CAACvC,IAAI,EAAC4B,KAAK,CAAC;IAC9C;;CAED;GACA5B,IAAI,CAACsC,eAAe,GAAG,MAAU;KAChC/B,UAAU,GAAG,EAAE;IACf;;CAED;CACA;CACAP,EAAAA,IAAI,CAACwC,IAAI,CAAC,SAAS,EAAC,mBAAmB,EAAC,MAAI;CAE3C,IAAA,IAAI/B,YAAY,EAAE;CACjB,MAAA;CACD;CAEA,IAAA,OAAON,sBAAsB,CAACoC,IAAI,CAACvC,IAAI,CAAC;CACzC,GAAC,CAAC;;CAEF;GACAA,IAAI,CAACwC,IAAI,CAAC,SAAS,EAAC,SAAS,EAAEZ,KAAY,IAAG;CAE7C;CACA,IAAA,IAAI,EAAEA,KAAK,IAAIrB,UAAU,CAAC,EAAE;CAC3B,MAAA,OAAON,YAAY,CAACsC,IAAI,CAACvC,IAAI,EAAC4B,KAAK,CAAC;CACrC;KAEA,OAAOD,WAAW,CAACC,KAAK,CAAC;CAC1B,GAAC,CAAC;;CAGF;GACA5B,IAAI,CAACwC,IAAI,CAAC,SAAS,EAAC,cAAc,EAAC,CAAEC,OAAmB,EAAEC,SAAqB,KAAG;KAEjF,IAAI,CAACjC,YAAY,EAAE;CAClBT,MAAAA,IAAI,CAAC2C,YAAY,CAACZ,WAAW,CAAC;MAC9B,MAAK,IAAIrB,aAAa,EAAE;CACxB,MAAA,MAAMkC,YAAY,GAAGH,OAAO,CAAC,CAAC,CAAC;OAC/B,IAAIG,YAAY,KAAKC,SAAS,EAAE;CAC/BnC,QAAAA,aAAa,CAACoC,OAAO,CAACb,KAAK,GAAIW,YAAY,CAAC5C,IAAI,CAACY,QAAQ,CAACmC,UAAU,CAAC;CACtE;CACD;KAEA1C,iBAAiB,CAACkC,IAAI,CAAEvC,IAAI,EAAEyC,OAAO,EAAEC,SAAS,CAAC;CAEjDjC,IAAAA,YAAY,GAAG,KAAK;CACrB,GAAC,CAAC;;CAGF;CACA;CACA;CACAT,EAAAA,IAAI,CAACwC,IAAI,CAAC,OAAO,EAAC,gBAAgB,EAAC,MAAI;CAEtC,IAAA,MAAMZ,KAAK,GAAI5B,IAAI,CAACgD,SAAS;CAC7B,IAAA,IAAIhB,MAAM;CAEV,IAAA,IAAIL,WAAW,CAACC,KAAK,CAAC,EAAE;CAEvBI,MAAAA,MAAM,GAAGhC,IAAI,CAACiD,MAAM,CAAC,cAAc,EAAC;CAACrB,QAAAA,KAAK,EAACA;CAAK,OAAC,CAAC;CAClD,MAAA,IAAII,MAAM,EAAE;SACXA,MAAM,CAACkB,YAAY,CAAC,iBAAiB,EAAC,EAAE,CAAC,CAAC;CAC1CxC,QAAAA,aAAa,GAAGsB,MAAM;CACvB;CAED,KAAC,MAAK,IAAKJ,KAAK,IAAIrB,UAAU,IAAK,CAACC,gBAAgB,CAAC2C,aAAa,CAAC,aAAa,CAAC,EAAE;CAClFnB,MAAAA,MAAM,GAAGhC,IAAI,CAACiD,MAAM,CAAC,iBAAiB,EAAC;CAACrB,QAAAA,KAAK,EAACA;CAAK,OAAC,CAAC;CACtD;CAEA,IAAA,IAAII,MAAM,EAAE;OACXnD,UAAU,CAACmD,MAAM,EAAChC,IAAI,CAACY,QAAQ,CAACwC,WAAW,CAAC;CAC5C5C,MAAAA,gBAAgB,CAAC6C,MAAM,CAAErB,MAAO,CAAC;CAClC;CAED,GAAC,CAAC;;CAGF;CACAhC,EAAAA,IAAI,CAACsD,EAAE,CAAC,YAAY,EAAC,MAAI;KACxB3C,cAAc,GAAG4C,MAAM,CAACC,IAAI,CAACxD,IAAI,CAACyC,OAAO,CAAC;KAC1CjC,gBAAgB,GAAGR,IAAI,CAACQ,gBAAgB;;CAExC;KACAR,IAAI,CAACY,QAAQ,CAACqC,MAAM,GAAGM,MAAM,CAACE,MAAM,CAAC,EAAE,EAAE;OACxChD,YAAY,EAACA,MAAM;CAClB,QAAA,OAAO,CAAmE,iEAAA,CAAA;QAC1E;OACDiD,eAAe,EAACA,MAAK;CACpB,QAAA,OAAO,CAAoD,kDAAA,CAAA;CAC5D;CACD,KAAC,EAAC1D,IAAI,CAACY,QAAQ,CAACqC,MAAM,CAAC;;CAGvB;CACAzC,IAAAA,gBAAgB,CAACmD,gBAAgB,CAAC,QAAQ,EAAC,MAAI;OAE9C,IAAI,CAAC3D,IAAI,CAACY,QAAQ,CAACC,cAAc,CAAC0B,IAAI,CAACvC,IAAI,CAAC,EAAE;CAC7C,QAAA;CACD;;CAEA;CACA,MAAA,IAAI,CAAC2B,WAAW,CAAC3B,IAAI,CAACgD,SAAS,CAAC,EAAE;CACjC,QAAA;CACD;;CAEA;CACA,MAAA,IAAIvC,YAAY,EAAG;CAGnBA,MAAAA,YAAY,GAAG,IAAI;OACnBT,IAAI,CAAC4D,IAAI,CAACrB,IAAI,CAACvC,IAAI,EAACA,IAAI,CAACgD,SAAS,CAAC;CACpC,KAAC,CAAC;CACH,GAAC,CAAC;CAEH;;;;;;;;"}